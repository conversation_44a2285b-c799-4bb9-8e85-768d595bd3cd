package com.wftk.scale.portal.service;


import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleQuestionTreeDTO;

import java.util.List;

/**
 * @InterfaceName: ScaleQuestionProxyService
 * @Description: 量表题目
 * @Author: mq
 * @Date: 2024/11/20
 * @Version: 1.0
 **/
public interface ScaleQuestionProxyService {

    /*
    * @Author: mq
    * @Description: 根据量表ID获取题目信息
    * @Date: 2024/11/20 18:44
    * @Param: scaleId
    * @return: Page<ScaleQuestionTreeDTO>
    **/
    List<ScaleQuestionTreeDTO> selectPage(Long scaleId);


//    List<ScaleQuestionTreeDTO> getQuestionsByScaleId(Long scaleId);
}
