package com.wftk.scale.portal;

import com.wftk.file.manager.spring.boot.autoconfigure.oss.EnableOSSClient;
import com.wftk.jackson.spring.boot.autoconfigure.datetime.EnableDateTimeFormatter;
import com.wftk.message.spring.boot.autoconfigure.ext.properties.MessageProperties;
import com.wftk.opt.log.spring.boot.autoconfigure.EnableOptLog;
import com.wftk.scale.biz.config.BizConfig;
import com.wftk.signature.spring.boot.autoconfigure.EnableIPFilter;
import com.wftk.signature.spring.boot.autoconfigure.EnableSignatureFilter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @create 2024/10/23 17:30
 */
@SpringBootApplication
@EnableDateTimeFormatter
@EnableOptLog
@Import(BizConfig.class)
//@EnableIPFilter
@EnableSignatureFilter
@EnableOSSClient
@EnableScheduling
@EnableConfigurationProperties({MessageProperties.class})
public class ScalePortalApplication {

    public static void main(String[] args) {
        SpringApplication.run(ScalePortalApplication.class, args);
    }
}
