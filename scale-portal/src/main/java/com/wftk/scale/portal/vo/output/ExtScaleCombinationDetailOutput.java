package com.wftk.scale.portal.vo.output;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import lombok.Data;


/**
 * <AUTHOR>
 * @createDate 2024/12/26 15:12
 */
@Data
public class ExtScaleCombinationDetailOutput {
    /**
     * 量表id
     */
    private Long id;

    /**
     * 量表code
     */
    private String code;

    /**
     * 量表名称
     */
    private String name;

    /**
     * 量表介绍
     */
    private String intro;

    /**
     * 量表指导语
     */
    private String guideline;

    /**
     * 量表问题数量
     */
    private Integer numOfQuestion;

    /**
     * 封面图url
     */
    @RFile(role = FileConstant.FILE_SCALE_PUB_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    private String cover;

}
