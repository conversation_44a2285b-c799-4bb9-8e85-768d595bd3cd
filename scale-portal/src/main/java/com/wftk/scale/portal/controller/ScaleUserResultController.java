package com.wftk.scale.portal.controller;

import cn.hutool.core.util.ObjUtil;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.scale.biz.entity.Order;
import com.wftk.scale.biz.entity.ScaleListing;
import com.wftk.scale.biz.entity.ScaleListingUserConf;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.lock.LockManager;
import com.wftk.scale.biz.service.OrderService;
import com.wftk.scale.biz.service.ScaleUserResultService;
import com.wftk.scale.portal.annotation.namespace.ScaleMapping;
import com.wftk.scale.portal.converter.ScaleUserResultConverter;
import com.wftk.scale.portal.vo.input.ScaleUserResultCheckInput;
import com.wftk.scale.portal.vo.input.ScaleUserResultCreateInput;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: ScaleUserResultController
 * @Description: 测评记录信息管理
 * @Author: mq
 * @Date: 2024-11-07 17:13
 * @Version: 1.0
 **/
@Tag(name = "量表测评管理相关API")
@RestController
@ScaleMapping("/user/result")
@Slf4j
public class ScaleUserResultController {

    @Autowired
    private ScaleUserResultService scaleUserResultService;

    @Autowired
    private ScaleUserResultConverter scaleUserResultConverter;

    @Autowired
    private OrderService orderService;
    @Resource
    private LockManager lockManager;

    @Operation(summary = "开始量表测评（校验量表是否可进行测试）")
    @OptLog(module = "量表测评模块", optType = OptType.CREATE, description = "开始量表测评")
    @PostMapping("check")
    public ApiResult<String> startEvaluation(@Valid @RequestBody ScaleUserResultCheckInput input) {
        ScaleUserResult userResult = scaleUserResultConverter.ScaleUserResultCheckInputToEntity(input);
        ScaleListing scaleListing = scaleUserResultService.findByListingId(userResult.getScaleListingId());
        Long userId = ObjUtil.defaultIfNull(userResult.getUserId(), AuthenticationHolder.getAuthentication().getAuthUser().getId());
        boolean result = scaleUserResultService.vaildScaleListingEnabled(scaleListing);
        if (result) {
            throw new BusinessException("测评失败,量表已禁用，无法进行测评!");
        }
        //校验答题顺序
        scaleUserResultService.validAnswerSort(input.getScaleId(), input.getScaleListingId(), input.getOrderNo(), userId);
        ScaleListingUserConf userConf = scaleUserResultService.findByListingIdAndlistingUserId(userResult.getScaleListingId(), userResult.getListingUserId());
        result = scaleUserResultService.vaildUserConfEvaluationTimeOut(userConf);
        if (result) {
            throw new BusinessException("测评失败,量表已超过配置的有效截止日期!");
        }

        result = scaleUserResultService.vaildScaleEvaluationAgain(userResult, userId, input.getOrderNo(), userConf);
        if (!result) {
            throw new BusinessException("测评失败,测评已完成，无法再次复测!");
        }
        return ApiResult.ok();
    }

    @Operation(summary = "提交用户测评记录信息")
    @OptLog(module = "量表测评模块", optType = OptType.CREATE, description = "提交用户测评记录信息")
    @PostMapping
    public ApiResult<Long> saveUserResult(@Valid @RequestBody ScaleUserResultCreateInput input) {
        DLock lock = lockManager.getLock(buildRedisKey(input.getUserResult().getUserId(), input.getUserResult().getOrderNo()));
        try{
            //防重复测评，参数：等待时间、占用时间、单位
            if(lock.tryLock(0, 30, TimeUnit.SECONDS)){
                ScaleUserResult userResult = scaleUserResultConverter.scaleUserResultInputToEntity(input.getUserResult());
                List<ScaleUserResultRecord> recordList = scaleUserResultConverter.scaleUserResultRecordListToEntityList(input.getResultRecordList());
                boolean result = scaleUserResultService.vaildScaleUserResultRecordEmpty(recordList);
                if (result) {
                    throw new BusinessException("测评失败,测评答题信息不能为空!");
                }
                ScaleListing scaleListing = scaleUserResultService.findByListingId(userResult.getScaleListingId());
                result = scaleUserResultService.vaildScaleListingEnabled(scaleListing);
                if (result) {
                    throw new BusinessException("测评失败,量表已禁用无法进行测评!");
                }
                ScaleListingUserConf userConf = scaleUserResultService.findByListingIdAndlistingUserId(userResult.getScaleListingId(), userResult.getListingUserId());
                result = scaleUserResultService.vaildUserConfEvaluationTimeOut(userConf);
                if (result) {
                    throw new BusinessException("测评失败,量表已超过配置的有效截止日期!");
                }
                result = scaleUserResultService.vaildScaleTimeLimit(userResult, userConf);
                if (result) {
                    throw new BusinessException("测评失败,作答时间不满足答题要求!");
                }
                Long userId = ObjUtil.defaultIfNull(userResult.getUserId(), AuthenticationHolder.getAuthentication().getAuthUser().getId());
                result = scaleUserResultService.vaildScaleEvaluationAgain(userResult, userId, userResult.getOrderNo(), userConf);
                if (!result) {
                    throw new BusinessException("测评失败,测评已完成，无法再次复测!");
                }
                userResult.setUserId(userId);
                ScaleUserResult scaleUserResult = scaleUserResultService.create(userResult, recordList);
                // 订单完成通知
                Order order = orderService.notifyComplete(scaleUserResult.getOrderNo());
                //通知用户测评结果
                scaleUserResultService.notifyUserResult(order, scaleUserResult);
                return ApiResult.ok(scaleUserResult.getId());
            }else{
                return ApiResult.fail(500, "请勿重复提交测评");
            }
        }catch (Exception e){
            log.error("saveUserResult is error .", e);
            throw new BusinessException("提交用户测评记录信息失败", e);
        }finally {
            lock.unLock();
        }
    }

    private String buildRedisKey(Long userId, String orderNo){
        return "user_result_" + userId + "_" + orderNo;
    }
}
