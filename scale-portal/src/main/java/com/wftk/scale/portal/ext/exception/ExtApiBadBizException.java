package com.wftk.scale.portal.ext.exception;

import com.wftk.scale.portal.ext.enums.ExtApiRespEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/3/6 14:15
 */
@Getter
public class ExtApiBadBizException extends RuntimeException {

    private final Integer code;

    public ExtApiBadBizException(Integer code) {
        this.code = code;
    }

    public ExtApiBadBizException(ExtApiRespEnum guorenRespEnum) {
        super(guorenRespEnum.getMsg());
        this.code = guorenRespEnum.getCode();
    }

    public ExtApiBadBizException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public ExtApiBadBizException(Throwable cause, Integer code) {
        super(cause);
        this.code = code;
    }

    public ExtApiBadBizException(Throwable cause, Integer code,String message) {
        super(message, cause);
        this.code = code;
    }


}
