package com.wftk.scale.portal.vo.output;

import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/12/17 16:24
 */
@Data
public class ExtValidCombinationScaleOutput {

    /**
     * 量表id
     */
    private Long id;

    /**
     * 上下架ID
     */
    private Long scalelistingId;

    /**
     * 量表名称
     */
    private String name;

    /**
     * 量表编号
     */
    private String code;

    /**
     * 测评方式，1依次测评，2选择测评
     */
    private Integer type;

    /**
     * 量表数量
     */
    private Integer numOfScale;


    /**
     * 量表创建时间
     */
    private LocalDateTime createTime;


    /**
     * 详情
     */
    private List<ScaleQueryDTO> details;

    /**
     * 量表上架项目配置开始时间
     */
    private LocalDateTime listingStartTime;

    /**
     * 量表上架项目配置结束时间
     */
    private LocalDateTime listingEndTime;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 订单号
     */
    private String orderNo;

}
