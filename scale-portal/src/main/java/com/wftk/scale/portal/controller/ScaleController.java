package com.wftk.scale.portal.controller;


import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordParamDTO;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordQueryDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedParamDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.portal.annotation.namespace.ScaleMapping;
import com.wftk.scale.portal.converter.ScaleListingConverter;
import com.wftk.scale.portal.service.ScaleProxyService;
import com.wftk.scale.portal.vo.input.ScaleListedQueryInput;
import com.wftk.scale.portal.vo.input.ScaleListingUserRecordQueryInput;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: ScaleController
 * @Description:
 * @Author: mq
 * @Date: 2024/11/20
 * @Version: 1.0
 **/
@Tag(name = "单个量表维护相关API")
@RestController
@ScaleMapping("single/base")
@Slf4j
public class ScaleController {

    @Autowired
    private ScaleProxyService scaleProxyService;

    @Autowired
    private ScaleListingConverter scaleListingConverter;

    @Operation(summary = "获取已上架单个量表列表数据（页面展示）")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取已上架单个量表列表数据")
    @GetMapping("/listing")
    public ApiResult<Page<ScaleListedQueryDTO>> getScaleListedByPageDisplay(ScaleListedQueryInput input) {
        ScaleListedParamDTO searchParam = scaleListingConverter.scaleListedQueryInputToParamDTO(input);
        return ApiResult.ok(scaleProxyService.getScaleListedByPageDisplay(searchParam));
    }

    @Operation(summary = "获取单个量表分发记录列表数据（用户分发）")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取单个量表分发记录列表数据")
    @GetMapping("distribute")
    public ApiResult<Page<ScaleListingUserRecordQueryDTO>> selectDistributePage(ScaleListingUserRecordQueryInput input) {
        ScaleListingUserRecordParamDTO searchParam = scaleListingConverter.scaleListingUserRecordQueryInputToParamDTO(input);
        return ApiResult.ok(scaleProxyService.getAllScaleDistributeRecord(searchParam));
    }

    @Operation(summary = "根据量表ID获取单个量表详情信息")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据量表ID获取单个量表详情信息")
    @GetMapping("detail/id")
    public ApiResult<ScaleQueryDTO> findByScaleId(Long scaleListingId, Long scaleId, Long listingUserId, String orderNo) {
        if (ObjUtil.isNull(scaleListingId)) {
            throw new BusinessException("获取失败,量表scaleListingId参数不允许为空!");
        }
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("获取失败,量表scaleId参数不允许为空!");
        }
        ScaleQueryDTO dto = scaleProxyService.findByScaleId(scaleListingId, scaleId, listingUserId, orderNo);
        return ApiResult.ok(dto);
    }

    @Operation(summary = "根据量表编号获取单个量表最新版本数据")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据量表编号获取单个量表最新版本数据")
    @GetMapping("detial/code")
    public ApiResult<ScaleQueryDTO> findByScaleCode(Long scaleListingId, String scaleCode) {
        if (ObjUtil.isNull(scaleListingId)) {
            throw new BusinessException("获取失败,量表scaleListingId参数不允许为空!");
        }
        if (StrUtil.isEmpty(scaleCode)) {
            throw new BusinessException("获取失败,量表scaleCode参数不允许为空!");
        }
        ScaleQueryDTO dto = scaleProxyService.findByScaleCode(scaleListingId, scaleCode);
        return ApiResult.ok(dto);
    }

//    @Operation(summary = "获取量表测评须知")
//    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取量表测评须知")
//    @GetMapping("/assessment/instructions")
//    public ApiResult<String> instructions() {
//        //TODO 模板待补充
//        return ApiResult.ok(
//                "<h2>请您仔细阅读以下每一条的说明，把意思弄明白，然后对照自己最近一周来的感受，选择最符合您实际情况的答案。</h2>" +
//                "<h3 style=\"margin-top:12px\">备注:</h3>" +
//                "<p style=\"margin-top:12px\">①“很少”表示出现类似情况的频率少于1天或没有出现;</p>" +
//                "<p style=\"margin-top:12px\">②“有时”表示至少2-3天会出现类似情况;</p>" +
//                "<p style=\"margin-top:12px\">③“经常”表示至少4-5天会出现类似情况;</p>" +
//                "<p style=\"margin-top:12px\">④“大部分时间”表示几乎每天都会出现类似情况。</p>");
//    }
}
