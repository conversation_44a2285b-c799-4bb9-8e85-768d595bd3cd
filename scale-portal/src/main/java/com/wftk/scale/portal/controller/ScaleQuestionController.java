package com.wftk.scale.portal.controller;


import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleQuestionTreeDTO;
import com.wftk.scale.biz.service.ScaleQuestionService;
import com.wftk.scale.portal.annotation.namespace.ScaleMapping;
import com.wftk.scale.portal.service.ScaleQuestionProxyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName: ScaleQuestionController
 * @Description:
 * @Author: mq
 * @Date: 2024/11/20
 * @Version: 1.0
 **/
@Tag(name = "量表题目管理相关API")
@RestController
@ScaleMapping("/questions")
@Slf4j
public class ScaleQuestionController {

    @Autowired
    private ScaleQuestionProxyService scaleQuestionProxyService;

    @Operation(summary = "根据量表ID获取量表题目信息列表数据")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据量表ID获取量表题目信息列表数据")
    @GetMapping
    public ApiResult<List<ScaleQuestionTreeDTO>> getQuestionList(Long scaleId) {
        if(ObjUtil.isNull(scaleId)) {
            throw new BusinessException("获取失败,量表scaleId参数不允许为空!");
        }
        List<ScaleQuestionTreeDTO> pageData = scaleQuestionProxyService.selectPage(scaleId);
        return ApiResult.ok(pageData);
    }
}
