package com.wftk.scale.portal.ext.handler;

import com.wftk.common.core.result.ApiResult;
import com.wftk.scale.portal.ext.exception.ExtApiBadBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @create 2024/3/6 15:53
 */
@RestControllerAdvice
@Slf4j
public class ExtApiBadBizExceptionHandler {


    @ExceptionHandler({ExtApiBadBizException.class})
    public ApiResult<Object> handleExtApiBadBizException(ExtApiBadBizException e) {
        log.error("ext api exception.", e);
        return ApiResult.fail(e.getCode(), e.getMessage());
    }


}