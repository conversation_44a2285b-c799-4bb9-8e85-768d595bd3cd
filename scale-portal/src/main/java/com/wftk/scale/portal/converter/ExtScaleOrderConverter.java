package com.wftk.scale.portal.converter;

import com.wftk.scale.biz.dto.scale.ScaleSerialDTO;
import com.wftk.scale.biz.entity.Order;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.portal.vo.input.ExtScaleSerialInput;
import com.wftk.scale.portal.vo.output.ExtEvaluationRecordOutput;
import com.wftk.scale.portal.vo.output.ExtOrderOutput;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/11/18 17:53
 */
@Mapper(componentModel = "spring")
public interface ExtScaleOrderConverter {

    ExtOrderOutput entityToOrderOutput(Order order);

    List<ExtOrderOutput> entityToOrderOutput(List<Order> orders);

    List<ScaleSerialDTO> scaleSerialInputsToScaleSerialDTO(List<ExtScaleSerialInput> scaleSerialInputs);

    List<ExtEvaluationRecordOutput> scaleUserResultsToExtEvaluationRecordOutput(List<ScaleUserResult> scaleUserResults);

}
