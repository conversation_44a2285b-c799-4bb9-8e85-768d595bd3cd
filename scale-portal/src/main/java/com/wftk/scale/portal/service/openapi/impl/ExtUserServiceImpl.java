package com.wftk.scale.portal.service.openapi.impl;

import cn.hutool.core.util.StrUtil;
import com.wftk.scale.biz.entity.Department;
import com.wftk.scale.biz.entity.User;
import com.wftk.scale.biz.entity.UserDepartment;
import com.wftk.scale.biz.entity.UserExtraInfo;
import com.wftk.scale.biz.service.*;
import com.wftk.scale.portal.converter.ExtUserConverter;
import com.wftk.scale.portal.ext.enums.ExtApiRespEnum;
import com.wftk.scale.portal.ext.exception.ExtApiBadBizException;
import com.wftk.scale.portal.service.openapi.ExtUserService;
import com.wftk.scale.portal.vo.input.ExtUserCreateInput;
import com.wftk.scale.portal.vo.output.ExtUserOutput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @createDate 2024/11/18 17:07
 */
@Service
@Slf4j
public class ExtUserServiceImpl implements ExtUserService {

    @Autowired
    UserService userService;

    @Autowired
    UserExtraInfoService userExtraInfoService;

    @Autowired
    TerminalService terminalService;

    @Autowired
    DepartmentService departmentService;

    @Autowired
    UserDepartmentService userDepartmentService;

    @Autowired
    ExtUserConverter userConverter;

    @Transactional
    @Override
    public ExtUserOutput createExtUser(ExtUserCreateInput createUserInput, String terminalCode) {

        // step1 判断用户是否存在
        // 通过终端编码查询部门
        Department department = departmentService.selectByTerminalCode(terminalCode);
        if(department == null){
            throw new ExtApiBadBizException(ExtApiRespEnum.TERMINAL_NOT_BINDING_DEPARTMENT);
        }

        User user = userService.selectByCodeAndDepartmentId(createUserInput.getCode(), department.getId());
        if(user != null){
            log.warn("user `{}` is register", createUserInput.getCode());
            return userConverter.entityToUserOutput(user);
        }

        // step2 新增用户
        User saveUser = userConverter.createUserInputToEntity(createUserInput);
        saveUser.setPassword(userService.getPassword(saveUser.getAccount()));
        userService.save(saveUser);

        // step3 新增用户额外信息
        if(createUserInput.getUserType() != null && StrUtil.isNotBlank(createUserInput.getExtraInfo())){
            UserExtraInfo userExtraInfo = new UserExtraInfo();
            userExtraInfo.setUserType(createUserInput.getUserType());
            userExtraInfo.setExtraInfo(createUserInput.getExtraInfo());
            userExtraInfo.setUserId(saveUser.getId());
            userExtraInfoService.save(userExtraInfo);
        }

        // step4 保存用户与部门关联数据
        UserDepartment userDepartment = new UserDepartment();
        userDepartment.setDepartmentId(department.getId());
        userDepartment.setUserId(saveUser.getId());
        userDepartmentService.save(userDepartment);

        return userConverter.entityToUserOutput(saveUser);
    }




}
