package com.wftk.scale.portal.vo.output;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/11/29 13:54
 */
@Data
public class ExtScaleCombinationQueryOutput {

    /**
     * 组合列表ID
     */
    private Long id;

    /**
     * 组合名称
     */
    private String name;

    /**
     * 上架Id
     */
    private Long listingId;

    /**
     * 上架呈现方式: 1.页面展示; 2.用户分发;
     */
    private Integer listingShowType;

    /**
     * 量表编码
     */
    private String code;

    /**
     * 测评方式，1依次测评，2选择测评
     */
    private Integer type;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 量表数量
     */
    private Integer numOfScale;

    /**
     * 列表名称
     */
    private List<ExtScaleCombinationDetailOutput> scaleCombinationDetails;
}
