package com.wftk.scale.portal.vo.input;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2024/11/18 17:12
 */
@Data
public class ExtUserCreateInput {

    /**
     * 用户编码
     */
    @NotBlank(message = "code不能为空")
    private String code;

    /**
     * 账户
     */
    @NotBlank(message = "account不能为空")
    private String account;

    /**
     * 姓名
     */
    @NotBlank(message = "name不能为空")
    private String name;

    /**
     * 性别
     */
    @NotNull(message = "sex不能为空")
    private Integer sex;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 手机号码
     */
    @NotBlank(message = "phone不能为空")
    private String phone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用户类型
     */
    private Integer userType;

    /**
     * 用户其他属性
     */
    private String extraInfo;

}
