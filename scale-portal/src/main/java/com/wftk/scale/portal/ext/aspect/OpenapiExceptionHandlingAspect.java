package com.wftk.scale.portal.ext.aspect;

import com.wftk.common.core.result.ApiResult;
import com.wftk.scale.portal.ext.enums.ExtApiRespEnum;
import com.wftk.scale.portal.ext.exception.ExtApiBadBizException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createDate 2024/12/14 15:11
 */
//@Aspect
//@Component
@Slf4j
public class OpenapiExceptionHandlingAspect {

    // 定义Pointcut，指定拦截哪些方法
    @Pointcut("execution(* com.wftk.scale.portal.controller.openapi.*(..))")
    public void controllerMethods() {
    }

    // 环绕通知，拦截方法并处理异常
    @Around("controllerMethods()")
    public Object handleExceptions(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            // 执行方法，即调用joinPoint.proceed()
            return joinPoint.proceed();
        } catch (ExtApiBadBizException e) {
            return ApiResult.fail(e.getCode(),e.getMessage());
        } catch (Exception e){
            log.error("openapi catch exception.",e);
            return ApiResult.fail(ExtApiRespEnum.SYSTEM_ERROR.getCode(),ExtApiRespEnum.SYSTEM_ERROR.getMsg());
        }
    }

}
