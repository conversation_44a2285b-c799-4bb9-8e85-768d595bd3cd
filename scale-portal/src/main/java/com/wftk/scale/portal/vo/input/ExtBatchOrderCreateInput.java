package com.wftk.scale.portal.vo.input;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/12/9 20:00
 */
@Data
public class ExtBatchOrderCreateInput {

    /**
     * 量表类型 1.单个量表   2.组合量表
     */
    @NotNull(message = "type不能为空")
    private Integer type;

    /**
     * 用户编码
     */
    @NotBlank(message = "userCode不能为空")
    private String userCode;

    /**
     * 支付渠道
     */
    @NotNull(message = "payChannel不能为空")
    private Integer payChannel;

    /**
     * 流水号以及量表编码
     */
    private List<ExtScaleSerialInput> scaleSerialInputs;


}
