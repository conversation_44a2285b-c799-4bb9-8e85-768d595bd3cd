package com.wftk.scale.portal.vo.input;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: ScaleUserResultCheckInput
 * @Description:
 * @Author: mq
 * @Date: 2024/12/13
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleUserResultCheckInput implements Serializable {

    /**
     * 用户id
     */
    @Schema(title = "用户ID", name = "userId", defaultValue = "622275771041221", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 量表id
     */
    @Schema(title = "量表ID", name = "userId", defaultValue = "622275771041221", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /**
     * 上下架id，来自scale_listing表的主键
     */
    @Schema(title = "上下架ID", name = "scaleListingId", defaultValue = "622275771041221", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "上下架ID不能为空")
    private Long scaleListingId;

    /**
     * 订单编号
     */
    @Schema(title = "订单编号", name = "orderNo", defaultValue = "100000", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String orderNo;

    /**
     * 如果是页面展示的，该字段无值来自scale_listing_user_record表的主键
     */
    @Schema(title = "分发记录ID", name = "listingUserId", defaultValue = "100000", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long listingUserId;
}
