package com.wftk.scale.portal.annotation.namespace;

import com.wftk.namespace.spring.boot.autoconfigure.annotation.NamespaceRequestMapping;
import com.wftk.scale.portal.common.constant.NamespaceConstant;
import org.springframework.core.annotation.AliasFor;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@NamespaceRequestMapping
@RestController
public @interface OrderMapping {

    @AliasFor(annotation = NamespaceRequestMapping.class)
    String namespace() default NamespaceConstant.ORDER;

    @AliasFor(annotation = NamespaceRequestMapping.class)
    String[] value() default {};

    @AliasFor(annotation = NamespaceRequestMapping.class)
    String[] path() default {};

    @AliasFor(annotation = NamespaceRequestMapping.class)
    RequestMethod[] method() default {};

}
