package com.wftk.scale.portal.vo.input;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2024/11/18 20:42
 */
@Data
public class ExtScaleCombinationQueryInput {

    @Schema(title = "量表编码",  requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String code;

    @Schema(title = "量表名称",  requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String scaleName;

    /**
     * 测评方式，1依次测评，2选择测评
     */
    @Schema(title = "测评方式",  requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer type;

    /**
     * 上架呈现方式: 1.页面展示; 2.用户分发;
     */
    @Schema(title = "呈现方式",  requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer listingShowType;

    @Schema(title = "分页页码",  requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "pageNum不能为空")
    private Integer pageNum;

    @Schema(title = "分页大小",  requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;


}
