package com.wftk.scale.portal.ext.sign;

import cn.hutool.core.util.StrUtil;
import com.wftk.scale.biz.entity.TenantClientInfo;
import com.wftk.scale.biz.entity.TenantSetting;
import com.wftk.scale.biz.service.TenantClientInfoService;
import com.wftk.scale.biz.service.TenantSettingService;
import com.wftk.scale.portal.ext.constant.AlgorithmConstant;
import com.wftk.signature.builder.ClientInfo;
import com.wftk.signature.loader.ClientCredentialLoader;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/11/30 11:29
 */
@Slf4j
public class TenantClientCredentialLoader implements ClientCredentialLoader {

    private final TenantSettingService tenantSettingService;
    private final TenantClientInfoService tenantClientInfoService;

    public TenantClientCredentialLoader(TenantSettingService tenantSettingService, TenantClientInfoService tenantClientInfoService) {
        this.tenantSettingService = tenantSettingService;
        this.tenantClientInfoService = tenantClientInfoService;
    }

    @Override
    public ClientInfo get(String clientId) {
        TenantClientInfo tenantClientInfo = tenantClientInfoService.findOneByClientId(clientId, true);
        if (tenantClientInfo == null) {
            log.warn("invalid clientId: {}", clientId);
            return null;
        }
        TenantSetting clientSetting = tenantSettingService.getTenantId(tenantClientInfo.getTenantId());
        if (clientSetting == null) {
            log.warn("invalid tenantId: {}", tenantClientInfo.getTenantId());
            return null;
        }
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setClientId(tenantClientInfo.getClientId());
        clientInfo.setClientSecret(tenantClientInfo.getClientSecret());
        clientInfo.setDefaultAlgorithm(tenantClientInfo.getAlgorithm());
        if(!AlgorithmConstant.MD5.equals(tenantClientInfo.getAlgorithm())){
            try {
                ClientInfo.EncryptionInfo entryptionInfo = new ClientInfo.EncryptionInfo();
                entryptionInfo.setPublicKey(tenantClientInfo.getPublicKey());
                entryptionInfo.setPrivateKey(tenantClientInfo.getPrivateKey());
                entryptionInfo.setTargetPublicKey(tenantClientInfo.getTargetPublicKey());
                clientInfo.setEncryptionInfo(entryptionInfo);
            }catch (Exception e){
                log.error("tenantId: {} invalid clientId: {} no key info", tenantClientInfo.getTenantId(),clientId);
                return null;
            }
        }
        clientInfo.setClientId(clientId);

        Set<String> ips = null;
        if(StrUtil.isNotBlank(tenantClientInfo.getServerIp())) {
            try {
                ips = Arrays.stream(tenantClientInfo.getServerIp().split(",")).map(String::trim).collect(Collectors.toSet());
            } catch (Exception e) {
                ips.add(tenantClientInfo.getServerIp().trim());
            }
        }
        ClientInfo.IPInfo ipInfo = new ClientInfo.IPInfo();
        ipInfo.setIps(ips);
        // ipInfo.setIgnoreInternalIp(false);
        clientInfo.setIpInfo(ipInfo);
        return clientInfo;
    }

    @Override
    public int getOrder() {
        return -600;
    }
}
