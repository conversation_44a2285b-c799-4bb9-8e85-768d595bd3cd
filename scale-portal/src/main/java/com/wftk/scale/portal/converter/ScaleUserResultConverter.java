package com.wftk.scale.portal.converter;


import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.portal.vo.input.ScaleUserResultCheckInput;
import com.wftk.scale.portal.vo.input.ScaleUserResultInput;
import com.wftk.scale.portal.vo.input.ScaleUserResultRecordInput;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @InterfaceName: ScaleUserResultConverter
 * @Description:
 * @Author: mq
 * @Date: 2024/12/13
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleUserResultConverter {

    /*
    * @Author: mq
    * @Description: 将提交测评记录信息实体转换为数据实体
    * @Date: 2024/12/13 17:52
    * @Param: resultInput
    * @return: ScaleUserResult
    **/
    ScaleUserResult scaleUserResultInputToEntity(ScaleUserResultInput resultInput);

    /*
     * @Author: mq
     * @Description: 将提交测评记录信息实体转换为数据实体
     * @Date: 2024/12/13 17:52
     * @Param: resultInput
     * @return: ScaleUserResult
     **/
    ScaleUserResult ScaleUserResultCheckInputToEntity(ScaleUserResultCheckInput resultCheckInput);

    /*
    * @Author: mq
    * @Description: 将提交测评记录答题信息实体转换为数据实体
    * @Date: 2024/12/13 17:54
    * @Param: resultRecordInput
    * @return: ScaleUserResultRecord
    **/
    ScaleUserResultRecord scaleUserResultRecordInputToEntity(ScaleUserResultRecordInput resultRecordInput);
    
    /*
    * @Author: mq
    * @Description: 将提交测评记录答题信息实体转换为数据实体
    * @Date: 2024/12/13 17:56
    * @Param: resultRecordInputList
    * @return: List<ScaleUserResultRecord>
    **/
    List<ScaleUserResultRecord> scaleUserResultRecordListToEntityList(List<ScaleUserResultRecordInput> resultRecordInputList);
}
