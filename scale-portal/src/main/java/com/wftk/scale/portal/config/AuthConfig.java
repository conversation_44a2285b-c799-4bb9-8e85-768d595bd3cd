package com.wftk.scale.portal.config;

import com.wftk.auth.spring.boot.autoconfigure.core.loader.ClientLoader;
import com.wftk.scale.biz.service.*;
import com.wftk.scale.portal.ext.auth.PortalUserLoader;
import com.wftk.scale.portal.ext.auth.SysClientLoader;
import com.wftk.scale.portal.ext.sign.TenantClientCredentialLoader;
import com.wftk.scale.portal.ext.sign.TerminalClientCredentialLoader;
import com.wftk.signature.spring.boot.autoconfigure.filter.SignatureFilter;
import com.wftk.signature.spring.boot.autoconfigure.properties.SignProperties;
import com.wftk.signature.spring.boot.autoconfigure.validator.DefaultSignValidator;
import com.wftk.signature.spring.boot.autoconfigure.validator.SignValidator;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerExceptionResolver;

import java.util.HashSet;

/**
 * <AUTHOR>
 * @create 2024/11/26 15:34
 */
@Configuration
public class AuthConfig {

    @Bean
    ClientLoader clientLoader(TenantSettingService tenantSettingService, TenantClientInfoService tenantClientInfoService) {
        return new SysClientLoader(tenantSettingService, tenantClientInfoService);
    }

    @Bean
    PortalUserLoader portalUserLoader(UserService userService) {
        return new PortalUserLoader(userService);
    }

    @Bean
    TenantClientCredentialLoader tenantClientCredentialLoader(TenantSettingService tenantSettingService, TenantClientInfoService tenantClientInfoService) {
        return new TenantClientCredentialLoader(tenantSettingService, tenantClientInfoService);
    }

    @Bean
    FilterRegistrationBean<SignatureFilter> openApiSignatureFilterRegistrationBean(SignProperties signProperties,
                                                                                   TenantSettingService tenantSettingService, TenantClientInfoService tenantClientInfoService,
                                                                                   @Qualifier("handlerExceptionResolver") HandlerExceptionResolver handlerExceptionResolver) {
        FilterRegistrationBean<SignatureFilter> signatureFilterFilterRegistrationBean = new FilterRegistrationBean<>();
        TerminalClientCredentialLoader terminalClientCredentialLoader = new TerminalClientCredentialLoader(tenantSettingService, tenantClientInfoService);
        // 此处签名参数中未包含秘钥(服务器端对接时，秘钥为隐私)
        SignValidator signValidator = new DefaultSignValidator(signProperties.getNonce(),
                signProperties.getExpireInMills(), terminalClientCredentialLoader, null, "client_id");
        SignatureFilter signatureFilter = new SignatureFilter(signValidator, new HashSet<>(), handlerExceptionResolver);
        signatureFilterFilterRegistrationBean.setFilter(signatureFilter);
        signatureFilterFilterRegistrationBean.setName("openApiSignatureFilter");
        signatureFilterFilterRegistrationBean.setOrder(signProperties.getFilter().getOrder() + 100);
        signatureFilterFilterRegistrationBean.addUrlPatterns("/openapi/*");
        return signatureFilterFilterRegistrationBean;
    }

}
