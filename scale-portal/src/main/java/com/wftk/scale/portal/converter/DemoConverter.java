package com.wftk.scale.portal.converter;

import com.wftk.scale.biz.entity.Demo;
import com.wftk.scale.portal.vo.input.DemoInput;
import com.wftk.scale.portal.vo.output.DemoOutput;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @create 2024/10/23 18:23
 */
@Mapper(componentModel = "spring")
public interface DemoConverter {

    DemoOutput entityToOutput(Demo demo);

    Demo inputToEntity(DemoInput demoInput);

}
