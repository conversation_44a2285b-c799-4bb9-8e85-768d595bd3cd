package com.wftk.scale.portal.service.openapi;

import com.wftk.scale.portal.vo.input.*;
import com.wftk.scale.portal.vo.output.ExtEvaluationRecordOutput;
import com.wftk.scale.portal.vo.output.ExtOrderLinkOutput;
import com.wftk.scale.portal.vo.output.ExtOrderOutput;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/11/12 11:04
 */
public interface ExtScaleOrderService {

    ExtOrderOutput create(ExtOrderCreateInput extOrderCreateInput, String terminalCode);

    List<ExtOrderOutput> createBatch(ExtBatchOrderCreateInput extBatchOrderCreateInput, String terminalCode);

    ExtOrderOutput queryStatus(ExtOrderQueryInput extOrderQueryInput,String terminalCode);

    ExtOrderLinkOutput genarateLink(ExtOrderLinkCreateInput extOrderLinkCreateInput,String terminalCode);

    List<ExtEvaluationRecordOutput> queryUserScaleRecord(ExtEvaluationRecordQueryInput extEvaluationRecordQueryInput, String terminalCode);
}
