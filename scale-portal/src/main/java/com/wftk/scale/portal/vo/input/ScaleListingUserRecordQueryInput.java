package com.wftk.scale.portal.vo.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: ScaleListingUserRecordQueryInput
 * @Description: 量表分发记录查询参数传输实体
 * @Author: mq
 * @Date: 2024-10-31 13:35
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleListingUserRecordQueryInput implements Serializable {

    /**
     * 量表名称
     */
    @Schema(title = "量表名称", name = "targetName", defaultValue = "睡眠测试量表", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String targetName;

    /**
     * 量表分类ID
     */
    @Schema(title = "量表分类ID", name = "targetType", defaultValue = "609527310759877", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long targetType;

    /**
     * 终端编号
     */
    @Schema(title = "终端编号", name = "terminalCode", defaultValue = "123", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String terminalCode;
}
