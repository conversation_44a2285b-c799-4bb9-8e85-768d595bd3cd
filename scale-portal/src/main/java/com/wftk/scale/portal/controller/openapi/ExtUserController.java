package com.wftk.scale.portal.controller.openapi;

import cn.hutool.core.collection.CollectionUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.jackson.core.JSONObject;
import com.wftk.scale.portal.annotation.namespace.OpenapiMapping;
import com.wftk.scale.portal.ext.enums.ExtApiRespEnum;
import com.wftk.scale.portal.ext.exception.ExtApiBadBizException;
import com.wftk.scale.portal.service.openapi.ExtUserService;
import com.wftk.scale.portal.vo.input.ExtUserCreateInput;
import com.wftk.scale.portal.vo.output.ExtUserOutput;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Set;

/**
 * <AUTHOR>
 * @createDate 2024/11/27 10:31
 */
@Tag(name = "外部用户相关接口")
@OpenapiMapping("/user")
@Slf4j
public class ExtUserController {

    @Autowired
    ExtUserService extUserService;

    @Autowired
    private LocalValidatorFactoryBean localValidatorFactoryBean;

    @Operation(summary = "创建用户")
    @PostMapping("/create")
    public ApiResult<ExtUserOutput> create(@RequestBody ExtUserCreateInput extOrderCreateInput, @RequestParam("tenantId")String tenantId, @RequestParam("terminalCode")String terminalCode){
        log.info("create user by {}. param: {}", terminalCode,
                JSONObject.getInstance().toJSONString(extOrderCreateInput));
        // 校验参数
        validateParams(extOrderCreateInput);
        ExtUserOutput user = extUserService.createExtUser(extOrderCreateInput, terminalCode);
        return ApiResult.ok(user);
    }

    private void validateParams(Object object) {
        Validator validator = localValidatorFactoryBean.getValidator();
        Set<ConstraintViolation<Object>> failConditions = validator.validate(object);
        if (CollectionUtil.isNotEmpty(failConditions)) {
            for (ConstraintViolation<Object> condition : failConditions) {
                throw new ExtApiBadBizException(ExtApiRespEnum.PARAMS_VERIFY_ERROR.getCode(),condition.getMessage());
            }
        }
    }

}
