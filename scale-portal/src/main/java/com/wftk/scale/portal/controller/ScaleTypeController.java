package com.wftk.scale.portal.controller;

import com.wftk.common.core.result.ApiResult;
import com.wftk.scale.biz.dto.scale.ScaleTypeDetailDTO;
import com.wftk.scale.biz.service.ScaleTypeService;
import com.wftk.scale.portal.annotation.namespace.ScaleMapping;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/11/29 15:32
 */
@Tag(name = "量表类型相关API")
@ScaleMapping("type")
public class ScaleTypeController {

    @Autowired
    ScaleTypeService scaleTypeService;


    @Operation(summary = "获取量表分类信息列表数据-全部")
    @GetMapping("datas")
    public ApiResult<List<ScaleTypeDetailDTO>> getAll(@RequestParam String terminalCode) {
        return ApiResult.ok(scaleTypeService.ownerScaleTypes(terminalCode));
    }
}
