package com.wftk.scale.portal.controller.openapi;

import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.LoadState;
import com.wftk.jackson.core.JSONObject;
import com.wftk.scale.biz.config.UserReportPdfPathConfig;
import com.wftk.scale.biz.manager.report.dto.FactorAnalyseReportDTO;
import com.wftk.scale.biz.manager.report.dto.ReportDTO;
import com.wftk.scale.biz.manager.report.dto.UserReportInfoDTO;
import com.wftk.scale.biz.manager.report.dto.base.*;
import com.wftk.scale.biz.manager.report.dto.content.*;
import com.wftk.scale.biz.service.ScaleUserReportService;
import com.wftk.scale.biz.service.ScaleUserResultService;
import com.wftk.scale.biz.util.PdfUtil;
import com.wftk.scale.portal.annotation.namespace.OpenBusinessMapping;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @createDate 2024/12/18 14:54
 */
@Tag(name = "pdf相关接口")
@OpenBusinessMapping("/pdf")
@Slf4j
public class PdfController {

    @Autowired
    private UserReportPdfPathConfig userReportPdfPathConfig;

    @Autowired
    private ScaleUserReportService scaleUserReportService;

    @Autowired
    ScaleUserResultService scaleUserResultService;

    @GetMapping("/test/template")
    public String echartsDemo() {
        //pdf生成
        String templateName = "echarts-template";

        // 设置页面基本信息
        Map<String, Object> data = new HashMap<>();

        data.put("pageTitle", "销售数据分析");
        data.put("currentDate", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 准备ECharts数据
        Map<String, Object> chartData = new HashMap<>();

        // X轴数据
        chartData.put("categories", Arrays.asList("1月", "2月", "3月", "4月", "5月", "6月"));

        // 系列数据
        List<Map<String, Object>> seriesList = new ArrayList<>();

        Map<String, Object> salesSeries = new HashMap<>();
        salesSeries.put("name", "销售额");
        salesSeries.put("type", "bar");
        salesSeries.put("data", Arrays.asList(120, 200, 150, 80, 70, 110));

        Map<String, Object> profitSeries = new HashMap<>();
        profitSeries.put("name", "利润");
        profitSeries.put("type", "line");
        profitSeries.put("data", Arrays.asList(60, 85, 72, 45, 35, 55));

        seriesList.add(salesSeries);
        seriesList.add(profitSeries);

        chartData.put("series", seriesList);

        // 将数据转换为JSON字符串（避免XSS攻击）
        ObjectMapper mapper = new ObjectMapper();
        try {
            String chartDataJson = mapper.writeValueAsString(chartData);
            data.put("chartDataJson", chartDataJson);
        } catch (JsonProcessingException e) {
            data.put("chartDataJson", "{}");
        }

        //调用具体的实现方法
        String fileName = IdUtil.getSnowflakeNextIdStr();
        try {
            String s = PdfUtil.contractHandler(userReportPdfPathConfig, templateName, data, fileName);
        } catch (Exception e) {
            log.error("pdf create fail.", e);
            return null;
        }
        return null;

    }

    @GetMapping
    public Map<String, Object> testReport() {
        ReportDTO reportDTO = new ReportDTO();
        ReadingInstructionsContentDTO readingInstructionsContentDTO = new ReadingInstructionsContentDTO();
        readingInstructionsContentDTO.setReadingInstruction("测评报告须结合实际情况及个体差异进行解读，避免信见与过度解读。测评没有绝对标准，结果不能代替专业诊 断，如有疑虑应咨询专业人士，报告须严格保密，未经被测者同意，不得泄鲜给第三");
        reportDTO.setReadingInstructionsContent(readingInstructionsContentDTO);

        reportDTO.setScaleName("匹兹堡睡眠");

        UserInfoReportContentDTO userInfoReportContent = new UserInfoReportContentDTO();
        UserReportInfoDTO userReportInfoDTO = new UserReportInfoDTO();
        userReportInfoDTO.setSex("男");
        userReportInfoDTO.setName("强哥");
        userReportInfoDTO.setBirthday("2002-10-08");
        userInfoReportContent.setUserReportInfo(userReportInfoDTO);
        userInfoReportContent.setStartTime("2025-9-15 11:00:00");
        userInfoReportContent.setEvaluationTime("120");
        reportDTO.setUserInfoReportContent(userInfoReportContent);
        JSONObject jsonObject = JSONObject.getInstance();

        reportDTO.setTotalScoreReportContent(buildTotal());

        reportDTO.setAvgScoreReportContent(buildAvg());

        reportDTO.setPositiveCountReportContent(buildPositiveCount());

        reportDTO.setFactorScoreContentDTO(buildFactorScoreCount());

        reportDTO.setFactorAnalyseReportContent(buildFactorAnalyse());


        String templateName = "userReport20250911";
        String fileName = IdUtil.getSnowflakeNextIdStr();
        String jsonString = jsonObject.toJSONString(reportDTO);
        Map<String, Object> data = jsonObject.parseMap(jsonString, String.class, Object.class);
        data.put("jsonData", jsonObject.toJSONString(data));
        log.info("data: {}", data);
        try {
            String path = PdfUtil.ftltoHtml(userReportPdfPathConfig, templateName, data, fileName);
            convertPdf(path);
        } catch (Exception e) {
            log.error("pdf create fail.", e);
            return null;
        }
        return data;
    }

    public String buildTitleTd(List<String> headers) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("<tr style=\"text-align:center;\">\n");
        headers.forEach(header -> stringBuilder.append("<td>").append(header).append("</td>"));
        stringBuilder.append("</tr>");
        return stringBuilder.toString();
    }

    public String buildDataTd(List<Map<String, Object>> datas, List<String> headers) {
        StringBuilder stringBuilder = new StringBuilder();
        for (Map<String, Object> row : datas) {
            stringBuilder.append("<tr style=\"text-align:center;\">");
            for (String field : headers) {
                Object value = row.get(field);
                stringBuilder.append("<td>").append(value != null ? value : "").append("</td>");
            }
            stringBuilder.append("</tr>");
        }
        return stringBuilder.toString();
    }

    public TotalScoreReportContentDTO buildTotal() {
        TotalScoreReportContentDTO totalScoreReportContentDTO = new TotalScoreReportContentDTO();
        totalScoreReportContentDTO.setRemark("备注1231024");

        ChartDTO chart = new ChartDTO();

        // 单向横断
        BarChartDTO barChartDTO = new BarChartDTO();
        List<Bar> bars = new ArrayList<>();
        Bar bar1 = new Bar();
        bar1.setId(1);
        bar1.setScore(null);

        Bar bar2 = new Bar();
        bar2.setId(2);
        bar2.setScore(null);

        Bar bar3 = new Bar();
        bar3.setId(3);
        bar3.setScore(BigDecimal.valueOf(7));

        Bar bar4 = new Bar();
        bar4.setId(4);
        bar4.setScore(null);

        bars.add(bar1);
        bars.add(bar2);
        bars.add(bar3);
        bars.add(bar4);
        barChartDTO.setBars(bars);
        barChartDTO.setLeft("正常");
        barChartDTO.setRight("严重");
        // 1.单向图表
        chart.setType(1);
        chart.setChartData(barChartDTO);

        FormDTO form = new FormDTO();
        List<String> headers = List.of("项目", "得分", "标识标签", "参考范围", "总分范围");
        form.setFormTitle(buildTitleTd(headers));
        List<Map<String, Object>> formData = new ArrayList<>();
        Map<String, Object> data1 = new HashMap<>();
        // scale_name\score\tag\scope\total_scope
        data1.put("项目", "匹兹堡睡眠指数");
        data1.put("得分", "7");
        data1.put("标识标签", "总体睡眠质量好");
        data1.put("参考范围", "0-10");
        data1.put("总分范围", "0-21");
        formData.add(data1);
        form.setFormData(buildDataTd(formData, headers));

        ReultIntroDTO resultIntro = new ReultIntroDTO();
        resultIntro.setResultRead("结果就会斤斤计较");
        resultIntro.setFactorName("总分");

        SuggestionDTO suggestion = new SuggestionDTO();
        suggestion.setFactorName("总分");
        suggestion.setSuggestion("建议就将计就计");
        suggestion.setFilePath("https://pic.rmb.bdstatic.com/bjh/240926/5c5a8c846b703816c4729b85c4be634a3888.jpeg");

        AudioDTO audio = new AudioDTO();
        audio.setFactorName("总分");
        audio.setFilePath("https://music.163.com/song/media/outer/url?id=1349292048.mp3");

        VideoDTO video = new VideoDTO();
        video.setFactorName("总分");
        video.setFilePath("https://media.w3.org/2010/05/sintel/trailer_hd.mp4");

        totalScoreReportContentDTO.setChart(chart);
        totalScoreReportContentDTO.setForm(form);
        totalScoreReportContentDTO.setResultIntro(resultIntro);
        totalScoreReportContentDTO.setSuggestion(suggestion);
        totalScoreReportContentDTO.setAudio(audio);
        totalScoreReportContentDTO.setVideo(video);
        totalScoreReportContentDTO.setWordage("总分得分：7分");
        return totalScoreReportContentDTO;

    }

    public AvgScoreReportContentDTO buildAvg() {
        AvgScoreReportContentDTO avgScoreReportContentDTO = new AvgScoreReportContentDTO();
        avgScoreReportContentDTO.setRemark("备注总均分1111");

        ChartDTO chart = new ChartDTO();

        // 单向横断
        BarChartDTO barChartDTO = new BarChartDTO();
        List<Bar> bars = new ArrayList<>();
        Bar bar1 = new Bar();
        bar1.setId(1);
        bar1.setScore(null);

        Bar bar2 = new Bar();
        bar2.setId(2);
        bar2.setScore(null);

        Bar bar3 = new Bar();
        bar3.setId(3);
        bar3.setScore(BigDecimal.valueOf(100));

        Bar bar4 = new Bar();
        bar4.setId(4);
        bar4.setScore(null);

        bars.add(bar1);
        bars.add(bar2);
        bars.add(bar3);
        bars.add(bar4);
        barChartDTO.setBars(bars);
        barChartDTO.setLeft("正常");
        barChartDTO.setRight("严重");
        // 1.单向图表
        chart.setType(1);
        chart.setChartData(barChartDTO);

        FormDTO form = new FormDTO();
        List<String> headers = List.of("量表名称", "总均分", "阳性数");
        form.setFormTitle(buildTitleTd(headers));
        List<Map<String, Object>> formData = new ArrayList<>();
        Map<String, Object> data1 = new HashMap<>();
        data1.put("量表名称", "匹兹堡睡眠指数");
        data1.put("总均分", "100");
        data1.put("阳性数", "50");
        formData.add(data1);
        form.setFormData(buildDataTd(formData, headers));

        ReultIntroDTO resultIntro = new ReultIntroDTO();
        resultIntro.setResultRead("结果就总均分会斤斤计较");
        resultIntro.setFactorName("总均分");

        SuggestionDTO suggestion = new SuggestionDTO();
        suggestion.setFactorName("总均分");
        suggestion.setSuggestion("建议就总均分将计就计");
        suggestion.setFilePath("https://pic.rmb.bdstatic.com/bjh/240926/5c5a8c846b703816c4729b85c4be634a3888.jpeg");

        AudioDTO audio = new AudioDTO();
        audio.setFactorName("总均分");
        audio.setFilePath("https://music.163.com/song/media/outer/url?id=1349292048.mp3");

        VideoDTO video = new VideoDTO();
        video.setFactorName("总均分");
        video.setFilePath("https://media.w3.org/2010/05/sintel/trailer_hd.mp4");

        avgScoreReportContentDTO.setChart(chart);
        avgScoreReportContentDTO.setForm(form);
        avgScoreReportContentDTO.setResultIntro(resultIntro);
        avgScoreReportContentDTO.setSuggestion(suggestion);
        avgScoreReportContentDTO.setAudio(audio);
        avgScoreReportContentDTO.setVideo(video);
        avgScoreReportContentDTO.setWordage("总均分得分：7分");
        return avgScoreReportContentDTO;

    }


    public PositiveCountReportContentDTO buildPositiveCount() {
        PositiveCountReportContentDTO positiveCountReportContentDTO = new PositiveCountReportContentDTO();
        positiveCountReportContentDTO.setRemark("备注总阳性数量1111");

        ChartDTO chart = new ChartDTO();

        // 单向横断
        BarChartDTO barChartDTO = new BarChartDTO();
        List<Bar> bars = new ArrayList<>();
        Bar bar1 = new Bar();
        bar1.setId(1);
        bar1.setScore(null);

        Bar bar2 = new Bar();
        bar2.setId(2);
        bar2.setScore(BigDecimal.valueOf(100));

        Bar bar3 = new Bar();
        bar3.setId(3);
        bar3.setScore(null);

        Bar bar4 = new Bar();
        bar4.setId(4);
        bar4.setScore(null);

        bars.add(bar1);
        bars.add(bar2);
        bars.add(bar3);
        bars.add(bar4);
        barChartDTO.setBars(bars);
        barChartDTO.setLeft("正常");
        barChartDTO.setRight("严重");
        // 1.单向图表
        chart.setType(1);
        chart.setChartData(barChartDTO);

        FormDTO form = new FormDTO();
        List<String> headers = List.of("量表名称", "项目数", "阳性数");
        form.setFormTitle(buildTitleTd(headers));
        List<Map<String, Object>> formData = new ArrayList<>();
        Map<String, Object> data1 = new HashMap<>();
        data1.put("量表名称", "匹兹堡睡眠指数");
        data1.put("项目数", "100");
        data1.put("阳性数", "50");
        formData.add(data1);
        form.setFormData(buildDataTd(formData, headers));

        ReultIntroDTO resultIntro = new ReultIntroDTO();
        resultIntro.setResultRead("结果就总阳性数量会斤斤计较");
        resultIntro.setFactorName("总阳性数量");

        SuggestionDTO suggestion = new SuggestionDTO();
        suggestion.setFactorName("总阳性数量");
        suggestion.setSuggestion("建议就总阳性数量将计就计");
        suggestion.setFilePath("https://pic.rmb.bdstatic.com/bjh/240926/5c5a8c846b703816c4729b85c4be634a3888.jpeg");

        AudioDTO audio = new AudioDTO();
        audio.setFactorName("总阳性数量");
        audio.setFilePath("https://music.163.com/song/media/outer/url?id=1349292048.mp3");

        VideoDTO video = new VideoDTO();
        video.setFactorName("总阳性数量");
        video.setFilePath("https://media.w3.org/2010/05/sintel/trailer_hd.mp4");

        positiveCountReportContentDTO.setChart(chart);
        positiveCountReportContentDTO.setForm(form);
        positiveCountReportContentDTO.setResultIntro(resultIntro);
        positiveCountReportContentDTO.setSuggestion(suggestion);
        positiveCountReportContentDTO.setAudio(audio);
        positiveCountReportContentDTO.setVideo(video);
        positiveCountReportContentDTO.setWordage("总阳性数量：25");
        return positiveCountReportContentDTO;
    }

    public FactorScoreContentDTO buildFactorScoreCount() {
        FactorScoreContentDTO factorScoreContentDTO = new FactorScoreContentDTO();
        factorScoreContentDTO.setRemark("备注因子得分1111");

        ChartDTO chart = new ChartDTO();

        // 柱状图
        ColumnarContent content = new ColumnarContent();
        content.setSeries(List.of("主观睡眠质量", "入睡时间", "睡眠时间", "睡眠效率"));

        Columnar columnar = new Columnar();
        columnar.setName("因子得分");
        columnar.setType("bar");
        columnar.setData(List.of("1", "0", "4", "1"));
        content.setCategories(columnar);
        // 2.柱状图
        chart.setType(2);
        chart.setChartData(content);

        FormDTO form = new FormDTO();
        List<String> headers = List.of("序号", "因子名称", "得分", "参考等级");
        form.setFormTitle(buildTitleTd(headers));
        List<Map<String, Object>> formData = new ArrayList<>();
        Map<String, Object> data1 = new HashMap<>();
        data1.put("序号", "1");
        data1.put("因子名称", "主观睡眠质量");
        data1.put("得分", "1");
        data1.put("参考等级", "0-3");
        formData.add(data1);
        Map<String, Object> data2 = new HashMap<>();
        data2.put("序号", "2");
        data2.put("因子名称", "入睡时间");
        data2.put("得分", "0");
        data2.put("参考等级", "0-6");
        formData.add(data2);
        Map<String, Object> data3 = new HashMap<>();
        data3.put("序号", "3");
        data3.put("因子名称", "睡眠时间");
        data3.put("得分", "4");
        data3.put("参考等级", "0-20");
        formData.add(data3);
        Map<String, Object> data4 = new HashMap<>();
        data4.put("序号", "4");
        data4.put("因子名称", "睡眠效率");
        data4.put("得分", "1");
        data4.put("参考等级", "0-25");
        formData.add(data4);
        form.setFormData(buildDataTd(formData, headers));


        factorScoreContentDTO.setChart(chart);
        factorScoreContentDTO.setForm(form);
        factorScoreContentDTO.setWordage(buildWordage(formData));
        return factorScoreContentDTO;
    }

    public String buildWordage(List<Map<String, Object>> datas) {
        StringBuilder stringBuilder = new StringBuilder();
        for (Map<String, Object> row : datas) {
            stringBuilder.append(row.get("因子名称")).append("得分：").append(row.get("得分")).append("分").append("<br/>");
        }
        return stringBuilder.toString();
    }

    public FactorAnalyseReportContentDTO buildFactorAnalyse() {
        FactorAnalyseReportContentDTO factorAnalyseReportContentDTO = new FactorAnalyseReportContentDTO();

        List<FactorAnalyseReportDTO> factorAnalyseReports = buildFactorAnalyseReportDTO();
        factorAnalyseReportContentDTO.setFactorAnalyseReports(factorAnalyseReports);

        return factorAnalyseReportContentDTO;
    }

    public List<FactorAnalyseReportDTO> buildFactorAnalyseReportDTO() {
        List<FactorAnalyseReportDTO> factorAnalyseReports = new ArrayList<>();
        List<String> factors = List.of("主观睡眠质量", "入睡时间", "睡眠时间", "睡眠效率");
        for (String name : factors) {
            FactorAnalyseReportDTO factorAnalyseReportDTO = new FactorAnalyseReportDTO();
            ReultIntroDTO resultIntro = new ReultIntroDTO();
            resultIntro.setResultRead("结果就会" + name + "斤斤计较");
            resultIntro.setFactorName(name);

            SuggestionDTO suggestion = new SuggestionDTO();
            suggestion.setFactorName(name);
            suggestion.setSuggestion("建议就" + name + "将计就计");
            suggestion.setFilePath("https://pic.rmb.bdstatic.com/bjh/240926/5c5a8c846b703816c4729b85c4be634a3888.jpeg");

            AudioDTO audio = new AudioDTO();
            audio.setFactorName(name);
            audio.setFilePath("https://music.163.com/song/media/outer/url?id=1349292048.mp3");

            VideoDTO video = new VideoDTO();
            video.setFactorName(name);
            video.setFilePath("https://media.w3.org/2010/05/sintel/trailer_hd.mp4");

            factorAnalyseReportDTO.setResultIntro(resultIntro);
            factorAnalyseReportDTO.setSuggestion(suggestion);
            factorAnalyseReportDTO.setVideo(video);
            factorAnalyseReportDTO.setAudio(audio);

            factorAnalyseReports.add(factorAnalyseReportDTO);
        }


        return factorAnalyseReports;
    }

//    public String convertPdf(String htmlPath) {
//        String htmlFilePath = htmlPath;
//        String outputPdfPath = "output_complete.pdf";
//
//        try {
//            convertWithAdvancedWaiting(htmlFilePath, outputPdfPath);
//            System.out.println("PDF 转换完成: " + outputPdfPath);
//        } catch (Exception e) {
//            System.err.println("转换失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//        return null;
//    }

    public static void convertWithAdvancedWaiting(String htmlFilePath, String outputPdfPath) {
        String htmlContent = readHtmlFile(htmlFilePath);
        if (htmlContent == null) {
            throw new RuntimeException("无法读取 HTML 文件");
        }

        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions()
                            .setHeadless(true)
                            .setArgs(java.util.Arrays.asList(
                                    "--disable-web-security",
                                    "--disable-features=VizDisplayCompositor",
                                    "--no-sandbox"
                            ))
            );

            BrowserContext context = browser.newContext(new Browser.NewContextOptions()
                    .setViewportSize(1200, 800));

            Page page = context.newPage();

            // 设置页面内容
            page.setContent(htmlContent);

            // 等待网络空闲
            page.waitForLoadState(LoadState.NETWORKIDLE);

            // 使用多种等待策略确保完全渲染
            waitForCompleteRendering(page);

            // 配置 PDF 选项
            Page.PdfOptions pdfOptions = new Page.PdfOptions()
                    .setFormat("A4")
                    .setPrintBackground(true)
                    .setPreferCSSPageSize(true)
                    .setDisplayHeaderFooter(true)
                    .setFooterTemplate("<div style=\"font-size: 8px; text-align: center; width: 100%;\">"
                            + "第 <span class=\"pageNumber\"></span> 页，共 <span class=\"totalPages\"></span> 页</div>");

            // 生成 PDF
            byte[] pdfBytes = page.pdf(pdfOptions);

            // 保存文件
            Files.write(Paths.get(outputPdfPath), pdfBytes);

        } catch (Exception e) {
            throw new RuntimeException("PDF 转换错误", e);
        }
    }

    private static void waitForCompleteRendering(Page page) {
        // 组合多个等待条件
        try {
            // 条件 1: 等待 ECharts 图表
            CompletableFuture<Void> chartsFuture = CompletableFuture.runAsync(() -> {
                page.waitForFunction("() => {"
                        + "  try {"
                        + "    const chartContainers = document.querySelectorAll('[id$=\"chart-container\"]');"
                        + "    for (let container of chartContainers) {"
                        + "      if (!container.querySelector('canvas')) return false;"
                        + "      const chart = echarts.getInstanceByDom(container);"
                        + "      if (!chart || !chart.getOption()) return false;"
                        + "    }"
                        + "    return true;"
                        + "  } catch (e) { return false; }"
                        + "}");
            });

            // 条件 2: 等待媒体资源
            CompletableFuture<Void> mediaFuture = CompletableFuture.runAsync(() -> {
                page.waitForFunction("() => {"
                        + "  const mediaElements = document.querySelectorAll('audio, video, img');"
                        + "  return Array.from(mediaElements).every(element => {"
                        + "    if (element.tagName === 'IMG') return element.complete;"
                        + "    return element.readyState >= 2;"
                        + "  });"
                        + "}");
            });

            // 条件 3: 等待动态内容
            CompletableFuture<Void> dynamicFuture = CompletableFuture.runAsync(() -> {
                page.waitForFunction("() => {"
                        + "  // 检查报告类型选择器是否加载"
                        + "  const reportSelector = document.getElementById('reportType');"
                        + "  if (!reportSelector) return false;"
                        + "  "
                        + "  // 检查所有表格数据是否加载"
                        + "  const tables = document.querySelectorAll('table.detail');"
                        + "  for (let table of tables) {"
                        + "    if (table.textContent.trim().length < 10) return false;"
                        + "  }"
                        + "  "
                        + "  return true;"
                        + "}");
            });

            // 等待所有条件完成
            CompletableFuture.allOf(chartsFuture, mediaFuture, dynamicFuture)
                    .get(30, TimeUnit.SECONDS);

            // 额外等待确保完全稳定
            page.waitForTimeout(3000);

        } catch (TimeoutException e) {
            System.err.println("部分等待条件超时，继续转换过程...");
        } catch (Exception e) {
            System.err.println("等待过程中发生错误: " + e.getMessage());
        }
    }

    private static String readHtmlFile(String filePath) {
        try {
            return Files.readString(Paths.get(filePath));
        } catch (IOException e) {
            System.err.println("读取 HTML 文件失败: " + e.getMessage());
            return null;
        }
    }

    //    @GetMapping("/test/convertPdf")
    public String convertPdf(String path) {
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch();
            BrowserContext context = browser.newContext();
            Page page = context.newPage();

            // 设置较大的视口
            page.setViewportSize(1200, 1000);

            // 加载本地 HTML 文件或在线 URL
            // 方法1: 加载本地文件
            page.navigate(path);

            // 等待图表容器可见
//            page.waitForSelector(".chart-container", new Page.WaitForSelectorOptions().setState(WaitForSelectorState.VISIBLE));

            // 确保 ECharts 完全渲染 - 执行 JavaScript 等待
            page.evaluate("() => new Promise((resolve) => {"
                    + "if (window.myChart && typeof window.myChart.on === 'function') {"
                    + "  window.myChart.on('finished', () => setTimeout(resolve, 300));"
                    + "} else {"
                    + "  setTimeout(resolve, 30000);"
                    + "}"
                    + "})");


            // 设置 PDF 选项
            Page.PdfOptions pdfOptions = new Page.PdfOptions()
                    .setPath(Paths.get("output2.pdf")) // 输出路径
                    .setPrintBackground(true)         // 打印背景
                    .setPreferCSSPageSize(true);

            // 生成 PDF
            page.pdf(pdfOptions);
            browser.close();

            System.out.println("PDF 生成成功: output.pdf");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

//    @PostMapping("create/test")
//    public ApiResult<Void> test() {
//        //pdf生成
//        String templateName = "userReport";
//        Map<String, Object> paramMap = new HashMap<String, Object>();
//        Map<String, Object> param = new HashMap<String, Object>();
//        param.put("title", "匹兹堡测评");
//        param.put("name", "秀儿");
//        param.put("sex", "1");
//        param.put("birthday", "1998-10-11");
//        paramMap.put("report", param);
//        //调用具体的实现方法
//        String fileName = IdUtil.getSnowflakeNextIdStr();
//        try {
//            String s = PdfUtil.contractHandler(userReportPdfPathConfig, templateName, paramMap, fileName);
//            File pdfFile = new File(s);
//        } catch (Exception e) {
//            log.error("pdf convert fail.", e);
//        }
//        return ApiResult.ok();
//    }

//    @GetMapping("result/report")
//    public ApiResult<String> report(@RequestParam("resultId") Long resultId) {
//        //pdf生成
//        String url = TenantExecutor.execute("system", () -> scaleUserReportService.generateReportPdf(resultId));
//        // 内部取oss文件
//        FileMeta fileMeta = new DefaultFileMeta(new File(url), null);
//        ResourceManager manager = (ResourceManager) ApplicationContextHolder.applicationContext.getBean(OSSConstant.BeanName.SIGNED);
//        UploadedFileMeta uploadedFileMeta = manager.get(FileConstant.FILE_SCALE_SIGN_ROLE, fileMeta);
//        return ApiResult.ok(uploadedFileMeta.getUrl().toString());
//    }

//    @GetMapping("result/report")
//    public ApiResult<String> report(@RequestParam("resultId") Long resultId) {
//        //pdf生成
//        String url = TenantExecutor.execute("system", () -> scaleUserResultService.getById(resultId).getReportUrl());
//        // 内部取oss文件
//        FileMeta fileMeta = new DefaultFileMeta(new File(url), null);
//        ResourceManager manager = (ResourceManager) ApplicationContextHolder.applicationContext.getBean(OSSConstant.BeanName.SIGNED);
//        UploadedFileMeta uploadedFileMeta = manager.get(FileConstant.FILE_SCALE_SIGN_ROLE, fileMeta);
//        return ApiResult.ok(uploadedFileMeta.getUrl().toString());
//    }

}
