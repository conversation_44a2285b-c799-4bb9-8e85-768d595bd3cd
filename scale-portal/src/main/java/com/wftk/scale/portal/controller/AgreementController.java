package com.wftk.scale.portal.controller;


import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.scale.biz.dto.scale.AgreementQueryDTO;
import com.wftk.scale.biz.service.AgreementService;
import com.wftk.scale.portal.annotation.namespace.ScaleMapping;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Tag(name = "协议相关API")
@ScaleMapping("/agreement")
@Slf4j
public class AgreementController {

    @Autowired
    private AgreementService agreementService;

    @Operation(summary = "获取量表测评须知")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取量表测评须知")
    @GetMapping("/assessment/instructions")
    public ApiResult<AgreementQueryDTO> instructions(@RequestParam(value = "code") String code) {
        return ApiResult.ok(agreementService.findByCode(code));
    }
}
