package com.wftk.scale.portal.ext.enums;

/**
 * <AUTHOR>
 * @createDate 2024/4/26 11:38
 */
public enum ExtApiRespEnum {

    SYSTEM_ERROR(500,"系统异常"),

    PARAMS_VERIFY_ERROR(400,"参数校验错误"),


    TERMINAL_NOT_EXIST(801,"终端不存在"),
    TERMINAL_FORBIDDEN(802,"终端已禁用"),
    TERMINAL_NOT_BINDING_DEPARTMENT(803,"终端未绑定机构"),
    TERMINAL_SECRET_NOT_CONFIG(804,"终端密钥未配置"),
    TERMINAL_LINK_NOT_CONFIG(805,"终端链接未配置"),

    USER_NOT_EXIST(901,"用户不存在"),

    SCALE_NOT_LISTED(1001,"量表未上架"),
    PART_SCALE_NOT_LISTED(1002,"部分量表未上架"),
    SCALE_CONFIG_FACTOR_ENABLE_FALSE(1003,"量表因子配置异常"),


    ORDER_QUERY_SERIAL_NO_IS_NULL(1101,"serialNo不能为空"),
    ORDER_QUERY_ORDER_NO_IS_NULL(1102,"orderNo不能为空"),
    ORDER_QUERY_ORDER_IS_NULL(1103,"未查询到订单"),
    ORDER_QUERY_ORDER_STATUS_1(1104,"订单未支付"),
    ORDER_QUERY_ORDER_STATUS_3(1105,"订单已完成"),
    ORDER_QUERY_ORDER_STATUS_4(1106,"订单已取消"),
    ORDER_CREATE_TYPE_INVALID(1107,"未知的量表类型"),
    ORDER_CREATED(1108,"存在已创建订单"),

    LOGIC_PROCESSING(9997,"数据处理中......"),
    UNOPEN_INTEFACE(9999,"待开放。。。。");


    private final Integer code;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    private final String msg;

    ExtApiRespEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }



}
