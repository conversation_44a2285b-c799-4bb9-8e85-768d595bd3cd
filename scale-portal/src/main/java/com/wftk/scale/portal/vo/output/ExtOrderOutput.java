package com.wftk.scale.portal.vo.output;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/12/3 14:00
 */
@Data
public class ExtOrderOutput {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 终端业务流水号
     */
    private String terminalSerialNo;

    /**
     * 订单金额,单位是分
     */
    private Integer amount;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 1支付宝2微信3现金4他人代付5.终端线下结算
     */
    private Integer payChannel;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 原价金额（分）
     */
    private Integer originalAmount;

    /**
     * 过期时间
     */
    private LocalDateTime expirTime;

}
