package com.wftk.scale.portal.vo.output;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/12/18 19:40
 */
@Data
public class ExtEvaluationRecordOutput {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 终端订单号
     */
    private String terminalSerialNo;

    /**
     * 记录号
     */
    private String evaluationRecordNo;

    /**
     * 量表编码
     */
    private String scaleCode;

    /**
     * 开始测评时间
     */
    private LocalDateTime startTime;

    /**
     * 结束测评时间
     */
    private LocalDateTime endTime;

    /**
     * 测评报告链接
     */
    @RFile(role = FileConstant.FILE_SCALE_SIGN_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    private String reportUrl;

    /**
     * 测评报告生成时间
     */
    private LocalDateTime reportTime;

    /**
     * 总分
     */
    private BigDecimal totalScore;

    /**
     * 预警标签
     */
    private String warnTag;

}
