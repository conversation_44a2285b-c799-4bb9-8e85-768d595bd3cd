package com.wftk.scale.portal.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.scale.biz.constant.enums.PayChannelEnum;
import com.wftk.scale.biz.constant.enums.ScaleEnum;
import com.wftk.scale.biz.constant.enums.ScaleListingShowTypeEnum;
import com.wftk.scale.biz.dto.order.BuyOrderDTO;
import com.wftk.scale.biz.entity.*;
import com.wftk.scale.biz.service.*;
import com.wftk.scale.portal.service.OrderPayService;
import com.wftk.scale.portal.vo.input.CreateOrderInput;
import com.wftk.scale.portal.vo.output.PrePaymentOutput;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2024/11/20 11:48
 */
@Service
@Slf4j
public class OrderPayServiceImpl implements OrderPayService {

    @Autowired
    TerminalService terminalService;

    @Autowired
    ScaleService scaleService;

    @Autowired
    ScaleCombinationService scaleCombinationService;

    @Autowired
    ScaleCombinationDetailService scaleCombinationDetailService;

    @Autowired
    ScaleListingService scaleListingService;

    @Autowired
    ScaleFactorService scaleFactorService;

    @Autowired
    ScaleListingUserRecordService scaleListingUserRecordService;

    @Autowired
    UserService userService;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;
    @Resource
    private UserDepartmentService userDepartmentService;
    @Resource
    private ScaleUserResultService scaleUserResultService;
    @Resource
    private ScaleListingUserConfService scaleListingUserConfService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PrePaymentOutput buyScaleWithPrePayment(CreateOrderInput createOrderInput) {
        log.info("buy scale order by {}. param: {}", createOrderInput.getTerminalCode(),
                JSONObject.getInstance().toJSONString(createOrderInput));
        // 获取终端
        Terminal terminal = terminalService.getByCode(createOrderInput.getTerminalCode());
        if (terminal == null || !terminal.getEnable()) {
            log.info("terminalCode `{}` not exist", createOrderInput.getTerminalCode());
            throw new BusinessException("终端不存在");
        }

        // 获取用户
        Long userId = AuthenticationHolder.getAuthentication().getAuthUser().getId();
        User user = userService.getById(userId);

        UserDepartment userDepartment = userDepartmentService.getOne(new LambdaQueryWrapper<UserDepartment>().eq(UserDepartment::getUserId, userId));

        // 查询上架
        ScaleListing scaleListing = scaleListingService.getById(createOrderInput.getScaleListingId());
        if (scaleListing == null || !scaleListing.getEnable()
                || ScaleEnum.SCALE_LISTING_OFF_SHELL_STATUS.getCode().equals(scaleListing.getStatus())) {
            throw new BusinessException("量表未上架");
        }

        Integer price = scaleListing.getPrice();
        if (Objects.equals(scaleListing.getShowType(), ScaleListingShowTypeEnum.USER_DISTRIBUTION.getValue())) {
            // 校验分发是否有数据
            if(createOrderInput.getScaleListingUserRecordId() == null){
                throw new BusinessException("分发id不能为空");
            }
            ScaleListingUserRecord scaleListingUserRecord = scaleListingUserRecordService.getById(createOrderInput.getScaleListingUserRecordId());
            if (scaleListingUserRecord == null) {
                throw new BusinessException("该用户未分发量表");
            }
            //查询分发记录是否已经支付，一次分发只能支付一次，页面展示可以多次购买
            String orderNo = scaleListingUserRecord.getOrderNo();
            if(StrUtil.isNotBlank(orderNo)){
                throw new BusinessException("该用户分发量表已完成支付，请勿重复支付");
            }
            //如果是免费的订单，则金额为0
            ScaleListingUserConf conf = scaleListingUserConfService.getById(scaleListingUserRecord.getUserConfId());
            // 0 免费 1 收费
            price = (conf != null && !conf.getOrderFree()) ? 0 : price;
        }

        // 查询量表因子是否被禁用
//        if (Objects.equals(createOrderInput.getType(), ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode())) {
//            // 校验因子是否被禁用
//            boolean factorFormulaEnable = scaleFactorService.vaildFactorFormulaEnable(createOrderInput.getTargetId());
//            if(!factorFormulaEnable){
//                log.info("scale `{}` factor formula is not enabled", createOrderInput.getTargetId());
//                throw new BusinessException("量表因子配置异常");
//            }
//        }else if(Objects.equals(createOrderInput.getType(), ScaleEnum.COMBINATION_SCALE_LISTING_TYPE.getCode())){
//            // 校验因子是否被禁用
//            List<Long> scaleIds = scaleCombinationDetailService.findScaleIdByCombinationId(createOrderInput.getTargetId());
//            boolean factorFormulaEnable = scaleFactorService.vaildFactorFormulaEnable(scaleIds);
//            if(!factorFormulaEnable){
//                log.info("scale `{}` factor formula is not enabled", createOrderInput.getTargetId());
//                throw new BusinessException("量表因子配置异常");
//            }
//        }else {
//            log.info("invalid type.");
//            throw new BusinessException("invalid type");
//        }

        // 生成订单和支付单
        BuyOrderDTO buyOrderDTO = new BuyOrderDTO();
        buyOrderDTO.setScaleListingId(scaleListing.getId());
        buyOrderDTO.setAmount(price);
        buyOrderDTO.setOriginalAmount(scaleListing.getOriginalPrice());
        buyOrderDTO.setTargetId(createOrderInput.getTargetId());
        buyOrderDTO.setType(createOrderInput.getType());
        buyOrderDTO.setPayChannel(PayChannelEnum.OFFLINE_SETTLE.getValue());
        buyOrderDTO.setTerminalCode(terminal.getCode());
        buyOrderDTO.setUserId(user.getId());
        buyOrderDTO.setUserName(user.getName());
        buyOrderDTO.setPhone(user.getPhone());

        //冗余字段
        buyOrderDTO.setDepartmentId(userDepartment == null ? null : userDepartment.getDepartmentId());
        buyOrderDTO.setTargetName(scaleListing.getTargetName());
        buyOrderDTO.setUserAccount(user.getAccount());

        OrderPayment orderPayment = orderService.createScaleOrderAndPayOrder(buyOrderDTO);

        //回写分发表order_no
        if(Objects.equals(scaleListing.getShowType(), ScaleListingShowTypeEnum.USER_DISTRIBUTION.getValue())){
            if(createOrderInput.getScaleListingUserRecordId() != null){
                scaleListingUserRecordService.update(new LambdaUpdateWrapper<ScaleListingUserRecord>()
                        .eq(ScaleListingUserRecord::getId, createOrderInput.getScaleListingUserRecordId())
                        .set(ScaleListingUserRecord::getOrderNo, orderPayment.getOrderNo())
                );
            }
        }

        // 发起支付
        Map<String, Object> prepayParam = orderPaymentService.doPrepay(orderPayment);

        // 封装支付数据
        PrePaymentOutput paymentOutput = new PrePaymentOutput();
        paymentOutput.setOrderNo(orderPayment.getOrderNo());
        paymentOutput.setPayChannel(orderPayment.getPayChannel());
        paymentOutput.setPrePayParam(prepayParam);
        return paymentOutput;
    }

    @Override
    public Boolean checkNeedBuy(Long listingUserId, Long scaleListingId) {
        boolean distribution = scaleListingService.vaildShowTypeOfDistribution(scaleListingId);
        if(distribution){
            if(listingUserId != null){
                ScaleListingUserRecord userRecord = scaleListingUserRecordService.getById(listingUserId);
                //分发的量表只能购买一次
                return StrUtil.isBlank(userRecord.getOrderNo());
            }else{
                throw new BusinessException("分发ID参数listingUserId必传!");
            }
        }else{
            //页面展示的可以重复购买
            String orderNo = orderService.getOrderNoByListingIdAndUserId(scaleListingId, AuthenticationHolder.getAuthentication().getAuthUser().getId());
            if(StrUtil.isBlank(orderNo)){
                return true;
            }
            return checkUserResultExist(orderNo);
        }
    }

    private boolean checkUserResultExist(String orderNo){
        return scaleUserResultService.exists(new LambdaQueryWrapper<ScaleUserResult>().eq(ScaleUserResult::getOrderNo, orderNo));
    }
}
