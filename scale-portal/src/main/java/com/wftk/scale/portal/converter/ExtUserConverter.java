package com.wftk.scale.portal.converter;

import com.wftk.scale.biz.entity.User;
import com.wftk.scale.portal.vo.input.ExtUserCreateInput;
import com.wftk.scale.portal.vo.output.ExtUserOutput;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @createDate 2024/11/18 17:53
 */
@Mapper(componentModel = "spring")
public interface ExtUserConverter {

    User createUserInputToEntity(ExtUserCreateInput createUserInput);

    ExtUserOutput entityToUserOutput(User user);

}
