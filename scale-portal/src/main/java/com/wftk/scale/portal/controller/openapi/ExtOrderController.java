package com.wftk.scale.portal.controller.openapi;

import cn.hutool.core.collection.CollectionUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.jackson.core.JSONObject;
import com.wftk.scale.portal.annotation.namespace.OpenapiMapping;
import com.wftk.scale.portal.ext.enums.ExtApiRespEnum;
import com.wftk.scale.portal.ext.exception.ExtApiBadBizException;
import com.wftk.scale.portal.service.openapi.ExtScaleOrderService;
import com.wftk.scale.portal.vo.input.*;
import com.wftk.scale.portal.vo.output.ExtEvaluationRecordOutput;
import com.wftk.scale.portal.vo.output.ExtOrderLinkOutput;
import com.wftk.scale.portal.vo.output.ExtOrderOutput;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @createDate 2024/11/27 10:31
 */
@Tag(name = "外部订单相关接口")
@OpenapiMapping("/order")
@Slf4j
public class ExtOrderController {

    @Autowired
    ExtScaleOrderService extScaleOrderService;

    @Autowired
    private LocalValidatorFactoryBean localValidatorFactoryBean;

    @Operation(summary = "创建订单")
    @PostMapping("/create")
    public ApiResult<ExtOrderOutput> create(@RequestBody ExtOrderCreateInput extOrderCreateInput, @RequestParam("tenantId")String tenantId,@RequestParam("terminalCode")String terminalCode){
        log.info("create order by {}. param: {}", terminalCode,
                JSONObject.getInstance().toJSONString(extOrderCreateInput));
        validateParams(extOrderCreateInput);
        ExtOrderOutput order = extScaleOrderService.create(extOrderCreateInput,terminalCode);
        return ApiResult.ok(order);
    }

    @Operation(summary = "批量创建订单")
    @PostMapping("/batch/create")
    public ApiResult<List<ExtOrderOutput>> createBatch(@RequestBody ExtBatchOrderCreateInput extBatchOrderCreateInput, @RequestParam("tenantId")String tenantId, @RequestParam("terminalCode")String terminalCode){
        log.info("batch create order by {}. param: {}", terminalCode,
                JSONObject.getInstance().toJSONString(extBatchOrderCreateInput));
        validateParams(extBatchOrderCreateInput);
        List<ExtOrderOutput> extOrderOutputs = extScaleOrderService.createBatch(extBatchOrderCreateInput, terminalCode);
        return ApiResult.ok(extOrderOutputs);
    }


    @Operation(summary = "查询状态")
    @PostMapping("/queryStatus")
    public ApiResult<ExtOrderOutput> queryStatus(@RequestBody ExtOrderQueryInput extOrderQueryInput, @RequestParam("tenantId")String tenantId, @RequestParam("terminalCode")String terminalCode){
        ExtOrderOutput order = extScaleOrderService.queryStatus(extOrderQueryInput,terminalCode );
        return ApiResult.ok(order);
    }

    @Operation(summary = "获取链接")
    @PostMapping("/genarateLink")
    public ApiResult<ExtOrderLinkOutput> genarateLink(@RequestBody ExtOrderLinkCreateInput extOrderLinkCreateInput, @RequestParam("tenantId")String tenantId, @RequestParam("terminalCode")String terminalCode){
        ExtOrderLinkOutput linkOutput = extScaleOrderService.genarateLink(extOrderLinkCreateInput,terminalCode );
        return ApiResult.ok(linkOutput);
    }

    @Operation(summary = "查询测评记录和报告")
    @PostMapping("/queryUserScaleRecord")
    public ApiResult<List<ExtEvaluationRecordOutput>> queryUserScaleRecord(@RequestBody ExtEvaluationRecordQueryInput extEvaluationRecordQueryInput, @RequestParam("tenantId")String tenantId, @RequestParam("terminalCode")String terminalCode){
        List<ExtEvaluationRecordOutput> extEvaluationRecordOutputs = extScaleOrderService.queryUserScaleRecord(extEvaluationRecordQueryInput,terminalCode );
        return ApiResult.ok(extEvaluationRecordOutputs);
    }

    private void validateParams(Object object) {
        Validator validator = localValidatorFactoryBean.getValidator();
        Set<ConstraintViolation<Object>> failConditions = validator.validate(object);
        if (CollectionUtil.isNotEmpty(failConditions)) {
            for (ConstraintViolation<Object> condition : failConditions) {
                throw new ExtApiBadBizException(ExtApiRespEnum.PARAMS_VERIFY_ERROR.getCode(),condition.getMessage());
            }
        }
    }
}
