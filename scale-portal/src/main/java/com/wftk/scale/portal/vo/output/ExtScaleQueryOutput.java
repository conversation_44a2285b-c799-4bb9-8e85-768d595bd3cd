package com.wftk.scale.portal.vo.output;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/11/29 13:54
 */
@Data
public class ExtScaleQueryOutput {

    /**
     * 量表Id
     */
    private Long id;

    /**
     * 上架Id
     */
    private Long listingId;

    /**
     * 上架呈现方式: 1.页面展示; 2.用户分发;
     */
    private Integer listingShowType;

    /**
     * 量表类型，来源于scale_type表的主键
     */
    private Long type;

    /**
     * 量表类型名称
     */
    private String typeName;

    /**
     * 量表编码
     */
    private String code;

    /**
     * 封面图url
     */
    @RFile(role = FileConstant.FILE_SCALE_PUB_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    private String cover;

    /**
     * 量表名称
     */
    private String name;

    /**
     * 简介
     */
    private String intro;

    /**
     * 指导语
     */
    private String guideline;


    /**
     * 限时秒数最小值
     */
    private Integer minTimeLimit;

    /**
     * 限时秒数最大值
     */
    private Integer maxTimeLimit;

    /**
     * 介绍
     */
    private String description;


    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 题目数量
     */
    private Integer numOfQuestion;


}
