package com.wftk.scale.portal.ext.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.client.ClientInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.loader.ClientLoader;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.client.DefaultClientConfig;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.client.DefaultClientInfo;
import com.wftk.scale.biz.entity.TenantClientInfo;
import com.wftk.scale.biz.entity.TenantSetting;
import com.wftk.scale.biz.service.TenantClientInfoService;
import com.wftk.scale.biz.service.TenantSettingService;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024/11/22 11:14
 */
@Slf4j
public class SysClientLoader implements ClientLoader {

    private final TenantSettingService tenantSettingService;
    private final TenantClientInfoService tenantClientInfoService;

    public SysClientLoader(TenantSettingService tenantSettingService, TenantClientInfoService tenantClientInfoService) {
        this.tenantSettingService = tenantSettingService;
        this.tenantClientInfoService = tenantClientInfoService;
    }

    @Override
    public ClientInfo load(String clientId) {
        TenantClientInfo tenantClientInfo = tenantClientInfoService.findOneByClientId(clientId, true);
        if (tenantClientInfo == null) {
            log.warn("invalid clientId: {}", clientId);
            return null;
        }
        TenantSetting clientSetting = tenantSettingService.getTenantId(tenantClientInfo.getTenantId());
        if (clientSetting == null) {
            log.warn("invalid tenantId: {}", tenantClientInfo.getTenantId());
            return null;
        }
        ClientInfo.ClientConfig clientConfig = buildConfig(tenantClientInfo);
        return new DefaultClientInfo(tenantClientInfo.getTenantId(), tenantClientInfo.getClientId(), tenantClientInfo.getClientSecret(), clientConfig);
    }
    /**
     * 构建客户端配置
     * @param tenantClientInfo
     * @return
     */
    private ClientInfo.ClientConfig buildConfig(TenantClientInfo tenantClientInfo) {
        DefaultClientConfig clientConfig = new DefaultClientConfig();
        if (StrUtil.isNotBlank(tenantClientInfo.getGrantType())) {
            Set<String> grantType = CollUtil.set(false, tenantClientInfo.getGrantType().split(","));
            clientConfig.setGrantType(grantType);
        }
        if (StrUtil.isNotBlank(tenantClientInfo.getPreGrantType())) {
            Set<String> preGrantType =CollUtil.set(false, tenantClientInfo.getPreGrantType().split(","));
            clientConfig.setPreGrantType(preGrantType);
        }
        clientConfig.setAccessTokenExpireInSeconds(tenantClientInfo.getAccessTokenExpireInSeconds());
        clientConfig.setPreAuthExpireInSeconds(tenantClientInfo.getPreAuthExpireInSeconds());
        clientConfig.setAccessTokenTransitionInSeconds(tenantClientInfo.getAccessTokenTransitionInSeconds());
        return clientConfig;
    }
}
