package com.wftk.scale.portal.vo.input;

import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: ScaleUserResultCreateInput
 * @Description: 创建用户测评记录信息
 * @Author: mq
 * @Date: 2024-11-07 17:17
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleUserResultCreateInput implements Serializable {

    /**
     * 测评量表信息
     */
    @Valid
    private ScaleUserResultInput userResult;

    /**
     * 测评回答答案信息
     */
    @Valid
    private List<ScaleUserResultRecordInput> resultRecordList;
}
