package com.wftk.scale.portal.config;

import com.wftk.scale.portal.ext.tenant.AuthUserTenantLoader;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2024/10/24 09:55
 */
@Configuration
@ComponentScan(basePackages = "com.wftk.scale.portal")
public class AppConfig {

    @Bean
    AuthUserTenantLoader authUserTenantLoader() {
        return new AuthUserTenantLoader();
    }


}
