package com.wftk.scale.portal.vo.input;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @ClassName: ScaleListedQueryDTO
 * @Description: 量表可上架信息传输实体
 * @Author: mq
 * @Date: 2024-10-29 16:05
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleListedQueryInput implements Serializable {

    /**
     * 量表名称
     */
    @Schema(title = "量表名称", name = "targetName", defaultValue = "我是测试量表名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 1, max = 50, message = "量表名称长度范围1-50个字符")
    private String targetName;

    /**
     * 量表分类ID
     */
    @Schema(title = "量表分类ID或测评方式", name = "targetType", defaultValue = "609527310759877", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long targetType;

    /**
     * 终端编号
     */
    @Schema(title = "终端编号", name = "terminalCode", defaultValue = "100", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "终端编号不能为空")
    private String terminalCode;
}
