package com.wftk.scale.portal.vo.input;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * @ClassName: ScaleUserResultRecordInput
 * @Description:
 * @Author: mq
 * @Date: 2024/12/13
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleUserResultRecordInput {

    /**
     * 量表ID
     */
    @Schema(title = "量表ID", name = "scaleId", defaultValue = "622275771041221", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /**
     * 问题id，来自scale_question表
     */
    @Schema(title = "题目ID", name = "questionId", defaultValue = "622275771041221", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /**
     * 问题选项ID
     */
    @Schema(title = "问题选项ID", name = "optionId", defaultValue = "622275771041221", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "问题选项ID不能为空")
    private Long optionId;

    /**
     * 选项标签
     */
    @Schema(title = "选项标签", name = "answer", defaultValue = "A", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "选项标签不能为空")
    @Length(min = 1, max = 50, message = "选项标签长度范围1-50个字符")
    private String answer;

    /**
     * 分数
     */
    @Schema(title = "分数", name = "score", defaultValue = "4", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "选项分数不能为空")
    private Integer score;

    /**
     * 0.阴; 1.阳;
     */
    @Schema(title = "性质", name = "result", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean result;

    /**
     * 其它-内容
     */
    @Schema(title = "其它-内容", name = "content", defaultValue = "当开启了其它输入内容信息", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String content;
}
