package com.wftk.scale.portal.vo.output;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/12/3 14:00
 */
@Data
public class UserOutput {

    /**
     * 用户编码
     */
    private String code;

    /**
     * 用户账号
     */
    private String account;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 1男，2女
     */
    private Integer sex;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;




}
