package com.wftk.scale.portal.service.impl;


import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.ScaleEnum;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordParamDTO;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordQueryDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedParamDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleCombinationQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.entity.ScaleListing;
import com.wftk.scale.biz.entity.ScaleListingUserConf;
import com.wftk.scale.biz.entity.ScaleListingUserRecord;
import com.wftk.scale.biz.service.*;
import com.wftk.scale.portal.service.ScaleProxyService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ScaleProxyServiceImpl
 * @Description:
 * @Author: mq
 * @Date: 2024/12/5
 * @Version: 1.0
 **/
@Service
@Slf4j
public class ScaleProxyServiceImpl implements ScaleProxyService {

    @Autowired
    private ScaleListingService scaleListingService;

    @Autowired
    private ScaleListingUserRecordService scaleListingUserRecordService;

    @Autowired
    private ScaleService scaleService;

    @Autowired
    private ScaleCombinationService scaleCombinationService;

    @Autowired
    private ScaleUserResultService scaleUserResultService;

    @Autowired
    private ScaleListingUserConfService scaleListingUserConfService;

    @Resource
    private OrderService orderService;

    @Override
    public Page<ScaleListedQueryDTO> getScaleListedByPageDisplay(ScaleListedParamDTO scaleListedParamDTO) {
        scaleListedParamDTO.setType(ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode());
        return scaleListingService.getScaleListedByPageDisplay(scaleListedParamDTO);
    }

    @Override
    public Page<ScaleListedQueryDTO> getScaleCombinationistedByPageDisplay(ScaleListedParamDTO scaleListedParamDTO) {
        scaleListedParamDTO.setType(ScaleEnum.COMBINATION_SCALE_LISTING_TYPE.getCode());
        return scaleListingService.getScaleCombinationListedByPageDisplay(scaleListedParamDTO);
    }

    @Override
    public Page<ScaleListingUserRecordQueryDTO> getAllScaleDistributeRecord(ScaleListingUserRecordParamDTO scaleListingUserParamDTO) {
        Long userId = AuthenticationHolder.getAuthentication().getAuthUser().getId();
        scaleListingUserParamDTO.setUserId(userId);
        return scaleListingUserRecordService.getAllScaleDistributeRecord(scaleListingUserParamDTO);
    }

    @Override
    public ScaleQueryDTO findByScaleId(Long scaleListingId, Long scaleId, Long listingUserId, String orderNo) {
        ScaleQueryDTO dto = scaleService.findByScaleId(scaleId);
        if (ObjUtil.isNull(dto)) {
            return dto;
        }
        return this.buildScaleListingDTO(dto, scaleListingId, listingUserId, orderNo);
    }

    @Override
    public ScaleQueryDTO findByScaleCode(Long scaleListingId, String scaleCode) {
        ScaleQueryDTO dto = scaleService.findByScaleCode(scaleCode);
        if (ObjUtil.isNull(dto)) {
            return dto;
        }
        return this.buildScaleListingDTO(dto, scaleListingId, null, null);
    }

    private ScaleQueryDTO buildScaleListingDTO(ScaleQueryDTO dto, Long scaleListingId, Long listingUserId, String orderNo) {
        ScaleListing byId = scaleListingService.getById(scaleListingId);
        if(byId != null){
            if(byId.getShowType() == 2){
                if(listingUserId == null){
                    return dto;
                }
                ScaleListingUserRecord scaleListingUserRecord = scaleListingUserRecordService.getById(listingUserId);
                if(scaleListingUserRecord == null){
                    return dto;
                }
                orderNo = scaleListingUserRecord.getOrderNo();
                ScaleListingUserConf userConf = scaleListingUserConfService.getById(scaleListingUserRecord.getUserConfId());
                dto.setOrderNo(orderNo);
                dto.setCompleted(!StrUtil.isBlank(orderNo) && scaleUserResultService.validScaleUserResultComplete(orderNo, dto.getId()));
                if(ObjUtil.isNotNull(userConf)){
                    //前端传值为 00:00:00 展示的时候需要处理下
                    dto.setListingStartTime(userConf.getStartTime());
                    LocalDateTime endTime = userConf.getEndTime();
                    if(endTime != null){
                        dto.setListingEndTime(endTime.toLocalDate().atTime(23, 59, 59));
                    }
                    dto.setAllowRepeat(userConf.getAllowRepeat());
                }
            }else{
                //如果传了orderNo则代表是做了题回显状态
                if(StrUtil.isNotBlank(orderNo)){
                    dto.setOrderNo(orderNo);
                    dto.setCompleted(scaleUserResultService.validScaleUserResultComplete(orderNo, dto.getId()));
                }
            }
        }
        return dto;
    }

    @Override
    public ScaleCombinationQueryDTO findByScaleCombinationId(Long listingId, Long scaleCombinationId, String terminalCode,
                                                             Long listingUserId, String orderNo) {
        ScaleCombinationQueryDTO dto = scaleCombinationService.findByScaleId(scaleCombinationId);
        return this.buildData(listingId, dto, listingUserId, orderNo);
    }

    @Override
    public ScaleCombinationQueryDTO findByScaleCombinationCode(Long listingId, String scaleCode, String terminalCode) {
        ScaleCombinationQueryDTO dto = scaleCombinationService.findByScaleCode(scaleCode);
        return this.buildData(listingId, dto, null, null);
    }

    protected ScaleCombinationQueryDTO buildData(Long listingId, ScaleCombinationQueryDTO dto, Long listingUserId, String orderNo) {
        List<ScaleQueryDTO> details = dto.getDetails();
        ScaleListing scaleListing = scaleListingService.getById(listingId);
        if(scaleListing != null){
            //用户分发
            if(scaleListing.getShowType() == 2){
                ScaleListingUserRecord byId = scaleListingUserRecordService.getById(listingUserId);
                if(byId == null){
                    return dto;
                }
                orderNo = byId.getOrderNo();
                dto.setOrderNo(orderNo);
                ScaleListingUserConf userConf = scaleListingUserConfService.getById(byId.getUserConfId());
                if(ObjUtil.isNotNull(userConf)){
                    //前端传值为 00:00:00 展示的时候需要处理下
                    dto.setListingStartTime(userConf.getStartTime());
                    LocalDateTime endTime = userConf.getEndTime();
                    if(endTime != null){
                        dto.setListingEndTime(endTime.toLocalDate().atTime(23, 59, 59));
                    }
                    dto.setAllowRepeat(userConf.getAllowRepeat());
                }
                for (ScaleQueryDTO detail : details) {
                    if(StrUtil.isBlank(orderNo)){
                        detail.setCompleted(false);
                    }else{
                        detail.setCompleted(scaleUserResultService.validScaleUserResultComplete(orderNo, detail.getId()));
                    }
                    detail.setOrderNo(orderNo);
                }
            }else{
                //页面展示的
                if(StrUtil.isNotBlank(orderNo)){
                    dto.setOrderNo(orderNo);
                    for (ScaleQueryDTO detail : details) {
                        detail.setCompleted(scaleUserResultService.validScaleUserResultComplete(orderNo, detail.getId()));
                        detail.setOrderNo(orderNo);
                    }
                }
            }
        }
        return dto;
    }
}
