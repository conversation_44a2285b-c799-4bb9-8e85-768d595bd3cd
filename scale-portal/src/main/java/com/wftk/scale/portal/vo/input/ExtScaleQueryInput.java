package com.wftk.scale.portal.vo.input;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2024/11/18 20:42
 */
@Data
public class ExtScaleQueryInput {

    @Schema(title = "量表ID",  requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long scaleId;

    @Schema(title = "量表编码",  requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String scaleCode;

    @Schema(title = "量表名称",  requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String scaleName;

    @Schema(title = "量表类型",  requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long scaleType;

    /**
     * 上架呈现方式: 1.页面展示; 2.用户分发;
     */
    @Schema(title = "上架呈现方式",  requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer listingShowType;

    @Schema(title = "分页页码",  requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "pageNum不能为空")
    private Integer pageNum;

    @Schema(title = "分页大小",  requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;

}
