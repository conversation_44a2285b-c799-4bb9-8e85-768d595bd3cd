package com.wftk.scale.portal.service.openapi.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.cache.spring.boot.autoconfigure.store.redisson.StringRedissonObjectStore;
import com.wftk.jackson.core.JSONObject;
import com.wftk.scale.biz.constant.TerminalConfConstant;
import com.wftk.scale.biz.constant.enums.*;
import com.wftk.scale.biz.dto.order.OrderPayDTO;
import com.wftk.scale.biz.dto.order.PlaceOrderDTO;
import com.wftk.scale.biz.dto.report.UserReportDTO;
import com.wftk.scale.biz.dto.scale.ScaleListingDetailDTO;
import com.wftk.scale.biz.dto.scale.ScaleSerialDTO;
import com.wftk.scale.biz.entity.*;
import com.wftk.scale.biz.service.*;
import com.wftk.scale.portal.common.constant.LinkConstant;
import com.wftk.scale.portal.converter.ExtScaleOrderConverter;
import com.wftk.scale.portal.ext.enums.ExtApiRespEnum;
import com.wftk.scale.portal.ext.exception.ExtApiBadBizException;
import com.wftk.scale.portal.service.openapi.ExtScaleOrderService;
import com.wftk.scale.portal.vo.input.*;
import com.wftk.scale.portal.vo.output.ExtEvaluationRecordOutput;
import com.wftk.scale.portal.vo.output.ExtOrderLinkOutput;
import com.wftk.scale.portal.vo.output.ExtOrderOutput;
import com.wftk.signature.builder.BaseSignBuilder;
import com.wftk.signature.builder.SignBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createDate 2024/11/12 11:47
 */
@Slf4j
@Service
public class ExtScaleOrderServiceImpl implements ExtScaleOrderService {

    @Autowired
    TerminalConfService terminalConfService;

    @Autowired
    ScaleService scaleService;

    @Autowired
    ScaleCombinationService scaleCombinationService;

    @Autowired
    ScaleCombinationDetailService scaleCombinationDetailService;

    @Autowired
    UserService userService;

    @Autowired
    DepartmentService departmentService;

    @Autowired
    ScaleUserResultService scaleUserResultService;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    ScaleFactorService scaleFactorService;

    @Autowired
    ExtScaleOrderConverter extScaleOrderConverter;

    @Autowired
    StringRedissonObjectStore stringRedissonObjectStore;

    @Autowired
    ScaleUserReportService scaleUserReportService;

    @Override
    public ExtOrderOutput create(ExtOrderCreateInput extOrderCreateInput, String terminalCode) {
        // step1 校验上架量表
        ScaleListingDetailDTO scaleListingDetailDTO;
        if (Objects.equals(extOrderCreateInput.getType(), ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode())) {
            // 单个量表
            scaleListingDetailDTO = scaleService.getListedScaleDetail(extOrderCreateInput.getScaleCode(),terminalCode, extOrderCreateInput.getListingId());
            if (scaleListingDetailDTO == null) {
                throw new ExtApiBadBizException(ExtApiRespEnum.SCALE_NOT_LISTED);
            }
            // 校验因子是否被禁用
//            boolean factorFormulaEnable = scaleFactorService.vaildFactorFormulaEnable(scaleListingDetailDTO.getId());
//            if (!factorFormulaEnable) {
//                log.info("scale `{}` factor formula's enabled is false ",scaleListingDetailDTO.getId());
//                throw new ExtApiBadBizException(ExtApiRespEnum.SCALE_CONFIG_FACTOR_ENABLE_FALSE);
//            }
        } else if (Objects.equals(extOrderCreateInput.getType(), ScaleEnum.COMBINATION_SCALE_LISTING_TYPE.getCode())) {
            // 组合量表
            scaleListingDetailDTO = scaleCombinationService.getScaleListingDetail(extOrderCreateInput.getScaleCode(), extOrderCreateInput.getListingId(), terminalCode);
            if (scaleListingDetailDTO == null) {
                throw new ExtApiBadBizException(ExtApiRespEnum.SCALE_NOT_LISTED);
            }
            // 校验因子是否被禁用
//            List<Long> scaleIds = scaleCombinationDetailService.findScaleIdByCombinationId(scaleListingDetailDTO.getId());
//            boolean factorFormulaEnable = scaleFactorService.vaildFactorFormulaEnable(scaleIds);
//            if (!factorFormulaEnable) {
//                log.info("scale `{}` factor formula's enabled is false ",scaleIds);
//                throw new ExtApiBadBizException(ExtApiRespEnum.SCALE_CONFIG_FACTOR_ENABLE_FALSE);
//            }
        } else {
            log.error("invalid type.{}", extOrderCreateInput.getType());
            throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_CREATE_TYPE_INVALID);
        }

        if(!scaleListingDetailDTO.getEnable()){
            throw new ExtApiBadBizException(ExtApiRespEnum.SCALE_NOT_LISTED);
        }

        // 校验用户
        String userCode = extOrderCreateInput.getUserCode();
        // 通过终端编码查询部门
        Department department = departmentService.selectByTerminalCode(terminalCode);
        if (department == null) {
            throw new ExtApiBadBizException(ExtApiRespEnum.TERMINAL_NOT_BINDING_DEPARTMENT);
        }
        User user = userService.selectByCodeAndDepartmentId(userCode, department.getId());
        if (user == null || !user.getEnable()) {
            log.error("user is null or enable is false.{} {}", userCode, department.getId());
            throw new ExtApiBadBizException(ExtApiRespEnum.USER_NOT_EXIST);
        }

        // step3 创建订单
        PlaceOrderDTO placeOrderDTO = new PlaceOrderDTO();
        placeOrderDTO.setScaleListingId(scaleListingDetailDTO.getListingId());
        placeOrderDTO.setUserId(user.getId());
        placeOrderDTO.setUserName(user.getName());
        placeOrderDTO.setPhone(user.getPhone());
        placeOrderDTO.setType(extOrderCreateInput.getType());
        placeOrderDTO.setPayChannel(extOrderCreateInput.getPayChannel());
        placeOrderDTO.setTargetId(scaleListingDetailDTO.getId());
        placeOrderDTO.setTerminalSerialNo(extOrderCreateInput.getSerialNo());
        placeOrderDTO.setAmount(scaleListingDetailDTO.getPrice());
        placeOrderDTO.setOriginalAmount(scaleListingDetailDTO.getOriginalPrice());
        placeOrderDTO.setTerminalCode(terminalCode);
        //冗余字段
        placeOrderDTO.setDepartmentId(department.getId());
        placeOrderDTO.setUserAccount(user.getAccount());
        placeOrderDTO.setTargetName(scaleListingDetailDTO.getScaleName());

        OrderPayDTO orderPayDTO = orderService.placeScaleOrder(placeOrderDTO);
        Order order = orderPayDTO.getOrder();
        if(Objects.equals(order.getPayChannel(), PayChannelEnum.OFFLINE_SETTLE.getValue())){
            // 修改支付订单为支付中
            orderPaymentService.updateStatus(orderPayDTO.getOrderPayment().getTradeNo(), PaymentEnum.PROCESSING.getStatus(),PaymentEnum.WAIT.getStatus());
            // 修改支付订单为已支付
            orderPaymentService.updateStatus(orderPayDTO.getOrderPayment().getTradeNo(), PaymentEnum.SUCCESS.getStatus(),PaymentEnum.PROCESSING.getStatus());
            // 修改订单为待完成
            orderService.updateStatus(order.getOrderNo(),OrderEnum.WAIT_COMPLETE.getStatus(),OrderEnum.WAIT_PAY.getStatus());
        }


        // setp4 返回响应
        return extScaleOrderConverter.entityToOrderOutput(order);
    }

    @Override
    public List<ExtOrderOutput> createBatch(ExtBatchOrderCreateInput extBatchOrderCreateInput, String terminalCode) {
        // 参数校验
        List<ExtScaleSerialInput> scaleSerialInputs = extBatchOrderCreateInput.getScaleSerialInputs();
        if (scaleSerialInputs == null || scaleSerialInputs.isEmpty()) {
            throw new ExtApiBadBizException(ExtApiRespEnum.PARAMS_VERIFY_ERROR);
        }
        boolean allSerialNoNotNull = scaleSerialInputs.stream()
                .allMatch(scaleSerialInput -> StrUtil.isNotBlank(scaleSerialInput.getSerialNo()));
        boolean allScaleCodeNotNull = scaleSerialInputs.stream()
                .allMatch(scaleSerialInput -> StrUtil.isNotBlank(scaleSerialInput.getScaleCode()));
        boolean allListingIdNotNull = scaleSerialInputs.stream()
                .allMatch(scaleSerialInput -> scaleSerialInput.getListingId() != null);
        if (!allSerialNoNotNull) {
            throw new ExtApiBadBizException(ExtApiRespEnum.PARAMS_VERIFY_ERROR);
        }
        if (!allScaleCodeNotNull) {
            throw new ExtApiBadBizException(ExtApiRespEnum.PARAMS_VERIFY_ERROR);
        }
        if (!allListingIdNotNull) {
            throw new ExtApiBadBizException(ExtApiRespEnum.PARAMS_VERIFY_ERROR);
        }

        List<Long> scaleIds;
        // 校验量表是否都存在\是否都已经上架
        List<ScaleListingDetailDTO> scaleListingDetailDTO;
        if (Objects.equals(extBatchOrderCreateInput.getType(), ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode())) {
            // 单个量表
            List<ScaleSerialDTO> scaleSerialDTOS = extScaleOrderConverter.scaleSerialInputsToScaleSerialDTO(scaleSerialInputs);
            scaleListingDetailDTO = scaleService.getScaleListingDetailDTO(scaleSerialDTOS, terminalCode);
            if (scaleListingDetailDTO == null || scaleListingDetailDTO.isEmpty()) {
                throw new ExtApiBadBizException(ExtApiRespEnum.SCALE_NOT_LISTED);
            }
            if (scaleListingDetailDTO.size() != scaleSerialInputs.size()) {
                throw new ExtApiBadBizException(ExtApiRespEnum.PART_SCALE_NOT_LISTED);
            }
            scaleIds = scaleListingDetailDTO.stream().map(ScaleListingDetailDTO::getId).collect(Collectors.toList());

        } else if (Objects.equals(extBatchOrderCreateInput.getType(), ScaleEnum.COMBINATION_SCALE_LISTING_TYPE.getCode())) {
            // 组合量表
            List<ScaleSerialDTO> scaleSerialDTOS = extScaleOrderConverter.scaleSerialInputsToScaleSerialDTO(scaleSerialInputs);
            scaleListingDetailDTO = scaleCombinationService.getScaleListingDetailDTO(scaleSerialDTOS, terminalCode);
            if (scaleListingDetailDTO == null || scaleListingDetailDTO.isEmpty()) {
                throw new ExtApiBadBizException(ExtApiRespEnum.SCALE_NOT_LISTED);
            }
            if (scaleListingDetailDTO.size() != scaleSerialInputs.size()) {
                throw new ExtApiBadBizException(ExtApiRespEnum.PART_SCALE_NOT_LISTED);
            }
            List<Long> combinationIds = scaleListingDetailDTO.stream().map(ScaleListingDetailDTO::getId).collect(Collectors.toList());
            scaleIds = scaleCombinationDetailService.findScaleIdByCombinationIds(combinationIds);
        } else {
            log.error("invalid type.{}", extBatchOrderCreateInput.getType());
            throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_CREATE_TYPE_INVALID);
        }

        // 校验因子是否被禁用
//        boolean factorFormulaEnable = scaleFactorService.vaildFactorFormulaEnable(scaleIds);
//        if (!factorFormulaEnable) {
//            log.info("scale `{}` factor formula's enabled is false ",scaleIds);
//            throw new ExtApiBadBizException(ExtApiRespEnum.SCALE_CONFIG_FACTOR_ENABLE_FALSE);
//        }

        // 校验用户
        String userCode = extBatchOrderCreateInput.getUserCode();
        // 通过终端编码查询部门
        Department department = departmentService.selectByTerminalCode(terminalCode);
        if (department == null) {
            throw new ExtApiBadBizException(ExtApiRespEnum.TERMINAL_NOT_BINDING_DEPARTMENT);
        }
        User user = userService.selectByCodeAndDepartmentId(userCode, department.getId());
        if (user == null || !user.getEnable()) {
            log.error("user is null or enable is false.{} {}", userCode, department.getId());
            throw new ExtApiBadBizException(ExtApiRespEnum.USER_NOT_EXIST);
        }

        // 批量创建订单
        // 查询订单是否创建
        List<String> serialNos = scaleSerialInputs.stream().map(ExtScaleSerialInput::getSerialNo).toList();
        List<Order> extOrderList = orderService.getExtOrderList(terminalCode, serialNos);
        if(!extOrderList.isEmpty()){
            log.error("exist created order. {}",serialNos);
            throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_CREATED);
        }

        // todo 批量处理订单
        List<Order> orders = new ArrayList<>();
        Map<Long, ScaleListingDetailDTO> listingDetailDTOMap = scaleListingDetailDTO.stream().collect(Collectors.toMap(ScaleListingDetailDTO::getListingId, Function.identity()));
        for (ExtScaleSerialInput extScaleSerialInput : scaleSerialInputs) {
            ScaleListingDetailDTO listingDetailDTO = listingDetailDTOMap.get(extScaleSerialInput.getListingId());
            PlaceOrderDTO placeOrderDTO = new PlaceOrderDTO();
            placeOrderDTO.setTargetId(listingDetailDTO.getId());
            placeOrderDTO.setScaleListingId(listingDetailDTO.getListingId());
            placeOrderDTO.setUserId(user.getId());
            placeOrderDTO.setUserName(user.getName());
            placeOrderDTO.setPhone(user.getPhone());
            placeOrderDTO.setType(extBatchOrderCreateInput.getType());
            placeOrderDTO.setPayChannel(extBatchOrderCreateInput.getPayChannel());
            placeOrderDTO.setTerminalSerialNo(extScaleSerialInput.getSerialNo());
            placeOrderDTO.setAmount(listingDetailDTO.getPrice());
            placeOrderDTO.setOriginalAmount(listingDetailDTO.getOriginalPrice());
            placeOrderDTO.setTerminalCode(terminalCode);
            //冗余字段
            placeOrderDTO.setDepartmentId(department.getId());
            placeOrderDTO.setUserAccount(user.getAccount());
            placeOrderDTO.setTargetName(listingDetailDTO.getScaleName());
            OrderPayDTO orderPayDTO = orderService.placeScaleOrder(placeOrderDTO);
            orders.add(orderPayDTO.getOrder());
            if(Objects.equals(orderPayDTO.getOrder().getPayChannel(), PayChannelEnum.OFFLINE_SETTLE.getValue())){
                // 修改支付订单为支付中
                orderPaymentService.updateStatus(orderPayDTO.getOrderPayment().getTradeNo(), PaymentEnum.PROCESSING.getStatus(),PaymentEnum.WAIT.getStatus());
                // 修改支付订单为已支付
                orderPaymentService.updateStatus(orderPayDTO.getOrderPayment().getTradeNo(), PaymentEnum.SUCCESS.getStatus(),PaymentEnum.PROCESSING.getStatus());
                // 修改订单为待完成
                orderService.updateStatus(orderPayDTO.getOrder().getOrderNo(),OrderEnum.WAIT_COMPLETE.getStatus(),OrderEnum.WAIT_PAY.getStatus());
            }
        }
        return extScaleOrderConverter.entityToOrderOutput(orders);
    }

    @Override
    public ExtOrderOutput queryStatus(ExtOrderQueryInput extOrderQueryInput, String terminalCode) {
        log.info("qeury order by {}. param: {}", terminalCode,
                JSONObject.getInstance().toJSONString(extOrderQueryInput));

        // 判断参数
        if (StrUtil.isBlank(extOrderQueryInput.getSerialNo()) && StrUtil.isBlank(extOrderQueryInput.getOrderNo())) {
            if (StrUtil.isBlank(extOrderQueryInput.getSerialNo())) {
                throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_SERIAL_NO_IS_NULL);
            }

            if (StrUtil.isBlank(extOrderQueryInput.getOrderNo())) {
                throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_ORDER_NO_IS_NULL);
            }
        }
        // 加读锁
        Order orderIndb = orderService.getByTerminalSerialNoAndOrderNo(terminalCode, extOrderQueryInput.getSerialNo(), extOrderQueryInput.getOrderNo());
        if (orderIndb == null) {
            throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_ORDER_IS_NULL);
        }
        return extScaleOrderConverter.entityToOrderOutput(orderIndb);
    }

    @Override
    public ExtOrderLinkOutput genarateLink(ExtOrderLinkCreateInput extOrderLinkCreateInput, String terminalCode) {
        log.info("genarate order link by {}. param: {}", terminalCode,
                JSONObject.getInstance().toJSONString(extOrderLinkCreateInput));

        // 判断参数
        if (StrUtil.isBlank(extOrderLinkCreateInput.getSerialNo()) && StrUtil.isBlank(extOrderLinkCreateInput.getOrderNo())) {
            if (StrUtil.isBlank(extOrderLinkCreateInput.getSerialNo())) {
                throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_SERIAL_NO_IS_NULL);
            }

            if (StrUtil.isBlank(extOrderLinkCreateInput.getOrderNo())) {
                throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_ORDER_NO_IS_NULL);
            }
        }

        // 加读锁
        Order orderIndb = orderService.getByTerminalSerialNoAndOrderNo(terminalCode, extOrderLinkCreateInput.getSerialNo(), extOrderLinkCreateInput.getOrderNo());

        // 生成链接
        // 封装参数 用户code 订单号 终端编码  量表id 量表类型 授权码 租户id
        if (orderIndb == null) {
            throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_ORDER_IS_NULL);
        }

        // 只有待完成的才能生成链接
        if (Objects.equals(orderIndb.getStatus(), OrderEnum.WAIT_PAY.getStatus())) {
            throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_ORDER_STATUS_1);
        } else if (Objects.equals(orderIndb.getStatus(), OrderEnum.COMPLETED.getStatus())) {
            throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_ORDER_STATUS_3);
        } else if (Objects.equals(orderIndb.getStatus(), OrderEnum.CANCLE.getStatus())) {
            throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_ORDER_STATUS_4);
        }

        // 查询终端
        String link = terminalConfService.findByItemCode(terminalCode, TerminalConfConstant.H5_LINK, true);
        if (StrUtil.isBlank(link)) {
            throw new ExtApiBadBizException(ExtApiRespEnum.TERMINAL_LINK_NOT_CONFIG);
        }
        link = link + LinkConstant.QUESTION_SYMBOL + buildParams(orderIndb);

        ExtOrderLinkOutput extOrderLinkOutput = new ExtOrderLinkOutput();
        extOrderLinkOutput.setLink(link);
        return extOrderLinkOutput;
    }

    @Override
    public List<ExtEvaluationRecordOutput> queryUserScaleRecord(ExtEvaluationRecordQueryInput extEvaluationRecordQueryInput, String terminalCode) {
        log.info("qeury user evaluation record by {}. param: {}", terminalCode,
                JSONObject.getInstance().toJSONString(extEvaluationRecordQueryInput));

        // 判断参数
        if (StrUtil.isBlank(extEvaluationRecordQueryInput.getSerialNo()) && StrUtil.isBlank(extEvaluationRecordQueryInput.getOrderNo())) {
            if (StrUtil.isBlank(extEvaluationRecordQueryInput.getSerialNo())) {
                throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_SERIAL_NO_IS_NULL);
            }

            if (StrUtil.isBlank(extEvaluationRecordQueryInput.getOrderNo())) {
                throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_ORDER_NO_IS_NULL);
            }
        }

        Order orderIndb = orderService.getByTerminalSerialNoAndOrderNo(terminalCode, extEvaluationRecordQueryInput.getSerialNo(), extEvaluationRecordQueryInput.getOrderNo());
        if (orderIndb == null) {
            throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_ORDER_IS_NULL);
        }

        // 只有待完成和已完成的才能查询报告
        if (Objects.equals(orderIndb.getStatus(), OrderEnum.WAIT_PAY.getStatus())) {
            throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_ORDER_STATUS_1);
        } else if (Objects.equals(orderIndb.getStatus(), OrderEnum.CANCLE.getStatus())) {
            throw new ExtApiBadBizException(ExtApiRespEnum.ORDER_QUERY_ORDER_STATUS_4);
        }

        List<ScaleUserResult> scaleUserResults = scaleUserResultService.selectByOrderNo(orderIndb.getOrderNo());

        List<ExtEvaluationRecordOutput> extEvaluationRecordOutputs = new ArrayList<>();
        for (ScaleUserResult scaleUserResult:scaleUserResults) {
            ExtEvaluationRecordOutput extEvaluationRecordOutput = new ExtEvaluationRecordOutput();
            extEvaluationRecordOutput.setOrderNo(scaleUserResult.getOrderNo());
            extEvaluationRecordOutput.setEvaluationRecordNo(String.valueOf(scaleUserResult.getId()));
            extEvaluationRecordOutput.setStartTime(scaleUserResult.getStartTime());
            extEvaluationRecordOutput.setEndTime(scaleUserResult.getEndTime());
            extEvaluationRecordOutput.setTerminalSerialNo(orderIndb.getTerminalSerialNo());
            UserReportDTO scaleUserReport = scaleUserReportService.getUserReportByResultId(scaleUserResult.getId());
            if(scaleUserReport != null){
                extEvaluationRecordOutput.setTotalScore(scaleUserReport.getTotalScore());
                if(scaleUserReport.getWarnTag() != null){
                    extEvaluationRecordOutput.setWarnTag(scaleUserReport.getWarnTag().getWarnTag());
                }
            }
            Scale scale = scaleService.getById(scaleUserResult.getScaleId());
            if(scale!=null){
                extEvaluationRecordOutput.setScaleCode(scale.getCode());
            }
            if (Objects.equals(orderIndb.getType(), ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode())) {
                extEvaluationRecordOutput.setReportTime(scaleUserResult.getReportTime());
                extEvaluationRecordOutput.setReportUrl(scaleUserResult.getReportUrl());
            }else {
                // 组合量表 判断测评方式
                ScaleCombination scaleCombination = scaleCombinationService.getById(orderIndb.getTargetId());
                if (Objects.equals(scaleCombination.getType(), ScaleEnum.COMBINATION_SCALE_TYPE_CHOOSE.getCode())) {
                    if (Objects.equals(orderIndb.getStatus(), OrderEnum.COMPLETED.getStatus())) {
                        extEvaluationRecordOutput.setReportTime(scaleUserResult.getReportTime());
                        extEvaluationRecordOutput.setReportUrl(scaleUserResult.getReportUrl());
                    }
                } else {
                    extEvaluationRecordOutput.setReportTime(scaleUserResult.getReportTime());
                    extEvaluationRecordOutput.setReportUrl(scaleUserResult.getReportUrl());
                }
            }
            extEvaluationRecordOutputs.add(extEvaluationRecordOutput);
        }

        return extEvaluationRecordOutputs;
    }

    private String buildParams(Order order) {
        Map<String, Object> map = new HashMap<>();
        // 用户编码
        map.put(LinkConstant.USER_CODE, userService.getById(order.getUserId()).getCode());

        // 订单号
        map.put(LinkConstant.ORDER_NO, order.getOrderNo());

        // 终端编码
        map.put(LinkConstant.TERMINAL_CODE, order.getTerminalCode());

        // 量表id
        map.put(LinkConstant.SCALE_ID, order.getTargetId());

        // 量表类型
        map.put(LinkConstant.SCALE_TYPE, order.getType());

        // 租户ID
        map.put(LinkConstant.TENANT_ID, order.getTenantId());

        // 授权码
        String code = IdUtil.getSnowflakeNextIdStr();
        String key = LinkConstant.PROJECT_NAME + ":" + LinkConstant.EXT_AUTHORIZATION_CODE + ":" + code;
        // 86400秒 == 1天 需要处理配置化
        stringRedissonObjectStore.save(key, order.getOrderNo(), 86400L);
        map.put(LinkConstant.AUTHORIZATION_CODE, code);

        // 签名摘要
        String secret = terminalConfService.findByItemCode(order.getTerminalCode(), TerminalConfConstant.CLIENT_SECRET, true);
        if (StrUtil.isBlank(secret)) {
            log.warn("terminal secret haven't configured, terminalCode: [{}]", order.getTerminalCode());
            throw new ExtApiBadBizException(ExtApiRespEnum.TERMINAL_SECRET_NOT_CONFIG);
        }
        SignBuilder signBuilder = new BaseSignBuilder(secret);
        String sign = signBuilder.addParams(map).build();
        map.put(LinkConstant.SIGN, sign);
        return StrUtil.join(LinkConstant.AND_SYMBOL, map.entrySet());
    }

}
