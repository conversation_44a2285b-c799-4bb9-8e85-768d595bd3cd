package com.wftk.scale.portal.controller.auth;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.manager.AuthenticationManager;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.manager.PreAuthenticationManager;
import com.wftk.auth.spring.boot.autoconfigure.core.token.AccessTokenInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.token.AccessTokenManager;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.DefaultAuthenticationInfo;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.DefaultPreAuthenticationInfo;
import com.wftk.auth.spring.boot.autoconfigure.ext.dto.AuthenticationInfoDTO;
import com.wftk.auth.spring.boot.autoconfigure.ext.dto.PreAuthenticationDTO;
import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2024/11/26 15:38
 */
@Tag(name = "登录认证相关")
@RestController
@RequestMapping("/user/auth")
public class AuthController {

    @Autowired
    private AccessTokenManager accessTokenManager;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Operation(summary = "登录认证")
    @OptLog(module = "登录认证模块", optType = OptType.QUERY, description = "登录认证")
    @PostMapping("/token")
    public ApiResult<AccessTokenInfo> auth(@RequestBody @Valid AuthenticationInfoDTO authenticationInfoDTO) {
        DefaultAuthenticationInfo authenticationInfo = authenticationInfoDTO.toEntity();
        Authentication authenticate = authenticationManager.authenticate(authenticationInfo);
        AccessTokenInfo tokenInfo = accessTokenManager.grant(authenticate);
        return ApiResult.ok(tokenInfo);
    }
}
