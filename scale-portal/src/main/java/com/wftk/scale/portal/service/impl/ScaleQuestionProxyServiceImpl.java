package com.wftk.scale.portal.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleQuestionTreeDTO;
import com.wftk.scale.biz.service.ScaleQuestionService;
import com.wftk.scale.portal.service.ScaleQuestionProxyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName: ScaleQuestionProxyServiceImpl
 * @Description: 量表题目信息
 * @Author: mq
 * @Date: 2024/11/20
 * @Version: 1.0
 **/
@Service
@Slf4j
public class ScaleQuestionProxyServiceImpl implements ScaleQuestionProxyService {

    @Autowired
    private ScaleQuestionService scaleQuestionService;

    @Override
    public List<ScaleQuestionTreeDTO> selectPage(Long scaleId) {
        List<ScaleQuestionQueryDTO> dtoList = scaleQuestionService.findByScaleId(scaleId);
        if(CollUtil.isEmpty(dtoList)){
            return List.of();
        }
        Map<String, List<ScaleQuestionQueryDTO>> treeMap = dtoList.stream().collect(Collectors.groupingBy(ScaleQuestionQueryDTO::getQuestionNumber));
        List<ScaleQuestionTreeDTO> treeList = new ArrayList<>();
        //修复排序乱了的bug
        treeMap.forEach((questionNumber, value) -> {
            ScaleQuestionTreeDTO tree = this.getTreeNode(questionNumber, dtoList);
            if (tree != null) {
                treeList.add(tree);
            }
        });
        treeList.sort((o1, o2) -> {
            Integer i1 = Integer.parseInt(o1.getQuestionNumber());
            Integer i2 = Integer.parseInt(o2.getQuestionNumber());
            int i = i1.compareTo(i2);
            if (i == 0) {
                Integer i3 = Integer.parseInt(o1.getSubNumber());
                Integer i4 = Integer.parseInt(o2.getSubNumber());
                return i3.compareTo(i4);
            }
            return i;
        });
        return treeList;
    }


    private ScaleQuestionTreeDTO getTreeNode(String questionNumber, List<ScaleQuestionQueryDTO> dtoList) {
        return dtoList.stream().filter(item -> {
            if(questionNumber.equals(item.getQuestionNumber()) && StrUtil.isEmpty(item.getSubNumber())){
                return true;
            }
            return false;
        }).map(item -> {
            ScaleQuestionTreeDTO tree = new ScaleQuestionTreeDTO();
            BeanUtils.copyProperties(item, tree);
            tree.setChildren(this.getChildren(item.getQuestionNumber(), dtoList));
            return tree;
        }).findFirst().orElse(null);
    }

    private List<ScaleQuestionTreeDTO> getChildren(String questionNumber, List<ScaleQuestionQueryDTO> treeList) {
        return treeList.stream().filter(item -> {
            if(item.getQuestionNumber().equals(questionNumber) && StrUtil.isNotEmpty(item.getSubNumber())){
                return true;
            }
            return false;
        }).map(item -> {
            ScaleQuestionTreeDTO tree = new ScaleQuestionTreeDTO();
            BeanUtils.copyProperties(item, tree);
            return tree;
        }).collect(Collectors.toList());
    }
}


