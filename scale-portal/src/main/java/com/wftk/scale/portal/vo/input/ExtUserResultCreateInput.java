package com.wftk.scale.portal.vo.input;

import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/12/10 9:37
 */
@Data
public class ExtUserResultCreateInput {

    /**
     * 授权码
     */
    @NotBlank(message = "授权码不能为空")
    private String authorizationCode;

    /**
     * 测评量表信息
     */
    @Valid
    private ScaleUserResultInput userResult;

    /**
     * 测评回答答案信息
     */
    @Valid
    private List<ScaleUserResultRecordInput> resultRecordList;

}
