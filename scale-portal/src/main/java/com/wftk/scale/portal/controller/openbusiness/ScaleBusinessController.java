package com.wftk.scale.portal.controller.openbusiness;

import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.scale.biz.dto.scale.ScaleQuestionTreeDTO;
import com.wftk.scale.biz.entity.ScaleListing;
import com.wftk.scale.biz.entity.ScaleListingUserConf;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.service.ScaleUserResultService;
import com.wftk.scale.portal.annotation.namespace.OpenBusinessMapping;
import com.wftk.scale.portal.converter.ScaleUserResultConverter;
import com.wftk.scale.portal.service.openbusiness.ScaleQuestionBusinessService;
import com.wftk.scale.portal.vo.input.ExtH5ParamInput;
import com.wftk.scale.portal.vo.input.ExtScaleQuestionInput;
import com.wftk.scale.portal.vo.input.ExtUserResultCreateInput;
import com.wftk.scale.portal.vo.input.ScaleUserResultCheckInput;
import com.wftk.scale.portal.vo.output.ExtValidCombinationScaleOutput;
import com.wftk.scale.portal.vo.output.ExtValidSingleScaleOutput;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/12/10 10:42
 */
@Tag(name = "外部h5相关接口")
@Slf4j
@OpenBusinessMapping("scale")
public class ScaleBusinessController {

    @Autowired
    ScaleQuestionBusinessService scaleQuestionBusinessService;

    @Autowired
    private ScaleUserResultService scaleUserResultService;

    @Autowired
    private ScaleUserResultConverter scaleUserResultConverter;

    @Operation(summary = "参数校验以及题目查询接口(单个量表)")
    @PostMapping("validParamAndQueryScaleQuestion")
    public ApiResult<ExtValidSingleScaleOutput> validParamAndQueryScaleQuestion(@RequestBody @Valid ExtH5ParamInput extH5ParamInput, @RequestParam String terminalCode, @RequestParam String tenantId) {
        ExtValidSingleScaleOutput extValidSingleScaleDTO = scaleQuestionBusinessService.validParamAndQueryScaleQuestion(extH5ParamInput, terminalCode, tenantId);
        return ApiResult.ok(extValidSingleScaleDTO);
    }

    @Operation(summary = "参数校验以及量表详情查询接口(组合量表)")
    @PostMapping("validParamAndQueryScaleCombination")
    public ApiResult<ExtValidCombinationScaleOutput> validParamAndQueryScaleCombination(@RequestBody @Valid ExtH5ParamInput extH5ParamInput, @RequestParam String terminalCode, @RequestParam String tenantId) {
        ExtValidCombinationScaleOutput scaleQuestion = scaleQuestionBusinessService.validParamAndQueryScaleCombination(extH5ParamInput, terminalCode, tenantId);
        return ApiResult.ok(scaleQuestion);
    }

    @Operation(summary = "根据量表id查询题目")
    @PostMapping("queryScaleQuestion")
    public ApiResult<List<ScaleQuestionTreeDTO>> queryScaleQuestion(@RequestBody @Valid ExtScaleQuestionInput extScaleQuestionInput) {
        List<ScaleQuestionTreeDTO> scaleQuestion = scaleQuestionBusinessService.queryScaleQuestion(extScaleQuestionInput);
        return ApiResult.ok(scaleQuestion);
    }

    @Operation(summary = "测评提交")
    @PostMapping("saveUserResult")
    public ApiResult<Long> saveUserResult(@RequestBody @Valid ExtUserResultCreateInput extUserResultCreateInput) {
        ScaleUserResult userResult = scaleUserResultConverter.scaleUserResultInputToEntity(extUserResultCreateInput.getUserResult());
        List<ScaleUserResultRecord> recordList = scaleUserResultConverter.scaleUserResultRecordListToEntityList(extUserResultCreateInput.getResultRecordList());
        boolean result = scaleUserResultService.vaildScaleUserResultRecordEmpty(recordList);
        if (result) {
            throw new BusinessException("测评失败,测评答题信息不能为空!");
        }
        ScaleListing scaleListing = scaleUserResultService.findByListingId(userResult.getScaleListingId());
        result = scaleUserResultService.vaildScaleListingEnabled(scaleListing);
        if (result) {
            throw new BusinessException("测评失败,量表已禁用无法进行测评!");
        }
        ScaleListingUserConf userConf = scaleUserResultService.findByListingIdAndlistingUserId(userResult.getScaleListingId(), userResult.getListingUserId());
        result = scaleUserResultService.vaildUserConfEvaluationTimeOut(userConf);
        if (result) {
            throw new BusinessException("测评失败,量表已超过配置的有效截止日期!");
        }
        result = scaleUserResultService.vaildScaleTimeLimit(userResult, userConf);
        if (result) {
            throw new BusinessException("测评失败,作答时间不满足答题要求!");
        }
        result = scaleUserResultService.vaildScaleEvaluationAgain(userResult, userResult.getUserId(), userResult.getOrderNo(), userConf);
        if (!result) {
            throw new BusinessException("测评失败,测评已完成，无法再次复测!");
        }
        Long resultId = scaleQuestionBusinessService.saveUserResult(extUserResultCreateInput.getAuthorizationCode(), userResult, recordList);
        return ApiResult.ok(resultId);
    }

    @Operation(summary = "开始量表测评（校验量表是否可进行测试）")
    @PostMapping("check")
    public ApiResult<String> startEvaluation(@Valid @RequestBody ScaleUserResultCheckInput input) {
        ScaleUserResult userResult = scaleUserResultConverter.ScaleUserResultCheckInputToEntity(input);
        ScaleListing scaleListing = scaleUserResultService.findByListingId(userResult.getScaleListingId());
        Long userId = userResult.getUserId();
        boolean result = scaleUserResultService.vaildScaleListingEnabled(scaleListing);
        if (result) {
            throw new BusinessException("测评失败,量表已禁用，无法进行测评!");
        }
        //校验答题顺序
        scaleUserResultService.validAnswerSort(input.getScaleId(), input.getScaleListingId(), input.getOrderNo(), userId);
        ScaleListingUserConf userConf = scaleUserResultService.findByListingIdAndlistingUserId(userResult.getScaleListingId(), userResult.getListingUserId());
        result = scaleUserResultService.vaildUserConfEvaluationTimeOut(userConf);
        if (result) {
            throw new BusinessException("测评失败,量表已超过配置的有效截止日期!");
        }
        result = scaleUserResultService.vaildScaleEvaluationAgain(userResult, userId, input.getOrderNo(), userConf);
        if (!result) {
            throw new BusinessException("测评失败,测评已完成，无法再次复测!");
        }
        return ApiResult.ok();
    }

}
