package com.wftk.scale.portal.vo.input;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2024/11/8 14:34
 */
@Data
public class ExtOrderCreateInput {

    /**
     * 三方订单号
     */
    @NotBlank(message = "serialNo不能为空")
    private String serialNo;

    /**
     * 量表类型 1.单个量表   2.组合量表
     */
    @NotNull(message = "type不能为空")
    private Integer type;

    /**
     * 量表编码
     */
    @NotBlank(message = "scaleCode不能为空")
    private String scaleCode;

    /**
     * 用户编码
     */
    @NotBlank(message = "userCode不能为空")
    private String userCode;

    /**
     * 用户编码
     */
    @NotNull(message = "listingId不能为空")
    private Long listingId;


    /**
     * 支付渠道
     */
    @NotNull(message = "payChannel不能为空")
    private Integer payChannel;


}
