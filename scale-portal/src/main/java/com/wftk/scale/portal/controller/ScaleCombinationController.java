package com.wftk.scale.portal.controller;

import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.listing.ScaleListedParamDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleCombinationQueryDTO;
import com.wftk.scale.portal.annotation.namespace.ScaleMapping;
import com.wftk.scale.portal.converter.ScaleListingConverter;
import com.wftk.scale.portal.service.ScaleProxyService;
import com.wftk.scale.portal.vo.input.ScaleListedQueryInput;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: ScaleCombinationController
 * @Description: 组合量表信息管理
 * @Author: mq
 * @Date: 2024-11-06 16:23
 * @Version: 1.0
 **/
@Tag(name = "组合量表维护相关API")
@RestController
@ScaleMapping("/combination/base")
@Slf4j
public class ScaleCombinationController {

    @Autowired
    private ScaleListingConverter scaleListingConverter;

    @Autowired
    private ScaleProxyService scaleProxyService;

    @Operation(summary = "获取已上架组合量表列表数据（页面展示）")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取已上架单个量表列表数据")
    @GetMapping("/listing")
    public ApiResult<Page<ScaleListedQueryDTO>> getScaleListedByPageDisplay(@Valid ScaleListedQueryInput input) {
        ScaleListedParamDTO searchParam = scaleListingConverter.scaleListedQueryInputToParamDTO(input);
        return ApiResult.ok(scaleProxyService.getScaleCombinationistedByPageDisplay(searchParam));
    }

    @Operation(summary = "根据量表ID获取单个量表详情信息")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据量表ID获取单个量表详情信息")
    @GetMapping("detail/id")
    public ApiResult<ScaleCombinationQueryDTO> findByScaleId(Long scaleListingId, Long scaleId, String terminalCode,
                                                             Long listingUserId, String orderNo) {
        if (ObjUtil.isNull(scaleListingId)) {
            throw new BusinessException("获取失败,量表scaleListingId参数不允许为空!");
        }
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("获取失败,量表scaleId参数不允许为空!");
        }
        if (ObjUtil.isEmpty(terminalCode)) {
            throw new BusinessException("获取失败,量表terminalCode参数不允许为空!");
        }
        return ApiResult.ok(scaleProxyService.findByScaleCombinationId(scaleListingId, scaleId, terminalCode, listingUserId, orderNo));
    }

    @Operation(summary = "根据量表编号获取单个量表最新版本数据")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据量表编号获取单个量表最新版本数据")
    @GetMapping("detail/code")
    public ApiResult<ScaleCombinationQueryDTO> findByScaleCode(Long scaleListingId, String scaleCode, String terminalCode) {
        if (ObjUtil.isNull(scaleListingId)) {
            throw new BusinessException("获取失败,量表scaleListingId参数不允许为空!");
        }
        if (ObjUtil.isEmpty(scaleCode)) {
            throw new BusinessException("获取失败,量表scaleCode参数不允许为空!");
        }
        if (ObjUtil.isEmpty(terminalCode)) {
            throw new BusinessException("获取失败,量表terminalCode参数不允许为空!");
        }
        ScaleCombinationQueryDTO dto = scaleProxyService.findByScaleCombinationCode(scaleListingId, scaleCode, terminalCode);
        return ApiResult.ok(dto);
    }
}
