package com.wftk.scale.portal.ext.auth;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;
import com.wftk.auth.spring.boot.autoconfigure.core.loader.UserLoader;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.user.DefaultAuthUser;
import com.wftk.scale.biz.entity.User;
import com.wftk.scale.biz.service.UserService;

/**
 * <AUTHOR>
 * @create 2024/11/25 14:08
 */
public class PortalUserLoader implements UserLoader<User> {

    private final UserService userService;

    public PortalUserLoader(UserService userService) {
        this.userService = userService;
    }

    @Override
    public AuthUser<User> load(String account) {
        User user = userService.selectByAccount(account, null);
        if (user == null) {
            return null;
        }
        return new DefaultAuthUser<>(user);
    }
}
