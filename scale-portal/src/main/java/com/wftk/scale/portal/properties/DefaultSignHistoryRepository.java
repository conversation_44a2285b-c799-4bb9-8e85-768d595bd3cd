package com.wftk.scale.portal.properties;

import cn.hutool.core.util.StrUtil;
import com.wftk.signature.repository.SignHistoryRepository;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.TimeUnit;

public class DefaultSignHistoryRepository implements SignHistoryRepository {

    private final StringRedisTemplate stringRedisTemplate;

    public DefaultSignHistoryRepository(StringRedisTemplate stringRedisTemplate){
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @Override
    public boolean isExists(String s) {
        return StrUtil.isNotBlank(stringRedisTemplate.opsForValue().get(s));
    }

    @Override
    public boolean save(String s, Long aLong) {
        stringRedisTemplate.opsForValue().set(s,"1",aLong, TimeUnit.MILLISECONDS);
        return true;
    }
}
