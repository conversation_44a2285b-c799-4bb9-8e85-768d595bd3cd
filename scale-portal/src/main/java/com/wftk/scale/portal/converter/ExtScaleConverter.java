package com.wftk.scale.portal.converter;

import com.wftk.scale.biz.dto.scale.ScaleCombinationDTO;
import com.wftk.scale.biz.dto.scale.ScaleCombinationQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleDTO;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.entity.ScaleCombination;
import com.wftk.scale.biz.entity.ScaleType;
import com.wftk.scale.portal.vo.output.*;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/11/29 15:21
 */
@Mapper(componentModel = "spring")
public interface ExtScaleConverter {

    ExtScaleQueryOutput scaleDtoToExtScaleQueryOutput(ScaleDTO scaleDTO);

    ExtScaleCombinationQueryOutput scaleCombinationToOutput(ScaleCombinationDTO scaleCombination);

    List<ExtScaleCombinationQueryOutput> scaleCombinationToOutput(List<ScaleCombinationDTO> scaleCombinations);

    List<ExtScaleCombinationDetailOutput> scaleQueryToDetailOutput(List<ScaleQueryDTO> scaleQueryDTOS);

    List<ExtScaleTypeOutput> scaleTypeToOutput(List<ScaleType> scaleTypes);

    ExtValidCombinationScaleOutput scaleCombinationQueryDTOToOutput(ScaleCombinationQueryDTO scaleCombinationQueryDTO);
}
