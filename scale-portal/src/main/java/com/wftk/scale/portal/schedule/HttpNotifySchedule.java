package com.wftk.scale.portal.schedule;

import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.TenantExecutor;
import com.wftk.scale.biz.entity.HttpJob;
import com.wftk.scale.biz.entity.NoticeMessage;
import com.wftk.scale.biz.lock.LockManager;
import com.wftk.scale.biz.manager.job.executor.RequestExecutor;
import com.wftk.scale.biz.service.HttpJobService;
import com.wftk.scale.biz.service.NoticeMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @create 2024/3/6 19:50
 */

@Slf4j
@Component
public class HttpNotifySchedule {


    @Autowired
    private HttpJobService httpJobService;

    @Autowired
    private RequestExecutor requestExecutor;

    @Autowired
    LockManager lockManager;

    @Autowired
    private NoticeMessageService noticeMessageService;

    @Value("${config.notice.max.count:3}")// 通知最大次数：默认3次
    private Integer noticeMaxCount;


    @Scheduled(cron = "0/30 * * * * ? ")
    public void httpRequest() {
        log.info("http job schedule start...");
        DLock dLock = lockManager.getHttpJobScheduleLock();
        try {
            if (dLock.tryLock()) {
                List<HttpJob> httpJobs = TenantExecutor.executeWithoutTenant(() -> httpJobService.selectNotEndList());
                for (HttpJob httpJob : httpJobs) {
                    TenantExecutor.execute(httpJob.getTenantId(), () -> {
                        requestExecutor.execute(httpJob);
                        return null;
                    });
                }
            }
        } finally {
            dLock.unLock();
        }

    }


    @Scheduled(cron = "17 */3 * * * ? ")
    public void noticeMessage() {
        DLock dLock = lockManager.getNoticeMessageScheduleLock();
        try {
            if(dLock.tryLock()){
                List<NoticeMessage> noticeMessageList = TenantExecutor.executeWithoutTenant(() -> noticeMessageService.getNeedSendNoticeMessage(noticeMaxCount));
                for (NoticeMessage noticeMessage : noticeMessageList) {
                    TenantExecutor.execute(noticeMessage.getTenantId(),()->{
                        noticeMessageService.sendNoticeByMessage(noticeMessage);
                        return null;
                    });
                }
            } else {
                log.info("get notice message schedule lock is fail..");
            }
        }finally {
            dLock.unLock();
        }

    }


}
