package com.wftk.scale.portal.controller.openapi;

import cn.hutool.core.collection.CollectionUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleQuestionTreeDTO;
import com.wftk.scale.portal.annotation.namespace.OpenapiMapping;
import com.wftk.scale.portal.ext.enums.ExtApiRespEnum;
import com.wftk.scale.portal.ext.exception.ExtApiBadBizException;
import com.wftk.scale.portal.service.openapi.ExtScaleService;
import com.wftk.scale.portal.vo.input.ExtScaleCombinationDetailQueryInput;
import com.wftk.scale.portal.vo.input.ExtScaleCombinationQueryInput;
import com.wftk.scale.portal.vo.input.ExtScaleQueryInput;
import com.wftk.scale.portal.vo.input.ExtScaleQuestionQueryInput;
import com.wftk.scale.portal.vo.output.ExtScaleCombinationQueryOutput;
import com.wftk.scale.portal.vo.output.ExtScaleQueryOutput;
import com.wftk.scale.portal.vo.output.ExtScaleTypeOutput;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @createDate 2024/11/27 10:31
 */
@Tag(name = "外部量表相关接口")
@OpenapiMapping("/scale")
public class ExtScaleController {

    @Autowired
    ExtScaleService extScaleService;

    @Autowired
    private LocalValidatorFactoryBean localValidatorFactoryBean;

    @Operation(summary = "单个量表查询")
    @PostMapping("/queryScaleList")
    public ApiResult<Page<ExtScaleQueryOutput>> queryScaleList(@RequestBody ExtScaleQueryInput extScaleQueryInput, @RequestParam("tenantId")String tenantId,@RequestParam("terminalCode")String terminalCode){
        validateParams(extScaleQueryInput);
        Page<ExtScaleQueryOutput> scales = extScaleService.queryScaleList(extScaleQueryInput, terminalCode);
        return ApiResult.ok(scales);
    }

    @Operation(summary = "单个量表类型查询")
    @PostMapping("/queryScaleTypeList")
    public ApiResult<List<ExtScaleTypeOutput>> queryScaleTypeList(@RequestParam("tenantId")String tenantId,@RequestParam("terminalCode")String terminalCode){
        List<ExtScaleTypeOutput> scaleTypeList = extScaleService.queryScaleTypeList( terminalCode);
        return ApiResult.ok(scaleTypeList);
    }


    @Operation(summary = "组合量表查询")
    @PostMapping("/queryScaleCombinationList")
    public ApiResult<Page<ExtScaleCombinationQueryOutput>> queryScaleCombinationList(@RequestBody ExtScaleCombinationQueryInput extScaleCombinationQueryInput, @RequestParam("tenantId")String tenantId,@RequestParam("terminalCode")String terminalCode){
        validateParams(extScaleCombinationQueryInput);
        Page<ExtScaleCombinationQueryOutput> scaleCombinations = extScaleService.queryScaleCombinationList(extScaleCombinationQueryInput,terminalCode);
        return ApiResult.ok(scaleCombinations);
    }

    @Operation(summary = "组合量表详情查询")
    @PostMapping("/queryScaleCombinationDetailList")
    public ApiResult<List<ExtScaleQueryOutput>> queryScaleCombinationDetailList(@RequestBody ExtScaleCombinationDetailQueryInput extScaleCombinationQueryInput, @RequestParam("tenantId")String tenantId,@RequestParam("terminalCode")String terminalCode){
        validateParams(extScaleCombinationQueryInput);
        List<ExtScaleQueryOutput> scaleCombinations = extScaleService.findScaleCombinationDetailByScaleCombinationId(extScaleCombinationQueryInput.getScaleCombinationId(),terminalCode);
        return ApiResult.ok(scaleCombinations);
    }

    @Operation(summary = "量表题目详情查询")
    @PostMapping("/queryScaleQuestion")
    public ApiResult<List<ScaleQuestionTreeDTO>> findScaleQuestion(@RequestBody ExtScaleQuestionQueryInput extScaleQuestionQueryInput, @RequestParam("tenantId")String tenantId, @RequestParam("terminalCode")String terminalCode){
        validateParams(extScaleQuestionQueryInput);
        List<ScaleQuestionTreeDTO> scaleQuestion = extScaleService.findScaleQuestion(extScaleQuestionQueryInput.getScaleId());
        return ApiResult.ok(scaleQuestion);
    }

    private void validateParams(Object object) {
        Validator validator = localValidatorFactoryBean.getValidator();
        Set<ConstraintViolation<Object>> failConditions = validator.validate(object);
        if (CollectionUtil.isNotEmpty(failConditions)) {
            for (ConstraintViolation<Object> condition : failConditions) {
                throw new ExtApiBadBizException(ExtApiRespEnum.PARAMS_VERIFY_ERROR.getCode(),condition.getMessage());
            }
        }
    }


}
