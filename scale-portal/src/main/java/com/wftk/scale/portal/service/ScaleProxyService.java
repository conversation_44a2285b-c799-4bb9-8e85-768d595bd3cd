package com.wftk.scale.portal.service;


import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordParamDTO;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordQueryDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedParamDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleCombinationQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;

/**
 * @InterfaceName: ScaleProxyService
 * @Description: 量表管理
 * @Author: mq
 * @Date: 2024/12/5
 * @Version: 1.0
 **/
public interface ScaleProxyService {

    /*
     * @Author: mq
     * @Description: 获取单个已上架量表且页面展示
     * @Date: 2024/12/5 15:38
     * @Param: scaleListedParamDTO-检索条件
     * @return: Page<ScaleListedQueryDTO>
     **/
    Page<ScaleListedQueryDTO> getScaleListedByPageDisplay(ScaleListedParamDTO scaleListedParamDTO);

    /*
     * @Author: mq
     * @Description: 获取组合已上架量表且页面展示
     * @Date: 2024/12/5 15:38
     * @Param: scaleListedParamDTO-检索条件
     * @return: Page<ScaleListedQueryDTO>
     **/
    Page<ScaleListedQueryDTO> getScaleCombinationistedByPageDisplay(ScaleListedParamDTO scaleListedParamDTO);

    /*
     * @Author: mq
     * @Description: 获取用户分发的单个和组合量表数据
     * @Date: 2024/12/5 15:45
     * @Param: scaleListingUserParamDTO
     * @return: Page<ScaleListingUserRecordQueryDTO>
     **/
    Page<ScaleListingUserRecordQueryDTO> getAllScaleDistributeRecord(ScaleListingUserRecordParamDTO scaleListingUserParamDTO);

    /*
     * @Author: mq
     * @Description: 根据量表ID获取量表详情信息
     * @Date: 2024/12/5 15:42
     * @Param: scaleListingId-上架配置ID
     * @Param: scaleId-量表ID
     * @return: ScaleQueryDTO
     **/
    ScaleQueryDTO findByScaleId(Long scaleListingId, Long scaleId, Long listingUserId, String orderNo);

    /*
     * @Author: mq
     * @Description: 根据量表编号获取量表详情信息
     * @Date: 2024/12/5 15:42
     * @Param: scaleListingId-上架配置ID
     * @Param: scaleCode-量表编号
     * @return: ScaleQueryDTO
     **/
    ScaleQueryDTO findByScaleCode(Long scaleListingId, String scaleCode);

    /*
     * @Author: mq
     * @Description: TODO
     * @Date: 2024/12/13 14:41
     * @Param: scaleListingId-上架配置ID
     * @Param: scaleCombinationId-组合量表ID
     * @Param: terminalCode-终端编号
     * @Param: scaleListingUserId-分发ID
     * @return: ScaleCombinationQueryDTO
     **/
    ScaleCombinationQueryDTO findByScaleCombinationId(Long scaleListingId, Long scaleCombinationId, String terminalCode,
                                                      Long listingUserId, String orderNo);

    /*
     * @Author: mq
     * @Description: 根据量表编号获取组合量表详情信息
     * @Date: 2024/12/13 14:40
     * @Param: scaleListingId-上架配置ID
     * @Param: scaleCode-组合量表编号
     * @Param: terminalCode-终端编号
     * @return: ScaleCombinationQueryDTO
     **/
    ScaleCombinationQueryDTO findByScaleCombinationCode(Long scaleListingId, String scaleCod, String terminalCode);
}
