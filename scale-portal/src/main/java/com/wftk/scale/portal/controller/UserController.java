package com.wftk.scale.portal.controller;

import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.common.core.result.ApiResult;
import com.wftk.scale.biz.entity.User;
import com.wftk.scale.biz.service.UserService;
import com.wftk.scale.portal.annotation.namespace.UserMapping;
import com.wftk.scale.portal.converter.UserInfoConverter;
import com.wftk.scale.portal.vo.input.UserPasswordChangeInput;
import com.wftk.scale.portal.vo.output.UserOutput;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @createDate 2024/11/29 11:38
 */
@Tag(name = "用户相关API")
@UserMapping
@Slf4j
public class UserController {


    @Autowired
    UserService userService;

    @Autowired
    UserInfoConverter userConverter;

    @PutMapping("/changePassword")
    public ApiResult<Void> changePassword(@RequestBody UserPasswordChangeInput userPasswordChangeInput){
        Long userId =  AuthenticationHolder.getAuthentication().getAuthUser().getId();
        userService.changePassword(userId,userPasswordChangeInput.getPassword());
        return ApiResult.ok();
    }


    @GetMapping("/base/info")
    public ApiResult<UserOutput> getUserInfo(){
        Long userId =  AuthenticationHolder.getAuthentication().getAuthUser().getId();
        User user = userService.getById(userId);
        return ApiResult.ok(userConverter.entityToUserOutput(user));
    }


}
