package com.wftk.scale.portal.service.openapi.impl;

import cn.hutool.core.collection.CollUtil;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleCombinationDTO;
import com.wftk.scale.biz.dto.scale.ScaleDTO;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleQuestionTreeDTO;
import com.wftk.scale.biz.service.*;
import com.wftk.scale.portal.converter.ExtScaleConverter;
import com.wftk.scale.portal.service.ScaleQuestionProxyService;
import com.wftk.scale.portal.service.openapi.ExtScaleService;
import com.wftk.scale.portal.vo.input.ExtScaleCombinationQueryInput;
import com.wftk.scale.portal.vo.input.ExtScaleQueryInput;
import com.wftk.scale.portal.vo.output.ExtScaleCombinationDetailOutput;
import com.wftk.scale.portal.vo.output.ExtScaleCombinationQueryOutput;
import com.wftk.scale.portal.vo.output.ExtScaleQueryOutput;
import com.wftk.scale.portal.vo.output.ExtScaleTypeOutput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/11/18 19:41
 */
@Service
@Slf4j
public class ExtScaleServiceImpl implements ExtScaleService {

    @Autowired
    ScaleService scaleService;

    @Autowired
    ScaleCombinationService scaleCombinationService;

    @Autowired
    ScaleCombinationDetailService scaleCombinationDetailService;

    @Autowired
    ScaleQuestionService scaleQuestionService;

    @Autowired
    ScaleTypeService scaleTypeService;

    @Autowired
    ExtScaleConverter extScaleConverter;

    @Autowired
    ScaleQuestionProxyService scaleQuestionProxyService;

    /**
     * 查询已上架量表
     *
     * @param extScaleQueryInput
     * @param terminalCode
     * @return
     */
    @Override
    public Page<ExtScaleQueryOutput> queryScaleList(ExtScaleQueryInput extScaleQueryInput, String terminalCode) {
        Page<ScaleDTO> scaleDTOPage = Page.doSelectPage(extScaleQueryInput.getPageNum(), extScaleQueryInput.getPageSize(),
                () -> scaleService.getListedScale(extScaleQueryInput.getScaleId(), extScaleQueryInput.getScaleCode(),
                        extScaleQueryInput.getScaleName(), extScaleQueryInput.getScaleType(), extScaleQueryInput.getListingShowType(), terminalCode));

        // 转换以及获取题目数量
        return scaleDTOPage.toPage(list -> {
            List<ExtScaleQueryOutput> scaleQueryOutputs = new ArrayList<>();
            for (ScaleDTO scaleDTO : list) {
                ExtScaleQueryOutput extScaleQueryOutput = extScaleConverter.scaleDtoToExtScaleQueryOutput(scaleDTO);
                Integer numOfQuestion = scaleQuestionService.getNumOfQuestion(scaleDTO.getId());
                extScaleQueryOutput.setNumOfQuestion(numOfQuestion);
                scaleQueryOutputs.add(extScaleQueryOutput);
            }
            return scaleQueryOutputs;
        });
    }

    @Override
    public Page<ExtScaleCombinationQueryOutput> queryScaleCombinationList(ExtScaleCombinationQueryInput extScaleCombinationQueryInput, String terminalCode) {
        Page<ScaleCombinationDTO> scaleCombinationPage = Page.doSelectPage(extScaleCombinationQueryInput.getPageNum(), extScaleCombinationQueryInput.getPageSize(),
                () -> scaleCombinationService.getListedScaleCombination(extScaleCombinationQueryInput.getCode(), terminalCode,
                        extScaleCombinationQueryInput.getScaleName(),extScaleCombinationQueryInput.getType(),extScaleCombinationQueryInput.getListingShowType() ));
        return scaleCombinationPage.toPage(list -> {
            List<ExtScaleCombinationQueryOutput> extScaleCombinationQueryOutputs = extScaleConverter.scaleCombinationToOutput(list);
            for (ExtScaleCombinationQueryOutput item : extScaleCombinationQueryOutputs) {
                List<ScaleQueryDTO> details = scaleCombinationDetailService.findByCombinationId(item.getId());
                List<ExtScaleCombinationDetailOutput> scaleNames = extScaleConverter.scaleQueryToDetailOutput(details);
                item.setScaleCombinationDetails(scaleNames);
                item.setNumOfScale(CollUtil.isEmpty(details) ? 0 : details.size());
            }
            return extScaleCombinationQueryOutputs;
        });
    }

    @Override
    public List<ExtScaleTypeOutput> queryScaleTypeList(String terminalCode) {
        return extScaleConverter.scaleTypeToOutput(scaleTypeService.getListByTerminalCode(terminalCode));
    }

    @Override
    public List<ExtScaleQueryOutput> findScaleCombinationDetailByScaleCombinationId(Long scaleCombinationId, String terminalCode) {
        List<ScaleDTO> list = scaleCombinationService.getListedScaleCombinationDetail(scaleCombinationId, terminalCode);
        List<ExtScaleQueryOutput> scaleQueryOutputs = new ArrayList<>();
        for (ScaleDTO scaleDTO : list) {
            ExtScaleQueryOutput extScaleQueryOutput = extScaleConverter.scaleDtoToExtScaleQueryOutput(scaleDTO);
            Integer numOfQuestion = scaleQuestionService.getNumOfQuestion(scaleDTO.getId());
            extScaleQueryOutput.setNumOfQuestion(numOfQuestion);
            scaleQueryOutputs.add(extScaleQueryOutput);
        }
        return scaleQueryOutputs;
    }

    @Override
    public List<ScaleQuestionTreeDTO> findScaleQuestion(Long scaleId) {
        return scaleQuestionProxyService.selectPage(scaleId);
    }

}
