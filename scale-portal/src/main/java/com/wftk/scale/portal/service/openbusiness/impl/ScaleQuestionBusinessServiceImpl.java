package com.wftk.scale.portal.service.openbusiness.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.cache.spring.boot.autoconfigure.store.redisson.StringRedissonObjectStore;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.scale.biz.constant.TerminalConfConstant;
import com.wftk.scale.biz.constant.enums.OrderEnum;
import com.wftk.scale.biz.dto.scale.ScaleCombinationQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleQuestionTreeDTO;
import com.wftk.scale.biz.entity.*;
import com.wftk.scale.biz.service.*;
import com.wftk.scale.portal.common.constant.LinkConstant;
import com.wftk.scale.portal.converter.ExtScaleConverter;
import com.wftk.scale.portal.ext.enums.ExtApiRespEnum;
import com.wftk.scale.portal.service.ScaleQuestionProxyService;
import com.wftk.scale.portal.service.openbusiness.ScaleQuestionBusinessService;
import com.wftk.scale.portal.vo.input.ExtH5ParamInput;
import com.wftk.scale.portal.vo.input.ExtScaleQuestionInput;
import com.wftk.scale.portal.vo.output.ExtValidCombinationScaleOutput;
import com.wftk.scale.portal.vo.output.ExtValidSingleScaleOutput;
import com.wftk.signature.builder.BaseSignBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createDate 2024/12/7 14:20
 */
@Slf4j
@Service
public class ScaleQuestionBusinessServiceImpl implements ScaleQuestionBusinessService {

    @Autowired
    OrderService orderService;

    @Autowired
    TerminalService terminalService;

    @Autowired
    TerminalConfService terminalConfService;

    @Autowired
    ScaleQuestionProxyService scaleQuestionProxyService;

    @Autowired
    ScaleCombinationService scaleCombinationService;

    @Autowired
    ScaleListingService scaleListingService;

    @Autowired
    ScaleUserResultService scaleUserResultService;

    @Autowired
    StringRedissonObjectStore stringRedissonObjectStore;

    @Autowired
    DepartmentService departmentService;

    @Autowired
    UserService userService;

    @Autowired
    ExtScaleConverter extScaleConverter;

    @Autowired
    ScaleListingUserConfService scaleListingUserConfService;


    @Override
    public ExtValidSingleScaleOutput validParamAndQueryScaleQuestion(ExtH5ParamInput extH5ParamInput, String terminalCode, String tenantId) {
        // 校验参数
        validParam(extH5ParamInput,terminalCode,tenantId);

        // 加读锁
        Order orderIndb = orderService.getByTerminalSerialNoAndOrderNo(terminalCode, null, extH5ParamInput.getOrderNo());
        if (orderIndb == null) {
            throw new BusinessException(ExtApiRespEnum.ORDER_QUERY_ORDER_IS_NULL.getMsg());
        }
        // 只有待完成的才能做题
        if (Objects.equals(orderIndb.getStatus(), OrderEnum.WAIT_PAY.getStatus())) {
            throw new BusinessException(ExtApiRespEnum.ORDER_QUERY_ORDER_STATUS_1.getMsg());
        } else if (Objects.equals(orderIndb.getStatus(), OrderEnum.COMPLETED.getStatus())) {
            throw new BusinessException(ExtApiRespEnum.ORDER_QUERY_ORDER_STATUS_3.getMsg());
        } else if (Objects.equals(orderIndb.getStatus(), OrderEnum.CANCLE.getStatus())) {
            throw new BusinessException(ExtApiRespEnum.ORDER_QUERY_ORDER_STATUS_4.getMsg());
        }

        // 查询题目
        List<ScaleQuestionTreeDTO> scaleQuestionQueryDTOS = scaleQuestionProxyService.selectPage(extH5ParamInput.getScaleId());
        ExtValidSingleScaleOutput extValidSingleScaleDTO = new ExtValidSingleScaleOutput();
        extValidSingleScaleDTO.setScaleQuestionTrees(scaleQuestionQueryDTOS);
        extValidSingleScaleDTO.setScaleListingId(orderIndb.getScaleListingId());
        extValidSingleScaleDTO.setOrderNo(orderIndb.getOrderNo());
        extValidSingleScaleDTO.setUserId(orderIndb.getUserId());
        return extValidSingleScaleDTO;
    }

    @Override
    public ExtValidCombinationScaleOutput validParamAndQueryScaleCombination(ExtH5ParamInput extH5ParamInput, String terminalCode, String tenantId) {
        // 校验
        validParam(extH5ParamInput,terminalCode,tenantId);

        // 加读锁
        Order orderIndb = orderService.getByTerminalSerialNoAndOrderNo(terminalCode, null, extH5ParamInput.getOrderNo());
        if (orderIndb == null) {
            throw new BusinessException(ExtApiRespEnum.ORDER_QUERY_ORDER_IS_NULL.getMsg());
        }
        // 只有待完成的才能做题
        if (Objects.equals(orderIndb.getStatus(), OrderEnum.WAIT_PAY.getStatus())) {
            throw new BusinessException(ExtApiRespEnum.ORDER_QUERY_ORDER_STATUS_1.getMsg());
        }  else if (Objects.equals(orderIndb.getStatus(), OrderEnum.CANCLE.getStatus())) {
            throw new BusinessException(ExtApiRespEnum.ORDER_QUERY_ORDER_STATUS_4.getMsg());
        }

        ScaleCombinationQueryDTO dto = scaleCombinationService.findByScaleId(extH5ParamInput.getScaleId());

        List<ScaleQueryDTO> details = dto.getDetails();
        details = details.stream().peek(scale -> {
            if(StrUtil.isBlank(extH5ParamInput.getOrderNo())){
                scale.setCompleted(false);
            }else {
                scale.setCompleted(scaleUserResultService.validScaleUserResultComplete(extH5ParamInput.getOrderNo(), scale.getId()));
            }
        }).collect(Collectors.toList());
        ScaleListingUserConf userConf = scaleListingUserConfService.findByListingId(orderIndb.getScaleListingId());
        dto.setListingStartTime(ObjUtil.isNotNull(userConf) ? userConf.getStartTime() : null);
        dto.setListingEndTime(ObjUtil.isNotNull(userConf) ? userConf.getEndTime() : null);
        dto.setDetails(details);

        dto.setScalelistingId(orderIndb.getScaleListingId());
        ExtValidCombinationScaleOutput validCombinationScaleOutput = extScaleConverter.scaleCombinationQueryDTOToOutput(dto);
        validCombinationScaleOutput.setUserName(orderIndb.getUserName());
        validCombinationScaleOutput.setUserId(orderIndb.getUserId());
        validCombinationScaleOutput.setOrderNo(orderIndb.getOrderNo());
        return validCombinationScaleOutput;
    }

    @Override
    public Long saveUserResult(String authorizationCode, ScaleUserResult scaleUserResult, List<ScaleUserResultRecord> resultRecordList) {
        validOrderExpir(authorizationCode,scaleUserResult.getOrderNo());
        return scaleUserResultService.create(scaleUserResult, resultRecordList).getId();
    }

    @Override
    public List<ScaleQuestionTreeDTO> queryScaleQuestion(ExtScaleQuestionInput extScaleQuestionInput) {
        validOrderExpir(extScaleQuestionInput.getAuthorizationCode(),extScaleQuestionInput.getOrderNo());
        return scaleQuestionProxyService.selectPage(extScaleQuestionInput.getScaleId());
    }

    private boolean validSign(ExtH5ParamInput extH5ParamInput, String terminalCode, String terminalSecret,String tenantId) {
        // 校验签名
        Map<String, Object> map = BeanUtil.beanToMap(extH5ParamInput);
        map.put(LinkConstant.TERMINAL_CODE, terminalCode);
        map.put(LinkConstant.TENANT_ID,tenantId);
        BaseSignBuilder baseSignBuilder = new BaseSignBuilder(terminalSecret);
        baseSignBuilder.addParams(map);
        String sign = baseSignBuilder.build();
        return sign.equals(extH5ParamInput.getSign());
    }

    private void validParam(ExtH5ParamInput extH5ParamInput,String terminalCode,String tenantId){
        // 校验终端
        Terminal terminal = terminalService.getByCode(terminalCode);
        if (terminal == null || !terminal.getEnable()) {
            log.warn("terminal not exits, terminalCode: [{}]", terminalCode);
            throw new BusinessException("非法的终端");
        }
        String terminalSecret = terminalConfService.findByItemCode(terminal.getId(), TerminalConfConstant.CLIENT_SECRET, true);
        if (StrUtil.isBlank(terminalSecret)) {
            log.warn("terminal secret haven't configured, terminalCode: [{}]", terminalCode);
            throw new BusinessException("非法的终端");
        }

        // 校验签名
        if (!validSign(extH5ParamInput,terminalCode,terminalSecret,tenantId)) {
            throw new BusinessException("非法签名");
        }

        // 校验用户
        String userCode = extH5ParamInput.getUserCode();
        // 通过终端编码查询部门
        Department department = departmentService.selectByTerminalCode(terminalCode);
        if (department == null) {
            throw new BusinessException("终端未绑定机构");
        }
        User user = userService.selectByCodeAndDepartmentId(userCode, department.getId());
        if (user == null || !user.getEnable()) {
            log.error("user is null or enable is false.{} {}", userCode, department.getId());
            throw new BusinessException("用户不存在");
        }

        validOrderExpir(extH5ParamInput.getAuthorizationCode(),extH5ParamInput.getOrderNo());

    }

    private void validOrderExpir(String extAuthorizationCode,String orderNo){
        // 校验订单
        String key = LinkConstant.PROJECT_NAME + ":" + LinkConstant.EXT_AUTHORIZATION_CODE + ":" + extAuthorizationCode;
        String orderNoCache = stringRedissonObjectStore.getObject(key);
        if (StrUtil.isBlank(orderNoCache)) {
            throw new BusinessException("链接已过期");
        }
        if (!orderNo.equals(orderNoCache)) {
            throw new BusinessException("非法订单");
        }
    }

}
