package com.wftk.scale.portal.converter;

import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordParamDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedParamDTO;
import com.wftk.scale.portal.vo.input.ScaleListedQueryInput;
import com.wftk.scale.portal.vo.input.ScaleListingUserRecordQueryInput;
import org.mapstruct.Mapper;

/**
 * @InterfaceName: ScaleListingConverter
 * @Description: 量表上架数据转换器
 * @Author: mq
 * @Date: 2024-10-29 10:30
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleListingConverter {

    /*
     * @Author: mq
     * @Description: 将已上架量表查询信息实体转换为数据实体
     * @Date: 2024/10/30 13:46 
     * @Param: scaleListedQueryInput  
     * @return: com.wftk.scale.biz.dto.listing.ScaleListedParamDTO 
     **/
    ScaleListedParamDTO scaleListedQueryInputToParamDTO(ScaleListedQueryInput scaleListedQueryInput);

    /*
     * @Author: mq
     * @Description: 将分发记录量表查询信息实体转换为数据实体
     * @Date: 2024/10/30 13:46
     * @Param: scaleListedQueryInput
     * @return: com.wftk.scale.biz.dto.listing.ScaleListedParamDTO
     **/
    ScaleListingUserRecordParamDTO scaleListingUserRecordQueryInputToParamDTO(ScaleListingUserRecordQueryInput scaleListingUserRecordQueryInput);
}
