package com.wftk.scale.portal.vo.input;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2024/12/7 15:39
 */
@Data
public class ExtH5ParamInput {

    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @NotBlank(message = "用户编码不能为空")
    private String userCode;

    @NotNull(message = "量表Id不能为空")
    private Long scaleId;

    @NotNull(message = "量表类型不能为空")
    private Integer scaleType;

    @NotBlank(message = "授权码不能为空")
    private String authorizationCode;

    @NotBlank(message = "签名串不能为空")
    private String sign;
}
