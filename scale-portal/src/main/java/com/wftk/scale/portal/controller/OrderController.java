package com.wftk.scale.portal.controller;

import com.wftk.common.core.result.ApiResult;
import com.wftk.scale.portal.annotation.namespace.OrderMapping;
import com.wftk.scale.portal.service.OrderPayService;
import com.wftk.scale.portal.vo.input.CreateOrderInput;
import com.wftk.scale.portal.vo.output.PrePaymentOutput;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @createDate 2024/11/21 11:33
 */
@Tag(name = "订单相关API")
@OrderMapping
@Slf4j
public class OrderController {

    @Autowired
    OrderPayService orderPayService;

    @Operation(summary = "购买量表")
    @PostMapping("/buyScale")
    public ApiResult<PrePaymentOutput> buyScale(@RequestBody @Valid CreateOrderInput createOrderInput){
        PrePaymentOutput paymentOutput = orderPayService.buyScaleWithPrePayment(createOrderInput);
        return ApiResult.ok(paymentOutput);
    }

    @Operation(summary = "通过订单号查询是否可以重新支付")
    @GetMapping("/checkNeedBuy")
    public ApiResult<Boolean> checkNeedBuy(Long listingUserId, Long scaleListingId){
        return ApiResult.ok(orderPayService.checkNeedBuy(listingUserId, scaleListingId));
    }
}
