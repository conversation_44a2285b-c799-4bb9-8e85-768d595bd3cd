package com.wftk.scale.portal.service.openapi;

import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleQuestionTreeDTO;
import com.wftk.scale.portal.vo.input.ExtScaleCombinationQueryInput;
import com.wftk.scale.portal.vo.input.ExtScaleQueryInput;
import com.wftk.scale.portal.vo.output.ExtScaleCombinationQueryOutput;
import com.wftk.scale.portal.vo.output.ExtScaleQueryOutput;
import com.wftk.scale.portal.vo.output.ExtScaleTypeOutput;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/11/18 19:41
 */
public interface ExtScaleService {

    /**
     * 查询量表 分页
     *
     * @param extScaleQueryInput
     * @param terminalCode
     * @return
     */
    Page<ExtScaleQueryOutput> queryScaleList(ExtScaleQueryInput extScaleQueryInput,String terminalCode);

    /**
     * 查询组合量表 分页
     *
     * @param extScaleCombinationQueryInput
     * @param terminalCode
     * @return
     */
    Page<ExtScaleCombinationQueryOutput> queryScaleCombinationList(ExtScaleCombinationQueryInput extScaleCombinationQueryInput,String terminalCode);

    /***
     * 量表类型查询
     * @return
     */
    List<ExtScaleTypeOutput> queryScaleTypeList(String terminalCode);



    // 查询组合量表详情
    List<ExtScaleQueryOutput> findScaleCombinationDetailByScaleCombinationId(Long scaleCombinationId,String terminalCode);

    // 查询问题详情
    List<ScaleQuestionTreeDTO> findScaleQuestion(Long scaleId);

}
