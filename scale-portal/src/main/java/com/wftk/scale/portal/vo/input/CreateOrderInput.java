package com.wftk.scale.portal.vo.input;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2024/11/19 17:11
 */
@Data
public class CreateOrderInput {

    /**
     * 终端编码
     */
    @NotBlank(message = "终端编码不能为空")
    private String terminalCode;

    /**
     * 量表类型
     */
    @NotNull(message = "量表类型不能为空")
    private Integer type;

    /**
     * 目标id
     */
    @NotNull(message = "量表Id不能为空")
    private Long targetId;

    /**
     * 上下架id
     */
    @NotNull(message = "上下架Id不能为空")
    private Long scaleListingId;

    /**
     * 分发id
     */
    private Long scaleListingUserRecordId;

}
