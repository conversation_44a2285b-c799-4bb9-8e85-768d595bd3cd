package com.wftk.scale.portal.service.openbusiness;

import com.wftk.scale.biz.dto.scale.ScaleCombinationQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleQuestionTreeDTO;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.portal.vo.input.ExtH5ParamInput;
import com.wftk.scale.portal.vo.input.ExtScaleQuestionInput;
import com.wftk.scale.portal.vo.output.ExtValidCombinationScaleOutput;
import com.wftk.scale.portal.vo.output.ExtValidSingleScaleOutput;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/12/7 14:15
 */
public interface ScaleQuestionBusinessService {

    // 解析单个量表参数以及校验
    ExtValidSingleScaleOutput validParamAndQueryScaleQuestion(ExtH5ParamInput extH5ParamInput, String terminalCode, String tenantId);

    // 解析组合量表参数以及校验
    ExtValidCombinationScaleOutput validParamAndQueryScaleCombination(ExtH5ParamInput extH5ParamInput, String terminalCode, String tenantId);

    // 提交答案
    Long saveUserResult(String authorizationCode, ScaleUserResult scaleUserResult, List<ScaleUserResultRecord> resultRecordList);

    // 获取单个列表的题目
    List<ScaleQuestionTreeDTO> queryScaleQuestion(ExtScaleQuestionInput extScaleQuestionInput);

}
