package com.wftk.scale.portal.service;

import com.wftk.scale.portal.vo.input.CreateOrderInput;
import com.wftk.scale.portal.vo.output.PrePaymentOutput;

/**
 * <AUTHOR>
 * @createDate 2024/11/20 11:45
 */
public interface OrderPayService {

    /**
     * 购买量表并发起预支付
     *
     * @return
     */
    PrePaymentOutput buyScaleWithPrePayment(CreateOrderInput createOrderInput);

    Boolean checkNeedBuy(Long listingUserId, Long scaleListingId);
}
