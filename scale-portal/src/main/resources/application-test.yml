spring:
  datasource:
    url: jdbc:mysql://***************:3306/scale_system?serverTimezone=GMT%2B8&useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 100
      max-wait: 10000
      time-between-eviction-runs-millis: 3600000

  data:
    redis:
      host: redis.vas.chili.com
      port: 6379
      timeout: 2000
      connect-timeout: 1000

  redis:
    redisson:
      config:
        singleServerConfig:
          subscriptionConnectionMinimumIdleSize: 1
          subscriptionConnectionPoolSize: 20
          connectionMinimumIdleSize: 5
          connectionPoolSize: 20

config:
  file:
    oss:
      scale:
        bucket: bmmicroservicesoss
        access-key: LTAI5tEx7YQ4LnQQ9v44FhG5
        access-secret: ******************************
        directory: 'scale-system'
        endpoint: "https://bmoss.sunnyflowers.net"
        readable: true
        writeable: true
        expiredInTimeSeconds: 7200
      scale-pub:
        bucket: bmmicroservicesoss
        access-key: LTAI5tNFdQHzLmz2nuRGoM9a
        access-secret: ******************************
        directory: 'scale-system/pub'
        endpoint: "https://bmoss.sunnyflowers.net"
        readable: true
        writeable: true
        expiredInTimeSeconds: 2147483647  # 设置超长时间
  sms:
    aliyun:
      enable: true
      access-key: LTAI5tRgvAtyqLQYJv49ihH7
      access-secret: ******************************
      templates:
        - scene:
          signature:
          code:

  report_pdf:
    temp-path: /opt/apps/scale/scale-portal/temp
    templates-path: /opt/apps/scale/scale-portal/templete
  message:
    mail:
      host: smtp.qiye.aliyun.com
      port: 465
      username: <EMAIL>
      password: wftk-alarm@123
      protocol: smtp
      properties:
        mail:
          smtp:
            auth: true
            starttls:
              enable: true
              required: true
            ssl:
              enable: true
    feishu:
      robot:
        enable: false