
server:
  port: 8700
  tomcat:
    threads:
      max: 300
  servlet:
    encoding:
      force-response: true

spring:
  application:
    name: scale-portal
  profiles:
    active: dev
  main:
    allow-circular-references: true
  web:
    resources:
      add-mappings: true 
  task:
    execution:
      pool:
        core-size: 12
        queue-capacity: 300
        max-size: 60
      thread-name-prefix: "async-biz-default-"
    scheduling:
      pool:
        size: 10


mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

management:
  server:
    port: 8701
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /management
  metrics:
    tags:
      application: ${spring.application.name}
  endpoint:
    health:
      show-details: always

config:
  auth:
    filter:
      ignore-patterns:
        - /management/**
        - /swagger-ui/**
        - /swagger-ui.html
        - /v3/api-docs/**
        - /favicon.ico
        - /doc.html
        - /webjars/**
        - /user/auth/**
        - /openapi/**
        - /openbusiness/**
        - /scale/single/base/assessment/instructions
      enable: true
    validation:
      client:
        enable: false
      account:
        enable: false
  file:
    item:
      scale_pic:
        role: scale-pub
        dir: scale/picture
      scale_video:
        role: scale
        dir: scale/video
  mybatis:
    tenant:
      enabled: true
      tenant-column-name: tenant_id
      ignore-tables: tenant_setting
      allow-null-tenant: false
      http:
        tenant-name: tenantId
  signature:
    filter:
      ignored-patterns:
        - /management/**
        - /swagger-ui/**
        - /swagger-ui.html
        - /v3/api-docs/**
        - /favicon.ico
        - /doc.html
        - /webjars/**
        - /openapi/**
        - /openbusiness/**
    client-id-param-name: clientId
  thread:
    task:
      pool:
        core-pool-size: 8 #设置核心线程数
        max-pool-size: 16  #设置最大线程数
        keep-alive-seconds: 60 #设置线程活跃时间（秒）
        queue-capacity: 100 #设置队列容量
        name-prefix: "scale-system-portal-async-"