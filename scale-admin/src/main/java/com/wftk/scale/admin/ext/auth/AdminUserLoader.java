package com.wftk.scale.admin.ext.auth;

import com.wftk.scale.biz.entity.SysUser;
import com.wftk.scale.biz.service.SysUserService;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;
import com.wftk.auth.spring.boot.autoconfigure.core.loader.UserLoader;
import lombok.extern.slf4j.Slf4j;

/**
 * 管理端用户查询，需要受到租户控制
 * <AUTHOR>
 * @create 2024/11/27 16:19
 */
@Slf4j
public class AdminUserLoader implements UserLoader<SysUser> {

    private final SysUserService sysUserService;

    public AdminUserLoader(SysUserService sysUserService) {
        this.sysUserService = sysUserService;
    }

    @Override
    public AuthUser<SysUser> load(String account) {
        SysUser adminUser = sysUserService.findByAccount(account, null);
        if (adminUser == null) {
            return null;
        }
        return new AdminAuthUser(adminUser);
    }
}
