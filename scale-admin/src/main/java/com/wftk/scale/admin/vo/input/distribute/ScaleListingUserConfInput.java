package com.wftk.scale.admin.vo.input.distribute;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: ScaleListingUserConfInput
 * @Description:
 * @Author: mq
 * @Date: 2024/12/16
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleListingUserConfInput implements Serializable {

    /**
     * 项目名称
     */
    @Schema(title = "项目名称", name = "name", defaultValue = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目名称不允许为空")
    @Length(min = 1, max = 255, message = "项目名称长度范围1-255个字符")
    private String name;

    /**
     * 截止开始时间
     */
    @Schema(title = "截止开始时间", name = "startTime", defaultValue = "2024-12-01 12:00:00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime startTime;

    /**
     * 截止结束时间
     */
    @Schema(title = "截止结束时间", name = "endTime", defaultValue = "2024-12-01 12:00:00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime endTime;

    /**
     * 测评类型: 1.普通测评; 2.团体测评;
     */
    @Schema(title = "测评类型", name = "type", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;

    /**
     * 是否允许重复测试: 0.否; 1.是;
     */
    @Schema(title = "是否允许重复测试", name = "allowRepeat", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean allowRepeat;

    /**
     * 是否允许获取报告: 0.不允许; 1.允许;
     */
    @Schema(title = "是否允许获取报告", name = "allowGetReport", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean allowGetReport;

    /**
     * 是否允许通知: 0.不允许; 1.允许;
     */
    @Schema(title = "是否允许通知", name = "allowNotify", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean allowNotify;

    /**
     * 订单是否免费: 0.否; 1.是;
     */
    @Schema(title = "订单是否免费", name = "orderFree", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean orderFree;

    /**
     * 是否允许限制作答时间: 0.否; 1.是;
     */
    @Schema(title = "是否允许限制作答时间", name = "allowTimeLimit", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean allowTimeLimit;
}
