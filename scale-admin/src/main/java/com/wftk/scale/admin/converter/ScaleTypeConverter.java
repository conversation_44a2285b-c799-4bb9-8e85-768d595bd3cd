package com.wftk.scale.admin.converter;

import com.wftk.scale.admin.vo.input.config.ScaleTypeCreateInput;
import com.wftk.scale.admin.vo.input.config.ScaleTypeModifyInput;
import com.wftk.scale.biz.entity.ScaleType;
import org.mapstruct.Mapper;

/**
 * @InterfaceName: ScaleTypeConverter
 * @Description: 量表分类信息数据转换器
 * @Author: mq
 * @Date: 2024-10-25 13:38
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleTypeConverter {

    /*
     * @Author: mq
     * @Description: 将创建量表分类实体转换为数据实体
     * @Date: 2024/10/25 13:41
     * @Param: scaleTypeCreateInput
     * @return: com.wftk.scale.biz.entity.ScaleType
     **/
    ScaleType scaleTypeCreateInputToEntity(ScaleTypeCreateInput scaleTypeCreateInput);

    /*
     * @Author: mq
     * @Description: 将修改量表分类实体转换为数据实体 
     * @Date: 2024/10/25 13:45
     * @Param: scaleTypeModifyInput  
     * @return: com.wftk.scale.biz.entity.ScaleType 
     **/
    ScaleType scaleTypeModifyInputToEntity(ScaleTypeModifyInput scaleTypeModifyInput);
}
