package com.wftk.scale.admin.converter;

import com.wftk.scale.admin.vo.input.scale.ScaleCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleModifyInput;
import com.wftk.scale.admin.vo.output.scale.ScaleOutput;
import com.wftk.scale.biz.entity.Scale;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @createDate 2024/10/24 16:55
 */
@Mapper(componentModel = "spring")
public interface ScaleConverter {

    /* 
     * @Author: mq
     * @Description: 将创建量表信息实体转换为数据传输实体
     * @Date: 2024/11/4 14:09 
     * @Param: scaleCreateInput-创建量表基本信息
     * @return: com.wftk.scale.biz.entity.Scale 
     **/
    Scale scaleCreateInputToEntity(ScaleCreateInput scaleCreateInput);

    /* 
     * @Author: mq
     * @Description: 将修改量表信息实体转换为数据传输实体
     * @Date: 2024/11/4 14:09 
     * @Param: scaleModifyInput  
     * @return: com.wftk.scale.biz.entity.Scale 
     **/
    Scale scaleModifyInputToEntity(ScaleModifyInput scaleModifyInput);

    ScaleOutput entityToOutput(Scale scale);
}
