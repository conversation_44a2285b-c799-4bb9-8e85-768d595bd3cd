package com.wftk.scale.admin.vo.input.scale;

import com.wftk.scale.biz.entity.ScaleCombination;
import com.wftk.scale.biz.entity.ScaleCombinationDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.util.List;

/**
 * @ClassName: ScaleCombinationCreateInput
 * @Description: 创建组合量表信息
 * @Author: mq
 * @Date: 2024-11-06 17:00
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleCombinationCreateInput {

    /**
     * 组合名称
     */
    @Schema(title = "组合量表名称", name = "name", defaultValue = "组合量表-10", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "组合量表名称不能为空")
    @Length(min = 1, max = 100, message = "组合量表名称长度范围1-255个字符")
    private String name;

    /**
     * 测评方式，1依次测评，2选择测评
     */
    @Schema(title = "测评方式", name = "type", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "测评方式不能为空")
    private Integer type;

}
