package com.wftk.scale.admin.controller.scale;

import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.vo.input.scale.ScaleReportConfSettingCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleReportConfSettingModifyInput;
import com.wftk.scale.biz.dto.scale.ScaleReportConfSettingDetailDTO;
import com.wftk.scale.biz.service.ScaleReportConfSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @ClassName: ScaleReportConfSettingController
 * @Description: 量表报告设置
 * @Author: mq
 * @Date: 2024-11-06 13:36
 * @Version: 1.0
 **/
@Tag(name = "量表报告设置相关API")
@RestController
@AdminMapping("/scale/report/setting")
@Slf4j
public class ScaleReportConfSettingController {


    @Autowired
    private ScaleReportConfSettingService scaleReportConfSettingService;


    @Operation(summary = "创建报告设置信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "创建报告设置信息")
    @PostMapping
    public ApiResult<Void> create(@Valid @RequestBody ScaleReportConfSettingCreateInput input) {
        Long reportConfId = input.getReportConfId();
        scaleReportConfSettingService.create(reportConfId,input.getReadingInstruction(),
                input.getRemarkSettings(),input.getFormSettings(),input.getChartSettings());
        return ApiResult.ok();
    }

    @Operation(summary = "修改报告设置信息")
    @OptLog(module = "量表管理模块", optType = OptType.MODIFY, description = "修改报告设置信息")
    @PutMapping
    public ApiResult<Void> modify(@Valid @RequestBody ScaleReportConfSettingModifyInput input) {
        Long reportConfId = input.getReportConfId();
        // 先删除数据再新增
        scaleReportConfSettingService.delByReportConfId(reportConfId);
        scaleReportConfSettingService.create(reportConfId,input.getReadingInstruction(),
                input.getRemarkSettings(),input.getFormSettings(),input.getChartSettings());

        return ApiResult.ok();
    }

    @Operation(summary = "根据量表ID获取报告设置详情信息")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据量表ID获取报告设置详情信息")
    @GetMapping("detail")
    public ApiResult<ScaleReportConfSettingDetailDTO> detail(@RequestParam("reportConfId") Long reportConfId) {
        ScaleReportConfSettingDetailDTO settingDetailDTO = scaleReportConfSettingService.getDetailByReportConfId(reportConfId);
        return ApiResult.ok(settingDetailDTO);
    }
}
