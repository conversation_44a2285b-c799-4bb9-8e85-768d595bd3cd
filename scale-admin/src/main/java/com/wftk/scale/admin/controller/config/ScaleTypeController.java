package com.wftk.scale.admin.controller.config;

import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleTypeConverter;
import com.wftk.scale.admin.vo.input.config.ScaleTypeCreateInput;
import com.wftk.scale.admin.vo.input.config.ScaleTypeModifyInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.dto.scale.ScaleTypeDetailDTO;
import com.wftk.scale.biz.entity.Scale;
import com.wftk.scale.biz.entity.ScaleFactorFormula;
import com.wftk.scale.biz.entity.ScaleType;
import com.wftk.scale.biz.service.ScaleTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName: ScaleTypeController
 * @Description: 量表分类信息
 * @Author: mq
 * @Date: 2024-10-24 15:57
 * @Version: 1.0
 **/
@Tag(name = "量表类型参数配置API")
@RestController
@AdminMapping("conf/scale/type")
@Slf4j
public class ScaleTypeController {

    @Autowired
    private ScaleTypeService scaleTypeService;

    @Autowired
    private ScaleTypeConverter scaleTypeConverter;

    @Operation(summary = "获取量表分类信息列表数据-全部")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "获取量表分类信息列表数据-全部")
    @GetMapping("datas")
    public ApiResult<List<ScaleTypeDetailDTO>> getAll(@RequestParam(required = false) String scaleTypeName) {
        return ApiResult.ok(scaleTypeService.getListOfEnabled(scaleTypeName));
    }

    @Operation(summary = "获取量表分类信息列表数据-分页")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "获取量表分类信息列表数据-分页")
    @GetMapping
    public ApiResult<Page<ScaleTypeDetailDTO>> selectPage(@RequestParam(required = false) String scaleTypeName) {
        return ApiResult.ok(scaleTypeService.selectPage(scaleTypeName));
    }

    @Operation(summary = "创建量表分类信息")
    @OptLog(module = "参数配置模块", optType = OptType.CREATE, description = "创建量表分类信息")
    @PostMapping
    public ApiResult<String> create(@Valid @RequestBody ScaleTypeCreateInput input) {
        String scaleTypeCode = input.getCode();
        boolean result = scaleTypeService.validScaleTypeCode(null, scaleTypeCode);
        if (result) {
            throw new BusinessException("创建失败,量表分类编号已存在!");
        }
        ScaleType scaleType = scaleTypeConverter.scaleTypeCreateInputToEntity(input);
        scaleTypeService.create(scaleType);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "修改量表分类信息")
    @OptLog(module = "参数配置模块", optType = OptType.MODIFY, description = "修改量表分类信息")
    @PutMapping
    public ApiResult<String> modify(@Valid @RequestBody ScaleTypeModifyInput input) {

        Long id = input.getId();
        String scaleTypeCode = input.getCode();
        boolean result = scaleTypeService.validScaleTypeCode(id, scaleTypeCode);
        if (result) {
            throw new BusinessException("修改失败,量表分类编号已存在!");
        }
        ScaleType scaleType = scaleTypeConverter.scaleTypeModifyInputToEntity(input);
        scaleTypeService.modify(scaleType);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "删除量表分类信息")
    @OptLog(module = "参数配置模块", optType = OptType.DELETE, description = "删除量表分类信息")
    @DeleteMapping
    public ApiResult<String> delete(Long scaleTypeId) {

        if (ObjUtil.isNull(scaleTypeId)) {
            throw new BusinessException("删除失败,量表分类scaleTypeId参数不允许为空!");
        }
        scaleTypeService.delete(scaleTypeId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "根据ID获取量表分类详情信息")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "根据ID获取量表分类详情信息")
    @GetMapping("detail")
    public ApiResult<ScaleType> detail(Long scaleTypeId) {

        if (ObjUtil.isNull(scaleTypeId)) {
            throw new BusinessException("获取详情失败,量表分类scaleTypeId参数不允许为空!");
        }
        ScaleType scaleType = scaleTypeService.getById(scaleTypeId);
        return ApiResult.ok(scaleType);
    }
}
