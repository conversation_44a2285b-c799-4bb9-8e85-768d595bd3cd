package com.wftk.scale.admin.vo.input.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @ClassName: ScaleTypeCreateInput
 * @Description: 创建量表分类信息实体参数
 * @Author: mq
 * @Date: 2024-10-24 18:15
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleTypeCreateInput implements Serializable {

    /**
     * 分类名称
     */
    @Schema(title = "量表分类名称", name = "name", defaultValue = "我是测试量表分类A", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表分类名称不能为空")
    @Length(min = 1, max = 50, message = "量表分类名称长度范围1-50个字符")
    private String name;

    /**
     * 分类编码
     */
    @Schema(title = "量表分类编码", name = "code", defaultValue = "NO2024", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表分类编码不能为空")
    @Length(min = 1, max = 50, message = "量表分类编码长度范围1-50个字符")
    private String code;

    /**
     * 分类描述
     */
    @Schema(title = "量表分类描述", name = "description", defaultValue = "我是测试量表分类A的描述信息",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 255, message = "量表分类描述长度范围1-255个字符")
    private String description;

    /**
     * 排序
     */
    @Schema(title = "量表分类排序", name = "sort", defaultValue = "10", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Max(value = Integer.MAX_VALUE,message = "不能超过Integer.MAX_VALUE最大数值")
    private Integer sort;

    /**
     * 租户ID
     */
    @Schema(title = "租户ID", name = "tenantId", defaultValue = "system", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 50, message = "租户ID长度范围1-50个字符")
    private String tenantId = "system";
}
