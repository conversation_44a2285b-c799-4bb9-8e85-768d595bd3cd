package com.wftk.scale.admin.vo.input.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @ClassName: ScaleFactorFormulaCreateInput
 * @Description: 创建因子公式信息实体参数
 * @Author: mq
 * @Date: 2024-10-29 14:28
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleFactorFormulaCreateInput implements Serializable {

    /**
     * 公式名称
     */
    @Schema(title = "公式名称", name = "name", defaultValue = "总分乘数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "公式名称不能为空")
    @Length(min = 1, max = 255, message = "公式名称长度范围1-255个字符")
    private String name;

    /**
     * 公式编码
     */
    @Schema(title = "公式编码", name = "code", defaultValue = "sum", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "公式编码不能为空")
    @Length(min = 1, max = 255, message = "公式编码长度范围1-255个字符")
    private String code;

    /**
     * 公式算法
     */
    @Schema(title = "公式算法", name = "formula", defaultValue = "factors_total*10-factors_avg", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "公式算法不能为空")
    private String formula;

    /**
     * 公式显示的标签
     */
    @Schema(title = "公式显示的标签", name = "formulaLabel", defaultValue = "因子总分*10-平均数", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String formulaLabel;

    /**
     * 状态(0.未上下架、 1.已上下架)
     */
    @Schema(title = "状态", name = "status", defaultValue = "0", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer status;

    /**
     * 租户ID
     */
    @Schema(title = "租户ID", name = "tenantId", defaultValue = "system", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 50, message = "租户ID长度范围1-50个字符")
    private String tenantId = "system";
}
