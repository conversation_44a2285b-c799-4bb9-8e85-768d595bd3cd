package com.wftk.scale.admin.controller.user;

import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.dto.user.sysuser.SysUserCreateDTO;
import com.wftk.scale.biz.dto.user.sysuser.SysUserDeleteDTO;
import com.wftk.scale.biz.dto.user.sysuser.SysUserQueryDTO;
import com.wftk.scale.biz.dto.user.sysuser.SysUserUpdateDTO;
import com.wftk.scale.biz.service.SysUserService;
import com.wftk.scale.biz.vo.SysUserDetailVO;
import com.wftk.scale.biz.vo.SysUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@Tag(name = "系统用户相关API")
@RestController
@AdminMapping("/user")
public class SysUserController {

    @Resource
    private SysUserService sysUserService;

//    @Operation(summary = "获取当前登录用户信息")
//    @OptLog(module = "登录认证", optType = OptType.QUERY, description = "获取当前登录用户信息")
//    @GetMapping("/currentUser")
//    public ApiResult<Object> currentUser() {
//        Object user = AuthenticationHolder.getAuthentication().getAuthUser().getUser();
//        SysUserVO sysUserVO = new SysUserVO();
//        BeanUtils.copyProperties(user, sysUserVO);
//        return ApiResult.ok(sysUserVO);
//    }

    @Operation(summary = "分页查询")
    @OptLog(module = "系统用户", optType = OptType.QUERY, description = "分页查询")
    @GetMapping("/sys/getList")
    public ApiResult<Page<SysUserVO>> getList(SysUserQueryDTO dto){
        return ApiResult.ok(sysUserService.getList(dto));
    }

    @Operation(summary = "用户详情")
    @OptLog(module = "系统用户", optType = OptType.QUERY, description = "分页查询")
    @GetMapping("/sys/detail")
    public ApiResult<SysUserDetailVO> detail(Long id){
        return ApiResult.ok(sysUserService.detailById(id));
    }

    @Operation(summary = "新增")
    @OptLog(module = "系统用户", optType = OptType.CREATE, description = "新增")
    @PostMapping("/sys/create")
    public ApiResult<Void> create(@RequestBody @Valid SysUserCreateDTO dto){
        sysUserService.create(dto);
        return ApiResult.ok();
    }

    @Operation(summary = "修改")
    @OptLog(module = "系统用户", optType = OptType.CREATE, description = "新增")
    @PutMapping("/sys/modify")
    public ApiResult<Void> modify(@RequestBody @Valid SysUserUpdateDTO dto){
        sysUserService.update(dto);
        return ApiResult.ok();
    }

    @Operation(summary = "删除")
    @OptLog(module = "系统用户", optType = OptType.CREATE, description = "新增")
    @DeleteMapping("/sys/delete")
    public ApiResult<Void> delete(@RequestBody @Valid SysUserDeleteDTO dto){
        sysUserService.delete(dto.getId());
        return ApiResult.ok();
    }

    @Operation(summary = "导出")
    @OptLog(module = "系统用户", optType = OptType.QUERY, description = "导出")
    @GetMapping("/sys/export")
    public void export(SysUserQueryDTO dto, HttpServletResponse response){
        sysUserService.export(dto, response);
    }

    @Operation(summary = "下载模板")
    @OptLog(module = "系统用户", optType = OptType.QUERY, description = "下载模板")
    @GetMapping("/sys/downTemplate")
    public void downTemplate(HttpServletResponse response){
        sysUserService.downTemplate(response);
    }

    @Operation(summary = "导入")
    @OptLog(module = "系统用户", optType = OptType.CREATE, description = "导入")
    @PostMapping("/sys/importExcel")
    public ApiResult<Void> importExcel(@RequestPart MultipartFile file){
        sysUserService.importExcelData(file);
        return ApiResult.ok();
    }
}
