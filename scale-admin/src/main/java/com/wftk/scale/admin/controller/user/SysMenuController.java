package com.wftk.scale.admin.controller.user;

import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.dto.user.menu.SysCreateMenuDTO;
import com.wftk.scale.biz.dto.user.menu.SysDeleteMenuDTO;
import com.wftk.scale.biz.dto.user.menu.SysUpdateMenuDTO;
import com.wftk.scale.biz.service.SysMenuService;
import com.wftk.scale.biz.vo.SysMenuVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "菜单管理")
@RestController
@AdminMapping("/menu")
public class SysMenuController {

    @Resource
    private SysMenuService sysMenuService;

    @Operation(summary = "查询菜单列表")
    @OptLog(module = "菜单管理", optType = OptType.QUERY, description = "查询菜单列表")
    @GetMapping("getList")
    public ApiResult<List<SysMenuVO>> getList(){
        return ApiResult.ok(sysMenuService.getList());
    }

    @Operation(summary = "查询编辑上级可选菜单")
    @OptLog(module = "菜单管理", optType = OptType.QUERY, description = "查询菜单列表")
    @GetMapping("getEditSelectParentList")
    public ApiResult<List<SysMenuVO>> getEditSelectParentList(Long id){
        return ApiResult.ok(sysMenuService.getEditSelectParentList(id));
    }

    @Operation(summary = "查询当前登录用户拥有的菜单权限")
    @OptLog(module = "菜单管理", optType = OptType.QUERY, description = "查询当前登录用户拥有的菜单权限")
    @GetMapping("getOwnerMenus")
    public ApiResult<List<SysMenuVO>> getOwnerMenus(){
        return ApiResult.ok(sysMenuService.getOwnerMenus());
    }

    @Operation(summary = "删除菜单")
    @OptLog(module = "菜单管理", optType = OptType.DELETE, description = "删除菜单")
    @DeleteMapping
    public ApiResult<SysMenuVO> deleteMenu(@RequestBody @Valid SysDeleteMenuDTO dto){
        sysMenuService.deleteMenu(dto.getId());
        return ApiResult.ok();
    }

    @Operation(summary = "新增菜单")
    @OptLog(module = "菜单管理", optType = OptType.CREATE, description = "新增菜单")
    @PostMapping
    public ApiResult<SysMenuVO> createMenu(@RequestBody @Valid SysCreateMenuDTO dto){
        sysMenuService.createMenu(dto);
        return ApiResult.ok();
    }

    @Operation(summary = "修改菜单")
    @OptLog(module = "菜单管理", optType = OptType.MODIFY, description = "修改菜单")
    @PutMapping
    public ApiResult<SysMenuVO> updateMenu(@RequestBody @Valid SysUpdateMenuDTO dto){
        sysMenuService.updateMenu(dto);
        return ApiResult.ok();
    }
}