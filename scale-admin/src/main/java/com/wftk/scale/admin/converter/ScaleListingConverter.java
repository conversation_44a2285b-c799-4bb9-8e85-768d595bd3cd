package com.wftk.scale.admin.converter;

import com.wftk.scale.admin.vo.input.listing.ScaleListedQueryInput;
import com.wftk.scale.admin.vo.input.listing.ScaleListingCreateInput;
import com.wftk.scale.admin.vo.input.listing.ScaleListingQueryInput;
import com.wftk.scale.biz.dto.listing.ScaleCombinationListingParamDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedParamDTO;
import com.wftk.scale.biz.dto.listing.ScaleListingParamDTO;
import com.wftk.scale.biz.entity.ScaleListing;
import org.mapstruct.Mapper;

/**
 * @InterfaceName: ScaleListingConverter
 * @Description: 量表上架数据转换器
 * @Author: mq
 * @Date: 2024-10-29 10:30
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleListingConverter {

    /*
     * @Author: mq
     * @Description: 将量表上架信息实体转换为数据实体
     * @Date: 2024/10/29 10:40
     * @Param: scaleListingCreateInput
     * @return: com.wftk.scale.biz.entity.ScaleListing
     **/
    ScaleListing scaleListingCreateInputToEntity(ScaleListingCreateInput scaleListingCreateInput);

    /*
     * @Author: mq
     * @Description: 将可上架量表查询信息实体转换为数据实体
     * @Date: 2024/10/30 13:46 
     * @Param: scaleListingQueryInput-筛选参数
     * @return: com.wftk.scale.biz.dto.listing.ScaleListingParamDTO
     **/
    ScaleListingParamDTO scaleListingQueryInputToParamDTO(ScaleListingQueryInput scaleListingQueryInput);

    /* 
     * @Author: mq
     * @Description: 将已上架量表查询信息实体转换为数据实体
     * @Date: 2024/10/30 13:46 
     * @Param: scaleListedQueryInput  
     * @return: com.wftk.scale.biz.dto.listing.ScaleListedParamDTO 
     **/
    ScaleListedParamDTO scaleListedQueryInputToParamDTO(ScaleListedQueryInput scaleListedQueryInput);

    /*
     * @Author: mq
     * @Description: 将可上架量表查询信息实体转换为数据实体
     * @Date: 2024/10/30 13:46
     * @Param: scaleListingQueryInput-筛选参数
     * @return: com.wftk.scale.biz.dto.listing.ScaleListingParamDTO
     **/
    ScaleCombinationListingParamDTO scaleCombinationListingQueryInputToParamDTO(ScaleListingQueryInput scaleListingQueryInput);
}
