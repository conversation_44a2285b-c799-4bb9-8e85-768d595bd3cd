package com.wftk.scale.admin.config;

import com.wftk.scale.admin.ext.auth.*;
import com.wftk.scale.admin.ext.sign.TenantClientCredentialLoader;
import com.wftk.scale.biz.service.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2024/11/22 16:14
 */
@Configuration
public class AuthConfig {

    @Bean
    SystemClientLoader systemClientLoader(TenantSettingService tenantSettingService,
            TenantClientInfoService tenantClientInfoService) {
        return new SystemClientLoader(tenantSettingService, tenantClientInfoService);
    }

    @Bean
    AdminUserLoader adminUserLoader(SysUserService sysUserService) {
        return new AdminUserLoader(sysUserService);
    }

    @Bean
    AdminPreAuthAccountValidator adminPreAuthAccountValidator(AdminUserLoader adminUserLoader) {
        return new AdminPreAuthAccountValidator(adminUserLoader);
    }

    @Bean
    InvalidPreAuthAccountExceptionTranslator invalidPreAuthAccountExceptionTranslator() {
        return new InvalidPreAuthAccountExceptionTranslator();
    }

    @Bean
    TenantClientCredentialLoader tenantClientCredentialLoader(TenantSettingService tenantSettingService,
            TenantClientInfoService tenantClientInfoService) {
        return new TenantClientCredentialLoader(tenantSettingService, tenantClientInfoService);
    }
}
