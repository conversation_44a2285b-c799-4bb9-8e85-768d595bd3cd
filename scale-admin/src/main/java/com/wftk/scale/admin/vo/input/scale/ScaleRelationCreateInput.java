package com.wftk.scale.admin.vo.input.scale;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: ScaleRelationCreateInput
 * @Description: 创建量表跳转逻辑信息
 * @Author: mq
 * @Date: 2024-11-01 16:34
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleRelationCreateInput implements Serializable {

    /**
     * 量表ID
     */
    @Schema(title = "量表ID", name = "scaleId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long scaleId;

    /**
     * 跳转题目ID
     */
    @Schema(title = "跳转题目ID", name = "questionId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long questionId;

    /**
     * 跳题逻辑: 1.单条件触发跳转; 2.多条件同时满足触发跳转; 3.多条件之一满足触发跳转;
     */
    @Schema(title = "跳题逻辑", name = "type", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;

    /**
     * 关系策略
     */
    @Schema(title = "关系策略[{questionId:1, lable:C, questionNum:'1', subNumber:''},{questionId:2, lable:D, questionNum:'2', subNumber:''}]", name = "strategy", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关系策略不能为空")
    private String strategy;

    /**
     * 提示
     */
    @Schema(title = "提示", name = "reminder", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String reminder;

    /**
     * 租户ID
     */
    @Schema(title = "租户ID", name = "tenantId", defaultValue = "system", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 50, message = "租户ID长度范围1-50个字符")
    private String tenantId = "system";
}
