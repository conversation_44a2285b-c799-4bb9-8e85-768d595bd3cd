package com.wftk.scale.admin.controller.scale;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleFactorConverter;
import com.wftk.scale.admin.vo.input.scale.ScaleFactorCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleFactorModifyInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.constant.enums.ScaleFactorTypeEnum;
import com.wftk.scale.biz.dto.scale.ScaleFactorDTO;
import com.wftk.scale.biz.dto.scale.ScaleQuestionHighestOverallScoreDTO;
import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: ScaleFactorController
 * @Description: 量表因子维度信息管理
 * @Author: mq
 * @Date: 2024-11-01 13:45
 * @Version: 1.0
 **/
@Tag(name = "量表因子维度相关API")
@RestController
@AdminMapping("/scale/factor")
@Slf4j
public class ScaleFactorController {

    @Autowired
    private ScaleFactorService scaleFactorService;

    @Autowired
    private ScaleFactorConverter scaleFactorConverter;

    @Autowired
    private ScaleResultIntroService scaleResultIntroService;

    @Autowired
    private ScaleWarnConfService scaleWarnConfService;

    @Autowired
    private ScaleService scaleService;

    @Autowired
    private ScaleQuestionOptionService scaleQuestionOptionService;

    @Operation(summary = "获取量表因子维度列表数据-全部")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取量表因子维度列表数据-全部")
    @GetMapping("datas")
    public ApiResult<List<ScaleFactorDTO>> getAll(Long scaleId) {
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("获取失败,量表scaleId不允许为空!");
        }
        List<ScaleFactorDTO> dtoList = scaleFactorConverter.entityToDTO(scaleFactorService.findByScaleId(scaleId));
        dtoList.forEach(sfd -> {
            List<Long> questionIds = Arrays.stream(sfd.getQuestionId().split("\\|"))
                    .filter(s -> !s.trim().isEmpty())
                    .map(Long::parseLong)
                    .distinct()
                    .collect(Collectors.toList());
            ScaleQuestionHighestOverallScoreDTO scoreDTO = scaleQuestionOptionService.findOverallScoreByScaleId(scaleId,questionIds);
            sfd.setTotalScore(scoreDTO.getTotalScore());
        });
        return ApiResult.ok(dtoList);
    }

    @Operation(summary = "获取量表因子维度信息列表数据-分页")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取量表因子维度信息列表数据-分页")
    @GetMapping
    public ApiResult<Page<ScaleFactorDTO>> selectPage(@RequestParam(required = true) Long scaleId, @RequestParam(required = false) String factorName) {
        return ApiResult.ok(scaleFactorService.selectPage(scaleId, factorName));
    }

    @Operation(summary = "创建量表因子维度信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "创建量表因子维度信息")
    @PostMapping
    public ApiResult<String> create(@Valid @RequestBody ScaleFactorCreateInput input) {
        ScaleFactor scaleFactor = scaleFactorConverter.scaleFactorCreateInputToEntity(input);
        boolean result = scaleService.vaildCompletedStatus(input.getScaleId());
        if (result) {
            throw new BusinessException("创建失败,已完成的量表无法进行编辑操作!");
        }
        result = scaleFactorService.vaildFactorName(null, input.getScaleId(), input.getName());
        if (result) {
            throw new BusinessException("创建失败,因子维度名称已存在!");
        }

        if(Objects.equals(ScaleFactorTypeEnum.TOTAL.getType(), input.getType())){
            result = scaleFactorService.checkFactorType(input.getScaleId(), null,ScaleFactorTypeEnum.TOTAL.getType());
            if (result ) {
                throw new BusinessException("创建失败,已存在总分因子!");
            }
        }

        scaleFactorService.create(scaleFactor);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "修改量表因子维度信息")
    @OptLog(module = "量表管理模块", optType = OptType.MODIFY, description = "修改量表因子维度信息")
    @PutMapping
    public ApiResult<String> modify(@Valid @RequestBody ScaleFactorModifyInput input) {
        ScaleFactor scaleFactor = scaleFactorConverter.scaleFactorModifyInputToEntity(input);
        boolean result = scaleService.vaildCompletedStatus(input.getScaleId());
        if (result) {
            throw new BusinessException("修改失败,已完成的量表无法进行编辑操作!");
        }
        result = scaleFactorService.vaildFactorName(input.getId(), input.getScaleId(), input.getName());
        if (result) {
            throw new BusinessException("修改失败,因子维度名称已存在!");
        }

        if(Objects.equals(ScaleFactorTypeEnum.TOTAL.getType(), input.getType())){
            result = scaleFactorService.checkFactorType(input.getScaleId(), input.getId(),ScaleFactorTypeEnum.TOTAL.getType());
            if (result ) {
                throw new BusinessException("创建失败,已存在总分因子!");
            }
        }
        scaleFactorService.modify(scaleFactor);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "删除量因子维度目信息")
    @OptLog(module = "参数配置模块", optType = OptType.DELETE, description = "删除量表因子维度信息")
    @DeleteMapping
    public ApiResult<String> delete(Long scaleId, Long factorId) {
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("删除失败,量表scaleId参数不允许为空!");
        }
        if (ObjUtil.isNull(factorId)) {
            throw new BusinessException("删除失败,因子维度factorId参数不允许为空!");
        }
        boolean result = scaleService.vaildCompletedStatus(scaleId);
        if (result) {
            throw new BusinessException("删除失败,已完成的量表无法进行编辑操作!");
        }
        String intro = scaleResultIntroService.checkFactorRelationIntro(factorId);
        if (StrUtil.isNotEmpty(intro)) {
            throw new BusinessException("删除失败,因子维度存在关联的结果解读数据!");
        }
        String tag = scaleWarnConfService.checkFactorRelationWarnConf(factorId);
        if (StrUtil.isNotEmpty(tag)) {
            throw new BusinessException("删除失败,因子维度存在关联的预警阈值数据!");
        }
        scaleFactorService.delete(factorId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "根据ID获取因子维度详情信息")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据ID获取因子维度详情信息")
    @GetMapping("detail")
    public ApiResult<ScaleFactor> detail(Long factorId) {
        if (ObjUtil.isNull(factorId)) {
            throw new BusinessException("获取失败,因子维度factorId不允许为空!");
        }
        return ApiResult.ok(scaleFactorService.getById(factorId));
    }

    @Operation(summary = "根据ID获取因子维度详情信息")
    @GetMapping("getById")
    public ApiResult<ScaleFactorDTO> getById(@RequestParam("id") Long id) {
        ScaleFactor scaleFactor = scaleFactorService.getById(id);
        return ApiResult.ok(scaleFactorConverter.entityToDTO(scaleFactor));
    }
}
