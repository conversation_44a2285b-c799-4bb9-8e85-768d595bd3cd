package com.wftk.scale.admin.vo.input.scale;


import jakarta.validation.Valid;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: ScaleQuestionInput
 * @Description:
 * @Author: mq
 * @Date: 2024/12/14
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleQuestionBaseCreateInput {

    /**
     * 题目信息
     */
    @Valid
    private ScaleQuestionCreateInput question;

    /**
     * 问题选项
     */
    @Valid
    private List<ScaleQuestionOptionCreateInput> options;
}
