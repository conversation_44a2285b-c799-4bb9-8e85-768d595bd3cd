package com.wftk.scale.admin.controller.user;

import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.dto.user.sysdepartment.SysDepartmentCreateDTO;
import com.wftk.scale.biz.dto.user.sysdepartment.SysDepartmentDeleteDTO;
import com.wftk.scale.biz.dto.user.sysdepartment.SysDepartmentQueryDTO;
import com.wftk.scale.biz.dto.user.sysdepartment.SysDepartmentUpdateDTO;
import com.wftk.scale.biz.service.SysDepartmentService;
import com.wftk.scale.biz.vo.SysDepartmentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "系统部门相关API")
@RestController
@AdminMapping("/sysDepartment")
public class SysDepartmentController {

    @Resource
    private SysDepartmentService sysDepartmentService;

    @Operation(summary = "创建机构")
    @OptLog(module = "系统部门管理", optType = OptType.CREATE, description = "创建机构")
    @PostMapping
    public ApiResult<Void> create(@RequestBody @Valid SysDepartmentCreateDTO dto){
        sysDepartmentService.createSysDepartment(dto);
        return ApiResult.ok();
    }

    @Operation(summary = "编辑机构")
    @OptLog(module = "系统部门管理", optType = OptType.MODIFY, description = "编辑机构")
    @PutMapping
    public ApiResult<Void> update(@RequestBody @Valid SysDepartmentUpdateDTO dto){
        sysDepartmentService.updateSysDepartment(dto);
        return ApiResult.ok();
    }

    @Operation(summary = "删除机构")
    @OptLog(module = "系统部门管理", optType = OptType.DELETE, description = "删除机构")
    @DeleteMapping
    public ApiResult<Void> delete(@RequestBody @Valid SysDepartmentDeleteDTO dto){
        sysDepartmentService.deleteById(dto.getId());
        return ApiResult.ok();
    }

    @Operation(summary = "查询机构")
    @OptLog(module = "系统部门管理", optType = OptType.QUERY, description = "查询机构")
    @GetMapping
    public ApiResult<List<SysDepartmentVO>> listPage(SysDepartmentQueryDTO dto){
        return ApiResult.ok(sysDepartmentService.selectSysDepartmentList(dto));
    }
}
