package com.wftk.scale.admin.controller.user;

import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.dto.user.position.PositionAddDTO;
import com.wftk.scale.biz.dto.user.position.PositionDeleteDTO;
import com.wftk.scale.biz.dto.user.position.PositionQueryDTO;
import com.wftk.scale.biz.dto.user.position.PositionUpdateDTO;
import com.wftk.scale.biz.service.PositionService;
import com.wftk.scale.biz.vo.PositionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Tag(name = "岗位管理")
@RestController
@AdminMapping("/position")
public class PositionController {

    @Resource
    private PositionService positionService;

    @Operation(summary = "分页查询岗位信息")
    @OptLog(module = "岗位管理", optType = OptType.QUERY, description = "分页查询岗位信息")
    @GetMapping("getList")
    public ApiResult<Page<PositionVO>> getList(PositionQueryDTO dto){
        return ApiResult.ok(positionService.getList(dto));
    }

    @Operation(summary = "详情")
    @OptLog(module = "岗位管理", optType = OptType.QUERY, description = "详情")
    @GetMapping("detail")
    public ApiResult<PositionVO> detail(Long id){
        return ApiResult.ok(positionService.detailById(id));
    }

    @Operation(summary = "新增岗位")
    @OptLog(module = "岗位管理", optType = OptType.CREATE, description = "新增岗位")
    @PostMapping
    public ApiResult<Void> add(@RequestBody @Valid PositionAddDTO dto){
        positionService.add(dto);
        return ApiResult.ok();
    }

    @Operation(summary = "编辑岗位")
    @OptLog(module = "岗位管理", optType = OptType.MODIFY, description = "编辑岗位")
    @PutMapping
    public ApiResult<Void> modify(@RequestBody @Valid PositionUpdateDTO dto){
        positionService.update(dto);
        return ApiResult.ok();
    }

    @Operation(summary = "删除岗位")
    @OptLog(module = "岗位管理", optType = OptType.DELETE, description = "删除岗位")
    @DeleteMapping()
    public ApiResult<Void> delete(@RequestBody @Valid PositionDeleteDTO dto){
        positionService.delete(dto.getId());
        return ApiResult.ok();
    }

    @Operation(summary = "导出")
    @OptLog(module = "岗位管理", optType = OptType.QUERY, description = "导出")
    @GetMapping("export")
    public void export(PositionQueryDTO dto, HttpServletResponse response){
        positionService.export(dto, response);
    }
}
