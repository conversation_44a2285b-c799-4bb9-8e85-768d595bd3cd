package com.wftk.scale.admin.ext.sign;

import com.wftk.scale.biz.entity.TenantClientInfo;
import com.wftk.scale.biz.entity.TenantSetting;
import com.wftk.scale.biz.service.TenantClientInfoService;
import com.wftk.scale.biz.service.TenantSettingService;
import com.wftk.signature.builder.ClientInfo;
import com.wftk.signature.loader.ClientCredentialLoader;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2024/11/30 11:29
 */
@Slf4j
public class TenantClientCredentialLoader implements ClientCredentialLoader {
    private final TenantSettingService tenantSettingService;
    private final TenantClientInfoService tenantClientInfoService;

    public TenantClientCredentialLoader(TenantSettingService tenantSettingService, TenantClientInfoService tenantClientInfoService) {
        this.tenantSettingService = tenantSettingService;
        this.tenantClientInfoService = tenantClientInfoService;
    }

    @Override
    public ClientInfo get(String clientId) {
        TenantClientInfo tenantClientInfo = tenantClientInfoService.findOneByClientId(clientId, true);
        if (tenantClientInfo == null) {
            log.warn("invalid clientId: {}", clientId);
            return null;
        }
        TenantSetting clientSetting = tenantSettingService.getTenantId(tenantClientInfo.getTenantId());
        if (clientSetting == null) {
            log.warn("invalid tenantId: {}", tenantClientInfo.getTenantId());
            return null;
        }
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setClientId(tenantClientInfo.getTenantId());
        clientInfo.setClientSecret(tenantClientInfo.getClientSecret());
//        clientInfo.setTenantId(tenantClientInfo.getTenantId());
        return clientInfo;
    }

    @Override
    public int getOrder() {
        return -100;
    }
}
