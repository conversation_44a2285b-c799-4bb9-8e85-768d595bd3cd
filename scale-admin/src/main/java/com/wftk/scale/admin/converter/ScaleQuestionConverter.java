package com.wftk.scale.admin.converter;


import com.wftk.scale.admin.vo.input.scale.ScaleQuestionCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleQuestionModifyInput;
import com.wftk.scale.admin.vo.input.scale.ScaleQuestionOptionCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleQuestionOptionModifyInput;
import com.wftk.scale.biz.entity.ScaleQuestion;
import com.wftk.scale.biz.entity.ScaleQuestionOption;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @InterfaceName: ScaleQuestionConverter
 * @Description:
 * @Author: mq
 * @Date: 2024/12/14
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleQuestionConverter {

    /*
    * @Author: mq
    * @Description: 将创建题目信息实体转换为数据实体
    * @Date: 2024/12/14 14:39
    * @Param: input
    * @return: ScaleQuestion
    **/
    ScaleQuestion scaleQuestionCreateInputToEntity(ScaleQuestionCreateInput input);

    /*
     * @Author: mq
     * @Description: 将修改题目信息实体转换为数据实体
     * @Date: 2024/12/14 14:39
     * @Param: input
     * @return: ScaleQuestion
     **/
    ScaleQuestion scaleQuestionModifyInputToEntity(ScaleQuestionModifyInput input);

    /*
    * @Author: mq
    * @Description: 将创建题目选项信息实体转换为数据实体
    * @Date: 2024/12/14 14:40
    * @Param: input
    * @return: ScaleQuestionOption
    **/
    ScaleQuestionOption scaleQuestionOptionCreateInputToEntity(ScaleQuestionOptionCreateInput input);

    /*
    * @Author: mq
    * @Description: 将修改题目选项信息实体转换为数据实体
    * @Date: 2024/12/14 14:40
    * @Param: input
    * @return: ScaleQuestionOption
    **/
    ScaleQuestionOption scaleQuestionOptionModifyInputToEntity(ScaleQuestionOptionModifyInput input);

    /*
    * @Author: mq
    * @Description: 将创建题目选项信息实体转换为数据实体
    * @Date: 2024/12/14 14:42
    * @Param: list
    * @return: List<ScaleQuestionOption>
    **/
    List<ScaleQuestionOption> scaleQuestionOptionCreateInputToList(List<ScaleQuestionOptionCreateInput> list);

    /*
    * @Author: mq
    * @Description: 将修改题目选项信息实体转换为数据实体
    * @Date: 2024/12/14 14:42
    * @Param: list
    * @return: List<ScaleQuestionOption>
    **/
    List<ScaleQuestionOption> scaleQuestionOptionModifyInputToList(List<ScaleQuestionOptionModifyInput> list);
}
