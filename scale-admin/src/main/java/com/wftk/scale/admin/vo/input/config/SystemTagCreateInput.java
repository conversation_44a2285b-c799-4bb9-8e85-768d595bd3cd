package com.wftk.scale.admin.vo.input.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @ClassName: SystemTagCreateInput
 * @Description: 创建预警标签信息
 * @Author: mq
 * @Date: 2024-10-25 14:40
 * @Version: 1.0
 **/
@Data
@Builder
public class SystemTagCreateInput implements Serializable {

    /**
     * 标签名称
     */
    @Schema(title = "预警标签名称", name = "tag", defaultValue = "我是预警标签名称-严重", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预警标签名称不能为空")
    @Length(min = 1, max = 50, message = "预警标签名称长度范围1-50个字符")
    private String tag;

    /**
     * 预警标签编码
     */
    @Schema(title = "预警编码", name = "code", defaultValue = "我是预警标签编号-warn", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预警标签编码不能为空")
    @Length(min = 1, max = 50, message = "预警标签编码长度范围1-50个字符")
    private String code;

    /**
     * 类型: 1.量表预警;
     */
    @Schema(title = "预警类型", name = "type", defaultValue = "1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer type;

    /**
     * 描述
     */
    @Schema(title = "描述", name = "description", defaultValue = "XXXX", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 255, message = "描述长度范围0-255个字符")
    private String description;

    /**
     * 租户ID
     */
    @Schema(title = "租户ID", name = "tenantId", defaultValue = "system", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 50, message = "租户ID长度范围1-50个字符")
    private String tenantId = "system";

}
