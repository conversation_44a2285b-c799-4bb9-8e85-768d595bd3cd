package com.wftk.scale.admin.vo.input.scale;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: ScaleResultIntroCreateInput
 * @Description: 创建量表解结果解读信息
 * @Author: mq
 * @Date: 2024-11-05 17:47
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleResultIntroModifyInput implements Serializable {

    /**
     * 结果ID(修改数据主键ID)
     */
    @Schema(title = "结果ID", name = "id", defaultValue = "604980089960133", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结果ID不能为空")
    private Long id;

    /**
     * 量表ID
     */
    @Schema(title = "量表ID", name = "scaleId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long scaleId;

    /**
     * 顺序
     */
    @Schema(title = "量表分类排序", name = "sort", defaultValue = "10", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Max(value = Integer.MAX_VALUE,message = "不能超过Integer.MAX_VALUE最大数值")
    private Integer sort;

    /**
     * 结果解读
     */
    @Schema(title = "结果解读", name = "intro", defaultValue = "评估日常休息情况", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结果解读不能为空")
    @Length(min = 1, max = 2048, message = "结果解读长度范围1-2048个字符")
    private String intro;

    /**
     * 因子ID
     */
    @Schema(title = "因子ID", name = "factorId", defaultValue = "604980089960133", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子ID不能为空")
    private Long factorId;

    /**
     * 量表因子所得分数（可能是个区间）
     */
    @Schema(title = "量表因子所得分数", name = "score", defaultValue = "10-50", requiredMode = Schema.RequiredMode.REQUIRED)
    private String score;

    /**
     * 因子分值转换方式: 1.等比转换  2.固定值转换 （如果是等比转换，则转换后的值是计算出来的区间）
     */
    @Schema(title = "因子分值转换方式", name = "scoreConvertType", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer scoreConvertType;

    /**
     * 转换分值
     */
    @Schema(title = "转换分值", name = "convertScore", defaultValue = "15", requiredMode = Schema.RequiredMode.REQUIRED)
    private String convertScore;

    /**
     * 结果: 0.阴; 1.阳;
     */
    @Schema(title = "结果性质", name = "result", defaultValue = "1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean result;

    /**
     * 展示类型
     */
    @Schema(title = "展示类型", name = "showType", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(message = "展示类型不能为空",min = 1)
    private List<String> showType;

    /**
     * 图表类型
     */
    @Schema(title = "图表类型", name = "chartType", defaultValue = "1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer chartType;

    /**
     * 租户ID
     */
    @Schema(title = "租户ID", name = "tenantId", defaultValue = "system", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 50, message = "租户ID长度范围1-50个字符")
    private String tenantId = "system";
}
