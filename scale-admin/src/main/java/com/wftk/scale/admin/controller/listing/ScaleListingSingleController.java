package com.wftk.scale.admin.controller.listing;

import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleListingConverter;
import com.wftk.scale.admin.vo.input.listing.ScaleListedQueryInput;
import com.wftk.scale.admin.vo.input.listing.ScaleListingCreateInput;
import com.wftk.scale.admin.vo.input.listing.ScaleListingQueryInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.dto.listing.ScaleListedParamDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO;
import com.wftk.scale.biz.dto.listing.ScaleListingChangeDto;
import com.wftk.scale.biz.dto.listing.ScaleListingParamDTO;
import com.wftk.scale.biz.dto.listing.ScaleListingQueryDTO;
import com.wftk.scale.biz.entity.ScaleListing;
import com.wftk.scale.biz.service.ScaleListingService;
import com.wftk.scale.biz.service.ScaleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: ScaleListingSingleController
 * @Description: 单个量表上下架管理
 * @Author: mq
 * @Date: 2024-10-25 15:53
 * @Version: 1.0
 **/
@Tag(name = "单个量表上架相关API")
@RestController
@AdminMapping("/scale/single/listing")
@Slf4j
public class ScaleListingSingleController {

    @Autowired
    private ScaleListingService scaleListingService;

    @Autowired
    private ScaleService scaleService;

    @Autowired
    private ScaleListingConverter scaleListingConverter;

    @Operation(summary = "获取可上架的单个量表列表数据")
    @OptLog(module = "量表上架管理", optType = OptType.QUERY, description = "获取可上架的单个量表列表数据")
    @GetMapping("undone")
    public ApiResult<Page<ScaleListingQueryDTO>> selectListingPage(ScaleListingQueryInput input) {
        ScaleListingParamDTO scaleListingParamDTO = scaleListingConverter.scaleListingQueryInputToParamDTO(input);
        return ApiResult.ok(scaleListingService.getScaleListing(scaleListingParamDTO));
    }

    @Operation(summary = "获取已上架单个量表列表数据")
    @OptLog(module = "量表上架管理", optType = OptType.QUERY, description = "获取已上架单个量表列表数据")
    @GetMapping("completed")
    public ApiResult<Page<ScaleListedQueryDTO>> selectListedPage(ScaleListedQueryInput input) {
        ScaleListedParamDTO scaleListedParamDTO = scaleListingConverter.scaleListedQueryInputToParamDTO(input);
        return ApiResult.ok(scaleListingService.getScaleListed(scaleListedParamDTO));
    }

    @Operation(summary = "单个量表上架")
    @OptLog(module = "量表上架管理", optType = OptType.MODIFY, description = "单个量表上架")
    @PostMapping("on")
    public ApiResult<String> scaleOnShell(@Valid @RequestBody ScaleListingCreateInput input) {
        boolean result = scaleService.vaildCompletedStatus(input.getTargetId());
        if (!result) {
            throw new BusinessException("上架失败,当前量表未完成配置!");
        }
        ScaleListing scaleListing = scaleListingConverter.scaleListingCreateInputToEntity(input);
        scaleListingService.scaleOnShell(scaleListing);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "单个量表下架")
    @OptLog(module = "量表上架管理", optType = OptType.MODIFY, description = "单个量表下架")
    @PostMapping("off")
    public ApiResult<String> scaleOffShell(@RequestBody @Valid ScaleListingChangeDto dto) {
        scaleListingService.scaleOffShell(dto.getScaleListingId());
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "生成单个量表二维码")
    @OptLog(module = "量表上架管理", optType = OptType.CREATE, description = "生成单个量表二维码")
    @PostMapping("qrcode")
    public ApiResult<String> qrcode(@RequestBody @Valid ScaleListingChangeDto dto) {
        return ApiResult.ok(scaleListingService.qrcode(dto.getScaleListingId()));
    }
}
