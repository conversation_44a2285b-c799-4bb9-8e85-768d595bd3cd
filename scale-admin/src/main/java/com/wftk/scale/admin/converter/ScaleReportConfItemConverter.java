package com.wftk.scale.admin.converter;


import com.wftk.scale.admin.vo.input.scale.ScaleReportConfCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleReportConfItemCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleReportConfItemModifyInput;
import com.wftk.scale.admin.vo.input.scale.ScaleReportConfModifyInput;
import com.wftk.scale.admin.vo.output.scale.ScaleReportConfItemOutput;
import com.wftk.scale.biz.entity.ScaleReportConf;
import com.wftk.scale.biz.entity.ScaleReportConfItem;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @InterfaceName: ScaleReportConfConverter
 * @Description: 量表报告设置数据转换器
 * @Author: mq
 * @Date: 2024/11/26
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleReportConfItemConverter {

    /*
    * @Author: mq
    * @Description: 将创建报告设置参数信息转为数据实体
    * @Date: 2024/11/26 11:23
    * @Param: createInput
    * @return: ScaleReportConf
    **/
    ScaleReportConfItem scaleReportConfItemCreateInputToEntity(ScaleReportConfItemCreateInput createInput);

    /*
    * @Author: mq
    * @Description: 将修改报告设置参数信息转为数据实体
    * @Date: 2024/11/26 11:23
    * @Param: modifyInput
    * @return: ScaleReportConf
    **/
    ScaleReportConfItem scaleReportConfItemModifyInputToEntity(ScaleReportConfItemModifyInput modifyInput);

    /*
     * @Author: mq
     * @Description: 将修改报告设置参数信息转为数据实体
     * @Date: 2024/11/26 11:23
     * @Param: modifyInput
     * @return: ScaleReportConf
     **/
    ScaleReportConfItemOutput entityToOutput(ScaleReportConfItem scaleReportConfItem);

    /*
     * @Author: mq
     * @Description: 将修改报告设置参数信息转为数据实体
     * @Date: 2024/11/26 11:23
     * @Param: modifyInput
     * @return: ScaleReportConf
     **/
    List<ScaleReportConfItemOutput> entityToOutput(List<ScaleReportConfItem> scaleReportConfItem);
}
