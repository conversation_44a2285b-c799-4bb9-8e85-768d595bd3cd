package com.wftk.scale.admin.vo.input.scale;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @createDate 2024/10/24 16:09
 */
@Data
public class ScaleCommitInput {

    @Schema(name = "量表id",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long scaleId;

    @Schema(name = "名称",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "名称不能为空")
    @Length(min = 1, max = 255, message = "名称长度范围1-255个字符")
    private String name;

    @Schema(name = "宣传页",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 1, max = 255, message = "宣传页长度范围1-255个字符")
    private String cover;

    @Schema(name = "简介",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String intro;

    @Schema(name = "指导语",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 1, max = 255, message = "指导语长度范围1-255个字符")
    private String guideline;

    @Schema(name = "排序",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    @Min(value = 1,message = "不能小于1")
    @Max(value = Integer.MAX_VALUE,message = "不能超过int最大数值")
    private Integer sort;

    /**
     * 时间需要转换单位
     * 分 --> 秒
     */
    @Schema(name = "限时（最小）",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "限时（最小）不能为空")
    @Max(value = Integer.MAX_VALUE,message = "不能超过int最大数值")
    // todo 时间单位转换注解
    private Integer minTimeLimit;

    /**
     * 时间需要转换单位
     * 分 --> 秒
     */
    @Schema(name = "限时（最大）",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "限时（最大）不能为空")
    @Min(value = 1,message = "不能小于1")
    @Max(value = Integer.MAX_VALUE,message = "不能超过int最大数值")
    // todo 时间单位转换注解
    private Integer maxTimeLimit;

    @Schema(name = "量表介绍",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String description;

}
