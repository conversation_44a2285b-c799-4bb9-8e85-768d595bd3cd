package com.wftk.scale.admin.vo.input.scale;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: ScaleUserResultInput
 * @Description:
 * @Author: mq
 * @Date: 2024/11/20
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleUserResultQueryInput implements Serializable {

    /**
     * 用户账号
     */
    @Schema(title = "用户账号", name = "userAccount", defaultValue = "zhangsan", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userAccount;

    /**
     * 用户名称
     */
    @Schema(title = "用户名称", name = "userName", defaultValue = "张三", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userName;

    /**
     * 部门ID
     */
    @Schema(title = "部门ID", name = "deptId", defaultValue = "***************", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long deptId;

    /**
     * 手机号
     */
    @Schema(title = "手机号", name = "phone", defaultValue = "***********", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String phone;

    /**
     * 量表名称
     */
    @Schema(title = "量表名称", name = "scaleName", defaultValue = "宗氏抑郁自评量表", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String scaleName;

    /**
     * 终端code
     */
    @Schema(title = "终端编号", name = "terminalCode", defaultValue = "XZD123", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String terminalCode;
}
