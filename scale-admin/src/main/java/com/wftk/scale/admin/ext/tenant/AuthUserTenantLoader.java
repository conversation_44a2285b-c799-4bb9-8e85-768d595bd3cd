package com.wftk.scale.admin.ext.tenant;

import cn.hutool.core.util.StrUtil;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.loader.TenantLoader;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;

/**
 * <AUTHOR>
 * @create 2024/11/22 16:09
 */
public class AuthUserTenantLoader implements TenantLoader {
    @Override
    public String getTenantId() {
        Authentication authentication = AuthenticationHolder.getAuthentication();
        if (authentication == null) {
            return null;
        }
        AuthUser<?> authUser = authentication.getAuthUser();
        if (authUser == null) {
            return null;
        }
        return authUser.getTenantId();
    }

    @Override
    public Expression getTenantExpression() {
        String clientId = getTenantId();
        if (StrUtil.isNotBlank(clientId)) {
            return new StringValue(clientId);
        }
        return null;
    }

    @Override
    public int getSort() {
        return -100;
    }
}
