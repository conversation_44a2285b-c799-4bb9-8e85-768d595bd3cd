package com.wftk.scale.admin.vo.input.scale;


import jakarta.validation.Valid;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: ScaleCombinationModifyBaseInput
 * @Description:
 * @Author: mq
 * @Date: 2024/12/16
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleCombinationModifyBaseInput implements Serializable {

    /**
     * 组合量表基本信息
     */
    @Valid
    private ScaleCombinationModifyInput scaleCombination;

    /**
     * 组合量表详情信息
     */
    @Valid
    private List<ScaleCombinationDetailModifyInput> scaleIds;
}
