package com.wftk.scale.admin.controller.scale;


import com.wftk.common.core.result.ApiResult;
import com.wftk.file.biz.spring.boot.autoconfigure.common.ApplicationContextHolder;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.DefaultFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.FileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.UploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.manager.ResourceManager;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.constant.FileConstant;
import com.wftk.scale.biz.dto.report.UserReportDTO;
import com.wftk.scale.biz.dto.report.UserReportQueryDTO;
import com.wftk.scale.biz.entity.ScaleUserReport;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.service.ScaleUserReportService;
import com.wftk.scale.biz.service.ScaleUserResultService;
import com.wftk.scale.biz.vo.ScaleUserReportUrlVO;
import com.wftk.scale.biz.vo.ScaleUserReportVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.File;

/**
 * @ClassName: ScaleUserResultController
 * @Description: 量表测评记录管理
 * @Author: mq
 * @Date: 2024/11/20
 * @Version: 1.0
 **/
@Tag(name = "用户报告相关API")
@AdminMapping("/scale/user/result")
@Slf4j
public class UserReportController {

    @Autowired
    private ScaleUserReportService scaleUserReportService;

    @Autowired
    private ScaleUserResultService scaleUserResultService;

    @Operation(summary = "获取报告列表数据")
    @OptLog(module = "报告管理", optType = OptType.QUERY, description = "获取报告列表数据")
    @GetMapping("page")
    public ApiResult<Page<ScaleUserReportVO>> page(UserReportQueryDTO dto) {
        return ApiResult.ok(scaleUserReportService.queryPage(dto));
    }

    @Operation(summary = "获取报告详情")
    @OptLog(module = "报告管理", optType = OptType.QUERY, description = "获取报告详情")
    @GetMapping("reportDetail")
    public ApiResult<ScaleUserReportUrlVO> reportDetail(@RequestParam("userReportId") Long userReportId) {
        ScaleUserReport scaleUserReport = scaleUserReportService.getById(userReportId);
        ScaleUserResult scaleUserResult = scaleUserResultService.getById(scaleUserReport.getResultId());
        ScaleUserReportUrlVO scaleUserReportUrlVO = new ScaleUserReportUrlVO();
        scaleUserReportUrlVO.setReportUrl(scaleUserResult.getReportUrl());
        scaleUserReportUrlVO.setId(scaleUserReport.getId());
        return ApiResult.ok(scaleUserReportUrlVO);
    }

    @Operation(summary = "获取报告分析")
    @OptLog(module = "报告管理", optType = OptType.QUERY, description = "获取报告分析")
    @GetMapping("analyze")
    public ApiResult<String> analyze() {
        // TODO
        return ApiResult.ok();
    }
}
