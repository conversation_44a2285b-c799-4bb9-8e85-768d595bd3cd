package com.wftk.scale.admin.vo.input.distribute;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: ScaleDistributeUserInput
 * @Description:
 * @Author: mq
 * @Date: 2024/12/16
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleDistributeUserInput implements Serializable {

    /**
     * 用户id
     */
    @Schema(title = "用户ID", name = "userId", defaultValue = "622323480376901", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    /**
     * 用户姓名
     */
    @Schema(title = "用户姓名", name = "userName", defaultValue = "张三", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userName;

    /**
     * 部门id，部门表还没建，表名待补充
     */
    @Schema(title = "部门ID", name = "departmentId", defaultValue = "622323480376901", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long departmentId;
}
