package com.wftk.scale.admin.converter;


import com.wftk.scale.admin.vo.input.scale.ScaleReportConfCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleReportConfModifyInput;
import com.wftk.scale.biz.entity.ScaleReportConf;
import org.mapstruct.Mapper;

/**
 * @InterfaceName: ScaleReportConfConverter
 * @Description: 量表报告设置数据转换器
 * @Author: mq
 * @Date: 2024/11/26
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleReportConfConverter {

    /*
    * @Author: mq
    * @Description: 将创建报告设置参数信息转为数据实体
    * @Date: 2024/11/26 11:23
    * @Param: createInput
    * @return: ScaleReportConf
    **/
    ScaleReportConf scaleReportConfCreateInputToEntity(ScaleReportConfCreateInput createInput);

    /*
    * @Author: mq
    * @Description: 将修改报告设置参数信息转为数据实体
    * @Date: 2024/11/26 11:23
    * @Param: modifyInput
    * @return: ScaleReportConf
    **/
    ScaleReportConf scaleReportConfModifyInputToEntity(ScaleReportConfModifyInput modifyInput);
}
