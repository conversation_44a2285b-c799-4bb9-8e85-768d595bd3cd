package com.wftk.scale.admin.controller.scale;

import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.dto.scale.ScaleWarnDetailDTO;
import com.wftk.scale.biz.service.ScaleWarnDetailService;
import com.wftk.scale.biz.vo.ScaleWarnVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Tag(name = "量表预警列表相关API")
@RestController
@AdminMapping("/scale/warnDetail")
public class ScaleWarnDetailController {

    @Resource
    private ScaleWarnDetailService scaleWarnDetailService;

    @GetMapping("/page")
    @Operation(summary = "分页查询预警列表")
    public ApiResult<Page<ScaleWarnVO>> selectListingPage(ScaleWarnDetailDTO dto) {
        return ApiResult.ok(
                scaleWarnDetailService.queryList(
                    dto.getAccount(),
                    dto.getUserName(),
                    dto.getDepartmentId(),
                    dto.getPhone(),
                    dto.getScaleName(),
                    dto.getTerminalCode()
                )
        );
    }

    @GetMapping
    @Operation(summary = "详情")
    public ApiResult<ScaleWarnVO> detail(Long id) {
        if(ObjUtil.isNull(id)){
            throw new BusinessException("参数ID不能为空");
        }
        return ApiResult.ok(scaleWarnDetailService.detail(id));
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @OptLog(module = "预警管理", optType = OptType.DELETE, description = "删除")
    public ApiResult<String> delete(Long id) {
        if(ObjUtil.isNull(id)){
            throw new BusinessException("参数ID不能为空");
        }
        scaleWarnDetailService.deleteById(id);
        return ApiResult.ok("删除成功");
    }

    @Operation(summary = "导出")
    @GetMapping("export")
    public void export(HttpServletResponse response, ScaleWarnDetailDTO dto){
        scaleWarnDetailService.export(response, dto);
    }
}
