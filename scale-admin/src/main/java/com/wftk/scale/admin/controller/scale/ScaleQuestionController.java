package com.wftk.scale.admin.controller.scale;

import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleQuestionConverter;
import com.wftk.scale.admin.vo.input.scale.ScaleQuestionBaseCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleQuestionBaseModifyInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO;
import com.wftk.scale.biz.entity.ScaleQuestion;
import com.wftk.scale.biz.entity.ScaleQuestionOption;
import com.wftk.scale.biz.service.ScaleQuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @ClassName: ScaleQuestionController
 * @Description: 量表题目信息管理
 * @Author: mq
 * @Date: 2024-11-01 13:42
 * @Version: 1.0
 **/
@Tag(name = "量表题目管理相关API")
@RestController
@AdminMapping("/scale/questions")
@Slf4j
public class ScaleQuestionController {

    @Autowired
    private ScaleQuestionService scaleQuestionService;
    @Autowired
    private ScaleQuestionConverter scaleQuestionConverter;

    @Operation(summary = "获取量表题目信息列表数据-全部")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取量表题目信息列表数据-全部")
    @GetMapping("datas")
    public ApiResult<List<ScaleQuestionQueryDTO>> getAll(Long scaleId) {
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("获取失败,量表scaleId不允许为空!");
        }
        //当题目数据量过多时,一次性将数据返回给前端会存在性能影响！！！
        return ApiResult.ok(scaleQuestionService.findByScaleId(scaleId));
    }

    @Operation(summary = "获取量表题目信息列表数据-分页")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取量表题目信息列表数据-分页")
    @GetMapping()
    public ApiResult<Page<ScaleQuestionQueryDTO>> selectPage(Long scaleId) {
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("获取失败,量表scaleId不允许为空!");
        }
        return ApiResult.ok(scaleQuestionService.selectPage(scaleId));
    }

    @Operation(summary = "创建量表题目信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "创建量表题目信息")
    @PostMapping
    public ApiResult<String> create(@Valid @RequestBody ScaleQuestionBaseCreateInput input) {
        ScaleQuestion question = scaleQuestionConverter.scaleQuestionCreateInputToEntity(input.getQuestion());
        List<ScaleQuestionOption> optionList = scaleQuestionConverter.scaleQuestionOptionCreateInputToList(input.getOptions());
        scaleQuestionService.create(question, optionList);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "修改量表题目信息")
    @OptLog(module = "量表管理模块", optType = OptType.MODIFY, description = "修改量表题目信息")
    @PutMapping
    public ApiResult<String> modify(@Valid @RequestBody ScaleQuestionBaseModifyInput input) {
        ScaleQuestion question = scaleQuestionConverter.scaleQuestionModifyInputToEntity(input.getQuestion());
        List<ScaleQuestionOption> optionList = scaleQuestionConverter.scaleQuestionOptionModifyInputToList(input.getOptions());
        scaleQuestionService.modify(question, optionList);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "删除量表题目信息")
    @OptLog(module = "量表管理模块", optType = OptType.DELETE, description = "删除量表题目信息")
    @DeleteMapping()
    public ApiResult<String> delete(Long scaleId, Long questionId) {
        scaleQuestionService.delete(scaleId, questionId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "清空量表题目信息")
    @OptLog(module = "量表管理模块", optType = OptType.DELETE, description = "清空量表题目信息")
    @DeleteMapping("/deleteAll")
    public ApiResult<String> deleteAll(Long scaleId) {
        scaleQuestionService.delAll(scaleId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "获取量表题目详情信息")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取量表题目详情信息")
    @GetMapping("detail")
    public ApiResult<ScaleQuestionQueryDTO> detail(Long scaleId, Long questionId) {
        return ApiResult.ok(scaleQuestionService.detail(scaleId, questionId));
    }

    @Operation(summary = "导入量表题目信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "导入量表题目信息")
    @PostMapping("import")
    public ApiResult<String> importExcel(@RequestPart(value = "file") MultipartFile file, Long scaleId){
        scaleQuestionService.importScaleQuestion(scaleId, file);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "导出量表题目信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "导出量表题目信息")
    @GetMapping("export")
    public void exportExcel(HttpServletResponse response, Long scaleId){
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("导出失败,量表ID不允许为空!");
        }
        scaleQuestionService.exportScaleQuestion(scaleId, response);
    }

    @Operation(summary = "下载量表题目模板")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "下载量表题目模板")
    @GetMapping("download")
    public void downloadExcel(HttpServletResponse response){
        scaleQuestionService.downloadTemplate(response);
    }

    @Operation(summary = "复制题目")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "复制题目")
    @GetMapping("copy")
    public ApiResult<Void> overallScore(Long scaleId, Long questionId) {
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("复制失败,量表scaleId和questionId不允许为空!");
        }
        scaleQuestionService.copy(scaleId,questionId);
        return ApiResult.ok();
    }
}
