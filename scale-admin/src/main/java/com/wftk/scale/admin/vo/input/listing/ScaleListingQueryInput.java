package com.wftk.scale.admin.vo.input.listing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @ClassName: ScaleListingQueryInput
 * @Description: 查询量表可上架信息传输实体
 * @Author: mq
 * @Date: 2024-10-29 16:05
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleListingQueryInput implements Serializable {

    /**
     * 量表ID
     */
    @Schema(title = "量表ID", name = "targetId", defaultValue = "我是测量表ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long targetId;

    /**
     * 量表名称
     */
    @Schema(title = "量表名称", name = "targetName", defaultValue = "我是测试量表名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 1, max = 255, message = "量表名称长度范围1-50个字符")
    private String targetName;

    /**
     * 量表类型
     */
    @Schema(title = "量表分类ID或者测评方式", name = "targetType", defaultValue = "609527310759877", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long targetType;

    /**
     * 终端编号
     */
    @Schema(title = "终端编号", name = "terminalCode", defaultValue = "60952", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String terminalCode;

    /**
     * 操作人
     */
    @Schema(title = "操作人", name = "opUser", defaultValue = "demo", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opUser;
}
