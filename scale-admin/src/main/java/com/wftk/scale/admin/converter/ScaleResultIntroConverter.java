package com.wftk.scale.admin.converter;

import com.wftk.scale.admin.vo.input.scale.ScaleResultIntroCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleResultIntroModifyInput;
import com.wftk.scale.biz.entity.ScaleResultIntro;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @InterfaceName: ScaleResultIntroConverter
 * @Description: 量表结果解读数据转换器
 * @Author: mq
 * @Date: 2024-11-05 17:55
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleResultIntroConverter {

    /* 
     * @Author: mq
     * @Description: 将创建结果解读参数信息转为数据实体
     * @Date: 2024/11/5 17:56 
     * @Param: ScaleResultIntroCreateInput  
     * @return: com.wftk.scale.biz.entity.ScaleResultIntro 
     **/
    @Mapping(target = "showType", ignore = true)
    ScaleResultIntro scaleResultIntroCreateInputToEntity(ScaleResultIntroCreateInput scaleResultIntroCreateInput);

    /* 
     * @Author: mq
     * @Description: 将修改结果解读参数信息转为数据实体
     * @Date: 2024/11/5 17:57 
     * @Param: scaleResultIntroModifyInput  
     * @return: com.wftk.scale.biz.entity.ScaleResultIntro 
     **/
    @Mapping(target = "showType", ignore = true)
    ScaleResultIntro scaleResultIntroModifyInputToEntity(ScaleResultIntroModifyInput scaleResultIntroModifyInput);
}
