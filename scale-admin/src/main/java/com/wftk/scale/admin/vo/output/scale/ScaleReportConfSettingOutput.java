package com.wftk.scale.admin.vo.output.scale;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11 15:20:06
 */
@Data
public class ScaleReportConfSettingOutput  {


    /**
     * id
     */
    private Long id;

    /**
     * 报告配置id。来自scale_report_conf的主键
     */
    private Long reportConfId;

    /**
     * 类型 1.阅读须知 2.备注 3.表格设置 4.图表设置
     */
    private Integer type;

    /**
     * 报告类型 1.总分  2.总均分 3.阳性数量 4.因子分析
     */
    private Integer reportType;

    /**
     * 图表类型
     */
    private Integer chartType;

    /**
     * 存储值或者json配置
     */
    private String value;


}
