package com.wftk.scale.admin.vo.input.scale;


import com.wftk.scale.biz.dto.report.ChartSettingDTO;
import com.wftk.scale.biz.dto.report.FormSettingDTO;
import com.wftk.scale.biz.dto.report.RemarkSettingDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: ScaleReportConfModifyInput
 * @Description:
 * @Author: mq
 * @Date: 2024/11/26
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleReportConfModifyInput implements Serializable {

    /**
     * 主键ID
     */
    @Schema(title = "报告ID", name = "id", defaultValue = "604980089960133", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报告ID不能为空")
    private Long id;

    /**
     * 量表id。来自scale表的主键
     */
    @Schema(title = "量表ID", name = "scaleId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long scaleId;

    /**
     * 用户编码开关
     */
    @Schema(title = "用户编码开关", name = "userCodeEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户编码开关不能为空")
    private Boolean userCodeEnable;

    /**
     * 用户姓名开关
     */
    @Schema(title = "用户姓名开关", name = "userNameEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户姓名开关不能为空")
    private Boolean userNameEnable;

    /**
     * 用户性别开关
     */
    @Schema(title = "用户性别开关", name = "userSexEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户性别开关不能为空")
    private Boolean userSexEnable;

    /**
     * 用户手机号开关
     */
    @Schema(title = "用户手机号开关", name = "userPhoneEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户手机号开关不能为空")
    private Boolean userPhoneEnable;

    /**
     * 用户生日开关
     */
    @Schema(title = "用户生日开关", name = "userBirthdayEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户生日开关不能为空")
    private Boolean userBirthdayEnable;

    /**
     * 用户身份证号开关
     */
    @Schema(title = "用户身份证号开关", name = "userIdCardEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户身份证号开关不能为空")
    private Boolean userIdCardEnable;

    /**
     * 测评时间开关
     */
    @Schema(title = "测评时间开关", name = "evaluationTimeEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "测评时间开关不能为空")
    private Boolean evaluationTimeEnable;

    /**
     * 阅读须知开关
     */
    @Schema(title = "阅读须知开关", name = "readingInstructionsEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "阅读须知开关不能为空")
    private Boolean readingInstructionsEnable;

    /**
     * 备注开关
     */
    @Schema(title = "备注开关", name = "remarkEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "备注开关不能为空")
    private Boolean remarkEnable;

    /**
     * 总分开关
     */
    @Schema(title = "总分开关", name = "totalScoreEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "总分开关不能为空")
    private Boolean totalScoreEnable;

    /**
     * 总分图表开关
     */
    @Schema(title = "总分图表开关", name = "totalScoreChartEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "总分图表开关不能为空")
    private Boolean totalScoreChartEnable;

    /**
     * 总分文字开关
     */
    @Schema(title = "总分文字开关", name = "totalScoreWordageEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "总分文字开关不能为空")
    private Boolean totalScoreWordageEnable;

    /**
     * 总分表格开关
     */
    @Schema(title = "总分表格开关", name = "totalScoreFormEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "总分表格开关不能为空")
    private Boolean totalScoreFormEnable;

    /**
     * 平均分开关
     */
    @Schema(title = "平均分开关", name = "avgScoreEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "平均分开关不能为空")
    private Boolean avgScoreEnable;

    /**
     * 平均分图表开关
     */
    @Schema(title = "平均分图表开关", name = "avgScoreChartEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "平均分图表开关不能为空")
    private Boolean avgScoreChartEnable;

    /**
     * 平均分文字开关
     */
    @Schema(title = "平均分文字开关", name = "avgScoreWordageEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "平均分文字开关不能为空")
    private Boolean avgScoreWordageEnable;

    /**
     * 平均分表格开关
     */
    @Schema(title = "平均分表格开关", name = "avgScoreFormEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "平均分表格开关不能为空")
    private Boolean avgScoreFormEnable;

    /**
     * 阳性数量开关
     */
    @Schema(title = "阳性数量开关", name = "positiveCountEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "阳性数量开关不能为空")
    private Boolean positiveCountEnable;

    /**
     * 阳性数量图表开关
     */
    @Schema(title = "阳性数量图表开关", name = "positiveCountChartEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "阳性数量图表开关不能为空")
    private Boolean positiveCountChartEnable;

    /**
     * 阳性数量文字开关
     */
    @Schema(title = "阳性数量文字开关", name = "positiveCountWordageEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "阳性数量文字开关不能为空")
    private Boolean positiveCountWordageEnable;

    /**
     * 阳性数量表格开关
     */
    @Schema(title = "阳性数量表格开关", name = "positiveCountFormEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "阳性数量表格开关不能为空")
    private Boolean positiveCountFormEnable;

    /**
     * 结果解读开关
     */
    @Schema(title = "结果解读开关", name = "resultIntroEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结果解读开关不能为空")
    private Boolean resultIntroEnable;

    /**
     * 音频视频解说开关
     */
    @Schema(title = "音频视频解说开关", name = "audioAndVideoNarrateEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "音频视频解说开关不能为空")
    private Boolean audioAndVideoNarrateEnable;

    /**
     * 改善建议开关
     */
    @Schema(title = "改善建议开关", name = "improvementSuggestionEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "改善建议开关不能为空")
    private Boolean improvementSuggestionEnable;

    /**
     * 因子分析开关
     */
    @Schema(title = "因子分析开关", name = "factorAnalysisEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子分析开关不能为空")
    private Boolean factorAnalysisEnable;

    /**
     * 因子分析图表开关
     */
    @Schema(title = "因子分析图表开关", name = "factorAnalysisChartEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子分析图表开关不能为空")
    private Boolean factorAnalysisChartEnable;

    /**
     * 因子分析文字开关
     */
    @Schema(title = "因子分析文字开关", name = "factorAnalysisWordageEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子分析文字开关不能为空")
    private Boolean factorAnalysisWordageEnable;

    /**
     * 因子分析表格开关
     */
    @Schema(title = "因子分析表格开关", name = "factorAnalysisFormEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子分析表格开关不能为空")
    private Boolean factorAnalysisFormEnable;


    /**
     * 因子结果解读开关
     */
    @Schema(title = "因子结果解读开关", name = "factorResultIntroEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子结果解读开关不能为空")
    private Boolean factorResultIntroEnable;

    /**
     * 因子音频视频解说开关
     */
    @Schema(title = "因子音频视频解说开关", name = "factorAudioAndVideoNarrateEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子音频视频解说开关不能为空")
    private Boolean factorAudioAndVideoNarrateEnable;

    /**
     * 因子改善建议开关
     */
    @Schema(title = "因子改善建议开关", name = "factorImprovementSuggestionEnable", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子改善建议开关不能为空")
    private Boolean factorImprovementSuggestionEnable;

    /**
     * 阅读须知
     */
    private String readingInstruction;

    /**
     * 备注
     */
    private List<RemarkSettingDTO> remarkSettings;

    /**
     * 表格设置
     */
    private List<FormSettingDTO> formSettings;

    /**
     * 图表设置
     */
    private List<ChartSettingDTO> chartSettings;


}
