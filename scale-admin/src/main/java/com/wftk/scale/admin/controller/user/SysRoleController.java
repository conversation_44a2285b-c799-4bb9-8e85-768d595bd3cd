package com.wftk.scale.admin.controller.user;

import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.dto.user.role.SysRoleCreateDTO;
import com.wftk.scale.biz.dto.user.role.SysRoleDeleteDTO;
import com.wftk.scale.biz.dto.user.role.SysRoleQueryDTO;
import com.wftk.scale.biz.dto.user.role.SysRoleUpdateDTO;
import com.wftk.scale.biz.dto.user.role.SysRoleUpdateEnableDTO;
import com.wftk.scale.biz.constant.enums.PermisssionRangeEnum;
import com.wftk.scale.biz.service.SysRoleService;
import com.wftk.scale.biz.vo.DicSelectListVO;
import com.wftk.scale.biz.vo.SysRoleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Tag(name = "角色管理")
@RestController
@AdminMapping("/role")
public class SysRoleController {

    @Resource
    private SysRoleService sysRoleService;

    @Operation(summary = "分页查询角色信息")
    @OptLog(module = "角色管理", optType = OptType.QUERY, description = "分页查询角色信息")
    @GetMapping("getList")
    public ApiResult<Page<SysRoleVO>> getList(SysRoleQueryDTO dto){
        return ApiResult.ok(sysRoleService.getList(dto));
    }

    @Operation(summary = "详情")
    @OptLog(module = "角色管理", optType = OptType.QUERY, description = "详情")
    @GetMapping("detail")
    public ApiResult<SysRoleVO> detail(Long id){
        return ApiResult.ok(sysRoleService.detailById(id));
    }

    @Operation(summary = "新增")
    @OptLog(module = "角色管理", optType = OptType.CREATE, description = "新增")
    @PostMapping
    public ApiResult<Void> add(@RequestBody @Valid SysRoleCreateDTO dto){
        sysRoleService.create(dto);
        return ApiResult.ok();
    }

    @Operation(summary = "修改")
    @OptLog(module = "角色管理", optType = OptType.MODIFY, description = "修改")
    @PutMapping
    public ApiResult<Void> modify(@RequestBody @Valid SysRoleUpdateDTO dto){
        sysRoleService.update(dto);
        return ApiResult.ok();
    }

    @Operation(summary = "修改状态")
    @OptLog(module = "角色管理", optType = OptType.MODIFY, description = "修改状态")
    @PutMapping("changeEnable")
    public ApiResult<Void> modify(@RequestBody @Valid SysRoleUpdateEnableDTO dto){
        sysRoleService.updateStatus(dto);
        return ApiResult.ok();
    }

    @Operation(summary = "删除")
    @OptLog(module = "角色管理", optType = OptType.DELETE, description = "删除")
    @DeleteMapping
    public ApiResult<Void> delete(@RequestBody @Valid SysRoleDeleteDTO dto){
        sysRoleService.deleteById(dto.getId());
        return ApiResult.ok();
    }

    @Operation(summary = "导出")
    @OptLog(module = "角色管理", optType = OptType.DELETE, description = "导出")
    @GetMapping("export")
    public void export(SysRoleQueryDTO dto, HttpServletResponse response){
        sysRoleService.export(dto, response);
    }

    @Operation(summary = "查询权限范围下拉列表")
    @OptLog(module = "角色管理", optType = OptType.DELETE, description = "查询权限范围下拉列表")
    @GetMapping("permissionRangeList")
    public ApiResult<List<DicSelectListVO>> permissionRangeList(){
        return ApiResult.ok(
                Arrays.stream(PermisssionRangeEnum.values())
                        .map(vo -> DicSelectListVO.builder()
                                .code(vo.getValue().toString())
                                .name(vo.getDesc())
                                .build()
                        ).collect(Collectors.toList()));
    }
}
