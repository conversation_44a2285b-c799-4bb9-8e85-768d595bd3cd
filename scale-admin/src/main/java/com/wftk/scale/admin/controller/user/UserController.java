package com.wftk.scale.admin.controller.user;

import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.dto.user.UserSearchDTO;
import com.wftk.scale.biz.vo.UserVO;
import com.wftk.scale.biz.dto.user.DistributableUserDTO;
import com.wftk.scale.biz.dto.user.UserChangeDTO;
import com.wftk.scale.biz.dto.user.UserChangeEnableDTO;
import com.wftk.scale.biz.dto.user.UserCreateDTO;
import com.wftk.scale.biz.dto.user.UserQueryDTO;
import com.wftk.scale.biz.entity.User;
import com.wftk.scale.biz.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/11/28 10:12
 */
@Tag(name = "业务用户相关API")
@RestController
@AdminMapping("/user")
@Slf4j
public class UserController {

    @Autowired
    UserService userService;

    @Operation(summary = "分页查询用户列表")
    @OptLog(module = "用户管理", optType = OptType.QUERY, description = "分页查询用户列表")
    @GetMapping("page")
    public ApiResult<Page<UserQueryDTO>> page(UserSearchDTO dto){
        return ApiResult.ok(userService.selectUserQueryList(dto));
    }

    @Operation(summary = "用户详情")
    @OptLog(module = "用户管理", optType = OptType.QUERY, description = "用户详情")
    @GetMapping("detail")
    public ApiResult<UserVO> detail(@RequestParam(value = "userId") Long userId){
        return ApiResult.ok(userService.selectByUserId(userId));
    }

    @Operation(summary = "查询分发用户")
    @OptLog(module = "用户管理", optType = OptType.QUERY, description = "查询分发用户")
    @GetMapping("findDistributionUser")
    public ApiResult<List<DistributableUserDTO>> findDistributionUser(
                                                      @RequestParam(value = "departmentCode") String departmentCode,
                                                      @RequestParam(value = "terminalCode",required = false) String terminalCode){
        return ApiResult.ok(userService.findDistributionUser(departmentCode, terminalCode));
    }

    @Operation(summary = "创建用户")
    @OptLog(module = "用户管理", optType = OptType.CREATE, description = "创建用户")
    @PostMapping("create")
    public ApiResult<User> create(@RequestBody @Valid UserCreateDTO userCreateDTO){
        return ApiResult.ok(userService.createUser(userCreateDTO));
    }

    @Operation(summary = "启用/禁用用户")
    @OptLog(module = "用户管理", optType = OptType.MODIFY, description = "启用/禁用用户")
    @PutMapping("/changeEnable")
    public ApiResult<String> changeEnable(@RequestBody @Valid UserChangeEnableDTO userChangeEnableDTO){
        userService.changeEnable(userChangeEnableDTO);
        return ApiResult.ok();
    }

    @Operation(summary = "编辑用户")
    @OptLog(module = "用户管理", optType = OptType.MODIFY, description = "编辑用户")
    @PutMapping("/changeUser")
    public ApiResult<String> changeUser(@RequestBody @Valid UserChangeDTO userChangeDTO){
        userService.changeUser(userChangeDTO);
        return ApiResult.ok();
    }

    @Operation(summary = "删除用户")
    @OptLog(module = "用户管理", optType = OptType.DELETE, description = "删除用户")
    @DeleteMapping("/deleteUser")
    public ApiResult<String> deleteUser(Long userId){
        userService.deleteUser(userId);
        return ApiResult.ok();
    }

    @Operation(summary = "导出")
    @OptLog(module = "用户管理", optType = OptType.QUERY, description = "导出")
    @GetMapping("export")
    public void exportExcel(HttpServletResponse response, UserSearchDTO dto){
        userService.exportUser(response, dto);
    }

    @Operation(summary = "下载模板")
    @OptLog(module = "用户管理", optType = OptType.QUERY, description = "下载模板")
    @GetMapping("downTemplate")
    public void downTemplate(HttpServletResponse response){
        userService.downTemplate(response);
    }

    @Operation(summary = "导入")
    @OptLog(module = "用户管理", optType = OptType.CREATE, description = "导入")
    @GetMapping("importData")
    public void importData(@RequestPart MultipartFile file){
        userService.importData(file);
    }
}
