package com.wftk.scale.admin.ext.auth;

import com.wftk.auth.spring.boot.autoconfigure.core.validator.PreAuthAccountValidator;

/**
 * <AUTHOR>
 * @create 2024/12/20 17:48
 */
public class AdminPreAuthAccountValidator implements PreAuthAccountValidator {

    private final AdminUserLoader adminUserLoader;

    public AdminPreAuthAccountValidator(AdminUserLoader adminUserLoader) {
        this.adminUserLoader = adminUserLoader;
    }

    @Override
    public boolean validate(String account, String preGrantType) {
        if (!"sms".equalsIgnoreCase(preGrantType)) {
            return true;
        }
        //短信场景下，提前校验手机号是否存在
        return adminUserLoader.load(account) != null;
    }
}
