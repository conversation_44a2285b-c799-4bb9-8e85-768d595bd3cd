package com.wftk.scale.admin.controller.config;

import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.SystemTagConverter;
import com.wftk.scale.admin.vo.input.config.SystemTagCreateInput;
import com.wftk.scale.admin.vo.input.config.SystemTagModifyInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.dto.tag.SystemTagChangeStatusDTO;
import com.wftk.scale.biz.entity.SystemTag;
import com.wftk.scale.biz.service.SystemTagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName: SystemTagController
 * @Description: 预警配置信息类
 * @Author: mq
 * @Date: 2024-10-24 15:53
 * @Version: 1.0
 **/
@Tag(name = "预警标签参数配置API")
@RestController
@AdminMapping("conf/scale/tag")
@Slf4j
public class SystemTagController {

    @Autowired
    private SystemTagService systemTagService;

    @Autowired
    private SystemTagConverter systemTagConverter;

    @Operation(summary = "获取预警标签信息列表数据-全部")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "获取预警标签信息列表数据-全部")
    @GetMapping("datas")
    public ApiResult<List<SystemTag>> getAll(@RequestParam(required = false) String tagName) {
        return ApiResult.ok(systemTagService.getListOfEnabled(tagName));
    }

    @Operation(summary = "获取预警标签列表数据")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "获取预警标签列表数据")
    @GetMapping
    public ApiResult<Page<SystemTag>> selectPage(@RequestParam(required = false) String tagName) {
        return ApiResult.ok(systemTagService.selectPage(tagName));
    }

    @Operation(summary = "创建预警标签信息")
    @OptLog(module = "参数配置模块", optType = OptType.CREATE, description = "创建预警标签信息")
    @PostMapping
    public ApiResult<String> create(@Valid @RequestBody SystemTagCreateInput input) {
        String tagCode = input.getCode();
        boolean result = systemTagService.validSystemTagCode(null, tagCode);
        if (result) {
            throw new BusinessException("创建失败,预警标签编号已存在!");
        }
        SystemTag systemTag = systemTagConverter.systemTagCreateInputToEntity(input);
        systemTagService.create(systemTag);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "修改预警标签信息")
    @OptLog(module = "参数配置模块", optType = OptType.MODIFY, description = "修改预警标签信息")
    @PutMapping
    public ApiResult<String> modify(@Valid @RequestBody SystemTagModifyInput input) {
        Long id = input.getId();
        String tagCode = input.getCode();
        boolean result = systemTagService.validSystemTagCode(id, tagCode);
        if (result) {
            throw new BusinessException("修改失败,预警标签编号已存在!");
        }
        SystemTag systemTag = systemTagConverter.systemTagModifyInputToEntity(input);
        systemTagService.modify(systemTag);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "删除预警标签信息")
    @OptLog(module = "参数配置模块", optType = OptType.DELETE, description = "删除预警标签信息")
    @DeleteMapping
    public ApiResult<String> delete(Long tagId) {
        if (ObjUtil.isNull(tagId)) {
            throw new BusinessException("删除失败,预警标签tagId参数不允许为空!");
        }
        systemTagService.delete(tagId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "更新预警标签数据禁用状态")
    @OptLog(module = "参数配置模块", optType = OptType.MODIFY, description = "更新预警标签数据禁用状态")
    @PutMapping("status")
    public ApiResult<String> updateEnabled(@RequestBody @Valid SystemTagChangeStatusDTO dto) {
        systemTagService.updateEnable(dto.getTagId(), dto.getEnabled() == 1);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "根据ID获取预警标签详情信息")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "根据ID获取预警标签详情信息")
    @GetMapping("detail")
    public ApiResult<SystemTag> detail(Long tagId) {
        if (ObjUtil.isNull(tagId)) {
            throw new BusinessException("获取详情失败,预警标签tagId参数不允许为空!");
        }
        return ApiResult.ok(systemTagService.getById(tagId));
    }

    @Operation(summary = "导出")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "导出")
    @GetMapping("export")
    public void export(HttpServletResponse response, @RequestParam(required = false) String tagName) throws Exception{
        systemTagService.export(response, tagName);
    }
}
