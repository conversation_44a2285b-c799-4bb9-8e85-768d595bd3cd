package com.wftk.scale.admin.converter;

import com.wftk.scale.admin.vo.input.scale.ScaleRelationCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleRelationModifyInput;
import com.wftk.scale.biz.entity.ScaleQuestionRelation;
import org.mapstruct.Mapper;

/**
 * @InterfaceName: ScaleRelationConverter
 * @Description: 量表跳转逻辑数据转换器
 * @Author: mq
 * @Date: 2024-11-05 10:35
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleRelationConverter {

    /*
     * @Author: mq
     * @Description: 将创建跳转逻辑参数信息转为数据实体
     * @Date: 2024/11/5 10:36 
     * @Param: scaleRelationCreateInput  
     * @return: com.wftk.scale.biz.entity.ScaleQuestionRelation 
     **/
    ScaleQuestionRelation scaleRelationCreateInputToEntity(ScaleRelationCreateInput scaleRelationCreateInput);

    /* 
     * @Author: mq
     * @Description: 将修改跳转逻辑参数信息转为数据实体
     * @Date: 2024/11/5 17:32 
     * @Param: scaleRelationModifyInput  
     * @return: com.wftk.scale.biz.entity.ScaleQuestionRelation 
     **/
    ScaleQuestionRelation scaleRelationModifyInputToEntity(ScaleRelationModifyInput scaleRelationModifyInput);
}
