package com.wftk.scale.admin.converter;

import com.wftk.scale.admin.vo.input.scale.ScaleFactorCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleFactorModifyInput;
import com.wftk.scale.biz.dto.scale.ScaleFactorDTO;
import com.wftk.scale.biz.entity.ScaleFactor;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @InterfaceName: ScaleFactorConverter
 * @Description: 量表因子维度数据转换器
 * @Author: mq
 * @Date: 2024-11-05 09:50
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleFactorConverter {

    /* 
     * @Author: mq
     * @Description: 将创建因子维度参数信息转为数据实体
     * @Date: 2024/11/5 9:51 
     * @Param: scaleFactorCreateInput  
     * @return: com.wftk.scale.biz.entity.ScaleFactor 
     **/
    ScaleFactor scaleFactorCreateInputToEntity(ScaleFactorCreateInput scaleFactorCreateInput);

    /*
     * @Author: mq
     * @Description: 将修改因子维度参数信息转为数据实体
     * @Date: 2024/11/5 10:04
     * @Param: scaleFactorModifyInput
     * @return: com.wftk.scale.biz.entity.ScaleFactor
     **/
    ScaleFactor scaleFactorModifyInputToEntity(ScaleFactorModifyInput scaleFactorModifyInput);

    ScaleFactorDTO entityToDTO(ScaleFactor scaleFactor);

    List<ScaleFactorDTO> entityToDTO(List<ScaleFactor> scaleFactor);
}
