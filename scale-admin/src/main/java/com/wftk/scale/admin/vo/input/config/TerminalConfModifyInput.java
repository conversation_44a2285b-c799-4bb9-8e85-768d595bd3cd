package com.wftk.scale.admin.vo.input.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * @ClassName: TerminalConfModifyInput
 * @Description: 修改终端配置信息实体参数
 * @Author: mq
 * @Date: 2024-10-25 09:43
 * @Version: 1.0
 **/
@Data
@Builder
public class TerminalConfModifyInput {

    /**
     * 终端配置ID(修改数据主键ID)
     */
    @Schema(title = "终端配置ID", name = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "终端配置ID不能为空")
    private Long id;

    /**
     * 配置编码
     */
    @Schema(title = "配置编码", name = "itemCode", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "终端配置编码不能为空")
    @Length(min = 1, max = 50, message = "终端配置编码长度范围1-50个字符")
    private String itemCode;

    /**
     * 配置的值
     */
    @Schema(title = "配置的值", name = "itemValue", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "终端配置值不能为空")
    @Length(min = 1, max = 255, message = "终端配置值长度范围1-255个字符")
    private String itemValue;

    /**
     * 终端类型
     */
    @Schema(title = "终端类型", name = "terminalId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "终端类型不能为空")
    private Integer terminalId;

    /**
     * 租户ID
     */
    @Schema(title = "租户ID", name = "tenantId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 50, message = "租户ID长度范围1-255个字符")
    private String tenantId = "system";
}
