package com.wftk.scale.admin.vo.input.distribute;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: ScaleDistributeCreateInput
 * @Description: 创建量表分发信息接收实体
 * @Author: mq
 * @Date: 2024-10-30 17:05
 * @Version: 1.0s
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleDistributeCreateInput implements Serializable {

    /**
     * 上架量表基础信息
     */
    @Valid
    private ScaleListingBaseInput scaleListingBase;

    /**
     * 分发用户配置信息
     */
    @Valid
    private ScaleListingUserConfInput userConf;

    /**
     * 分发用户信息
     */
    @Valid
    private List<ScaleListingUserRecordInput> userRecords;
}
