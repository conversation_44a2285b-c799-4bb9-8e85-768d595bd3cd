package com.wftk.scale.admin.converter;


import com.wftk.scale.admin.vo.input.scale.ScaleUserResultQueryInput;
import com.wftk.scale.biz.dto.scale.ScaleUserResultParamDTO;
import org.mapstruct.Mapper;

/**
 * @InterfaceName: ScaleUserResultConverter
 * @Description: 量表测评记录数据转换器
 * @Author: mq
 * @Date: 2024/11/20
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleUserResultConverter {

    /*
    * @Author: mq
    * @Description: 将测评记录查询信息实体转换为数据实体
    * @Date: 2024/11/20 11:05
    * @Param: queryInput-测评记录查询条件
    * @return: ScaleUserResultParamDTO
    **/
    ScaleUserResultParamDTO scaleUserResultQueryInputToParamDTO(ScaleUserResultQueryInput queryInput);

}
