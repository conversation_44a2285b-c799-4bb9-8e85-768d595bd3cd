package com.wftk.scale.admin.vo.input.scale;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * @ClassName: ScaleQuestionOptionCreateInput
 * @Description:
 * @Author: mq
 * @Date: 2024/12/14
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleQuestionOptionCreateInput {

    /**
     * 量表ID
     */
    @Schema(title = "量表ID", name = "scaleId", defaultValue = "604980089960133", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /**
     * 问题选项，就是答案
     */
    @Schema(title = "答案", name = "value", defaultValue = "很好", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "答案不能为空")
    @Length(min = 1, max = 255, message = "答案长度范围1-255个字符")
    private String value;

    /**
     * 标签: 例如（1,2,3,4; A,B,C,D）
     */
    @Schema(title = "标签", name = "label", defaultValue = "A", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "标签不能为空")
    @Length(min = 1, max = 50, message = "标签长度范围1-50个字符")
    private String label;

    /**
     * 分数
     */
    @Schema(title = "分数", name = "score", defaultValue = "4", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Max(value = Integer.MAX_VALUE, message = "分数不能超过int最大数值")
//    @NotNull(message = "分数不能为空")
    private Integer score;

    /**
     * 0.阴; 1.阳;
     */
    @Schema(title = "性质", name = "result", defaultValue = "0", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean result;

    /**
     * 顺序
     */
    @Schema(title = "排序", name = "sort", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer sort;

    /**
     * 开启其它（0.未开启，1.未开启）
     */
    @Schema(title = "开启其它", name = "enableInput", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean enableInput;

    /**
     * 备注
     */
    @Schema(title = "备注", name = "remark", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 255, message = "备注长度范围1-255个字符")
    private String remark;

    /**
     * 附件地址
     */
    @Schema(title = "附件地址", name = "filePath", defaultValue = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 255, message = "附件地址长度范围0-255个字符")
    private String filePath;

    /**
     * 偏移位置(1.上 2.下 3.左 4.右)
     */
    @Schema(title = "偏移位置", name = "offset", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer offset;

    /**
     * 操作值(转换结果)
     */
    @Schema(title = "操作值", name = "operateValue", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String operateValue;
}
