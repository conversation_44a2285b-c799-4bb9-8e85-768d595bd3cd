package com.wftk.scale.admin.converter;

import com.wftk.scale.admin.vo.input.config.TerminalCreateInput;
import com.wftk.scale.admin.vo.input.config.TerminalModifyInput;
import com.wftk.scale.biz.dto.scale.TerminalDTO;
import com.wftk.scale.biz.entity.Terminal;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @InterfaceName: TerminalConverter
 * @Description: 终端信息数据转换器
 * @Author: mq
 * @Date: 2024-11-06 16:15
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface TerminalConverter {

    /* 
     * @Author: mq
     * @Description: 将创建终端信息实体转换为数据实体 
     * @Date: 2024/11/6 16:15 
     * @Param: terminalCreateInput  
     * @return: com.wftk.scale.biz.entity.Terminal 
     **/
    Terminal terminalCreateInputToEntity(TerminalCreateInput terminalCreateInput);

    /* 
     * @Author: mq
     * @Description: 将修改终端信息实体转换为数据实体
     * @Date: 2024/11/6 16:16 
     * @Param: terminalModifyInput  
     * @return: com.wftk.scale.biz.entity.Terminal 
     **/
    Terminal terminalModifyInputToEntity(TerminalModifyInput terminalModifyInput);

    TerminalDTO entityToDTO(Terminal terminal);

    List<TerminalDTO> entityToDTO(List<Terminal> terminal);
}
