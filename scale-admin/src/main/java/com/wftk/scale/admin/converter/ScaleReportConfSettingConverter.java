package com.wftk.scale.admin.converter;


import com.wftk.scale.admin.vo.output.scale.ScaleReportConfSettingOutput;
import com.wftk.scale.biz.entity.ScaleReportConfSetting;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @InterfaceName: ScaleReportConfConverter
 * @Description: 量表报告设置数据转换器
 * @Author: mq
 * @Date: 2024/11/26
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleReportConfSettingConverter {

    /*
    * @Author: mq
    * @Description: 将创建报告设置参数信息转为数据实体
    * @Date: 2024/11/26 11:23
    * @Param: createInput
    * @return: ScaleReportConf
    **/
    List<ScaleReportConfSettingOutput> toOutput(List<ScaleReportConfSetting> settingList);

}
