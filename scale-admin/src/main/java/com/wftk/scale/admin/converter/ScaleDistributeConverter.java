package com.wftk.scale.admin.converter;

import com.wftk.scale.admin.vo.input.distribute.*;
import com.wftk.scale.biz.dto.distribute.ScaleListingBaseDTO;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordParamDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedParamDTO;
import com.wftk.scale.biz.entity.ScaleListingUserConf;
import com.wftk.scale.biz.entity.ScaleListingUserRecord;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @InterfaceName: ScaleDistributeConverter
 * @Description: 量表分发数据转换器
 * @Author: mq
 * @Date: 2024-10-30 16:59
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleDistributeConverter {

    /*
     * @Author: mq
     * @Description: 将可分发量表查询信息实体转换为数据实体
     * @Date: 2024/10/30 17:00
     * @Param: scaleDistributeQueryInput
     * @return: com.wftk.scale.biz.dto.listing.ScaleListedParamDTO
     **/
    ScaleListedParamDTO scaleDistributeQueryInputToParamDTO(ScaleDistributeQueryInput scaleDistributeQueryInput);

    /*
     * @Author: mq
     * @Description: 将量表分发记录查询参数转化为数据实体
     * @Date: 2024/12/16 11:03
     * @Param: scaleListingUserRecordInput
     * @return: ScaleListingUserRecordParamDTO
     **/
    ScaleListingUserRecordParamDTO scaleDistributeRecordQueryInputToParamDTO(ScaleListingUserRecordInput scaleListingUserRecordInput);

    /*
     * @Author: mq
     * @Description: 将量表上架基本信息转换为数据实体
     * @Date: 2024/12/16 11:05
     * @Param: scaleListingBaseInput
     * @return: ScaleListingBaseDTO
     **/
    ScaleListingBaseDTO scaleListingBaseInputToEntity(ScaleListingBaseInput scaleListingBaseInput);

    /*
     * @Author: mq
     * @Description: 将量表分发用户配置信息转换为数据实体
     * @Date: 2024/12/16 11:07
     * @Param: scaleListingUserConfInput
     * @return: ScaleListingUserConf
     **/
    ScaleListingUserConf scaleListingUserConfInputToEntity(ScaleListingUserConfInput scaleListingUserConfInput);

    /*
     * @Author: mq
     * @Description: 将量表分发用户信息转换为数据实体
     * @Date: 2024/12/16 11:08
     * @Param: distributeUserInput
     * @return: ScaleListingUserRecord
     **/
    ScaleListingUserRecord scaleDistributeUserInputToEntity(ScaleListingUserRecordInput distributeUserInput);

    /*
     * @Author: mq
     * @Description: 将量表分发用户信息转换为数据实体
     * @Date: 2024/12/16 11:09
     * @Param: userInputList
     * @return: List<ScaleListingUserRecord>
     **/
    List<ScaleListingUserRecord> scaleDistributeUserListInputToEntityList(List<ScaleListingUserRecordInput> userInputList);
}
