package com.wftk.scale.admin.vo.input.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ScaleFactorFormulaChangeEnableInput implements Serializable {

    /**
     * 公式ID(修改数据主键ID)
     */
    @Schema(title = "公式ID", name = "factorFormulaId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子公式ID不能为空")
    private Long factorFormulaId;


    @Schema(title = "启用禁用标识", name = "enabled", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用禁用标识不能为空")
    private Integer enabled;
}
