package com.wftk.scale.admin.vo.input.scale;


import com.wftk.scale.biz.dto.report.ChartSettingDTO;
import com.wftk.scale.biz.dto.report.FormSettingDTO;
import com.wftk.scale.biz.dto.report.RemarkSettingDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: ScaleReportConfSettingCreateInput
 * @Description:
 * @Author: mq
 * @Date: 2024/11/26
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleReportConfSettingCreateInput implements Serializable {

    /**
     * 报告id
     */
    @NotNull(message = "报告设置id不能为空")
    @Schema(title = "报告id", name = "reportConfId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long reportConfId;

    /**
     * 阅读须知
     */
    @Schema(title = "阅读须知", name = "readingInstruction", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String readingInstruction;

    /**
     * 备注
     */
    @Schema(title = "备注", name = "remarkSettings", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<RemarkSettingDTO> remarkSettings;

    /**
     * 表格设置
     */
    @Schema(title = "表格设置", name = "formSettings", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<FormSettingDTO> formSettings;

    /**
     * 图表设置
     */
    @Schema(title = "图表设置", name = "chartSettings", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<ChartSettingDTO> chartSettings;


}
