package com.wftk.scale.admin.converter;

import com.wftk.scale.admin.vo.input.config.TerminalConfCreateInput;
import com.wftk.scale.admin.vo.input.config.TerminalConfModifyInput;
import com.wftk.scale.biz.entity.TerminalConf;
import org.mapstruct.Mapper;

/**
 * @InterfaceName: TerminalConfConverter
 * @Description: 终端配置信息数据转换器
 * @Author: mq
 * @Date: 2024-10-29 10:13
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface TerminalConfConverter {

    /*
     * @Author: mq
     * @Description:  将创建终端配置信息实体转换为数据实体
     * @Date: 2024/10/29 10:15
     * @Param: terminalConfCreateInput
     * @return: com.wftk.scale.biz.entity.TerminalConf
     **/
    TerminalConf terminalConfCreateInputToEntity(TerminalConfCreateInput terminalConfCreateInput);

    /*
     * @Author: mq
     * @Description:  将修改终端配置信息实体转换为数据实体
     * @Date: 2024/10/29 10:15
     * @Param: terminalConfModifyInput
     * @return: com.wftk.scale.biz.entity.TerminalConf
     **/
    TerminalConf terminalConfModifyInputToEntity(TerminalConfModifyInput terminalConfModifyInput);

}
