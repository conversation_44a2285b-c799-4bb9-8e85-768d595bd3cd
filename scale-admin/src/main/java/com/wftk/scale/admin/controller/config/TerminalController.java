package com.wftk.scale.admin.controller.config;

/**
 * @ClassName: TerminalController
 * @Description: 路由终端信息
 * @Author: mq
 * @Date: 2024-11-06 15:51
 * @Version: 1.0
 **/

import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.TerminalConverter;
import com.wftk.scale.admin.vo.input.config.TerminalCreateInput;
import com.wftk.scale.admin.vo.input.config.TerminalModifyInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.dto.dict.DictDTO;
import com.wftk.scale.biz.dto.scale.TerminalDTO;
import com.wftk.scale.biz.entity.SystemTag;
import com.wftk.scale.biz.entity.Terminal;
import com.wftk.scale.biz.entity.TerminalConf;
import com.wftk.scale.biz.service.DictService;
import com.wftk.scale.biz.service.TerminalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName: TerminalConfController
 * @Description: 终端(需求模糊, 待整理)
 * @Author: mq
 * @Date: 2024-10-24 18:04
 * @Version: 1.0
 **/
@Tag(name = "终端信息参数配置API")
@RestController
@AdminMapping("conf/scale/terminal")
@Slf4j
public class TerminalController {

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private TerminalConverter terminalConverter;


    @Operation(summary = "获取终端信息列表数据-全部")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "获取终端信息列表数据-全部")
    @GetMapping("datas")
    public ApiResult<List<TerminalDTO>> getAll(@RequestParam(required = false) String terminalName) {
        return ApiResult.ok(terminalService.getListOfEnabled(terminalName));
    }

    @Operation(summary = "获取终端信息列表数据-未绑定机构")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "获取终端信息列表数据-未绑定机构")
    @GetMapping("getNotBindingDepartmentList")
    public ApiResult<List<TerminalDTO>> getNotBindingDepartmentList() {
        return ApiResult.ok(terminalService.getNotBindingDepartmentList());
    }



    @Operation(summary = "获取终端信息列表数据-分页")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "获取终端信息列表数据-分页")
    @GetMapping
    public ApiResult<Page<TerminalDTO>> selectPage(@RequestParam(required = false) String terminalName) {
        return ApiResult.ok(terminalService.selectPage(terminalName));
    }

    @Operation(summary = "创建终端信息")
    @OptLog(module = "参数配置模块", optType = OptType.CREATE, description = "创建终端信息")
    @PostMapping
    public ApiResult<String> create(@Valid @RequestBody TerminalCreateInput input) {
        Terminal terminal = terminalConverter.terminalCreateInputToEntity(input);
        terminalService.create(terminal);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "修改终端信息")
    @OptLog(module = "参数配置模块", optType = OptType.MODIFY, description = "修改终端信息")
    @PutMapping
    public ApiResult<String> modify(@Valid @RequestBody TerminalModifyInput input) {
        Terminal terminal = terminalConverter.terminalModifyInputToEntity(input);
        terminalService.modify(terminal);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "删除终端信息")
    @OptLog(module = "参数配置模块", optType = OptType.CREATE, description = "删除终端信息")
    @DeleteMapping
    public ApiResult<String> delete(Long terminalId) {
        if (ObjUtil.isNull(terminalId)) {
            throw new BusinessException("删除失败,终端信息ID不允许为空!");
        }
        terminalService.delete(terminalId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "更新终端数据禁用状态")
    @OptLog(module = "参数配置模块", optType = OptType.MODIFY, description = "更新终端数据禁用状态")
    @PutMapping("status")
    public ApiResult<String> updateEnabled(Long terminalId, Boolean enabled) {
        if (ObjUtil.isNull(terminalId)) {
            throw new BusinessException("状态更新失败,终端信息ID不允许为空!");
        }
        terminalService.updateEnable(terminalId, enabled);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "根据ID获取终端详情信息")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "根据ID获取终端详情信息")
    @GetMapping("detail")
    public ApiResult<Terminal> detail(Long terminalId){
        if (ObjUtil.isNull(terminalId)) {
            throw new BusinessException("获取详情失败,终端信息ID不允许为空!");
        }
        return ApiResult.ok(terminalService.getById(terminalId));
    }
}
