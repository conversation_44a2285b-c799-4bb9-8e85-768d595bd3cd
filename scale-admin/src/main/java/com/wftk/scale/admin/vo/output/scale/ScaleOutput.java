package com.wftk.scale.admin.vo.output.scale;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/12/3 15:53
 */
@Data
public class ScaleOutput {

    private Long id;

    /**
     * 量表提供商代码: SYSTEM代表本系统
     */
    private String provider;

    /**
     * 量表厂商名称
     */
    private String providerName;

    /**
     * 量表类型，来源于scale_type表的主键
     */
    private Long type;

    /**
     * 量表编码
     */
    private String code;

    /**
     * 封面图url
     */
    @RFile(role = FileConstant.FILE_SCALE_PUB_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    private String cover;

    /**
     * 量表名称
     */
    private String name;

    /**
     * 版本号(hash值，实现过程中通过指定内容生成hash，hash值有变动认为数据有修改，此时新写入1条记录，量表编码不变)
     */
    private String version;

    /**
     * 简介
     */
    private String intro;

    /**
     * 指导语
     */
    private String guideline;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 限时秒数最小值
     */
    private Integer minTimeLimit;

    /**
     * 限时秒数最大值
     */
    private Integer maxTimeLimit;

    /**
     * 介绍
     */
    private String description;

    /**
     * 0未完成，1已完成，问题，因子，解读等没完成之前是未完成
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

}
