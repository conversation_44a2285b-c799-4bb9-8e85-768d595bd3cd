package com.wftk.scale.admin.ext.auth;

import com.wftk.scale.biz.entity.SysUser;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;

/**
 * <AUTHOR>
 * @create 2024/12/4 16:06
 */
public class AdminAuthUser extends AuthUser<SysUser> {

    @JsonCreator
    public AdminAuthUser(@JsonProperty("user") SysUser user) {
        super(user);
    }

    @Override
    public String getTenantId() {
        return null;
    }

    @Override
    public Long getId() {
        return getUser().getId();
    }

    @Override
    public String getAccount() {
        return getUser().getAccount();
    }

    @Override
    public String getPassword() {
        return getUser().getPassword();
    }

    @Override
    public boolean isDisabled() {
        return getUser().getEnable() == null || !getUser().getEnable();
    }
}
