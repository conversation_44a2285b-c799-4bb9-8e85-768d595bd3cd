package com.wftk.scale.admin.controller.scale;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleConverter;
import com.wftk.scale.admin.vo.input.scale.ScaleCopyInput;
import com.wftk.scale.admin.vo.input.scale.ScaleCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleModifyInput;
import com.wftk.scale.admin.vo.output.scale.ScaleOutput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.constant.enums.ScaleEnum;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.entity.Scale;
import com.wftk.scale.biz.entity.ScaleListing;
import com.wftk.scale.biz.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @createDate 2024/10/24 16:58
 */
@Tag(name = "单个量表维护相关API")
@RestController
@AdminMapping("/scale/single/base")
@Slf4j
public class ScaleController {

    @Autowired
    private ScaleService scaleService;

    @Autowired
    private ScaleQuestionService scaleQuestionService;

    @Autowired
    private ScaleFactorService scaleFactorService;

    @Autowired
    private ScaleResultIntroService scaleResultIntroService;

    @Autowired
    private ScaleReportConfService scaleReportConfService;

    @Autowired
    private ScaleConverter scaleConverter;

    @Operation(summary = "获取单个量表最新版本数据")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取单个量表最新版本数据")
    @GetMapping
    public ApiResult<Page<ScaleQueryDTO>> selectPage(@RequestParam(required = false) String name, @RequestParam(required = false) Integer status) {
        return ApiResult.ok(scaleService.selectScalePage(name, status));
    }

    @Operation(summary = "创建量表基本信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "创建量表基本信息")
    @PostMapping
    public ApiResult<String> create(@Valid @RequestBody ScaleCreateInput input) {
        boolean result = scaleService.vaildScaleTimeRange(input.getMinTimeLimit(), input.getMaxTimeLimit());
        if (result) {
            throw new BusinessException("创建失败,量表限时区间设置不合理!");
        }
        result = scaleService.validScaleName(null, input.getName());
        if (result) {
            throw new BusinessException("创建失败,量表名称已存在!");
        }
        Scale scale = scaleConverter.scaleCreateInputToEntity(input);
        scaleService.create(scale);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "修改量表基本信息")
    @OptLog(module = "量表管理模块", optType = OptType.MODIFY, description = "修改量表基本信息")
    @PutMapping
    public ApiResult<String> modify(@Valid @RequestBody ScaleModifyInput input) {
        Long scaleId = input.getId();
        boolean result = scaleService.vaildCompletedStatus(scaleId);
        if (result) {
            throw new BusinessException("修改失败,已完成的量表无法进行编辑操作!");
        }
        result = scaleService.validScaleName(scaleId, input.getName());
        if (result) {
            throw new BusinessException("修改失败,量表名称已存在!");
        }
        Scale scale = scaleConverter.scaleModifyInputToEntity(input);
        scaleService.modify(scale);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }


    @Operation(summary = "删除量表信息")
    @OptLog(module = "量表管理模块", optType = OptType.DELETE, description = "删除量表信息")
    @DeleteMapping
    public ApiResult<String> delete(Long scaleId) {
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("删除失败,量表scaleId参数不允许为空!");
        }
        boolean result = scaleService.vaildCompletedStatus(scaleId);
        if (result) {
            throw new BusinessException("删除失败,已完成的量表无法进行删除操作!");
        }
        scaleService.delete(scaleId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "更新量表数据完成状态")
    @OptLog(module = "量表管理模块", optType = OptType.MODIFY, description = "更新量表数据完成状态")
    @PutMapping("status")
    public ApiResult<String> updateStatus(Long scaleId, Integer status) {
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("更新失败,量表scaleId参数不允许为空!");
        }
        boolean isCompleted = ScaleEnum.SCALE_COMPLETED.getCode().equals(status);
        boolean result = scaleQuestionService.vaildQuestionDataIntegrity(scaleId);
        if (!result && isCompleted) {
            throw new BusinessException("更新失败,量表题目内容不完整!");
        }
        result = scaleFactorService.vaildFactorDataIntegrity(scaleId);
        if (!result && isCompleted) {
            throw new BusinessException("更新失败,量表因子维度不完整!");
        }
        result = scaleResultIntroService.vaildResultIntroDataIntegrity(scaleId);
        if (!result && isCompleted) {
            throw new BusinessException("更新失败,量表解读内容不完整!");
        }
        result = scaleReportConfService.validReportConfDataIntegrity(scaleId);
        if (!result && isCompleted) {
            throw new BusinessException("更新失败,量表报告内容不完整!");
        }
        scaleService.updateComplateStatus(scaleId, status);
        return ApiResult.ok("修改成功!");
    }

    @Operation(summary = "复制量表基本信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "复制量表基本信息")
    @PostMapping("copy")
    public ApiResult<String> copy(@RequestBody ScaleCopyInput scaleCopyInput) {
        Long scaleId = scaleCopyInput.getScaleId();
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("复制失败,量表scaleId参数不允许为空!");
        }
        scaleService.copyScale(scaleId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "根据ID获取量表详情信息")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据ID获取量表详情信息")
    @GetMapping("detail")
    public ApiResult<ScaleOutput> detail(Long scaleId){
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("获取失败,量表scaleId参数不允许为空!");
        }
        Scale scale = scaleService.getById(scaleId);
        return ApiResult.ok(scaleConverter.entityToOutput(scale));
    }
}
