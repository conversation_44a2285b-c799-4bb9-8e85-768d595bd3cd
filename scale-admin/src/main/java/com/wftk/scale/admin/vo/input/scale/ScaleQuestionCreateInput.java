package com.wftk.scale.admin.vo.input.scale;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @ClassName: ScaleQuestionCreateInput
 * @Description: 创建量表题目信息
 * @Author: mq
 * @Date: 2024-11-01 16:34
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleQuestionCreateInput implements Serializable {

    /**
     * 题号
     */
    @Schema(title = "题号", name = "questionNumber", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "题号不能为空")
    @Length(min = 1, max = 20, message = "题号长度范围1-20个字符")
    private String questionNumber;

    /**
     * 子题号
     */
    @Schema(title = "子题号", name = "subNumber", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 20, message = "字题号长度范围1-20个字符")
    private String subNumber;

    /**
     * 量表ID
     */
    @Schema(title = "量表ID", name = "scaleId", defaultValue = "604980089960133", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /**
     * 排序
     */
    @Schema(title = "排序", name = "sort", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer sort;

    /**
     * 问题,富文本
     */
    @Schema(title = "问题", name = "question", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "问题不能为空")
    @Length(min = 1, max = 512, message = "问题长度范围1-512个字符")
    private String question;

    /**
     * 题干数据类型: TEXT.普通文本; 不明确字段作用 前端传值与question的值一样
     */
    @Schema(title = "题干数据类型", name = "dataType", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 1, max = 512, message = "题干数据类型长度范围1-512个字符")
    private String dataType;

    /**
     * 题型，1单选，2单选其他，3多选，4单行输入
     */
    @Schema(title = "题型", name = "type", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "题型不能为空")
    private Integer type;

    /**
     * 1必答，0不必答
     */
    @Schema(title = "是否必答", name = "requireAnswer", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否必答不能为空")
    private Boolean requireAnswer;

    /**
     * 附件地址
     */
    @Schema(title = "附件地址", name = "filePath", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String filePath;

    /**
     * 偏移位置(1.上 2.下 3.左 4.右)
     */
    @Schema(title = "偏移位置", name = "offset", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer offset;


    /**
     * 计分类型：1.正向计分 2.反向计分
     */
    @Schema(title = "计分类型", name = "scoringType", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotNull(message = "计分类型不能为空")
    private Integer scoringType;
}
