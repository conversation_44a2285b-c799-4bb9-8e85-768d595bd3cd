package com.wftk.scale.admin.controller.config;

import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.TerminalConfConverter;
import com.wftk.scale.admin.vo.input.config.TerminalConfCreateInput;
import com.wftk.scale.admin.vo.input.config.TerminalConfModifyInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.entity.SystemTag;
import com.wftk.scale.biz.entity.TerminalConf;
import com.wftk.scale.biz.service.TerminalConfService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName: TerminalConfController
 * @Description: 终端配置(需求模糊, 待整理)
 * @Author: mq
 * @Date: 2024-10-24 18:04
 * @Version: 1.0
 **/
@Tag(name = "终端设置参数配置API")
@RestController
@AdminMapping("conf/scale/terminal/conf")
@Slf4j
public class TerminalConfController {

    @Autowired
    private TerminalConfService terminalConfService;

    @Autowired
    private TerminalConfConverter terminalConfConverter;

    @Operation(summary = "获取终端配置信息列表数据")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "获取终端配置信息列表数据")
    @GetMapping
    public ApiResult<Page<TerminalConf>> selectPage(@RequestParam(required = false) String terminalName,
                                   @RequestParam(required = false) Integer terminalType,
                                   @RequestParam(required = false) Long userId) {

        return ApiResult.ok(terminalConfService.selectPage(terminalName, terminalType, userId));
    }

    @Operation(summary = "创建终端配置信息")
    @OptLog(module = "参数配置模块", optType = OptType.CREATE, description = "创建终端配置信息")
    @PostMapping
    public ApiResult<String> create(@Valid @RequestBody TerminalConfCreateInput input) {
        String itemCode = input.getItemCode();
        String itemValue = input.getItemValue();
        boolean result = terminalConfService.validTerminalCodeAndValue(null, itemCode, itemValue);
        if (result) {
            throw new BusinessException("创建失败,终端配置信息已存在!");
        }
        TerminalConf terminalConf = terminalConfConverter.terminalConfCreateInputToEntity(input);
        terminalConfService.create(terminalConf);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "修改终端配置信息")
    @OptLog(module = "参数配置模块", optType = OptType.MODIFY, description = "修改终端配置信息")
    @PutMapping
    public ApiResult<String> modify(@Valid @RequestBody TerminalConfModifyInput input) {
        Long id = input.getId();
        String itemCode = input.getItemCode();
        String itemValue = input.getItemValue();
        boolean result = terminalConfService.validTerminalCodeAndValue(id, itemCode, itemValue);
        if (result) {
            throw new BusinessException("修改失败,终端配置信息已存在!");
        }
        TerminalConf terminalConf = terminalConfConverter.terminalConfModifyInputToEntity(input);
        terminalConfService.modify(terminalConf);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "删除终端配置信息")
    @OptLog(module = "参数配置模块", optType = OptType.CREATE, description = "删除终端配置信息")
    @DeleteMapping
    public ApiResult<String> delete(Long id) {

        if (ObjUtil.isNull(id)) {
            throw new BusinessException("删除失败,终端配置信息ID不允许为空!");
        }
        terminalConfService.delete(id);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "更新终端配置数据禁用状态")
    @OptLog(module = "参数配置模块", optType = OptType.MODIFY, description = "更新终端配置数据禁用状态")
    @PutMapping("status")
    public ApiResult<String> updateEnabled(Long id, Boolean enabled) {
        if (ObjUtil.isNull(id)) {
            throw new BusinessException("状态更新失败,终端配置信息ID不允许为空!");
        }
        terminalConfService.updateEnable(id, enabled);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "根据ID获取终端配置详情信息")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "根据ID获取终端配置详情信息")
    @GetMapping("detail")
    public ApiResult<TerminalConf> detail(Long id){
        if (ObjUtil.isNull(id)) {
            throw new BusinessException("获取详情失败,终端配置信息ID不允许为空!");
        }
        return ApiResult.ok(terminalConfService.getById(id));
    }
}
