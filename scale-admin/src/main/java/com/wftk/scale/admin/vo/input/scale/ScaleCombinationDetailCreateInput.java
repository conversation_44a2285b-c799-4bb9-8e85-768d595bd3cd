package com.wftk.scale.admin.vo.input.scale;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: ScaleCombinationDetailCreateInput
 * @Description:
 * @Author: mq
 * @Date: 2024/12/16
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleCombinationDetailCreateInput implements Serializable {

    /**
     * 量表记录id
     */
    @Schema(title = "量表记录ID", name = "scaleId", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表记录scaleId方式不能为空")
    private Long scaleId;
}
