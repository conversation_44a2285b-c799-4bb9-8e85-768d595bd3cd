package com.wftk.scale.admin.config;

import com.wftk.scale.admin.ext.tenant.AuthUserTenantLoader;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2024/10/24 11:38
 */
@Configuration
@ComponentScan(basePackages = "com.wftk.scale.admin")
public class AppConfig {

    @Bean
    AuthUserTenantLoader authUserTenantLoader() {
        return new AuthUserTenantLoader();
    }

}
