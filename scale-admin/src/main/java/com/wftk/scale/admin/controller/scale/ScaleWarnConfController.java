package com.wftk.scale.admin.controller.scale;

import cn.hutool.core.util.StrUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleWarnConfConverter;
import com.wftk.scale.admin.vo.input.scale.ScaleWarnConfCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleWarnConfModifyInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfCreateDTO;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfDTO;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfModifyDTO;
import com.wftk.scale.biz.service.ScaleWarnConfService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName: ScaleWarnConfController
 * @Description: 量表预警阈值信息管理
 * @Author: mq
 * @Date: 2024-11-05 17:11
 * @Version: 1.0
 **/
@Tag(name = "量表预警阈值相关API")
@RestController
@AdminMapping("/scale/warn")
@Slf4j
public class ScaleWarnConfController {

    @Autowired
    private ScaleWarnConfService scaleWarnConfService;

    @Autowired
    private ScaleWarnConfConverter scaleWarnConfConverter;

    @Operation(summary = "获取量表预警阈值信息列表数据")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取量表预警阈值信息列表数据")
    @GetMapping
    public ApiResult<Page<ScaleWarnConfDTO>> selectPage(Long scaleId) {
        Page<ScaleWarnConfDTO> warnConfDTOPage = scaleWarnConfService.selectPage(scaleId).toPage(list -> scaleWarnConfService.getScaleWarnConfDTOList(list));
        return ApiResult.ok(warnConfDTOPage);
    }

    @Operation(summary = "创建量表预警阈值信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "创建量表预警阈值信息")
    @PostMapping
    public ApiResult<String> create(@Valid @RequestBody ScaleWarnConfCreateInput input) {
        ScaleWarnConfCreateDTO scaleWarnConfCreateDTO = scaleWarnConfConverter.scaleWarnConfCreateInputToScaleWarnConf(input);
        scaleWarnConfService.create(scaleWarnConfCreateDTO);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "修改量表预警阈值信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "修改量表预警阈值信息")
    @PutMapping
    public ApiResult<String> modify(@Valid @RequestBody ScaleWarnConfModifyInput input) {
        ScaleWarnConfModifyDTO scaleWarnConfCreateDTO = scaleWarnConfConverter.scaleWarnConfModifyInputToScaleWarnConf(input);
        scaleWarnConfService.modify(scaleWarnConfCreateDTO);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "删除量表预警阈值信息")
    @OptLog(module = "参数配置模块", optType = OptType.DELETE, description = "删除量表预警阈值信息")
    @DeleteMapping
    public ApiResult<String> delete(String batchNo) {
        if (StrUtil.isBlank(batchNo)) {
            throw new BusinessException("删除失败,预警阈值ID不允许为空!");
        }
        scaleWarnConfService.delete(batchNo);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "根据ID获取预警阈值详情信息")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据ID获取预警阈值详情信息")
    @GetMapping("detail")
    public ApiResult<ScaleWarnConfDTO> detail(String batchNo) {
        if (StrUtil.isBlank(batchNo)) {
            throw new BusinessException("删除失败,预警阈值ID不允许为空!");
        }
        return ApiResult.ok(scaleWarnConfService.detail(batchNo));
    }
}
