package com.wftk.scale.admin.vo.input.scale;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @ClassName: ScaleFactorModifyInput
 * @Description: 修改量表因子维度信息
 * @Author: mq
 * @Date: 2024-11-05 14:25
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleFactorModifyInput implements Serializable {

    /**
     * 因子维度ID(修改数据主键ID)
     */
    @Schema(title = "因子维度ID", name = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子维度ID不能为空")
    private Long id;

    /**
     * 量表ID
     */
    @Schema(title = "量表ID", name = "scaleId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long scaleId;

    /**
     * 因子名称
     */
    @Schema(title = "因子名称", name = "name", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子名称不能为空")
    @Length(min = 1, max = 255, message = "因子名称长度范围1-255个字符")
    private String name;

    /**
     * 因子类型 1.总分因子 2.其他因子
     */
    @Schema(title = "因子类型", name = "type", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子类型不能为空")
    private Integer type;

    /**
     * 顺序
     */
    @Schema(title = "因子排序", name = "sort", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Min(value = 1, message = "因子排序不能小于1")
    @Max(value = Integer.MAX_VALUE, message = "因子排序不能超过int最大数值")
    private Integer sort;

    /**
     * 题目ID|隔开
     */
    @Schema(title = "题目ID|隔开", name = "questionId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "题目ID不能为空")
    private String questionId;

    /**
     * 运算公式描述
     */
    @Schema(title = "运算公式描述", name = "formulaLabel", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "运算公式描述不能为空")
    private String formulaLabel;

    /**
     * 运算公式
     */
    @Schema(title = "运算公式", name = "formula", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "运算公式不能为空")
    private String formula;

    /**
     * 公式值
     */
    @Schema(title = "公式值", name = "formulaParam", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String formulaParam;


    /**
     * 租户ID
     */
    @Schema(title = "租户ID", name = "tenantId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tenantId ;
}
