package com.wftk.scale.admin.controller.scale;

import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleReportConfItemConverter;
import com.wftk.scale.admin.vo.input.scale.ScaleReportConfItemCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleReportConfItemModifyInput;
import com.wftk.scale.admin.vo.output.scale.ScaleReportConfItemOutput;
import com.wftk.scale.biz.entity.ScaleReportConfItem;
import com.wftk.scale.biz.service.ScaleReportConfItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName: ScaleReportConfItemController
 * @Description: 量表报告设置
 * @Author: mq
 * @Date: 2024-11-06 13:36
 * @Version: 1.0
 **/
@Tag(name = "量表报告设置相关API")
@AdminMapping("/scale/report/item")
@Slf4j
public class ScaleReportConfItemController {

    @Autowired
    private ScaleReportConfItemService scaleReportConfItemService;

    @Autowired
    private ScaleReportConfItemConverter scaleReportConfItemConverter;

    @Operation(summary = "创建报告设置项")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "创建报告设置项")
    @PostMapping
    public ApiResult<Void> create(@Valid @RequestBody ScaleReportConfItemCreateInput input) {
        ScaleReportConfItem reportConf = scaleReportConfItemConverter.scaleReportConfItemCreateInputToEntity(input);
        scaleReportConfItemService.create(reportConf);
        return ApiResult.ok();
    }

    @Operation(summary = "修改报告设置项")
    @OptLog(module = "量表管理模块", optType = OptType.MODIFY, description = "修改报告设置项")
    @PutMapping
    public ApiResult<Void> modify(@Valid @RequestBody ScaleReportConfItemModifyInput input) {
        ScaleReportConfItem reportConf = scaleReportConfItemConverter.scaleReportConfItemModifyInputToEntity(input);
        scaleReportConfItemService.modify(reportConf);
        return ApiResult.ok();
    }

    @Operation(summary = "根据报告设置ID获取报告项信息列表")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据报告设置ID获取报告项信息列表")
    @GetMapping("list")
    public ApiResult<List<ScaleReportConfItemOutput>> list(@RequestParam("reportConfId") Long reportConfId,@RequestParam("itemCodes") List<String> itemCodes){
        List<ScaleReportConfItem> scaleReportConfItems = scaleReportConfItemService.findByReportConfId(reportConfId, itemCodes);
        return ApiResult.ok(scaleReportConfItemConverter.entityToOutput(scaleReportConfItems));
    }

    @Operation(summary = "删除报告设置项")
    @OptLog(module = "量表管理模块", optType = OptType.DELETE, description = "删除报告设置项")
    @DeleteMapping
    public ApiResult<Void> delete(@RequestParam("scaleReportConfItemId")Long scaleReportConfItemId) {
        scaleReportConfItemService.delete(scaleReportConfItemId);
        return ApiResult.ok();
    }

    @Operation(summary = "根据ID获取报告项目信息")
    @GetMapping
    public ApiResult<ScaleReportConfItemOutput> getById(@RequestParam("id") Long id){
        ScaleReportConfItem scaleReportConfItem = scaleReportConfItemService.getById(id);
        return ApiResult.ok(scaleReportConfItemConverter.entityToOutput(scaleReportConfItem));
    }
}
