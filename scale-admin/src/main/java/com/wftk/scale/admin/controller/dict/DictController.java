package com.wftk.scale.admin.controller.dict;

import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.dto.dict.DictDTO;
import com.wftk.scale.biz.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Tag(name = "字典相关API")
@RestController
@AdminMapping("/dict")
@Slf4j
public class DictController {

    @Autowired
    private DictService dictService;

    @Operation(summary = "获取字典列表数据")
    @OptLog(module = "字典管理模块", optType = OptType.QUERY, description = "获取字典列表数据")
    @GetMapping("/list")
    public ApiResult<List<DictDTO>> getList(@RequestParam(value = "code") String code) {
        if (ObjUtil.isNull(code)) {
            throw new BusinessException("获取失败,字典code不允许为空!");
        }
        return ApiResult.ok(dictService.findByCode(code));
    }
}
