package com.wftk.scale.admin.controller.order;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.constant.PaymentErrorCode;
import com.wftk.scale.biz.dto.order.AdminOrderQueryDto;
import com.wftk.scale.biz.dto.order.OrderQueryDTO;
import com.wftk.scale.biz.ext.wechat.core.WechatClient;
import com.wftk.scale.biz.ext.wechat.enums.WechatPaySceneEnum;
import com.wftk.scale.biz.entity.WechatPaySetting;
import com.wftk.scale.biz.ext.wechat.core.WechatClientFactory;
import com.wftk.scale.biz.ext.wechat.input.WechatPrepayInput;
import com.wftk.scale.biz.service.OrderService;
import com.wftk.scale.biz.service.WechatPaySettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import java.util.Map;

/**
 * @ClassName: ScaleListingCombinationController
 * @Description: 组合量表上下架管理
 * @Author: mq
 * @Date: 2024-10-25 16:47
 * @Version: 1.0
 **/
@Tag(name = "订单相关API")
@AdminMapping("/order")
@Slf4j
public class OrderController {

    @Autowired
    OrderService orderService;
    @Autowired
    WechatClientFactory wechatClientFactory;
    @Autowired
    WechatPaySettingService wechatPaySettingService;

    @Operation(summary = "获取订单列表数据")
    @OptLog(module = "订单管理", optType = OptType.QUERY, description = "获取订单列表数据")
    @GetMapping("page")
    public ApiResult<Page<OrderQueryDTO>> page(AdminOrderQueryDto queryDto) {
        return ApiResult.ok(orderService.getList(queryDto));
    }

    @Operation(summary = "获取订单详情")
    @OptLog(module = "订单管理", optType = OptType.QUERY, description = "获取订单详情")
    @GetMapping("detail")
    public ApiResult<OrderQueryDTO> detail(AdminOrderQueryDto queryDto) {
        return ApiResult.ok(orderService.detailById(queryDto));
    }

    @Operation(summary = "删除")
    @OptLog(module = "订单管理", optType = OptType.DELETE, description = "删除")
    @DeleteMapping("delete")
    public ApiResult<Void> delete(Long id) {
        if(id == null){
            throw new BusinessException("参数id不能为空");
        }
        orderService.deleteOrder(id);
        return ApiResult.ok();
    }



    /**
     * 预支付
     *
     * @return
     */
    @GetMapping("/doPrepay")
    public WechatPrepayOutput doPrepay() {
        PrepayInput prepayInput = new PrepayInput();
        prepayInput.setScene(WechatPaySceneEnum.H5);
        //step1.获取当前接入方的微信配置项并校验是否支持当前支付场景
        WechatPaySetting setting = getValidSetting(2L, prepayInput.getScene());
        WechatPrepayInput wechatPrepayInput = new WechatPrepayInput();
        wechatPrepayInput.setAmount(1);
        wechatPrepayInput.setOrderNo(IdUtil.getSnowflakeNextIdStr());
        wechatPrepayInput.setIp("**************");
        wechatPrepayInput.setAppId(setting.getAppId());
        wechatPrepayInput.setSubject("测试");
        wechatPrepayInput.setType("Android");
        wechatPrepayInput.setMchId(setting.getMchId());


        //发起微信支付获取prepayId
        WechatClient wechatClient = wechatClientFactory.get(setting);

        String paymentParams;
        try {
            switch (prepayInput.getScene()) {
                case H5 -> {
                    wechatPrepayInput.setNotifyUrl("http://**************:17520/payment/callback/v1/pay/h5");
                    paymentParams = wechatClient.h5Prepay(wechatPrepayInput);
                }
                case Native -> {
                    wechatPrepayInput.setNotifyUrl("http://**************:17520/payment/callback/v1/pay/h5");
                    paymentParams = wechatClient.nativePrepay(wechatPrepayInput);
                }
                default -> throw new BusinessException(PaymentErrorCode.UNSUPPORTED_SCENE);
            }
        } catch (Exception e) {
            log.error("wechat pay error.", e);
            throw new BusinessException(PaymentErrorCode.PAY_ERROR);
        }

        Map<String, Object> paymentParamsMap = JSONObject.getInstance().parseMap(paymentParams, String.class, Object.class);
        return new WechatPrepayOutput(wechatPrepayInput.getOrderNo(), paymentParamsMap);
    }


    private WechatPaySetting getValidSetting(Long clientId, @Nullable WechatPaySceneEnum sceneEnum) {
        WechatPaySetting setting = wechatPaySettingService.getById(clientId);
        if (setting == null || !setting.getEnabled()) {
            throw new BusinessException(PaymentErrorCode.UNSUPPORTED_SCENE);
        }
        if (sceneEnum != null) {
            String sceneCode = setting.getSceneCode();
            if (StrUtil.isBlank(sceneCode) || !sceneCode.toLowerCase().contains(sceneEnum.name().toLowerCase())) {
                throw new BusinessException(PaymentErrorCode.UNSUPPORTED_SCENE);
            }
        }
        return setting;
    }

}
