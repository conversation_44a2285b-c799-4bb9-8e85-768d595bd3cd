package com.wftk.scale.admin.converter;

import com.wftk.scale.admin.vo.input.config.ScaleFactorFormulaCreateInput;
import com.wftk.scale.admin.vo.input.config.ScaleFactorFormulaModifyInput;
import com.wftk.scale.biz.entity.ScaleFactorFormula;
import org.mapstruct.Mapper;

/**
 * @InterfaceName: ScaleFactorFormulaConverter
 * @Description: 因子公式数据转换器
 * @Author: mq
 * @Date: 2024-11-01 11:37
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleFactorFormulaConverter {

    /* 
     * @Author: mq
     * @Description: 将创建因子公式参数信息转为数据实体 
     * @Date: 2024/11/1 11:38 
     * @Param: input  
     * @return: com.wftk.scale.biz.entity.ScaleFactorFormula 
     **/
    ScaleFactorFormula scaleFactorFormulaCreateInputToEntity(ScaleFactorFormulaCreateInput input);

    /* 
     * @Author: mq
     * @Description: 将修改因子公式参数信息转为数据实体
     * @Date: 2024/11/1 11:39
     * @Param: input  
     * @return: com.wftk.scale.biz.entity.ScaleFactorFormula 
     **/
    ScaleFactorFormula ScaleFactorFormulaModifyInputToEntity(ScaleFactorFormulaModifyInput input);
    
}



