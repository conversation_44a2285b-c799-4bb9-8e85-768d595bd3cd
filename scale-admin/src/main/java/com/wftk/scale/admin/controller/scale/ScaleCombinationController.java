package com.wftk.scale.admin.controller.scale;

import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleCombinationConverter;
import com.wftk.scale.admin.vo.input.scale.ScaleCombinationCopyInput;
import com.wftk.scale.admin.vo.input.scale.ScaleCombinationCreateBaseInput;
import com.wftk.scale.admin.vo.input.scale.ScaleCombinationModifyBaseInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.dto.scale.ScaleCombinationQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.entity.ScaleCombination;
import com.wftk.scale.biz.entity.ScaleCombinationDetail;
import com.wftk.scale.biz.service.ScaleCombinationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName: ScaleCombinationController
 * @Description: 组合量表信息管理
 * @Author: mq
 * @Date: 2024-11-06 16:23
 * @Version: 1.0
 **/
@Tag(name = "组合量表维护相关API")
@RestController
@AdminMapping("/scale/combination/base")
@Slf4j
public class ScaleCombinationController {

    @Autowired
    private ScaleCombinationService scaleCombinationService;

    @Autowired
    private ScaleCombinationConverter scaleCombinationConverter;

    @Operation(summary = "获取组合量表最新版本数据")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取组合量表最新版本数据")
    @GetMapping
    public ApiResult<Page<ScaleCombinationQueryDTO>> selectPage(@RequestParam(required = false) String name) {
        return ApiResult.ok(scaleCombinationService.selectScalePage(name));
    }

    @Operation(summary = "获取组合量表可配置的量表数据")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取组合量表可配置的量表数据")
    @GetMapping("config")
    public ApiResult<List<ScaleQueryDTO>> selectPage(Long combinationId) {
        return ApiResult.ok(scaleCombinationService.selectScaleList(combinationId));
    }

    @Operation(summary = "创建组合量表基本信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "创建组合量表基本信息")
    @PostMapping
    public ApiResult<String> create(@Valid @RequestBody ScaleCombinationCreateBaseInput input) {
        ScaleCombination combination = scaleCombinationConverter.scaleCombinationCreateInputToEntity(input.getScaleCombination());
        List<ScaleCombinationDetail> detailList = scaleCombinationConverter.scaleCombinationDetailCreateListToEntityList(input.getScaleIds());
        boolean result = scaleCombinationService.vaildScaleConfDetails(detailList);
        if (!result) {
            throw new BusinessException("创建失败,组合量表未配置量表信息或配置项重复!");
        }
        result = scaleCombinationService.validScaleName(null, input.getScaleCombination().getName());
        if (result) {
            throw new BusinessException("创建失败,组合量表名称已存在!");
        }
        scaleCombinationService.create(combination, detailList);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "修改量表基本信息")
    @OptLog(module = "量表管理模块", optType = OptType.MODIFY, description = "修改量表基本信息")
    @PutMapping
    public ApiResult<String> modify(@Valid @RequestBody ScaleCombinationModifyBaseInput input) {
        ScaleCombination combination = scaleCombinationConverter.scaleCombinationModifyInputToEntity(input.getScaleCombination());
        List<ScaleCombinationDetail> detailList = scaleCombinationConverter.scaleCombinationDetailModifyListToEntityList(input.getScaleIds());
        boolean result = scaleCombinationService.vaildScaleConfDetails(detailList);
        if (!result) {
            throw new BusinessException("修改失败,组合量表未配置量表信息或配置项重复!");
        }
        result = scaleCombinationService.vaildCompletedStatus(combination.getId());
        if (result) {
            throw new BusinessException("修改失败,已完成的量表无法进行编辑操作!");
        }
        result = scaleCombinationService.validScaleName(combination.getId(), combination.getName());
        if (result) {
            throw new BusinessException("修改失败,组合量表名称已存在!");
        }
        scaleCombinationService.modify(combination, detailList);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "删除组合量表信息")
    @OptLog(module = "量表管理模块", optType = OptType.DELETE, description = "删除组合量表信息")
    @DeleteMapping
    public ApiResult<String> delete(Long scaleCombinationId) {
        if (ObjUtil.isNull(scaleCombinationId)) {
            throw new BusinessException("删除失败,组合量表scaleCombinationId参数不允许为空!");
        }
        boolean result = scaleCombinationService.vaildCompletedStatus(scaleCombinationId);
        if (result) {
            throw new BusinessException("删除失败,已完成的量表无法进行删除操作!");
        }
        scaleCombinationService.delete(scaleCombinationId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "更新量表数据完成状态")
    @OptLog(module = "量表管理模块", optType = OptType.MODIFY, description = "更新量表数据完成状态")
    @PutMapping("status")
    public ApiResult<String> updateStatus(Long scaleCombinationId, Integer status) {
        if (ObjUtil.isNull(scaleCombinationId)) {
            throw new BusinessException("更新失败,组合量表scaleCombinationId参数不允许为空!");
        }
        scaleCombinationService.updateComplateStatus(scaleCombinationId, status);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "复制组合量表基本信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "复制组合量表基本信息")
    @PostMapping("copy")
    public ApiResult<String> copy(@RequestBody ScaleCombinationCopyInput scaleCombinationCopyInput) {
        Long scaleCombinationId = scaleCombinationCopyInput.getScaleCombinationId();
        if (ObjUtil.isNull(scaleCombinationId)) {
            throw new BusinessException("复制失败,组合量表scaleCombinationId参数不允许为空!");
        }
        scaleCombinationService.copyScale(scaleCombinationId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "根据ID获取组合量表详情信息")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据ID获取组合量表详情信息")
    @GetMapping("detail")
    public ApiResult<ScaleCombinationQueryDTO> detail(Long scaleCombinationId) {
        if (ObjUtil.isNull(scaleCombinationId)) {
            throw new BusinessException("获取失败,组合量表scaleCombinationId参数不允许为空!");
        }
        return ApiResult.ok(scaleCombinationService.findByScaleId(scaleCombinationId));
    }
}
