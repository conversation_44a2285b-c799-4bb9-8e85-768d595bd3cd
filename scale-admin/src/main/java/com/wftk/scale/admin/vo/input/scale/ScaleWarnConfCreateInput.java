package com.wftk.scale.admin.vo.input.scale;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @ClassName: ScaleWarnConfCreateInput
 * @Description: 创建量表预警阈值信息
 * @Author: mq
 * @Date: 2024-11-05 14:25
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleWarnConfCreateInput implements Serializable {

    /**
     * 量表ID
     */
    @Schema(title = "量表ID", name = "scaleId", defaultValue = "604980089960133", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /**
     * 因子ID
     */
    @Schema(title = "因子ID", name = "factorId", defaultValue = "604980089960133", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "因子ID不能为空")
    private Long factorId;

    /**
     * 预警逻辑，1大于，2等于，3小于, 4区间
     */
    @Schema(title = "预警逻辑", name = "type", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预警逻辑类型不能为空")
    private Integer type;

    /**
     * 预警阈值
     */
    @Schema(title = "预警阈值", name = "threshold", defaultValue = "10", requiredMode = Schema.RequiredMode.REQUIRED)
    @Max(value = Integer.MAX_VALUE, message = "预警阈值不能超过int最大数值")
    private Integer threshold;

    /**
     * 区间起始值
     */
    @Schema(title = "区间起始值", name = "start", defaultValue = "10", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Max(value = Integer.MAX_VALUE, message = "预警阈值不能超过int最大数值")
    private Integer start;

    /**
     * 区间结束值
     */
    @Schema(title = "区间结束值", name = "end", defaultValue = "10", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Max(value = Integer.MAX_VALUE, message = "预警阈值不能超过int最大数值")
    private Integer end;


    /**
     * 标识标签
     */
    @Schema(title = "标识标签",name = "tag", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "标识标签不能为空")
    @Length(min = 1, max = 50, message = "标识标签长度范围1-50个字符")
    private String tag;

    /**
     * 标识标签
     */
    @Schema(title = "标识标签ID",name = "tagId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "标识标签ID不能为空")
    private Long tagId;

    /**
     * 预警方式，1短信，2邮件,可多个，逗号分隔。
     */
    @Schema(title = "预警方式",name = "warnType", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预警方式不能为空")
    @Length(min = 1, max = 255, message = "预警方式长度范围1-255个字符")
    private String warnType;

}
