package com.wftk.scale.admin.vo.output.scale;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/12/4 16:00
 */
@Data
public class ScaleReportConfItemOutput {

    private Long id;

    /**
     * 量表id。来自scale表的主键
     */
    private Long scaleId;

    /**
     * 因子id,全部代号为all
     */
    private String factorId;

    /**
     * 报告配置id。来自scale_report_conf的主键
     */
    private Long reportConfId;

    /**
     * 配置项编码
     */
    private String itemCode;

    /**
     * 配置项值
     */
    private String itemValue;

    /**
     * 配置项文件
     */
    @RFile(role = FileConstant.FILE_SCALE_SIGN_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    private String itemFilePath;

    /**
     * 配置项描述
     */
    private String itemDescribe;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

}
