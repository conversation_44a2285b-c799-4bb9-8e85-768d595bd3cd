package com.wftk.scale.admin.controller.config;

import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleFactorFormulaConverter;
import com.wftk.scale.admin.vo.input.config.ScaleFactorFormulaChangeEnableInput;
import com.wftk.scale.admin.vo.input.config.ScaleFactorFormulaChangeStatusInput;
import com.wftk.scale.admin.vo.input.config.ScaleFactorFormulaCreateInput;
import com.wftk.scale.admin.vo.input.config.ScaleFactorFormulaModifyInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.entity.ScaleFactorFormula;
import com.wftk.scale.biz.service.ScaleFactorFormulaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName: ScaleFactorFormulaController
 * @Description: 因子公式类
 * @Author: mq
 * @Date: 2024-10-24 15:53
 * @Version: 1.0
 **/
@Tag(name = "因子公式参数配置API")
@RestController
@AdminMapping("conf/scale/factorformula")
@Slf4j
public class ScaleFactorFormulaController {

    @Autowired
    private ScaleFactorFormulaService scaleFactorFormulaService;

    @Autowired
    private ScaleFactorFormulaConverter scaleFactorFormulaConverter;

    @Operation(summary = "获取因子公式信息列表数据-全部")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "获取因子公式信息列表数据-全部")
    @GetMapping("datas")
    public ApiResult<List<ScaleFactorFormula>> getAll(@RequestParam(required = false) String formulaName, 
            @RequestParam(required = false, defaultValue = "1") Integer status) {
        return ApiResult.ok(scaleFactorFormulaService.getListOfEnabled(formulaName, status));
    }

    @Operation(summary = "获取因子公式信息列表数据-分页")
    @OptLog(module = "参数配置模块", optType = OptType.QUERY, description = "获取因子公式信息列表数据-分页")
    @GetMapping
    public ApiResult<Page<ScaleFactorFormula>> selectPage(@RequestParam(required = false) String formulaName) {
        return ApiResult.ok(scaleFactorFormulaService.selectPage(formulaName));
    }

    @Operation(summary = "创建因子公式信息")
    @OptLog(module = "参数配置模块", optType = OptType.CREATE, description = "创建因子公式信息")
    @PostMapping
    public ApiResult<String> create(@Valid @RequestBody ScaleFactorFormulaCreateInput input) {

        String formulaName = input.getName();
        boolean result = scaleFactorFormulaService.validFormulaName(null, formulaName);
        if (result) {
            throw new BusinessException("创建失败,因子公式名称已存在!");
        }
        ScaleFactorFormula scaleFactorFormula = scaleFactorFormulaConverter.scaleFactorFormulaCreateInputToEntity(input);
        scaleFactorFormulaService.create(scaleFactorFormula);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "修改因子公式信息")
    @OptLog(module = "参数配置模块", optType = OptType.MODIFY, description = "修改因子公式信息")
    @PutMapping
    public ApiResult<String> modify(@Valid @RequestBody ScaleFactorFormulaModifyInput input) {

        String formulaName = input.getName();
        boolean result = scaleFactorFormulaService.validFormulaName(input.getId(), formulaName);
        if (result) {
            throw new BusinessException("修改失败,因子公式名称已存在!");
        }
        ScaleFactorFormula scaleFactorFormula = scaleFactorFormulaConverter.ScaleFactorFormulaModifyInputToEntity(input);
        scaleFactorFormulaService.modify(scaleFactorFormula);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "删除因子公式信息")
    @OptLog(module = "参数配置模块", optType = OptType.DELETE, description = "删除因子公式信息")
    @DeleteMapping
    public ApiResult<String> delete(Long factorFormulaId) {

        Assert.notNull(factorFormulaId, " can not be null");
        if(ObjUtil.isNull(factorFormulaId)){
            throw new BusinessException("删除失败, 因子公式factorFormulaId参数不允许为空");
        }
//        String usedFormulaName = scaleFactorFormulaService.checkFormulaUsed(factorFormulaId);
//        if (StrUtil.isNotEmpty(usedFormulaName)) {
//            throw new BusinessException("删除失败, 因子公式已被嵌套使用!(" + usedFormulaName + ")");
//        }
        scaleFactorFormulaService.delete(factorFormulaId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "更新因子公式数据禁用状态")
    @OptLog(module = "参数配置模块", optType = OptType.MODIFY, description = "更新因子公式数据禁用状态")
    @PutMapping("enable")
    public ApiResult<String> updateEnabled(@RequestBody @Valid ScaleFactorFormulaChangeEnableInput input) {
//        if (Boolean.compare(EnableEnum.DISABLE.getEnable(), enabled) == 0) {
//            //禁用公式时,如果被禁用的公式被其它公式嵌套使用,则无法禁用。
//            String usedFormulaName = scaleFactorFormulaService.checkFormulaUsed(factorFormulaId);
//            if (StrUtil.isNotEmpty(usedFormulaName)) {
//                throw new BusinessException("状态更新失败, 因子公式已被嵌套使用!(" + usedFormulaName + ")");
//            }
//        }
        scaleFactorFormulaService.updateEnable(input.getFactorFormulaId(), input.getEnabled() == 1 ? Boolean.TRUE : Boolean.FALSE);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "更新因子公式数据上下架状态")
    @OptLog(module = "参数配置模块", optType = OptType.MODIFY, description = "更新因子公式数据上下架状态")
    @PutMapping("status")
    public ApiResult<String> updateStatus(@RequestBody @Valid ScaleFactorFormulaChangeStatusInput input) {
        scaleFactorFormulaService.updateStatus(input.getFactorFormulaId(), input.getStatus() == 1 ? Boolean.TRUE : Boolean.FALSE);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }
}
