package com.wftk.scale.admin.vo.input.scale;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @ClassName: ScaleCreateInput
 * @Description: 创建量表基本信息
 * @Author: mq
 * @Date: 2024-11-01 14:25
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleCreateInput implements Serializable {

    /**
     * 量表提供商代码: SYSTEM代表本系统
     */
    @Schema(title = "量表提供商代码", name = "provider", defaultValue = "SYSTEM", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表提供商代码不能为空")
    @Length(min = 1, max = 100, message = "量表提供商代码长度范围1-100个字符")
    private String provider;

    /**
     * 量表厂商名称
     */
    @Schema(title = "量表厂商名称", name = "providerName", defaultValue = "本地系统", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表厂商名称不能为空")
    @Length(min = 1, max = 100, message = "量表厂商名称长度范围1-100个字符")
    private String providerName;

    /**
     * 量表名称
     */
    @Schema(title = "量表名称", name = "name", defaultValue = "宗氏抑郁自评量表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表名称不能为空")
    @Length(min = 1, max = 255, message = "名称长度范围1-255个字符")
    private String name;

    /**
     * 量表类型，来源于scale_type表的主键
     */
    @Schema(title = "类型", name = "type", defaultValue = "614196074369221", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "类型不能为空")
    private Long type;

    /**
     * 封面图
     */
    @Schema(title = "封面图", name = "cover", defaultValue = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotBlank(message = "封面图不能为空")
    @Length(min = 0, max = 255, message = "封面图url长度范围0-255个字符")
    private String cover;

    /**
     * 量表简介
     */
    @Schema(title = "量表简介", name = "intro", defaultValue = "抑郁自评量表是一种测量抑郁的工具。", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String intro;

    /**
     * 指导语
     */
    @Schema(title = "指导语", name = "guideline", defaultValue = "作出独立的、不受任何人影响的自我评定。", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 1, max = 255, message = "指导语长度范围1-255个字符")
    private String guideline;

    /**
     * 排序
     */
    @Schema(title = "排序", name = "sort", defaultValue = "10", requiredMode = Schema.RequiredMode.REQUIRED)
    @Max(value = Integer.MAX_VALUE, message = "排序不能超过int最大数值")
    @NotNull(message = "排序不能为空")
    private Integer sort;

    /**
     * 时间需要转换单位
     */
    @Schema(title = "限时（最小）", name = "minTimeLimit", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "限时最小不能为空")
    @Min(value = 0, message = "限时最小值不能小于0")
    @Max(value = Integer.MAX_VALUE, message = "限时最小值不能超过int最大数值")
    private Integer minTimeLimit;

    /**
     * 时间需要转换单位
     */
    @Schema(title = "限时（最大）", name = "maxTimeLimit", defaultValue = "10", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "限时最大不能为空")
    @Min(value = 0, message = "限时最大值不能小于0")
    @Max(value = Integer.MAX_VALUE, message = "限时最小值不能超过int最大数值")
    private Integer maxTimeLimit;

    /**
     * 量表介绍
     */
    @Schema(title = "量表介绍", name = "description", defaultValue = "抑郁自评量表（Self-Rating Depression Scale，SDS）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotBlank(message = "量表介绍不能为空")
    private String description;

    /**
     * 量表备注
     */
    @Schema(title = "量表备注", name = "remark", defaultValue = "我是备注信息，选填", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remark;

    /**
     * 租户ID
     */
    @Schema(title = "租户ID", name = "tenantId", defaultValue = "system", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 50, message = "租户ID长度范围1-50个字符")
    private String tenantId = "system";
}




