package com.wftk.scale.admin.controller.scale;


import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleUserResultConverter;
import com.wftk.scale.admin.vo.input.scale.ScaleUserResultQueryInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.dto.scale.ScaleUserResultParamDTO;
import com.wftk.scale.biz.service.ScaleUserResultService;
import com.wftk.scale.biz.vo.ScaleUserResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: ScaleUserResultController
 * @Description: 量表测评记录管理
 * @Author: mq
 * @Date: 2024/11/20
 * @Version: 1.0
 **/
@Tag(name = "量表测评记录相关API")
@RestController
@AdminMapping("/scale/user/result")
@Slf4j
public class ScaleUserResultController {

    @Autowired
    private ScaleUserResultService scaleUserResultService;

    @Autowired
    private ScaleUserResultConverter scaleUserResultConverter;

    @Operation(summary = "获取用户测评记录列表数据")
    @OptLog(module = "量表测评模块", optType = OptType.QUERY, description = "获取用户测评记录列表数据")
    @GetMapping
    public ApiResult<Page<ScaleUserResultVO>> selectPage(@Valid ScaleUserResultQueryInput input) {
        ScaleUserResultParamDTO searchParam = scaleUserResultConverter.scaleUserResultQueryInputToParamDTO(input);
        return ApiResult.ok(scaleUserResultService.selectPage(searchParam));
    }

    @Operation(summary = "删除用户测评记录")
    @OptLog(module = "量表测评模块", optType = OptType.DELETE, description = "删除用户测评记录")
    @DeleteMapping
    public ApiResult<String> delete(Long resultId) {
        if (ObjUtil.isNull(resultId)) {
            throw new BusinessException("删除失败,测评结果resultId不允许为空!");
        }
        scaleUserResultService.delete(resultId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "查询用户测评记录详情")
    @OptLog(module = "量表测评模块", optType = OptType.QUERY, description = "查询用户测评记录详情")
    @GetMapping("detail")
    public ApiResult<ScaleUserResultVO> detail(Long resultId) {
        if (ObjUtil.isNull(resultId)) {
            throw new BusinessException("获取失败,测评结果resultId不允许为空!");
        }
        return ApiResult.ok(scaleUserResultService.detail(resultId));
    }

    @Operation(summary = "导出")
    @OptLog(module = "量表测评模块", optType = OptType.QUERY, description = "导出")
    @GetMapping("export")
    public void export(HttpServletResponse response, ScaleUserResultQueryInput input){
        scaleUserResultService.export(response, scaleUserResultConverter.scaleUserResultQueryInputToParamDTO(input));
    }
}
