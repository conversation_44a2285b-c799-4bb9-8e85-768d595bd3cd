package com.wftk.scale.admin.vo.input.distribute;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: ScaleListingUserRecordInput
 * @Description: 量表分发记录查询参数传输实体
 * @Author: mq
 * @Date: 2024-10-31 13:35
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleListingUserRecordInput implements Serializable {

    /**
     * 用户账号
     */
    @Schema(title = "用户账号", name = "userAccount", defaultValue = "user_account_01", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userAccount;

    /**
     * 用户名称
     */
    @Schema(title = "用户名称", name = "userName", defaultValue = "测试人员", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userName;

    /**
     * 用户ID
     */
    @Schema(title = "用户ID", name = "userId", defaultValue = "*************", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long userId;

    /**
     * 部门ID
     */
    @Schema(title = "部门ID", name = "departmentId", defaultValue = "999", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long deptId;

    /**
     * 手机号码
     */
    @Schema(title = "手机号码", name = "phone", defaultValue = "999", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String phone;

    /**
     * 量表名称
     */
    @Schema(title = "量表名称", name = "targetName", defaultValue = "999", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String targetName;

    /**
     * 终端编号
     */
    @Schema(title = "终端编号", name = "terminalCode", defaultValue = "999", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String terminalCode;
}
