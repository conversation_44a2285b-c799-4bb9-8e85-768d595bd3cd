package com.wftk.scale.admin.controller.scale;

import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleReportConfConverter;
import com.wftk.scale.admin.vo.input.scale.ScaleReportConfCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleReportConfModifyInput;
import com.wftk.scale.biz.entity.ScaleReportConf;
import com.wftk.scale.biz.service.ScaleReportConfService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName: ScaleReportConfController
 * @Description: 量表报告设置
 * @Author: mq
 * @Date: 2024-11-06 13:36
 * @Version: 1.0
 **/
@Tag(name = "量表报告设置相关API")
@RestController
@AdminMapping("/scale/report")
@Slf4j
public class ScaleReportConfController {

    @Autowired
    private ScaleReportConfService scaleReportConfService;

    @Autowired
    private ScaleReportConfConverter scaleReportConfConverter;

    @Operation(summary = "创建报告设置基本信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "创建报告设置基本信息")
    @PostMapping
    public ApiResult<Long> create(@Valid @RequestBody ScaleReportConfCreateInput input) {
        ScaleReportConf reportConf = scaleReportConfConverter.scaleReportConfCreateInputToEntity(input);
        Long reportConfId = scaleReportConfService.create(reportConf);
        return ApiResult.ok(reportConfId);
    }

    @Operation(summary = "修改报告设置基本信息")
    @OptLog(module = "量表管理模块", optType = OptType.MODIFY, description = "修改报告设置基本信息")
    @PutMapping
    public ApiResult<Void> modify(@Valid @RequestBody ScaleReportConfModifyInput input) {
        ScaleReportConf reportConf = scaleReportConfConverter.scaleReportConfModifyInputToEntity(input);
        scaleReportConfService.modify(reportConf);
        return ApiResult.ok();
    }

    @Operation(summary = "根据量表ID获取报告设置详情信息")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据量表ID获取报告设置详情信息")
    @GetMapping("detail")
    public ApiResult<ScaleReportConf> detail(@RequestParam("scaleId") Long scaleId) {
        ScaleReportConf reportConf = scaleReportConfService.detail(scaleId);
        return ApiResult.ok(reportConf);
    }
}
