package com.wftk.scale.admin.converter;

import com.wftk.scale.admin.vo.input.scale.ScaleWarnConfCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleWarnConfModifyInput;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfCreateDTO;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfDTO;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfModifyDTO;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfQueryDTO;
import com.wftk.scale.biz.entity.ScaleWarnConf;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @InterfaceName: ScaleWarnConfConverter
 * @Description: 量表预警阈值信息数据转换器
 * @Author: mq
 * @Date: 2024-11-05 17:06
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleWarnConfConverter {

    /*
     * @Author: mq
     * @Description: 将创建量表预警阈值信息实体转换为数据实体
     * @Date: 2024/11/5 17:07
     * @Param: input
     * @return: com.wftk.scale.biz.entity.ScaleWarnConf
     **/
    ScaleWarnConfCreateDTO scaleWarnConfCreateInputToScaleWarnConf(ScaleWarnConfCreateInput input);


    /* 
     * @Author: mq
     * @Description: 将修改量表预警阈值信息实体转换为数据实体
     * @Date: 2024/11/5 17:08 
     * @Param: input  
     * @return: com.wftk.scale.biz.entity.ScaleWarnConf 
     **/
    ScaleWarnConfModifyDTO scaleWarnConfModifyInputToScaleWarnConf(ScaleWarnConfModifyInput input);

}
