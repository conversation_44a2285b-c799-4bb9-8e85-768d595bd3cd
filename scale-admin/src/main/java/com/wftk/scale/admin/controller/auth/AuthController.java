package com.wftk.scale.admin.controller.auth;

import cn.hutool.core.util.RandomUtil;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.manager.PreAuthenticationManager;
import com.wftk.auth.spring.boot.autoconfigure.core.token.AccessTokenInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.token.AccessTokenManager;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.DefaultAuthenticationInfo;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.DefaultPreAuthenticationInfo;
import com.wftk.auth.spring.boot.autoconfigure.ext.dto.AuthenticationInfoDTO;
import com.wftk.auth.spring.boot.autoconfigure.ext.dto.PreAuthenticationDTO;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.ext.sms.SmsApi;
import com.wftk.scale.biz.ext.sms.request.VerifyCodeRequest;
import com.wftk.sms.client.spring.boot.autoconfigure.core.client.SmsClient;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.request.AliyunSendSmsRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2024/11/22 14:16
 */
@Tag(name = "登录认证相关")
@RestController
@RequestMapping("/admin/auth")
public class AuthController {

    @Autowired
    private PreAuthenticationManager preAuthenticationManager;

    @Autowired
    private AccessTokenManager accessTokenManager;

    @Autowired
    private SmsApi smsApi;

    @Operation(summary = "预授权")
    @OptLog(module = "登录认证模块", optType = OptType.QUERY, description = "预授权")
    @PostMapping("/token/pre")
    public ApiResult<String> preAuth(@RequestBody @Valid PreAuthenticationDTO preAuthenticationInfoDTO) {
        String smsCode = RandomUtil.randomNumbers(6);
        DefaultPreAuthenticationInfo preAuthenticationInfo = preAuthenticationInfoDTO.toEntity();
        preAuthenticationInfo.setPreAuthCode(smsCode);
        preAuthenticationManager.preAuthenticate(preAuthenticationInfo);
        // 发送短信验证码
        boolean sendResut = smsApi.sendVerificationCode(VerifyCodeRequest.builder().phone(preAuthenticationInfoDTO.getPreAuthAccount()).code(smsCode).build());
        if (!sendResut) {
            throw new BusinessException("短信发送失败，请稍后再试");
        }
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "登录认证")
    @OptLog(module = "登录认证模块", optType = OptType.QUERY, description = "认证")
    @PostMapping("/token")
    public ApiResult<AccessTokenInfo> auth(@RequestBody @Valid AuthenticationInfoDTO authenticationInfoDTO) {
        DefaultAuthenticationInfo authenticationInfo = authenticationInfoDTO.toEntity();
        Authentication authenticate = preAuthenticationManager.authenticate(authenticationInfo);
        return ApiResult.ok(accessTokenManager.grant(authenticate));
    }

    @Operation(summary = "通过token获取登陆用户信息")
    @GetMapping("/getUser")
    public ApiResult<?> getUser(@RequestParam("token") String token) {
        Authentication authentication = accessTokenManager.extract(token);
        return ApiResult.ok(authentication.getAuthUser());
    }
}
