package com.wftk.scale.admin.converter;

import com.wftk.scale.admin.vo.input.config.SystemTagCreateInput;
import com.wftk.scale.admin.vo.input.config.SystemTagModifyInput;
import com.wftk.scale.biz.entity.SystemTag;
import org.mapstruct.Mapper;

/**
 * @InterfaceName: SystemTagConverter
 * @Description: 预警标签信息数据转换器
 * @Author: mq
 * @Date: 2024-10-25 14:48
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface SystemTagConverter {

    /*
     * @Author: mq
     * @Description: 将创建预警标签实体转换为数据实体
     * @Date: 2024/10/25 14:50
     * @Param: systemTagCreateInput
     * @return: com.wftk.scale.biz.entity.SystemTag
     **/
    SystemTag systemTagCreateInputToEntity(SystemTagCreateInput systemTagCreateInput);

    /*
     * @Author: mq
     * @Description: 将修改预警标签实体转换为数据实体
     * @Date: 2024/10/25 14:50
     * @Param: systemTagModifyInput
     * @return: com.wftk.scale.biz.entity.SystemTag
     **/
    SystemTag systemTagModifyInputToEntity(SystemTagModifyInput systemTagModifyInput);
}
