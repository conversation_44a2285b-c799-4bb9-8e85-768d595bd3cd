package com.wftk.scale.admin.vo.input.listing;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wftk.jackson.deserializer.rmb.RMBYuanToFenDeserializer;
import com.wftk.scale.biz.jackson.deserializer.StringArrayToStringDeserializer;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @ClassName: ScaleListingCreateInput
 * @Description: 创建量表上架参数信息实体
 * @Author: mq
 * @Date: 2024-10-29 10:31
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleListingCreateInput implements Serializable {

    /**
     * 可能是量表ID，也可能是组合量表ID
     */
    @Schema(title = "量表ID", name = "targetId", defaultValue = "我是测量表ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表ID不能为空")
    private Long targetId;

    /**
     * 量表名称
     */
    @Schema(title = "量表名称", name = "targetName", defaultValue = "我是测试量表名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表名称不能为空")
    @Length(min = 1, max = 255, message = "量表名称长度范围1-50个字符")
    private String targetName;

    /**
     * 量表分类ID
     */
    @Schema(title = "量表分类ID", name = "targetType", defaultValue = "609527310759877", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long targetType;

    /**
     * 类型: 1.量表; 2.组合量表;
     */
    @Schema(title = "量表类型", name = "type", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;

    /**
     * 量表编号
     */
    @Schema(title = "量表编号", name = "targetCode", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private String targetCode;

    /**
     * 终端Code
     */
    @Schema(title = "终端Code", name = "terminalCode", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private String terminalCode;

    /**
     * 呈现方式: 1.页面展示; 2.用户分发;
     */
    @Schema(title = "呈现方式", name = "showType", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer showType;

    /**
     * 报告地址，多个用逗号分隔
     */
    @Schema(title = "报告地址", name = "reportUrl", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报告地址不能为空")
    @JsonDeserialize(using = StringArrayToStringDeserializer.class)
    private String reportUrl;

    /**
     * 量表API路径
     */
    @Schema(title = "量表API路径", name = "api", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotNull(message = "量表API路径不能为空")
    @Length(min = 1, max = 255, message = "量表API路径长度范围1-255个字符")
    private String api;

    /**
     * 量表服务费原价, 单位:分
     */
    @Schema(title = "量表服务费原价", name = "originalPrice", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonDeserialize(using = RMBYuanToFenDeserializer.class)
    private Integer originalPrice;

    /**
     * 量表服务费优惠后价格，单位: 分
     */
    @Schema(title = "量表服务费优惠后价格", name = "price", defaultValue = "1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonDeserialize(using = RMBYuanToFenDeserializer.class)
    private Integer price;

    /**
     * 上架状态: 0.未上架; 1.已上架;
     */
    @Schema(title = "上架状态", name = "status", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer status;

    /**
     * 是否启用: 0.未启用; 1.已开启;
     * 注意：此字段控制当前上架的量表是否可做业务（当值为0时用户即使买了也不能做，量表本身的enable字段不能限制用户做业务）
     */
    @Schema(title = "是否启用", name = "enable", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean enable;

    /**
     * 租户ID
     */
    @Schema(title = "租户ID", name = "tenantId", defaultValue = "system", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 50, message = "租户ID长度范围1-50个字符")
    private String tenantId = "system";

    /**
     * 封面图
     */
    @Schema(title = "封面图", name = "cover", defaultValue = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 255, message = "封面图url长度范围0-255个字符")
    private String cover;
}
