package com.wftk.scale.admin.vo.input.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @ClassName: TerminalModifyInput
 * @Description: 修改终端信息实体参数
 * @Author: mq
 * @Date: 2024-11-06 16:10
 * @Version: 1.0
 **/
@Data
@Builder
public class TerminalModifyInput implements Serializable {

    /**
     * 终端ID(修改数据主键ID)
     */
    @Schema(title = "终端ID", name = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "终端ID不能为空")
    private Long id;

    /**
     * 终端名称
     */
    @Schema(title = "终端名称", name = "name", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "终端名称不能为空")
    @Length(min = 1, max = 255, message = "终端名称长度范围1-255个字符")
    private String name;

    /**
     * 终端编码 department_terminal表中存的是终端编码，不修改这个字段
     */
//    @Schema(title = "终端编码", name = "code", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "终端编码不能为空")
//    @Length(min = 1, max = 50, message = "终端编码长度范围1-50个字符")
//    private String code;

    /**
     * 路由类型
     */
    @Schema(title = "路由类型", name = "type", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "路由类型不能为空")
    private Integer type;

    /**
     * 路由地址
     */
    @Schema(title = "路由地址", name = "code", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "路由地址不能为空")
    @Length(min = 1, max = 100, message = "路由地址长度范围1-100个字符")
    private String path;

    /**
     * 租户ID
     */
    @Schema(title = "租户ID", name = "tenantId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @Length(min = 0, max = 50, message = "租户ID长度范围1-255个字符")
    private String tenantId = "system";
}
