package com.wftk.scale.admin;

import com.wftk.file.manager.spring.boot.autoconfigure.oss.EnableOSSClient;
import com.wftk.jackson.spring.boot.autoconfigure.datetime.EnableDateTimeFormatter;
import com.wftk.message.spring.boot.autoconfigure.ext.properties.MessageProperties;
import com.wftk.opt.log.spring.boot.autoconfigure.EnableOptLog;
import com.wftk.scale.biz.config.BizConfig;
import com.wftk.signature.spring.boot.autoconfigure.EnableSignatureFilter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @create 2024/10/24 11:37
 */
@SpringBootApplication
@EnableDateTimeFormatter
@EnableOptLog
@Import(BizConfig.class)
@EnableOSSClient
@EnableSignatureFilter
@EnableConfigurationProperties({MessageProperties.class})
public class ScaleAdminApplication {
    public static void main(String[] args) {
        SpringApplication.run(ScaleAdminApplication.class, args);
    }
}
