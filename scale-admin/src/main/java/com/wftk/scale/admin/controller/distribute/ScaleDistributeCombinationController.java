package com.wftk.scale.admin.controller.distribute;

import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleDistributeConverter;
import com.wftk.scale.admin.vo.input.distribute.ScaleDistributeCreateInput;
import com.wftk.scale.admin.vo.input.distribute.ScaleDistributeQueryInput;
import com.wftk.scale.admin.vo.input.distribute.ScaleListingUserRecordInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.dto.distribute.ScaleListingBaseDTO;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordParamDTO;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordQueryDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedParamDTO;
import com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO;
import com.wftk.scale.biz.entity.ScaleListingUserConf;
import com.wftk.scale.biz.entity.ScaleListingUserRecord;
import com.wftk.scale.biz.service.ScaleListingService;
import com.wftk.scale.biz.service.ScaleListingUserConfService;
import com.wftk.scale.biz.service.ScaleListingUserRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName: ScaleDistributeCombinationController
 * @Description: 组合量表分发管理
 * @Author: mq
 * @Date: 2024-10-30 13:54
 * @Version: 1.0
 **/
@Tag(name = "组合量表分发相关API")
@RestController
@AdminMapping("/scale/combination/distribute")
@Slf4j
public class ScaleDistributeCombinationController {

    @Autowired
    private ScaleListingService scaleListingService;

    @Autowired
    private ScaleListingUserRecordService userRecordService;

    @Autowired
    private ScaleListingUserConfService userConfService;

    @Autowired
    private ScaleDistributeConverter scaleDistributeConverter;

    @Operation(summary = "获取组合量表分发列表数据")
    @OptLog(module = "量表分发管理", optType = OptType.QUERY, description = "获取单个量表分发列表数据")
    @GetMapping()
    public ApiResult<Page<ScaleListedQueryDTO>> selectDistributePage(ScaleDistributeQueryInput param) {
        ScaleListedParamDTO searchParam = scaleDistributeConverter.scaleDistributeQueryInputToParamDTO(param);
        return ApiResult.ok(scaleListingService.getScaleCombinationDistribute(searchParam));
    }

    @Operation(summary = "提交组合量表分发用户")
    @OptLog(module = "量表分发管理", optType = OptType.CREATE, description = "提交组合量表分发用户")
    @PostMapping()
    public ApiResult<String> saveDistributeUser(@Valid @RequestBody ScaleDistributeCreateInput input) throws Exception {
        ScaleListingBaseDTO scaleListingBase = scaleDistributeConverter.scaleListingBaseInputToEntity(input.getScaleListingBase());
        ScaleListingUserConf userConf = scaleDistributeConverter.scaleListingUserConfInputToEntity(input.getUserConf());
        List<ScaleListingUserRecord> userRecords = scaleDistributeConverter.scaleDistributeUserListInputToEntityList(input.getUserRecords());
        boolean result = userRecordService.vaildListingUserRecord(userRecords);
        if (!result) {
            throw new BusinessException("分发失败,分发用户不能为空!");
        }
        result = userConfService.vaildUserConfTime(userConf);
        if(!result){
            throw new BusinessException("分发失败,结束时间必须大于开始时间且结束时间不能早于当前时间!");
        }
        ScaleListingUserConf scaleListingUserConf = userConfService.saveDistributeUserConf(scaleListingBase, userConf);
        userRecordService.saveDistributeUser(scaleListingBase, scaleListingUserConf, userRecords);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "获取组合量表分发记录数据")
    @OptLog(module = "量表分发管理", optType = OptType.QUERY, description = "获取组合量表分发记录数据")
    @GetMapping("record")
    public ApiResult<Page<ScaleListingUserRecordQueryDTO>> selectRecordPage(ScaleListingUserRecordInput input) {
        ScaleListingUserRecordParamDTO searchParam = scaleDistributeConverter.scaleDistributeRecordQueryInputToParamDTO(input);
        return ApiResult.ok(userRecordService.getScaleCombinationDistributeRecord(searchParam));
    }

    @Operation(summary = "删组合量表分发记录信息")
    @OptLog(module = "量表分发管理", optType = OptType.DELETE, description = "删组合量表分发记录信息")
    @DeleteMapping
    public ApiResult<String> delete(Long id) {
        if (ObjUtil.isNull(id)) {
            throw new BusinessException("删除失败,分发记录ID不允许为空!");
        }
        userRecordService.delete(id);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }
}
