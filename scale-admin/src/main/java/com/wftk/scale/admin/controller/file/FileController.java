package com.wftk.scale.admin.controller.file;

import com.wftk.common.core.result.ApiResult;
import com.wftk.file.biz.spring.boot.autoconfigure.common.FileItem;
import com.wftk.file.biz.spring.boot.autoconfigure.common.FileResult;
import com.wftk.file.biz.spring.boot.autoconfigure.uploader.FileUploader;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @createDate 2024/11/25 20:20
 */
@AdminMapping("/file")
public class FileController {

    @Autowired
    FileUploader fileUploader;

    @PostMapping("/upload")
    @Operation(summary="获取上传地址")
    public ApiResult<FileResult> getUploadPathByCode(@RequestBody @Valid FileItem fileItem) {
        return ApiResult.ok(fileUploader.upload(fileItem, true));
    }

}
