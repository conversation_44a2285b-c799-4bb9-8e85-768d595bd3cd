package com.wftk.scale.admin.controller.user;


import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.dto.user.DepartmentCreateDTO;
import com.wftk.scale.biz.dto.user.DepartmentQueryDTO;
import com.wftk.scale.biz.dto.user.DepartmentUpdateDTO;
import com.wftk.scale.biz.service.DepartmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/11/28 10:51
 */
@Tag(name = "业务机构相关API")
@RestController
@AdminMapping("/department")
@Slf4j
public class DepartmentController {

    @Autowired
    DepartmentService departmentService;

    @Operation(summary = "创建机构")
    @OptLog(module = "机构管理", optType = OptType.CREATE, description = "创建机构")
    @PostMapping("create")
    public ApiResult<Void> create(@RequestBody @Valid DepartmentCreateDTO departmentCreateDTO){
        departmentService.createDepartment(departmentCreateDTO);
        return ApiResult.ok();
    }

    @Operation(summary = "查询树状机构数据")
    @OptLog(module = "机构管理", optType = OptType.QUERY, description = "查询树状机构数据")
    @GetMapping("getTreeList")
    public ApiResult<List<DepartmentQueryDTO>> getTreeList(@RequestParam(value = "name",required = false) String name,
                                                           @RequestParam(value = "enable",required = false) Integer enable,
                                                           @RequestParam(value = "terminalCode", required = false) String terminalCode,
                                                           @RequestParam(value = "parentName", required = false) String parentName
    ){
        return ApiResult.ok(departmentService.selectTreeList(name, enable, terminalCode, parentName));
    }

    @Operation(summary = "编辑机构")
    @OptLog(module = "编辑机构", optType = OptType.MODIFY, description = "编辑机构")
    @PutMapping("update")
    public ApiResult<String> update(@RequestBody @Valid DepartmentUpdateDTO departmentUpdateDTO){
        departmentService.updateDepartment(departmentUpdateDTO);
        return ApiResult.ok("修改成功");
    }

    @Operation(summary = "删除机构")
    @OptLog(module = "删除机构", optType = OptType.MODIFY, description = "编辑机构")
    @DeleteMapping("delete")
    public ApiResult<String> update(Long id){
        if(id == null){
            throw new BusinessException("机构ID不能为空");
        }
        departmentService.deleteDepartment(id);
        return ApiResult.ok("删除成功");
    }

}
