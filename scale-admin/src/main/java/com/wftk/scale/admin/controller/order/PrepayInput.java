package com.wftk.scale.admin.controller.order;

import com.wftk.scale.biz.ext.wechat.enums.WechatPaySceneEnum;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @create 2023/10/7 20:07
 */
@Data
public class PrepayInput {

    /**
     * 支付场景
     */
    @NotNull(message = "scene不能为空")
    private WechatPaySceneEnum scene;

    /**
     * 外部商户订单号(主订单号)
     */
    @NotBlank(message = "orderNo不能为空")
    private String orderNo;


    /**
     * 用户IP地址
     */
    @NotBlank(message = "ip不能为空")
    private String ip;

    /**
     * 订单标题/描述
     */
    @NotBlank(message = "subject不能为空")
    private String subject;

    /**
     * 总支付金额(单位: 分)
     */
    @NotNull(message = "支付金额不能为空")
    @Min(1)
    private Integer totalAmount;

    /**
     * 订单有效期
     */
    @NotNull(message = "过期时间不能为空")
    private LocalDateTime expireAt;


    /**
     * 实际支付金额(单位：分)
     */
    private Integer realAmount;

    /**
     * 外部商户支付人ID
     */
    private String payerId;

    /**
     * 外部商户支付人姓名
     */
    private String payerName;

    /**
     * 三方支付平台openId
     */
    private String openId;

    /**
     * 通知应用方(client)地址(如果字段为空，则通过应用方client的配置项来获取)
     */
    private String notifyUrl;

    /**
     * 商品信息
     */
    private String productInfo;
}
