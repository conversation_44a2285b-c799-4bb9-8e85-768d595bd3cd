package com.wftk.scale.admin.vo.input.scale;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: ScaleRelationStrategyInput
 * @Description:
 * @Author: mq
 * @Date: 2024/11/22
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleRelationStrategyInput implements Serializable {

    /**
     * 题号
     */
    @Schema(title = "题号", name = "questionNumber", defaultValue = "system", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String questionNumber;

    /**
     * 选项标签
     */
    @Schema(title = "选项标签", name = "label", defaultValue = "system", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String label;

}
