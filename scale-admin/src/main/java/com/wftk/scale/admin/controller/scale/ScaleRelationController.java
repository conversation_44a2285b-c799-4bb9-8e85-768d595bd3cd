package com.wftk.scale.admin.controller.scale;

import cn.hutool.core.util.ObjUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleRelationConverter;
import com.wftk.scale.admin.vo.input.scale.ScaleRelationCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleRelationDeleteDTO;
import com.wftk.scale.admin.vo.input.scale.ScaleRelationModifyInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.dto.scale.ScaleQuestionRelationDTO;
import com.wftk.scale.biz.entity.ScaleQuestionRelation;
import com.wftk.scale.biz.service.ScaleQuestionRelationService;
import com.wftk.scale.biz.service.ScaleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: ScaleRelationController
 * @Description: 量表逻辑跳转信息管理
 * @Author: mq
 * @Date: 2024-11-01 13:45
 * @Version: 1.0
 **/
@Tag(name = "量表逻辑跳转相关API")
@RestController
@AdminMapping("/scale/relation")
@Slf4j
public class ScaleRelationController {

    @Autowired
    private ScaleQuestionRelationService relationService;

    @Autowired
    private ScaleRelationConverter scaleRelationConverter;

    @Autowired
    private ScaleService scaleService;

    @Operation(summary = "获取量表逻辑跳转信息列表数据")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取量表逻辑跳转信息列表数据")
    @GetMapping
    public ApiResult<Page<ScaleQuestionRelationDTO>> selectPage(Long scaleId) {
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("获取失败,量表scaleId参数不允许为空!");
        }
        return ApiResult.ok(relationService.selectPage(scaleId));
    }

    @Operation(summary = "创建量表逻辑跳转信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "创建量表逻辑跳转信息")
    @PostMapping
    public ApiResult<String> create(@Valid @RequestBody ScaleRelationCreateInput input) {
        boolean result = scaleService.vaildCompletedStatus(input.getScaleId());
        if(result) {
            throw new BusinessException("创建失败,已完成的量表无法进行编辑操作!");
        }
        result = relationService.validRelationStrategyCorrect(null, input.getScaleId(), input.getQuestionId(), input.getStrategy(), input.getType());
        if (!result) {
            throw new BusinessException("创建失败,跳转题号与目标题号不能相同且目标题号不能小于跳转题号,子题不允许设置跳转,只允许跳转最大题号的子题!");
        }
        ScaleQuestionRelation relation = scaleRelationConverter.scaleRelationCreateInputToEntity(input);
        relationService.create(relation);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "修改量表逻辑跳转信息")
    @OptLog(module = "量表管理模块", optType = OptType.MODIFY, description = "修改量表逻辑跳转信息")
    @PutMapping
    public ApiResult<String> modify(@Valid @RequestBody ScaleRelationModifyInput input) {
        boolean result = scaleService.vaildCompletedStatus(input.getScaleId());
        if(result) {
            throw new BusinessException("修改失败,已完成的量表无法进行编辑操作!");
        }
        result = relationService.validRelationStrategyCorrect(input.getId(), input.getScaleId(), input.getQuestionId(), input.getStrategy(), input.getType());
        if (!result) {
            throw new BusinessException("修改失败,跳转题号与目标题号不能相同且目标题号不能小于跳转题号,子题不允许设置跳转,只允许跳转最大题号的子题!");
        }
        ScaleQuestionRelation relation = scaleRelationConverter.scaleRelationModifyInputToEntity(input);
        relationService.modify(relation);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "删除逻辑跳转信息")
    @OptLog(module = "参数配置模块", optType = OptType.DELETE, description = "删除逻辑跳转信息")
    @DeleteMapping
    public ApiResult<String> delete(@RequestBody @Valid ScaleRelationDeleteDTO dto) {
        boolean result = scaleService.vaildCompletedStatus(dto.getScaleId());
        if(result) {
            throw new BusinessException("删除失败,已完成的量表无法进行编辑操作!");
        }
        relationService.delete(dto.getId());
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "根据ID获取逻辑跳转详情信息")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据ID获取逻辑跳转详情信息")
    @GetMapping("detail")
    public ApiResult<ScaleQuestionRelation> detail(Long relationId) {
        if (ObjUtil.isNull(relationId)) {
            throw new BusinessException("获取失败,量表relationId参数不允许为空!");
        }
        return ApiResult.ok(relationService.getById(relationId));
    }
}
