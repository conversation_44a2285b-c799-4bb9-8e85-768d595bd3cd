package com.wftk.scale.admin.controller.user;

import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.dto.user.rolemenu.SysRoleMenuCreateDTO;
import com.wftk.scale.biz.service.SysRoleMenuService;
import com.wftk.scale.biz.vo.SysRoleMenuVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "角色菜单管理")
@RestController
@AdminMapping("/roleMenu")
public class SysRoleMenuController {

    @Resource
    private SysRoleMenuService sysRoleMenuService;

    @Operation(summary = "查询角色关联菜单信息")
    @OptLog(module = "角色菜单管理", optType = OptType.QUERY, description = "查询角色关联菜单信息")
    @GetMapping("getList")
    public ApiResult<List<SysRoleMenuVO>> getList(Long roleId){
        return ApiResult.ok(sysRoleMenuService.getList(roleId));
    }

    @Operation(summary = "保存或修改角色菜单关联关系")
    @OptLog(module = "角色菜单管理", optType = OptType.QUERY, description = "保存角色菜单关联关系")
    @PostMapping
    public ApiResult<Void> create(@RequestBody @Valid SysRoleMenuCreateDTO dto){
        sysRoleMenuService.create(dto);
        return ApiResult.ok();
    }
}
