package com.wftk.scale.admin.vo.input.scale;


import com.wftk.scale.biz.entity.ScaleCombinationDetail;
import jakarta.validation.Valid;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: ScaleCombinationCreateBaseInput
 * @Description:
 * @Author: mq
 * @Date: 2024/12/16
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleCombinationCreateBaseInput implements Serializable {

    /**
     * 组合量表基本信息
     */
    @Valid
    private ScaleCombinationCreateInput scaleCombination;

    /**
     * 组合量表详情信息
     */
    @Valid
    private List<ScaleCombinationDetailCreateInput> scaleIds;
}
