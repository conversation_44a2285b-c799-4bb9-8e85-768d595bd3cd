package com.wftk.scale.admin.converter;


import com.wftk.scale.admin.vo.input.scale.ScaleCombinationCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleCombinationDetailCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleCombinationDetailModifyInput;
import com.wftk.scale.admin.vo.input.scale.ScaleCombinationModifyInput;
import com.wftk.scale.biz.entity.ScaleCombination;
import com.wftk.scale.biz.entity.ScaleCombinationDetail;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @InterfaceName: ScaleCombinationConverter
 * @Description:
 * @Author: mq
 * @Date: 2024/12/16
 * @Version: 1.0
 **/
@Mapper(componentModel = "spring")
public interface ScaleCombinationConverter {

    /*
    * @Author: mq
    * @Description: 将创建组合量表基本信息实体转换为数据实体
    * @Date: 2024/12/16 14:32
    * @Param: scaleCombination
    * @return: ScaleCombination
    **/
    ScaleCombination scaleCombinationCreateInputToEntity(ScaleCombinationCreateInput createInput);

    /*
     * @Author: mq
     * @Description: 将修改组合量表基本信息实体转换为数据实体
     * @Date: 2024/12/16 14:32
     * @Param: scaleCombination
     * @return: ScaleCombination
     **/
    ScaleCombination scaleCombinationModifyInputToEntity(ScaleCombinationModifyInput modifyInput);

    /*
    * @Author: mq
    * @Description: 将创建组合量表详情信息实体转换为数据实体
    * @Date: 2024/12/16 14:34
    * @Param: createInput
    * @return: ScaleCombinationDetail
    **/
    ScaleCombinationDetail scaleCombinationDetailCreateInputToEntity(ScaleCombinationDetailCreateInput createInput);

    /*
    * @Author: mq
    * @Description: 将创建组合量表详情信息实体转换为数据实体
    * @Date: 2024/12/16 14:35
    * @Param: createInputList
    * @return: List<ScaleCombinationDetail>
    **/
    List<ScaleCombinationDetail> scaleCombinationDetailCreateListToEntityList(List<ScaleCombinationDetailCreateInput> createInputList);

    /*
     * @Author: mq
     * @Description: 将修改组合量表详情信息实体转换为数据实体
     * @Date: 2024/12/16 14:34
     * @Param: createInput
     * @return: ScaleCombinationDetail
     **/
    ScaleCombinationDetail scaleCombinationDetailModifyInputToEntity(ScaleCombinationDetailModifyInput modifyInput);

    /*
    * @Author: mq
    * @Description: 将修改组合量表详情信息实体转换为数据实体
    * @Date: 2024/12/16 14:35
    * @Param: createInputList
    * @return: List<ScaleCombinationDetail>
    **/
    List<ScaleCombinationDetail> scaleCombinationDetailModifyListToEntityList(List<ScaleCombinationDetailModifyInput> createInputList);
}
