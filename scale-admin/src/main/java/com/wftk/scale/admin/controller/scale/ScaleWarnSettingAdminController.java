package com.wftk.scale.admin.controller.scale;

import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.biz.input.ScaleWarnSettingCreateInput;
import com.wftk.scale.biz.input.ScaleWarnSettingPageQueryInput;
import com.wftk.scale.biz.input.ScaleWarnSettingUpdateInput;
import com.wftk.scale.biz.output.ScaleScaleFactorOutput;
import com.wftk.scale.biz.output.ScaleWarnSettingDetailOutput;
import com.wftk.scale.biz.output.ScaleWarnSettingPageQueryOutput;
import com.wftk.scale.biz.service.ScaleWarnSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@AdminMapping("scale/setting")
@Tag(name = "量表预警设置")
@RequiredArgsConstructor
public class ScaleWarnSettingAdminController {

    private final ScaleWarnSettingService scaleWarnSettingService;

    @GetMapping("page")
    @Operation(summary = "获取量表预警设置列表")
    public ApiResult<Page<ScaleWarnSettingPageQueryOutput>> page(ScaleWarnSettingPageQueryInput input) {
        Page<ScaleWarnSettingPageQueryOutput> page = scaleWarnSettingService.pageQuery(input);
        return ApiResult.ok(page);
    }

    @GetMapping
    @Operation(summary = "获取量表预警设置详情")
    public ApiResult<ScaleWarnSettingDetailOutput> get(@Param("id")Long id) {
        ScaleWarnSettingDetailOutput output = scaleWarnSettingService.getDetailById(id);
        return ApiResult.ok(output);
    }

    @GetMapping("scaleScaleFactor")
    @Operation(summary = "获取量表预警以及因子")
    public ApiResult<List<ScaleScaleFactorOutput>> queryScaleScaleFactor() {
        List<ScaleScaleFactorOutput> list = scaleWarnSettingService.queryScaleScaleFactor();
        return ApiResult.ok(list);
    }

    @PostMapping
    @Operation(summary = "创建量表设置")
    @OptLog(module = "量表预警设置", optType = OptType.CREATE, description = "创建量表设置")
    public ApiResult<Void> create(@RequestBody @Valid ScaleWarnSettingCreateInput createInput) {
        scaleWarnSettingService.create(createInput);
        return ApiResult.ok();
    }

    @PutMapping
    @Operation(summary = "修改量表设置")
    @OptLog(module = "量表预警设置", optType = OptType.MODIFY, description = "修改量表设置")
    public ApiResult<Void> update(@RequestBody @Valid ScaleWarnSettingUpdateInput updateInput) {
        scaleWarnSettingService.update(updateInput);
        return ApiResult.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除量表设置")
    @OptLog(module = "量表预警设置", optType = OptType.DELETE, description = "删除量表设置")
    public ApiResult<Void> delete(@RequestParam("id") Long id) {
        scaleWarnSettingService.delete(id);
        return ApiResult.ok();
    }
}
