package com.wftk.scale.admin.controller.scale;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.admin.annotation.namespace.AdminMapping;
import com.wftk.scale.admin.converter.ScaleResultIntroConverter;
import com.wftk.scale.admin.vo.input.scale.ScaleResultIntroCreateInput;
import com.wftk.scale.admin.vo.input.scale.ScaleResultIntroModifyInput;
import com.wftk.scale.biz.constant.ResponseMsgConstant;
import com.wftk.scale.biz.constant.enums.ScaleResultIntroShowTypeEnum;
import com.wftk.scale.biz.dto.scale.ScaleResultIntroDTO;
import com.wftk.scale.biz.entity.ScaleResultIntro;
import com.wftk.scale.biz.service.ScaleResultIntroService;
import com.wftk.scale.biz.service.ScaleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * @ClassName: ScaleResultController
 * @Description: 量表结果解读信息管理
 * @Author: mq
 * @Date: 2024-11-01 13:48
 * @Version: 1.0
 **/
@Tag(name = "量表结果解读相关API")
@RestController
@AdminMapping("/scale/result")
@Slf4j
public class ScaleResultController {

    @Autowired
    private ScaleResultIntroConverter scaleResultIntroConverter;

    @Autowired
    private ScaleResultIntroService scaleResultIntroService;

    @Autowired
    private ScaleService scaleService;

    @Operation(summary = "获取量表结果解读信息列表数据")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "获取量表结果解读信息列表数据")
    @GetMapping
    public ApiResult<Page<ScaleResultIntroDTO>> selectPage(Long scaleId) {
        return ApiResult.ok(scaleResultIntroService.seletePage(scaleId));
    }

    @Operation(summary = "创建量表结果解读信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "创建量表结果解读信息")
    @PostMapping
    public ApiResult<String> create(@Valid @RequestBody ScaleResultIntroCreateInput input) {
        ScaleResultIntro scaleResultIntro = scaleResultIntroConverter.scaleResultIntroCreateInputToEntity(input);
        scaleResultIntro.setShowType(StringUtils.join(input.getShowType(),","));
        boolean result = scaleResultIntroService.vaildResultIntroName(input.getScaleId(), scaleResultIntro.getFactorId(), input.getIntro());
        if (result) {
            throw new BusinessException("创建失败, 结果解读信息不允许重复设置!");
        }
        result = scaleResultIntroService.vaildFactorScoreRangeOverlap(scaleResultIntro);
        if (result) {
            throw new BusinessException("创建失败, 结果解读信原始得分区间不允许重叠交叉!");
        }
        //校验下原始得分数值和转换分值，不能为负数
        String score = input.getScore();
        String convertScore = input.getConvertScore();
        Integer scoreConvertType = input.getScoreConvertType();
        if(checkNumInputError(score, 2)){
            throw new BusinessException("原始得分输入有误：需是大于0的数字");
        }
        if(checkNumInputError(convertScore, scoreConvertType)){
            throw new BusinessException("转换分值输入有误：需是大于0的数字");
        }
        // 校验展示类型如果包含图表，那图表类型不能为空
        if(input.getShowType().contains(ScaleResultIntroShowTypeEnum.ECHART.getCode())){
            if(input.getChartType() == null){
                throw new BusinessException("展示类型包含图表,图表类型不能为空");
            }
        }

        scaleResultIntroService.create(scaleResultIntro);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    private boolean checkNumInputError(String score, Integer scoreConvertType){
        if(StrUtil.isBlank(score)){
            return false;
        }
        //1：等比情况下，传递的值为单个数字，2：非等比， 传递的是区间
        if(1 == scoreConvertType){
            return checkNumberVal(score);
        }else if(2 == scoreConvertType){
            if(score.length() > 2){
                String[] split = score.substring(1, score.length() - 1).split(",");
                for (String s : split) {
                    if(checkNumberVal(s)){
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private boolean checkNumberVal(String numberStr){
        try {
            BigDecimal val = new BigDecimal(numberStr);
            return val.compareTo(BigDecimal.ZERO) < 0;
        }catch (Exception e){
            return true;
        }
    }

    @Operation(summary = "修改量表结果解读信息")
    @OptLog(module = "量表管理模块", optType = OptType.CREATE, description = "修改量表结果解读信息")
    @PutMapping
    public ApiResult<String> modify(@Valid @RequestBody ScaleResultIntroModifyInput input) {
        ScaleResultIntro scaleResultIntro = scaleResultIntroConverter.scaleResultIntroModifyInputToEntity(input);
        scaleResultIntro.setShowType(StringUtils.join(input.getShowType(),","));
        ScaleResultIntro dbData = scaleResultIntroService.getById(input.getId());
        if(dbData == null){
            throw new BusinessException("修改失败，数据不存在!");
        }
        //名称不可重复，编辑时，名称发生变化时再校验
        if(!Objects.equals(input.getIntro(), dbData.getIntro())){
            boolean result = scaleResultIntroService.vaildResultIntroName(input.getScaleId(), input.getFactorId(), input.getIntro());
            if (result) {
                throw new BusinessException("修改失败, 结果解读信息不允许重复设置!");
            }
        }
        boolean result = scaleResultIntroService.vaildFactorScoreRangeOverlap(scaleResultIntro);
        if (result) {
            throw new BusinessException("修改失败, 结果解读信原始得分区间不允许重叠交叉!");
        }

        // 校验展示类型如果包含图表，那图表类型不能为空
        if(input.getShowType().contains(ScaleResultIntroShowTypeEnum.ECHART.getCode())){
            if(input.getChartType() == null){
                throw new BusinessException("展示类型包含图表,图表类型不能为空");
            }
        }
        scaleResultIntroService.modify(scaleResultIntro);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "删除量结果解读目信息")
    @OptLog(module = "量表管理模块", optType = OptType.DELETE, description = "删除量表结果解读信息")
    @DeleteMapping
    public ApiResult<String> delete(Long scaleId, Long resultId) {
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("删除失败,量表scaleId不允许为空!");
        }
        if (ObjUtil.isNull(resultId)) {
            throw new BusinessException("删除失败,结果解读resultId不允许为空!");
        }
        boolean result = scaleService.vaildCompletedStatus(scaleId);
        if (result) {
            throw new BusinessException("删除失败,已完成的量表无法进行编辑操作!");
        }
        scaleResultIntroService.delete(resultId);
        return ApiResult.ok(ResponseMsgConstant.SUCCESS_MSG);
    }

    @Operation(summary = "根据ID获取结果解读详情信息")
    @OptLog(module = "量表管理模块", optType = OptType.QUERY, description = "根据ID获取结果解读详情信息")
    @GetMapping("detail")
    public ApiResult<ScaleResultIntroDTO> detail(Long resultId) {
        if (ObjUtil.isNull(resultId)) {
            throw new BusinessException("获取失败,结果解读resultId不允许为空!");
        }
        return ApiResult.ok(scaleResultIntroService.findById(resultId));
    }
}
