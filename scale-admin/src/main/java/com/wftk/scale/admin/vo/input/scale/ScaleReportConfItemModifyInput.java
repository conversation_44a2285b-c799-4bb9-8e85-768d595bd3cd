package com.wftk.scale.admin.vo.input.scale;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: ScaleReportConfCreateInput
 * @Description:
 * @Author: mq
 * @Date: 2024/11/26
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleReportConfItemModifyInput implements Serializable {

    /**
     * 量表id。来自scale表的主键
     */
    @Schema(title = "量表ID", name = "scaleId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /**
     * 因子id,全部代号为all
     */
    @Schema(title = "因子id", name = "factorId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "因子id不能为空")
    private String factorId;

    /**
     * 量表id。来自scale表的主键
     */
    @Schema(title = "id", name = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 报告配置id。来自scale_report_conf的主键
     */
    @Schema(title = "报告配置ID", name = "reportConfId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报告配置ID不能为空")
    private Long reportConfId;

    /**
     * 配置项编码
     */
    @Schema(title = "报告配置编码", name = "itemCode", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报告配置编码不能为空")
    private String itemCode;

    /**
     * 配置项值
     */
    @Schema(title = "配置项值", name = "itemValue", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String itemValue;

    /**
     * 配置项文件
     */
    @Schema(title = "配置项文件", name = "itemFilePath", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String itemFilePath;

    /**
     * 配置项描述
     */
    @Schema(title = "配置项描述（区间）", name = "itemDescribe",defaultValue = "0,10", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "区间不能为空")
    private String itemDescribe;



}
