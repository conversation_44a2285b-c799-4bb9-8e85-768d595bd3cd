package com.wftk.scale.admin.vo.input.distribute;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @ClassName: ScaleListingBaseInput
 * @Description:
 * @Author: mq
 * @Date: 2024/12/16
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleListingBaseInput implements Serializable {

    /**
     * 上架配置ID
     */
    @Schema(title = "上架配置ID", name = "scaleListingId", defaultValue = "622323480376901", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "上架配置scaleListingId不允许为空")
    private Long scaleListingId;

    /**
     * 本次上架的量表ID或者组合ID(字段冗余,便于查询分发记录)
     */
    @Schema(title = "量表ID", name = "targetId", defaultValue = "622323480376901", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "量表targetId不允许为空")
    private Long targetId;

    /**
     * 量表名称
     */
    @Schema(title = "量表名称", name = "targetName", defaultValue = "我是测试量表名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @Length(min = 1, max = 255, message = "量表名称长度范围1-255个字符")
    private String targetName;

    /**
     * 量表类型
     */
    @Schema(title = "量表分类", name = "targetType", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long targetType;

    /**
     * 本次上架的分类(字段冗余,便于查询分发记录)
     */
    @Schema(title = "上架类型", name = "type", defaultValue = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long type;
}
