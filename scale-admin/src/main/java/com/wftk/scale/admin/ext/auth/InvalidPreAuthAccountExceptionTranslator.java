package com.wftk.scale.admin.ext.auth;

import com.wftk.auth.spring.boot.autoconfigure.exception.auth.InvalidPreAuthAccountException;
import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;

/**
 * <AUTHOR>
 * @create 2024/12/21 11:05
 */
public class InvalidPreAuthAccountExceptionTranslator extends AbstractExceptionTranslator {
    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        return ResolvedResultBuilder.build(GlobalErrorConstants.BAD_REQUEST.code(), "账号错误", HttpStatusCode.BAD_REQUEST);
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof InvalidPreAuthAccountException;
    }
}
