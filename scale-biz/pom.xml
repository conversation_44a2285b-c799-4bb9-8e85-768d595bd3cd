<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wftk.scale</groupId>
        <artifactId>scale-system</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>scale-biz</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 私库 -->
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-http-client-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-exception-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-jackson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-log-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-opt-log-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-pageable-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-lock-redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-file-biz-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-signature-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-file-manager-spring-boot-starter</artifactId>
        </dependency>
        <!--   待移除     -->
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-auth-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-message-spring-boot-starter</artifactId>
        </dependency>


        <!-- 外部库 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
        </dependency>


        <!--pdf-->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.28</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.10</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-pdf</artifactId>
            <version>9.1.22</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.3.3</version>
        </dependency>

        <!--    pdf渲染    -->
        <dependency>
            <groupId>com.microsoft.playwright</groupId>
            <artifactId>playwright</artifactId>
            <version>1.40.0</version>
        </dependency>

        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-sms-client-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>

</project>