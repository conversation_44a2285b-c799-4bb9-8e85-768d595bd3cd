<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleFactorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleFactor">
        <id column="id" property="id" />
        <result column="scale_id" property="scaleId" />
        <result column="name" property="name" />
        <result column="sort" property="sort" />
        <result column="question_id" property="questionId" />
        <result column="formula" property="formula" />
        <result column="formula_compiled" property="formulaCompiled" />
        <result column="formula_label" property="formulaLabel" />
        <result column="formula_param" property="formulaParam" />
        <result column="formula_param_name" property="formulaParamName" />
        <result column="enable" property="enable" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, scale_id, name, sort, question_id, formula, formula_compiled, formula_label, formula_param, formula_param_name, enable, create_time, update_time, deleted, create_by, update_by, tenant_id
    </sql>

    <!-- 校验量表因子维度内容是否存在 -->
    <select id="vaildFactorDataIntegrity" resultType="java.lang.Boolean">
        select count(1)
        from scale_factor
        where scale_id = #{scaleId}
          and deleted = 0 limit 1
    </select>

    <select id="checkFactorIsDepend" resultType="java.lang.Boolean">
        select count(1) from scale_factor a
            join scale b on a.scale_id = b.id and b.deleted = 0
            join scale_question c on find_in_set(c.id, replace(a.question_id,'|',',')) > 0 and c.deleted = 0
        where a.formula_id = #{factorFormulaId} and a.deleted = 0
    </select>

    <!-- 校验因子公式是否被禁用 -->
    <select id="checkFactorFormulaEnable" resultType="java.lang.String">
        select v.name
        from scale_factor f
                 inner join scale_factor_formula v
                            on f.formula_id = v.id and v.enable = 0 and v.deleted = 0
        where f.deleted = 0
          and scale_id = #{scaleId} limit 1
    </select>

    <!-- 校验量表因子维度名称是否存在 -->
    <select id="vaildFactorNameExists" resultType="java.lang.Boolean">
        select count(1)
        from scale_factor
        where deleted = 0
        <if test="factorId != null and factorId != ''">
            and id &lt;> #{factorId}
        </if>
        <if test="scaleId != null and scaleId != ''">
            and scale_id = #{scaleId}
        </if>
        <if test="factorName != null and factorName != ''">
            and name = #{factorName}
        </if>
        limit 1
    </select>

    <!-- 根据量表ID获取关联的因子维度信息 -->
    <select id="findByScaleId" resultMap="BaseResultMap">
        select
        *
        from scale_factor
        where scale_id = #{scaleId}
        and deleted = 0
    </select>

    <!-- 根据量表ID获取关联的因子维度信息 -->
    <select id="getList" resultMap="BaseResultMap">
        select
        *
        from scale_factor
        where scale_id = #{scaleId}
        and deleted = 0
        <if test="factorName != null and factorName != ''">
            and name = #{factorName}
        </if>
        order by sort,id
    </select>

    <select id="getFactorFormulaNotEnableCount" resultType="java.lang.Integer">
        select count(*)
        from scale_factor_formula
        where deleted = 0
          and id in (select formula_id
                     from scale_factor
                     where scale_id = #{scaleId}
                       and deleted = 0)
          and (enable = 0 or status = 0)
    </select>

    <select id="getFactorFormulaNotEnableCountByScaleIds" resultType="java.lang.Integer">
        select count(*) from
        scale_factor_formula
        where deleted = 0 and id in (
        select
        formula_id
        from scale_factor
        where deleted = 0 and scale_id in
        <foreach collection="scaleIds" item="scaleId" separator="," open="(" close=")">
            #{scaleId}
        </foreach>
        ) and (enable = 0 or status = 0)
    </select>

    <select id="getByScaleIdAndName" resultType="com.wftk.scale.biz.entity.ScaleFactor">
        select
            *
        from scale_factor
        where scale_id = #{scaleId}
        and deleted = 0
        and name = #{factorName}
    </select>

    <select id="checkFactorType" resultType="java.lang.Boolean">
        select
        count(*)
        from scale_factor
        <where>
            scale_id = #{scaleId}
            and deleted = 0
            and type = #{type}
            <if test="id != null">
                and id != #{id}
            </if>
        </where>
    </select>

    <select id="getFactorByScaleIdAndType" resultType="com.wftk.scale.biz.entity.ScaleFactor">
        select
        *
        from scale_factor
        <where>
            scale_id = #{scaleId}
            and deleted = 0
            and type = #{type}
        </where>
        order by id asc limit 1
    </select>
    <select id="getFactorListByScaleIdAndType" resultType="com.wftk.scale.biz.entity.ScaleFactor">
        select
        *
        from scale_factor
        <where>
            scale_id = #{scaleId}
            and deleted = 0
            and type = #{type}
        </where>
    </select>
    <select id="getById" resultType="com.wftk.scale.biz.entity.ScaleFactor">
        select * from scale_factor where id = #{id} and deleted = 0
    </select>


</mapper>
