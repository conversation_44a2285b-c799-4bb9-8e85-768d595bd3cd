<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleWarnMapper">

    <select id="queryList" resultType="com.wftk.scale.biz.vo.ScaleWarnVO">
        select id, account, user_name userName, sex, department_name departmentName,
               department_id departmentId, phone, scale_id scaleId, scale_name scaleName,
               scale_type scaleType, scale_type_id scaleTypeId, type, terminal_code terminalCode,
               terminal_name terminalName, assessment_time assessmentTime, cost, contact_mode contactMode,
               tag_name tagName, risk_level riskLevel, status, create_time,notice_type, receiving_warn_user_id, receiving_warn_user_name from scale_warn
        <where>
            deleted = 0
            <if test="account != null and account != ''">
                and account = #{account}
            </if>
            <if test="userName != null and userName != ''">
                and user_name like concat('%', #{userName}, '%')
            </if>
            <if test="departmentId != null">
                and department_id = #{departmentId}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="scaleName != null and scaleName != ''">
                and scale_name like concat('%', #{scaleName}, '%')
            </if>
            <if test="terminalCode != null and terminalCode != ''">
                and terminal_code = #{terminalCode}
            </if>
        </where>
        order by create_time desc, id desc
    </select>
</mapper>