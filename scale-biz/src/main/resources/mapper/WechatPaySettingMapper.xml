<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.WechatPaySettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.WechatPaySetting">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="app_secret" property="appSecret" />
        <result column="app_name" property="appName" />
        <result column="mch_id" property="mchId" />
        <result column="mch_no" property="mchNo" />
        <result column="api_secret" property="apiSecret" />
        <result column="scene_code" property="sceneCode" />
        <result column="enabled" property="enabled" />
        <result column="is_default" property="isDefault" />
        <result column="notify_url" property="notifyUrl" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
        <result column="api_version" property="apiVersion" />
        <result column="deleted" property="deleted" />
        <result column="cert_file" property="certFile" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, app_secret, app_name, mch_id, mch_no, api_secret, scene_code, enabled, is_default, notify_url, create_time, update_time, create_by, update_by, tenant_id, api_version, deleted, cert_file
    </sql>

</mapper>
