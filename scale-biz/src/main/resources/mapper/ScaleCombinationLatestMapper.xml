<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleCombinationLatestMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleCombinationLatest">
        <id column="id" property="id" />
        <result column="combination_id" property="combinationId" />
        <result column="code" property="code"/>
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, combination_id, code, deleted, create_time, update_time, create_by, update_by, tenant_id
    </sql>


    <!-- 根据量表编码获取最新版本信息 -->
    <select id="findByCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from scale_combination_latest
        where code=#{code}
        limit 1
    </select>

</mapper>
