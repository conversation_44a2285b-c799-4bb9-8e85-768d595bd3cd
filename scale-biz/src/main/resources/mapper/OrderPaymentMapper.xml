<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.OrderPaymentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.OrderPayment">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="trade_no" property="tradeNo" />
        <result column="extra_trade_no" property="extraTradeNo" />
        <result column="status" property="status" />
        <result column="pay_channel" property="payChannel" />
        <result column="payment_time" property="paymentTime" />
        <result column="payment_result_time" property="paymentResultTime" />
        <result column="expir_time" property="expirTime" />
        <result column="memo" property="memo" />
        <result column="trade_amount" property="tradeAmount" />
        <result column="extra_code" property="extraCode" />
        <result column="extra_msg" property="extraMsg" />
        <result column="extra_req_json" property="extraReqJson" />
        <result column="extra_resp_json" property="extraRespJson" />
        <result column="extra_user_id" property="extraUserId" />
        <result column="app_id" property="appId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, trade_no, extra_trade_no, status, pay_channel, payment_time, payment_result_time, memo, trade_amount, extra_code, extra_msg, extra_req_json, extra_resp_json, extra_user_id, app_id, create_time, update_time, deleted, create_by, update_by, tenant_id,expir_time
    </sql>
    <update id="updateStatus">
        UPDATE order_payment SET status = #{status}
        where trade_no = #{tradeNo} and status = #{oldStatus} and deleted = 0
    </update>

</mapper>
