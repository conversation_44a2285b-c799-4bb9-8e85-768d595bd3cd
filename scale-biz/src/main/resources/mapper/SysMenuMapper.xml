<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.SysMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.SysMenu">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="type" property="type" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="route_name" property="routeName" />
        <result column="route_path" property="routePath" />
        <result column="component_name" property="componentName" />
        <result column="component_path" property="componentPath" />
        <result column="permission_code" property="permissionCode" />
        <result column="sort" property="sort" />
        <result column="icon" property="icon" />
        <result column="description" property="description" />
        <result column="hidden" property="hidden" />
        <result column="enable" property="enable" />
        <result column="enable" property="enable" />
        <result column="cache" property="cache" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, type, name, code, route_name, route_path,
        component_name, component_path, permission_code, sort, icon,
        description, hidden, enable, cache, create_time, update_time,
        deleted, create_by, update_by, tenant_id
    </sql>

    <select id="selectChildrenByMenuId" resultMap="BaseResultMap">
        with recursive tmp as (
            select * from sys_menu where id = #{menuId} and deleted = 0
            union all
            select t1.* from sys_menu t1 join tmp t2 on t1.parent_id = t2.id and t1.deleted = 0
        )
        select * from tmp
    </select>

    <select id="selectRoleMenuByRoleId" resultType="com.wftk.scale.biz.vo.SysRoleMenuVO">
        select t1.id, t1.name, t1.parent_id parentId, case when t2.menu_id is null then 0 else 1 end ownerPermission
        from sys_menu t1
        left join (
            select t2.role_id, t2.menu_id from sys_role_menu t2
                join sys_role t3 on t3.id = t2.role_id and t3.deleted = 0
            where t2.deleted = 0
              <if test="roleId != null">
                  and t3.id = #{roleId}
              </if> ) t2
        on t1.id = t2.menu_id
        where t1.deleted = 0 and t1.enable = 1 and t1.hidden = 0 and t1.type in (1,2)
    </select>

    <select id="selectMenuByUserId" resultMap="BaseResultMap">
        select t1.* from sys_menu t1
        join (
            select t4.menu_id from sys_role t3
            join sys_user_role t2 on t2.deleted = 0 and t2.role_id = t3.id
            join sys_role_menu t4 on t4.role_id = t3.id and t4.deleted = 0
            where t3.deleted = 0
            <if test="userId != null">
                and t2.user_id = #{userId}
            </if>
        ) t2 on t1.id = t2.menu_id
        where t1.deleted = 0 and t1.hidden = 0 and t1.enable = 1 and t1.type in (1,2)
    </select>
</mapper>
