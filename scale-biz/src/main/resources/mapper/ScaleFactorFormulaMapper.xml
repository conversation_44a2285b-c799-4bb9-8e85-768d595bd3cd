<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleFactorFormulaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleFactorFormula">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="formula" property="formula" />
        <result column="formula_label" property="formulaLabel" />
        <result column="description" property="description" />
        <result column="scene_code" property="sceneCode" />
        <result column="action_code" property="actionCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
        <result column="enable" property="enable" />
        <result column="builtin" property="builtin" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, code, formula, formula_label, description, scene_code, action_code, create_time, update_time, deleted, create_by, update_by, tenant_id, enable, builtin, status
    </sql>

    <!-- 校验因子公式名称是否已经存在 -->
    <select id="validFormulaNameExist" resultType="java.lang.Boolean">
        select count(id)
        from scale_factor_formula
        where deleted = 0
        <if test="factorFormulaId != null and factorFormulaId != ''">
            and id &lt;> #{factorFormulaId}
        </if>
        <if test="formulaName != null and formulaName != ''">
            and name = #{formulaName}
        </if>
        limit 1
    </select>

    <!-- 校验因子公式是否被其它公式嵌套使用 -->
    <select id="checkFormulaUsed" resultType="java.lang.String">
        select name from scale_factor_formula
        where deleted = 0 and params field_in_set(#{factorFormulaId}, params)
        limit 1
    </select>

    <!-- 更新数据启用状态 -->
    <update id="updateEnable">
        update scale_factor_formula
        set enable = #{enable}
        where id = #{factorFormulaId}
    </update>

    <!-- 根据条件检索因子公式列表数据 -->
    <select id="getList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from scale_factor_formula
        where deleted = 0
        <if test="formulaName != null and formulaName != ''">
            and name like concat('%',#{formulaName},'%')
        </if>
        <if test="enable != null and enable != ''">
            and enable = #{enable}
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        order by id desc
    </select>
    <select id="getByCode" resultType="com.wftk.scale.biz.entity.ScaleFactorFormula">
        select * from scale_factor_formula where code = #{code} and deleted = 0
    </select>
</mapper>
