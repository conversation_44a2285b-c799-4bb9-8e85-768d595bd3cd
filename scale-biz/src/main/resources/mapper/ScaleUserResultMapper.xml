<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleUserResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleUserResult">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="scale_id" property="scaleId"/>
        <result column="scale_listing_id" property="scaleListingId"/>
        <result column="order_no" property="orderNo"/>
        <result column="listing_user_id" property="listingUserId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="report_time" property="reportTime"/>
        <result column="report_url" property="reportUrl"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, scale_id, scale_listing_id, order_no, listing_user_id, start_time, end_time, create_time, update_time, report_time, report_url, deleted, create_by, update_by, tenant_id
    </sql>

    <!--校验量表是否存在测评记录-->
    <select id="vaildScaleUserResultRecord" resultType="java.lang.Boolean">
        select count(id)
        from scale_user_result
        where deleted = 0
        <if test="userId != null and userId != ''">
            and user_id = ${userId}
        </if>
        <if test="scaleId != null and scaleId != ''">
            and scale_id = ${scaleId}
        </if>
        limit 1
    </select>

    <!--根据条件获取用户测评记录,建议生成测评记录时,将用户信息部门信息进行冗余,减少查询关联!!!-->
    <select id="getList"
            parameterType="com.wftk.scale.biz.dto.scale.ScaleUserResultParamDTO"
            resultType="com.wftk.scale.biz.vo.ScaleUserResultVO">
        select r.id, u.account, u.name userName, u.sex, u.phone, d.name as departmentName,
               d.department_id departmentId, l.target_name scaleName, l.target_type scaleType,
               l.type, l.terminal_code terminalCode, f.name terminalName,
               case when r.start_time is null or r.end_time is null then 0
                    else
                        case when TIMESTAMPDIFF(SECOND, r.start_time, r.end_time) >= 0
                             then TIMESTAMPDIFF(SECOND, r.start_time, r.end_time)
                             else 0 end
                    end as cost, l.show_type contactMode
        from scale_user_result r
            left join scale_listing l on r.scale_listing_id = l.id and l.deleted = 0
            left join user u on u.deleted = 0 and r.user_id = u.id
            left join (
                select d.*,m.name from user_department d
                left join department m on d.department_id = m.id and m.deleted = 0
                where d.deleted = 0
            ) d on u.id = d.user_id
            left join terminal f on f.deleted = 0 and f.code = l.terminal_code
        where r.deleted = 0
          <if test="id != null">
              and r.id = #{id}
          </if>
        <if test="userAccount != null and userAccount != ''">
            and u.account like concat('%',#{userAccount},'%')
        </if>
        <if test="userName != null and userName != ''">
            and u.name like concat('%',#{userName},'%')
        </if>
        <if test="phone != null and phone != ''">
            and u.phone like concat('%',#{phone},'%')
        </if>
        <if test="terminalCode != null and terminalCode != ''">
            and l.terminal_code = #{terminalCode}
        </if>
        <if test="deptId != null and deptId != ''">
            and d.department_id = #{deptId}
        </if>
        <if test="scaleName != null and scaleName != ''">
            and l.target_name like concat('%',#{scaleName},'%')
        </if>
        order by r.create_time desc
    </select>

    <!--根据测评记录ID更新测评结束时间-->
    <update id="updateEndTime">
        update scale_user_result
        set end_time=#{endTime}
        where id = #{resultId}
    </update>

    <!--根据量表ID、用户ID和上架ID获取用户最后一条测评记录-->
    <select id="findLastUserResult" resultMap="BaseResultMap">
        select *
        from scale_user_result
        where deleted = 0
          and scale_id = #{scaleId}
          and user_id = #{userId}
          and scale_listing_id = ${listingId}
          <if test="orderNo != null and orderNo != ''">
              and order_no = #{orderNo}
          </if>
        order by id desc limit 1
    </select>
    <select id="selectByOrderNo" resultType="com.wftk.scale.biz.entity.ScaleUserResult">
        select *
        from scale_user_result
        where deleted = 0
          and order_no = #{orderNo}
    </select>
</mapper>
