<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.UserExtraInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.UserExtraInfo">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="extra_info" property="extraInfo" />
        <result column="user_type" property="userType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, extra_info, user_type, create_time, update_time, tenant_id, deleted, create_by, update_by
    </sql>

</mapper>
