<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleCombinationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleCombination">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code"/>
        <result column="type" property="type" />
        <result column="status" property="status" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, type, status, version, create_time, update_time, deleted, create_by, update_by, tenant_id
    </sql>

    <!-- 查询量表名称是否已经存在 -->
    <select id="validScaleNameExists" resultType="java.lang.Boolean">
        select count(s.id)
        from scale_combination s inner join scale_combination_latest v on s.id = v.combination_id
        where s.deleted = 0
        <if test="scaleCombinationId != null and scaleCombinationId != ''">
            and s.id &lt;> #{scaleCombinationId}
        </if>
        <if test="scaleCode != null and scaleCode != ''">
            and s.code &lt;> #{scaleCode}
        </if>
        <if test="scaleName != null and scaleName != ''">
            and s.name = #{scaleName}
        </if>
        limit 1
    </select>

    <!-- 获取组合量表最新版本数据 -->
    <select id="getList" resultType="com.wftk.scale.biz.dto.scale.ScaleCombinationQueryDTO">
        select s.* from scale_combination s inner join scale_combination_latest v on s.id = v.combination_id
        where s.deleted = 0
        <if test="scaleName != null and scaleName != ''">
            and s.name like concat('%',#{scaleName},'%')
        </if>
        order by s.create_time desc,s.id desc
    </select>

    <select id="getLatestScaleByCode" resultType="com.wftk.scale.biz.entity.ScaleCombination">
        select s.*
            from scale_combination s
                inner join scale_combination_latest v
                    on s.id = v.combination_id
        <where>
            s.deleted = 0 and s.code = #{code}
        </where>
    </select>

    <select id="getListedScaleCombination" resultType="com.wftk.scale.biz.dto.scale.ScaleCombinationDTO">
        select
            s.id,sl.id listingId,sl.show_type listingShowType,s.name,s.code,s.type,s.status,
            sl.create_time createTime,sl.update_time updateTime
        from scale_combination s
            inner join scale_listing sl
                on sl.target_id = s.id and sl.type = 2 and sl.deleted = 0
        <where>
            s.deleted = 0 and sl.terminal_code = #{terminalCode}
            and sl.status = 1 and sl.enable = 1
            <if test="scaleCode != null and scaleCode != ''">
                and s.code = #{scaleCode}
            </if>
            <if test="scaleName != null and scaleName != ''">
                and s.name like concat('%',#{scaleName},'%')
            </if>
            <if test="type != null">
                and s.type = #{type}
            </if>
            <if test="listingShowType != null ">
                and sl.show_type = #{listingShowType}
            </if>
        </where>
    </select>
    <select id="getListedScaleCombinationDetail" resultType="com.wftk.scale.biz.dto.scale.ScaleDTO">
        select s.*
        from scale_combination_detail d
                 inner join scale s on d.scale_id = s.id
        where d.combination_id = #{combinationId}
          and d.deleted = 0
        order by s.sort, s.id
    </select>
    <select id="getScaleListingDetailDTO" resultType="com.wftk.scale.biz.dto.scale.ScaleListingDetailDTO">
        SELECT
        sc.id id ,sc.name scaleName, sl.id listingId, sc.code code, sc.name name,
        sl.original_price originalPrice,sl.price price ,sl.terminal_code terminalCode
        FROM
        scale_combination sc
        INNER JOIN scale_listing sl ON sc.id = sl.target_id
        AND sl.type = 2
        AND sl.deleted = 0
        WHERE
        sc.deleted = 0
        AND sl.STATUS = 1
        AND ( sc.CODE, sl.id ) IN
        <foreach collection="scaleSerials" item="scaleSerial" separator="," open="(" close=")">
            (#{scaleSerial.scaleCode},#{scaleSerial.listingId})
        </foreach>
        and sl.terminal_code = #{terminalCode}
    </select>
    <select id="getScaleListingDetail" resultType="com.wftk.scale.biz.dto.scale.ScaleListingDetailDTO">
        SELECT
            sc.id id ,sl.id listingId, sc.code code, sc.name name,sl.enable
            sl.original_price originalPrice,sl.price price ,sl.terminal_code terminalCode
        FROM
            scale_combination sc
                INNER JOIN scale_listing sl ON sc.id = sl.target_id
                AND sl.type = 2
                AND sl.deleted = 0
        WHERE
            sc.deleted = 0
          AND sl.STATUS = 1 and sl.terminal_code = #{terminalCode}
          and sc.code = #{scaleCode} and sl.id = #{listingId}
    </select>

    <!-- 根据ID更新量表完成状态 -->
    <update id="updateComplateStatus">
        update scale_combination
        set status = #{status},
            update_by = #{opUser}
        where id = #{scaleCombinationId}
    </update>

</mapper>
