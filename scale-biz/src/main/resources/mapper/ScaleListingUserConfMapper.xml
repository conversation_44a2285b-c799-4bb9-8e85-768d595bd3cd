<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleListingUserConfMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleListingUserConf">
        <id column="id" property="id" />
        <result column="scale_listing_id" property="scaleListingId" />
        <result column="name" property="name" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="type" property="type" />
        <result column="allow_repeat" property="allowRepeat" />
        <result column="allow_get_report" property="allowGetReport" />
        <result column="allow_notify" property="allowNotify" />
        <result column="order_free" property="orderFree" />
        <result column="allow_time_limit" property="allowTimeLimit" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, scale_listing_id, name, start_time, end_time, type, allow_repeat, allow_get_report, allow_notify, order_free, allow_time_limit, create_time, update_time, deleted, create_by, update_by, tenant_id
    </sql>

    <!-- 根据量表上架配置ID获取用户分发配置信息 -->
    <select id="findByScaleListingId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from scale_listing_user_conf
        where scale_listing_id = #{scaleListingId}
        limit 1
    </select>
    <select id="getById" resultType="com.wftk.scale.biz.entity.ScaleListingUserConf">
        select * from scale_listing_user_conf where id = #{id} and deleted = 0
    </select>

</mapper>
