<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleLatestMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleLatest">
        <id column="id" property="id" />
        <result column="scale_id" property="scaleId" />
        <result column="code" property="code" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, scale_id, code, create_time, update_time, deleted, tenant_id, create_by, update_by
    </sql>

    <!-- 根据量表编码获取最新版本信息 -->
    <select id="findByCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from scale_latest
        where code=#{code}
        limit 1
    </select>

</mapper>
