<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleWarnConfMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleWarnConf">
        <id column="id" property="id"/>
        <result column="scale_id" property="scaleId"/>
        <result column="factor_id" property="factorId"/>
        <result column="type" property="type"/>
        <result column="threshold" property="threshold"/>
        <result column="batch_no" property="batchNo"/>
        <result column="tag" property="tag"/>
        <result column="tag_id" property="tagId"/>
        <result column="warn_type" property="warnType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , scale_id, factor_id, type, threshold, tag,tag_id, warn_type, create_time, update_time, deleted, create_by, update_by, tenant_id,batch_no
    </sql>

    <!-- 校验量表预警因子阈值信息是否已经存在 -->
    <select id="validWarnConFactorExists" resultType="java.lang.Boolean">
        select count(1)
        from scale_warn_conf
        where deleted = 0
        <if test="id != null and id != ''">
            and id &lt;> #{id}
        </if>
        <if test="scaleId != null and scaleId != ''">
            and scale_id = #{scaleId}
        </if>
        <if test="factorId != null and factorId != ''">
            and factor_id = #{factorId}
        </if>
    </select>

    <select id="validWarnConfFactorTagExists" resultType="java.lang.Boolean">
        select count(1)
        from scale_warn_conf
        where deleted = 0
        <if test="scaleId != null and scaleId != ''">
            and scale_id = #{scaleId}
        </if>
        <if test="factorId != null and factorId != ''">
            and factor_id = #{factorId}
        </if>
        <if test="tagId != null and tagId != ''">
            and tag_id = #{tagId}
        </if>
        <if test="batchNo != null and batchNo != ''">
            and batch_no &lt;> #{batchNo}
        </if>
    </select>

    <!-- 校验因子维度信息关联的预警阈值信息 -->
    <select id="checkFactorRelationWarnConf" resultType="java.lang.String">
        select tag
        from scale_warn_conf
        where factor_id = #{factorId}
          and deleted = 0 limit 1
    </select>

    <!-- 根据条量表ID或获取量表预警阈值信息 -->
    <select id="getList" resultType="com.wftk.scale.biz.dto.scale.ScaleWarnConfQueryDTO">
        select c.*, f.name as factorName
        from scale_warn_conf c
                 left join scale_factor f on c.factor_id = f.id and c.scale_id = f.scale_id and f.deleted = 0
        where c.scale_id = #{scaleId}
          and c.deleted = 0
    </select>

    <select id="getFactorList" resultType="com.wftk.scale.biz.dto.scale.ScaleWarnConfQueryDTO">
        select *
        from scale_warn_conf
        where scale_id = #{scaleId}
          and deleted = 0
          and factor_id = #{factorId}
    </select>

    <delete id="deleteByBatchNo">
       update scale_warn_conf set deleted = 1 where deleted = 0 and batch_no = #{batchNo}
    </delete>

    <select id="getListByBatchNo" resultType="com.wftk.scale.biz.dto.scale.ScaleWarnConfQueryDTO">
        select *
        from scale_warn_conf
        where deleted = 0
          and batch_no = #{batchNo}
    </select>
    <select id="getByIds" resultType="com.wftk.scale.biz.dto.scale.ScaleWarnConfQueryDTO">
        select
            swc.*,
            sf.name as factorName
        from
            scale_warn_conf swc
            left join scale_factor sf on swc.factor_id = sf.id and swc.scale_id = sf.scale_id and sf.deleted = 0
        where
            swc.scale_id = #{scaleId}
            and swc.deleted = 0
            and sf.deleted = 0
            and swc.id in
            <foreach collection="ids" item="id" separator="," open="(" close=")" >
                #{id}
            </foreach>
    </select>


</mapper>
