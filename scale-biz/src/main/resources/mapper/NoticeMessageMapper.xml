<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.NoticeMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.NoticeMessage">
        <id column="id" property="id" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="biz_no" property="bizNo" />
        <result column="account" property="account" />
        <result column="identifier" property="identifier" />
        <result column="content" property="content" />
        <result column="event" property="event" />
        <result column="status" property="status" />
        <result column="channel" property="channel" />
        <result column="count" property="count" />
        <result column="next_time" property="nextTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, deleted, create_time, update_time, tenant_id, biz_no, account, identifier, content, event, status, channel, count, next_time, remark
    </sql>
    <select id="getNeedSendNoticeMessage" resultType="com.wftk.scale.biz.entity.NoticeMessage">
        select * from notice_message where status in (0,3) and next_time <![CDATA[ < ]]> now() and count <![CDATA[ < ]]> #{maxNoticeCount}
    </select>

</mapper>
