<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleWarnScopeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleWarnScope">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="scope_id" property="scopeId" />
        <result column="scope_type" property="scopeType" />
        <result column="scale_warn_setting_id" property="scaleWarnSettingId" />
        <result column="scale_id" property="scaleId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, deleted, tenant_id, create_by, update_by, scope_id, scope_type, scale_warn_setting_id, scale_id
    </sql>
    <delete id="deleteByByScaleIdAndScaleWarnSettingId">
        update scale_warn_scope set deleted = 1 where scale_id = #{scaleId} and scale_warn_setting_id = #{scaleWarnSettingId}
    </delete>
    <select id="queryByScaleIdAndScaleWarnSettingId" resultType="com.wftk.scale.biz.entity.ScaleWarnScope">
        select * from scale_warn_scope where scale_id=#{scaleId} and scale_warn_setting_id=#{scaleWarnSettingId} and deleted = 0
    </select>

</mapper>
