<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleReportConfItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleReportConfItem">
        <id column="id" property="id"/>
        <result column="scale_id" property="scaleId"/>
        <result column="factor_id" property="factorId"/>
        <result column="report_conf_id" property="reportConfId"/>
        <result column="item_code" property="itemCode"/>
        <result column="item_value" property="itemValue"/>
        <result column="item_file_path" property="itemFilePath"/>
        <result column="item_describe" property="itemDescribe"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, scale_id, factor_id,report_conf_id, item_code, item_value, item_file_path, item_describe, create_time, update_time, deleted, create_by, update_by, tenant_id
    </sql>
    <update id="updateDeletedById">
        update scale_report_conf_item
        set deleted = 1
        where id = #{id}
    </update>

    <select id="findByReportConfId" resultType="com.wftk.scale.biz.entity.ScaleReportConfItem">
        select
        <include refid="Base_Column_List"/>
        from scale_report_conf_item
        <where>
            deleted = 0 and report_conf_id = #{reportConfId}
            <if test="itemCodes != null and itemCodes.size() > 0">
                and item_code in
                <foreach collection="itemCodes" item="itemCode" separator="," open="(" close=")">
                    #{itemCode}
                </foreach>
            </if>
        </where>
    </select>
    <select id="findByReportConfIdAndFactorId" resultType="com.wftk.scale.biz.entity.ScaleReportConfItem">
        select
        <include refid="Base_Column_List"/>
        from scale_report_conf_item
        <where>
            deleted = 0 and report_conf_id = #{reportConfId}
            and (factor_id = 'all' or factor_id = #{factorId})
            <if test="itemCodes != null and itemCodes.size() > 0">
                and item_code in
                <foreach collection="itemCodes" item="itemCode" separator="," open="(" close=")">
                    #{itemCode}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getById" resultType="com.wftk.scale.biz.entity.ScaleReportConfItem">
        select * from scale_report_conf_item where deleted = 0 and id = #{id}
    </select>

</mapper>
