<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.AgreementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.Agreement">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="enabled" property="enabled" />
        <result column="code" property="code" />
        <result column="terminal" property="terminal" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, deleted,tenant_id, title, content, enabled, code, terminal
    </sql>
    <select id="findByCode" resultType="com.wftk.scale.biz.dto.scale.AgreementQueryDTO">
        select * from agreement where deleted = 0 and enabled = 1 and code = #{code}
    </select>
</mapper>
