<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.SysDepartmentMapper">

    <select id="concatCodeAndName" resultType="java.lang.String">
        select concat(d.code, '-', d.name) from sys_department d where d.deleted = 0 and d.enable = 1
    </select>

    <select id="countSysUserByIdAndEnable" resultType="java.lang.Boolean">
        select count(d.id) from
            (select code from sys_department where id = #{id}) a
            join sys_department b on find_in_set(a.code, b.parent_path) > 0
            join sys_user_department c on b.id = c.sys_department_id and and c.deleted = 0
            join sys_user d on c.sys_user_id = d.id and d.deleted = 0
            <if test="enable != null">
                and enable = #{enable}
            </if>
    </select>

    <select id="selectChildIdList" resultType="java.lang.Long">
        select a.id from sys_department a
        join sys_department b on find_in_set(a.code, b.parent_path) > 0
        where a.id = #{id} and b.deleted = 0 and a.deleted = 0
    </select>
</mapper>