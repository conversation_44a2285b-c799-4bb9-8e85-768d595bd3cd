<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.TerminalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.Terminal">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="deleted" property="deleted" />
        <result column="enable" property="enable" />
        <result column="type" property="type" />
        <result column="path" property="path" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, code, deleted, enable, type, path, create_time, update_time, create_by, update_by, tenant_id
    </sql>

    <!-- 查询终端编号是否已经存在 -->
    <select id="vaildTerminalCodeExists" resultType="java.lang.Boolean">
        select count(id)
        from terminal
        where deleted = 0
        <if test="terminalId != null and terminalId != ''">
            and id &lt;> #{terminalId}
        </if>
        <if test="terminalCode != null and terminalCode != ''">
            and code = #{terminalCode}
        </if>
        limit 1
    </select>

    <!-- 更新数据启用状态 -->
    <update id="updateEnable">
        update terminal
        set enable = #{enable},update_by = #{opUser}
        where id = #{terminalId}
    </update>

    <select id="getByCode" resultType="com.wftk.scale.biz.entity.Terminal">
        select <include refid="Base_Column_List"/>
        from terminal
        <where>
            deleted = 0
            <if test="terminalCode != null and terminalCode != ''">
                and code = #{terminalCode}
            </if>
        </where>
    </select>
    <select id="getNotBindingDepartmentList" resultType="com.wftk.scale.biz.dto.scale.TerminalDTO">
        select
          t.*
        from
            terminal t
        left join department_terminal dt on dt.terminal_code = t.code and dt.deleted = 0
        where t.deleted = 0 and t.enable = 1 and dt.id is null
    </select>
    <select id="getList" resultType="com.wftk.scale.biz.dto.scale.TerminalDTO">
        select * from terminal
        where deleted = 0
        <if test="terminalName != null and terminalName != ''">
            and name like concat('%',#{terminalName},'%')
        </if>
    </select>

</mapper>
