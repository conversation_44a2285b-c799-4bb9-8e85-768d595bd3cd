<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.DepartmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.Department">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="parent_path" property="parentPath" />
        <result column="manager_name" property="managerName" />
        <result column="manager_phone" property="managerPhone" />
        <result column="manager_email" property="managerEmail" />
        <result column="parent_id" property="parentId" />
        <result column="sort" property="sort" />
        <result column="enable" property="enable" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, code,parent_path, manager_name, manager_phone, manager_email, parent_id, sort, enable, remark, create_time, update_time, deleted, create_by, update_by, tenant_id
    </sql>

    <select id="selectByTerminalCode" resultType="com.wftk.scale.biz.entity.Department">
        select
            d.*
        from
            department d
        inner join department_terminal dt on dt.department_id = d.id and dt.deleted = 0
        inner join terminal t on t.code = dt.terminal_code and t.deleted = 0 and t.enable = 1
        <where>
            d.deleted = 0
            <if test="terminalCode != null and terminalCode != ''">
                and t.code = #{terminalCode}
            </if>
        </where>

    </select>

    <select id="findChildrenByParentId" resultType="com.wftk.scale.biz.entity.Department">
        with recursive tmp as(
            select d1.id,d1.tenant_id from department d1 where d1.id = #{parentId} and d1.deleted = 0
            union all
            select d1.id,d1.tenant_id from department d1 join tmp t on d1.parent_id = t.id where d1.deleted = 0
        )
        select d.* from department d inner join tmp t on d.id  = t.id where d.deleted = 0
    </select>

    <select id="validDepartmentBindTerminal" resultType="java.lang.Boolean">
        select
        count(*)
        from
        department d
        inner join department_terminal dt on dt.department_id = d.id and dt.deleted = 0
        inner join terminal t on t.code = dt.terminal_code and t.deleted = 0 and t.enable = 1
        <where>
            d.deleted = 0
            <if test="departmentId != null">
                and d.id = #{departmentId}
            </if>
        </where>
        limit 1
    </select>
    <select id="validTerminalBindDepartment" resultType="java.lang.Boolean">
        select
           count(*)
        from
           department d
        inner join department_terminal dt on dt.department_id = d.id and dt.deleted = 0
        inner join terminal t on t.code = dt.terminal_code and t.deleted = 0 and t.enable = 1
        <where>
            d.deleted = 0
            <if test="terminalCode != null and terminalCode != ''">
                and t.code = #{terminalCode}
            </if>
        </where>
        limit 1
    </select>

    <select id="selectTreeList" resultMap="BaseResultMap">
        select
        distinct d.*
        from
        department d
        left join department_terminal dt on dt.department_id = d.id and dt.deleted = 0
        left join terminal t on t.code = dt.terminal_code and t.deleted = 0 and t.enable = 1
        <where>
            d.deleted = 0
            <if test="terminalCode != null and terminalCode != ''">
                and t.code = #{terminalCode}
            </if>
            <if test="name != null and name != ''">
                and d.name like concat('%', #{name}, '%')
            </if>
            <if test="enable != null">
                and d.enable = #{enable}
            </if>
        </where>
    </select>

    <select id="concatCodeAndName" resultType="java.lang.String">
        select concat(d.code, '-', d.name) from department d where d.deleted = 0 and d.enable = 1
    </select>
    <select id="getChildDepartmentById" resultType="com.wftk.scale.biz.entity.Department">
        select distinct * from department where deleted = 0 and locate ((select code from department where id = #{id} and enable = 1), parent_path) > 0 and deleted = 0 and enable = 1
    </select>
    <select id="getByIds" resultType="com.wftk.scale.biz.entity.Department">
        select * from department where deleted = 0 and enable = 1 and id in
        <foreach item="item" collection="ids" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
