<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.SysUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.SysUser">
        <id column="id" property="id" />
        <result column="account" property="account" />
        <result column="password" property="password" />
        <result column="name" property="name" />
        <result column="sex" property="sex" />
        <result column="tel" property="tel" />
        <result column="birthday" property="birthday" />
        <result column="email" property="email" />
        <result column="entry_date" property="entryDate" />
        <result column="id_card" property="idCard" />
        <result column="remark" property="remark" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="enable" property="enable" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, tel, account, password, tenant_id, create_time, update_time, enable, deleted
    </sql>
    <select id="selectOneByAccount" resultType="com.wftk.scale.biz.entity.SysUser">
        select * from sys_user
        <where>
            account = #{account}
            <if test="enable != null">
                and status = #{enable}
            </if>
            and deleted = 0
        </where>
    </select>

    <select id="queryListByQueryDto" resultType="com.wftk.scale.biz.vo.SysUserVO"
            parameterType="com.wftk.scale.biz.dto.user.sysuser.SysUserQueryDTO">
        select t1.id, t1.account, t1.name, t3.id departmentId, t3.name departmentName,
               t1.tel, t4.roleIds, t4.roleNames, t1.sex,
               t1.enable, t1.update_by updateBy, t1.update_time updateTime
        from sys_user t1
        join sys_user_department t2 on t1.id = t2.sys_user_id and t2.deleted = 0
        join sys_department t3 on t2.sys_department_id = t3.id and t3.deleted = 0
        left join (
            select b.user_id, group_concat(a.id) roleIds, group_concat(a.name) roleNames
            from sys_role a
                join sys_user_role b on a.id = b.role_id and b.deleted = 0
            where a.deleted = 0
            group by b.user_id) t4 on t1.id = t4.user_id
        where t1.deleted = 0
        <if test="account != null and account != ''">
            and t1.account = #{account}
        </if>
        <if test="name != null and name != ''">
            and t1.name like concat('%', #{name}, '%')
        </if>
        <if test="departmentId != null">
            and t1.account = #{departmentId}
        </if>
        <if test="roleId != null">
            and find_in_set(#{roleId}, t4.roleIds) > 0
        </if>
        <if test="enable != null">
            and t1.enable = #{enable}
        </if>
        order by t1.create_time desc
    </select>


    <select id="detailById" resultType="com.wftk.scale.biz.vo.SysUserDetailVO">
        select t1.id, t1.account, t1.name, t3.id departmentId, t3.name departmentName,
               t1.tel, t4.roleIds, t4.roleNames, t1.sex,
               t1.enable, t1.update_by updateBy, t1.update_time updateTime
        from sys_user t1
        join sys_user_department t2 on t1.id = t2.sys_user_id and t2.deleted = 0
        join sys_department t3 on t2.sys_department_id = t3.id and t3.deleted = 0
        left join (
            select b.user_id, group_concat(a.id) roleIds, group_concat(a.name) roleNames
            from sys_role a
                     join sys_user_role b on a.id = b.role_id and b.deleted = 0
            where a.deleted = 0
            group by b.user_id) t4 on t1.id = t4.user_id
        where t1.deleted = 0 and t1.id = #{id}
    </select>
</mapper>
