<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatiorg//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleListingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleListing">
        <id column="id" property="id"/>
        <result column="cover" property="cover"/>
        <result column="target_id" property="targetId"/>
        <result column="target_name" property="targetName"/>
        <result column="target_type" property="targetType"/>
        <result column="target_code" property="targetCode"/>
        <result column="type" property="type"/>
        <result column="terminal_code" property="terminalCode"/>
        <result column="show_type" property="showType"/>
        <result column="report_url" property="reportUrl"/>
        <result column="api" property="api"/>
        <result column="original_price" property="originalPrice"/>
        <result column="price" property="price"/>
        <result column="status" property="status"/>
        <result column="enable" property="enable"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="deleted" property="deleted"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,cover,target_id,target_name,target_type, target_code, type, terminal_code, show_type, report_url, api, original_price, price,
        status, enable, create_time, update_time, create_by, update_by, deleted, tenant_id
    </sql>

    <!-- 根据量表ID和终端ID获取上架信息 -->
    <select id="findByTerminalIdAndTargetCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scale_listing
        where target_code = #{targetCode} and terminal_code = #{terminalCode} and show_type = #{showType} and deleted =
        0 limit 1
    </select>

    <!-- 更新量表下架状态 -->
    <update id="updateScaleListingStatus">
        update scale_listing
        set status   =#{status},
            update_by=#{opUser}
        where id = #{scaleListingId}
    </update>

    <!-- 获取已上架量表信息 -->
    <select id="getScaleListedList" resultType="com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO">
        select
        l.*, t.name as terminalName,n.name as targetTypeName
        from scale_listing l left join terminal t on l.terminal_code = t.code and t.deleted = 0
        left join scale_type n on l.target_type = n.id and n.deleted = 0
        where l.status = 1 and l.deleted = 0
        <if test="param.type != null and param.type != ''">
            and l.type = #{param.type}
        </if>
        <if test="param.targetType != null and param.targetType != ''">
            and l.target_type = #{param.targetType}
        </if>
        <if test="param.showType != null and param.showType != ''">
            and l.show_type = #{param.showType}
        </if>
        <if test="param.terminalCode != null and param.terminalCode != ''">
            and l.terminal_code = #{param.terminalCode}
        </if>
        <if test="param.opUser != null and param.opUser != ''">
            and l.create_by=#{param.opUser}
        </if>
        <if test="param.targetName != null and param.targetName != ''">
            and l.target_name like concat('%',#{param.targetName},'%')
        </if>
        order by l.id desc
    </select>


    <!-- 获取已上架量表信息 -->
    <select id="getScaleListedByPageDisplay" resultType="com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO">
        select
        l.*,l.id as scaleListingId, t.name as terminalName,n.name as targetTypeName
        from scale_listing l left join terminal t on l.terminal_code = t.code and t.deleted = 0
        left join scale_type n on l.target_type = n.id and n.deleted = 0
        where l.status = 1 and l.show_type = 1 and l.deleted = 0
        <if test="param.type != null and param.type != ''">
            and l.type = #{param.type}
        </if>
        <if test="param.targetType != null and param.targetType != ''">
            and l.target_type = #{param.targetType}
        </if>
        <if test="param.showType != null and param.showType != ''">
            and l.show_type = #{param.showType}
        </if>
        <if test="param.terminalCode != null and param.terminalCode != ''">
            and l.terminal_code = #{param.terminalCode}
        </if>
        <if test="param.opUser != null and param.opUser != ''">
            and l.create_by=#{param.opUser}
        </if>
        <if test="param.targetName != null and param.targetName != ''">
            and l.target_name like concat('%',#{param.targetName},'%')
        </if>
        order by l.id desc
    </select>

    <!-- 统计量表已上架终端信息 -->
    <select id="getNumOfScaleListed" resultType="java.lang.Integer">
        select count(t1.id) from scale_listing t1
        where status = 1
          and deleted = 0
          and type = #{type}
          and target_id in
            <if test="type == 2">
               (select id from scale_combination where code = (select code from scale_combination where id = #{targetId}) and deleted = 0)
            </if>
            <if test="type == 1">
               (select id from scale where code = (select code from scale where id = #{targetId}) and deleted = 0)
            </if>
    </select>

    <!-- 获取单个可上架量表信息 -->
    <select id="getScaleListingList" resultType="com.wftk.scale.biz.dto.listing.ScaleListingQueryDTO">
        select s.id as targetId,
        s.name as targetName,
        s.type as targetType,
        s.code as targetCode,
        t.name as targetTypeName,
        s.create_time as createTime,
        s.create_by as createBy,
        s.update_time as updateTime,
        s.update_by as updateBy
        from scale s inner join scale_latest v on s.id = v.scale_id
        left join scale_type t on s.type = t.id
        where s.deleted = 0 and s.status = 1
        <if test="param.targetType != null and param.targetType != ''">
            and s.type = #{param.targetType}
        </if>
        <if test="param.opUser != null and param.opUser != ''">
            and s.update_by = #{param.opUser}
        </if>
        <if test="param.targetName != null and param.targetName != ''">
            and s.name like concat('%',#{param.targetName},'%')
        </if>
        order by s.create_time desc,s.id desc
    </select>

    <!-- 获取组合可上架量表信息 -->
    <select id="getScaleCombinationListingList" resultType="com.wftk.scale.biz.dto.listing.ScaleListingQueryDTO">
        select s.id as targetId,
        s.name as targetName,
        s.type as evaluationType,
        s.code as targetCode,
        s.create_time as createTime,
        s.create_by as createBy,
        s.update_time as updateTime,
        s.update_by as updateBy
        from scale_combination s inner join scale_combination_latest v on s.id = v.combination_id
        where s.deleted = 0 and s.status = 1
        <if test="param.targetType != null and param.targetType != ''">
            and s.type = #{param.targetType}
        </if>
        <if test="param.opUser != null and param.opUser != ''">
            and s.create_by=#{param.opUser}
        </if>
        <if test="param.targetName != null and param.targetName != ''">
            and s.name like concat('%',#{param.targetName},'%')
        </if>
        order by s.create_time desc,s.id desc
    </select>

    <!-- 获取可分发量表信息 -->
    <select id="getScaleDistribute" resultType="com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO">
        select
        l.*, t.name as terminalName,n.name as targetTypeName
        from scale_listing l left join terminal t on l.terminal_code = t.code and t.deleted = 0
        left join scale_type n on l.target_type = n.id and n.deleted = 0
        where l.status = 1 and l.deleted = 0 and l.show_type = 2
        <if test="param.type != null and param.type != ''">
            and l.type = #{param.type}
        </if>
        <if test="param.targetType != null and param.targetType != ''">
            and l.target_type = #{param.targetType}
        </if>
        <if test="param.terminalCode != null and param.terminalCode != ''">
            and l.terminal_code = #{param.terminalCode}
        </if>
        <if test="param.opUser != null and param.opUser != ''">
            and l.create_by=#{param.opUser}
        </if>
        <if test="param.targetName != null and param.targetName != ''">
            and l.target_name like concat('%',#{param.targetName},'%')
        </if>
        order by l.id desc
    </select>
    <select id="getByTargetIdAndType" resultType="com.wftk.scale.biz.entity.ScaleListing">
        select
        <include refid="Base_Column_List"/>
        from scale_listing
        <where>
            deleted = 0 and target_id = #{targetId} and type = #{type} AND terminal_code = #{terminalCode}
        </where>
    </select>

    <select id="findById" resultType="com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO">
        select
        <include refid="Base_Column_List"/>
        from scale_listing
        where deleted =0 and id = #{scaleListingId}
        limit 1
    </select>
    <select id="getReportUrl" resultType="java.lang.String">
        select report_url from scale_listing where deleted = 0 and status = 1 and id = #{scaleListingId}
    </select>

</mapper>
