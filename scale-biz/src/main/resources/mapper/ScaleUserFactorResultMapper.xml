<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleUserFactorResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleUserFactorResult">
        <id column="id" property="id"/>
        <result column="result_id" property="resultId"/>
        <result column="factor_id" property="factorId"/>
        <result column="score" property="score"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="raw_score" property="rawScore"/>
        <result column="result_intro" property="resultIntro"/>
        <result column="inversion_score" property="inversionScore"/>
        <result column="factor_name" property="factorName"/>
        <result column="score_section" property="scoreSection"/>
        <result column="result" property="result"/>
        <result column="factor_show_type" property="factorShowType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, result_id, factor_id, score, create_time, update_time, deleted, create_by, update_by, tenant_id,
        raw_score, result_intro, inversion_score, factor_name, score_section,result,factor_show_type
    </sql>

    <!--根据测评记录ID获取测评因子得分情况-->
    <select id="getList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scale_user_factor_result
        where deleted = 0 and result_id = #{resultId}
    </select>

    <!--根据测评记录ID获取测评因子得分详情信息-->
    <select id="getDetailByResultId" resultType="com.wftk.scale.biz.dto.scale.ScaleUserFactorResultDetailDTO">
        select r.factor_id as factorId,
               r.factor_name      as factorName,
               r.score     as score,
               r.intro     as intro
        from scale_user_factor_result r
        where r.result_id = #{resultId}
    </select>

    <select id="getByResultIdAndFactorId" resultType="com.wftk.scale.biz.entity.ScaleUserFactorResult">
        select
        *
        from scale_user_factor_result
        <where>
            result_id = #{resultId}
            and factor_id = #{factorId}
            <if test="showType != null and showType != ''">
                and factor_show_type like concat('%',#{showType},'%')
            </if>
            and deleted = 0
        </where>
    </select>
    <select id="getListByResultId" resultType="com.wftk.scale.biz.entity.ScaleUserFactorResult">
        select
            *
        from scale_user_factor_result
        <where>
            result_id = #{resultId}
            and factor_id in
             <foreach collection="factorIds" open="(" close=")" separator="," item="factorId">
                 #{factorId}
             </foreach>
            and deleted = 0
        </where>
    </select>

    <select id="getScoreByResultIdAndFactorId" resultType="java.math.BigDecimal">
        select
            score
        from scale_user_factor_result
        where result_id = #{resultId}
          and factor_id = #{factorId}
          and deleted = 0
    </select>

    <select id="selectReportFormData" resultType="java.util.Map">
        select ${clonwnStr}
        from scale_user_factor_result
        <where>
            result_id = #{resultId}
            and deleted = 0
            and factor_id in
            <foreach collection="factorIds" item="factorId" separator="," open="(" close=")">
                #{factorId}
            </foreach>
        </where>
    </select>


</mapper>
