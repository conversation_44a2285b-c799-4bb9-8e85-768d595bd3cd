<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.DepartmentTerminalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.DepartmentTerminal">
        <id column="id" property="id" />
        <result column="terminal_code" property="terminalCode" />
        <result column="department_id" property="departmentId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, terminal_code, department_id, create_time, update_time, deleted, create_by, update_by, tenant_id
    </sql>

    <select id="getConcatTerminalCodeByDepartmentIds" resultMap="BaseResultMap">
        select department_id, group_concat(DISTINCT terminal_code) terminal_code from department_terminal
               <where>
                   deleted = 0
                   <if test="departmentIds != null and departmentIds.size() > 0">
                       and department_id in
                       <foreach collection="departmentIds" item="id" separator="," open="(" close=")">
                           #{id}
                       </foreach>
                   </if>
               </where>
        group by department_id
    </select>
</mapper>
