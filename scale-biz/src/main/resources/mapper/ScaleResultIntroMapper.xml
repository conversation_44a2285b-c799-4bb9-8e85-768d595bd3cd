<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleResultIntroMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleResultIntro">
        <id column="id" property="id"/>
        <result column="scale_id" property="scaleId"/>
        <result column="sort" property="sort"/>
        <result column="intro" property="intro"/>
        <result column="factor_id" property="factorId"/>
        <result column="score" property="score"/>
        <result column="score_convert_type" property="scoreConvertType"/>
        <result column="convert_score" property="convertScore"/>
        <result column="result" property="result"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="show_type" property="showType"/>
        <result column="chart_type" property="chartType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id , scale_id, sort, intro, factor_id, score, score_convert_type, convert_score, result, create_time, update_time, deleted, create_by, update_by, tenant_id,show_type,chart_type
    </sql>

    <!-- 校验量表结果解读内容是否存在 -->
    <select id="vaildResultIntroDataIntegrity" resultType="java.lang.Boolean">
        select count(1)
        from scale_result_intro
        where scale_id = #{scaleId}
          and deleted = 0 limit 1
    </select>

    <!-- 校验量表结果解读名称是否存在 -->
    <select id="vaildResultIntroNameExists" resultType="java.lang.Boolean">
        select count(1)
        from scale_result_intro
        where deleted = 0
        <if test="factorId != null ">
            and factor_id = #{factorId}
        </if>
        <if test="scaleId != null ">
            and scale_id = #{scaleId}
        </if>
        <if test="resultIntroName != null and resultIntroName != ''">
            and intro = #{resultIntroName}
        </if>
        limit 1
    </select>

    <!-- 校验因子维度关联的结果解读信息 -->
    <select id="checkFactorRelationIntro" resultType="java.lang.String">
        select intro from scale_result_intro where factor_id=#{factorId} and deleted = 0 limit 1;
    </select>

    <!-- 根据量表ID获取结果解读列表数据 -->
    <select id="getList" resultType="com.wftk.scale.biz.dto.scale.ScaleResultIntroDTO">
        select i.*,i.show_type as original_show_type, f.name as factorName
        from scale_result_intro i left join scale_factor f on i.factor_id = f.id and i.scale_id = f.scale_id and f.deleted = 0
        where i.deleted = 0 and i.scale_id = #{scaleId}
    </select>

    <!-- 根据结果解读ID获取结果解读详情 -->
    <select id="findById" resultType="com.wftk.scale.biz.dto.scale.ScaleResultIntroDTO">
        select i.*, f.name as factorName
        from scale_result_intro i left join scale_factor f on i.factor_id = f.id and i.scale_id = f.scale_id and f.deleted = 0
        where i.deleted = 0 and i.id = #{resultIntroId}
        limit 1
    </select>

    <!-- 根据量表ID和因子维度ID获取结果解读信息 -->
    <select id="findByScaleIdAndFactorId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scale_result_intro
        where deleted = 0 and scale_id = #{scaleId} and factor_id = #{factorId}
    </select>

</mapper>
