<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.SystemOptLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.SystemOptLog">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="module" property="module" />
        <result column="ip" property="ip" />
        <result column="user_type" property="userType" />
        <result column="opt_type" property="optType" />
        <result column="request_method" property="requestMethod" />
        <result column="params" property="params" />
        <result column="uri" property="uri" />
        <result column="description" property="description" />
        <result column="opt_time" property="optTime" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="user_agent" property="userAgent" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_name, module, ip, user_type, opt_type, request_method, params, uri, description, opt_time, deleted, create_time, user_agent, tenant_id
    </sql>

</mapper>
