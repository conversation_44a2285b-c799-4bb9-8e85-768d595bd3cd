<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.TenantSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.TenantSetting">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="client_name" property="clientName" />
        <result column="client_secret" property="clientSecret" />
        <result column="enabled" property="enabled" />
        <result column="client_description" property="clientDescription" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, tenant_id, client_name, client_secret, enabled, client_description, create_by, update_by
    </sql>

    <select id="getTenantId" resultType="com.wftk.scale.biz.entity.TenantSetting">
        SELECT * FROM tenant_setting
        WHERE enabled = 1 and tenant_id = #{tenantId}
    </select>
    <select id="getAll" resultType="com.wftk.scale.biz.entity.TenantSetting">
        select * from tenant_setting where enabled = 1
    </select>
</mapper>
