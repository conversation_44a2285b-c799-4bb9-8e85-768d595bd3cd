<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleCombinationDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleCombinationDetail">
        <id column="id" property="id"/>
        <result column="combination_id" property="combinationId"/>
        <result column="scale_id" property="scaleId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , combination_id, scale_id, create_time, update_time, deleted, create_by, update_by, tenant_id
    </sql>

    <!-- 根据组合量表ID删除关联的量表信息 -->
    <update id="deleteByCombinationId">
        update scale_combination_detail
        set deleted = 1
        where combination_id = #{combinationId}
    </update>

    <!-- 根据组合量表ID获取关联的量表数据 -->
    <select id="findByCombinationId" resultType="com.wftk.scale.biz.dto.scale.ScaleQueryDTO">
        select s.*, st.name typeName
        from scale_combination_detail d
                 inner join scale s on d.scale_id = s.id and s.deleted = 0
                 inner join scale_type st on s.type = st.id and st.deleted = 0
        where d.combination_id = #{combinationId}
          and d.deleted = 0
        order by s.sort, s.id
    </select>

    <select id="findScaleIdByCombinationId" resultType="java.lang.Long">
        select scale_id
        from scale_combination_detail
        where combination_id = #{combinationId}
          and deleted = 0
    </select>
    <select id="findScaleIdByCombinationIds" resultType="java.lang.Long">
        select scale_id
        from scale_combination_detail
        where combination_id in
           <foreach collection="combinationIds" item="combinationId" separator="," open="(" close=")">
               #{combinationId}
           </foreach>
          and deleted = 0
    </select>

</mapper>
