<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.HttpJobMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.HttpJob">
        <id column="id" property="id" />
        <result column="biz_id" property="bizId" />
        <result column="biz_code" property="bizCode" />
        <result column="status" property="status" />
        <result column="count" property="count" />
        <result column="max_count" property="maxCount" />
        <result column="params" property="params" />
        <result column="next_time" property="nextTime" />
        <result column="deleted" property="deleted" />
        <result column="ended" property="ended" />
        <result column="end_time" property="endTime" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, biz_id, biz_code, status, count, max_count, params, next_time, deleted, ended, end_time, remark, create_time, update_time, tenant_id
    </sql>

    <update id="updateHttpJobResultByBizCodeAndBizId">
        update http_job
        <set>
            status = #{status},
            count = count + 1,
            <if test="nextTime != null">
                next_time = #{nextTime},
            </if>
            <if test="ended != null">
                ended = #{ended},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="remark != null and remark != ''">
                remark= #{remark}
            </if>
        </set>
        <where>
            deleted = 0
            and biz_id = #{bizId}
            and biz_code = #{bizCode}
        </where>
    </update>
    <update id="updateStatusByBizCodeAndBizId">
        update http_job
        <set>
            status = #{status}
        </set>
        <where>
            deleted = 0
            and biz_id = #{bizId}
            and biz_code = #{bizCode}
        </where>
    </update>
    <update id="updateStatusByBizCodeAndBizIds">
        update http_job
        <set>
            status = #{status},
            next_time = #{nextTime}
        </set>
        <where>
            deleted = 0
            and biz_code = #{bizCode}
            and biz_id in
        <foreach collection="bizIds" item="bizId" open="(" close=")" separator=",">
            #{bizId}
        </foreach>
        </where>
    </update>

    <select id="selectByBizCodeAndBizId" resultType="com.wftk.scale.biz.entity.HttpJob">
        select * from http_job where biz_id = #{bizId} and biz_code = #{bizCode} and deleted = 0
    </select>
    <select id="selectNotEndList" resultType="com.wftk.scale.biz.entity.HttpJob">
        select *
        from http_job
        where deleted = 0
          and status in (1, 10)
          and ended = 0
          and next_time &lt; now()
          and count &lt; max_count
    </select>

</mapper>
