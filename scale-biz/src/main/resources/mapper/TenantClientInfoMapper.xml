<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.TenantClientInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.TenantClientInfo">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="client_id" property="clientId" />
        <result column="client_secret" property="clientSecret" />
        <result column="enable" property="enable" />
        <result column="description" property="description" />
        <result column="pre_grant_type" property="preGrantType" />
        <result column="grant_type" property="grantType" />
        <result column="pre_auth_expire_in_seconds" property="preAuthExpireInSeconds" />
        <result column="access_token_expire_in_seconds" property="accessTokenExpireInSeconds" />
        <result column="access_token_transition_in_seconds" property="accessTokenTransitionInSeconds" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="algorithm" property="algorithm" />
        <result column="public_key" property="publicKey" />
        <result column="private_key" property="privateKey" />
        <result column="target_public_key" property="targetPublicKey" />
        <result column="server_ip" property="serverIp" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, client_id, client_secret, enable, description, pre_grant_type, grant_type, pre_auth_expire_in_seconds, access_token_expire_in_seconds, access_token_transition_in_seconds, deleted, create_time, update_time,algorithm,public_key,private_key,target_public_key,server_ip, create_by, update_by
    </sql>
    <select id="selectByClintId" resultType="com.wftk.scale.biz.entity.TenantClientInfo">
        select * from tenant_client_info
        <where>
            deleted = 0 and client_id = #{clientId}
            <if test="enable != null">
                and enable = #{enable}
            </if>
        </where>
    </select>

</mapper>
