<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleQuestionOptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleQuestionOption">
        <id column="id" property="id"/>
        <result column="question_id" property="questionId"/>
        <result column="scale_id" property="scaleId"/>
        <result column="value" property="value"/>
        <result column="label" property="label"/>
        <result column="score" property="score"/>
        <result column="result" property="result"/>
        <result column="sort" property="sort"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="enable_input" property="enableInput"/>
        <result column="remark" property="remark"/>
        <result column="file_path" property="filePath"/>
        <result column="offset" property="offset"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="operate_value" property="operateValue"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , question_id, scale_id, value, label, score, result, sort, create_time, update_time, deleted, create_by, update_by, enable_input, remark, file_path, offset,tenant_id,operate_value
    </sql>

    <!-- 根据量表ID和题目ID获取题目选项信息 -->
    <select id="findByScaleIdAndQuestionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scale_question_option
        where deleted = 0
        <if test="scaleId != null and scaleId != ''">
            and scale_id = #{scaleId}
        </if>
        <if test="questionId != null and questionId != ''">
            and question_id = #{questionId}
        </if>
        order by sort, id
    </select>
    <select id="getQuestionTotalScore" resultType="java.math.BigDecimal">
        select sum(a.score) from (select max(score) score from scale_question_option where scale_id = #{scaleId} group by question_id ) a
    </select>

    <!-- 根据量表ID和题目ID删除选项数据 -->
    <update id="deleteByScaleIdAndQuestionId">
        update scale_question_option set deleted = 1
        <where>
            <if test="scaleId != null and scaleId != ''">
                and scale_id = #{scaleId}
            </if>
            <if test="questionId != null and questionId != ''">
                and question_id = #{questionId}
            </if>
        </where>
    </update>

    <select id="findOverallScoreByScaleId"
            resultType="com.wftk.scale.biz.dto.scale.ScaleQuestionHighestOverallScoreDTO">
        select IFNULL(sum(score),0) as totalScore from (
        select max(score) as score from scale_question_option
        where deleted = 0
        and scale_id = #{scaleId} and question_id in
        <foreach collection="questionIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        group by question_id
        ) as temp
    </select>
</mapper>
