<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleQuestionRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleQuestionRelation">
        <id column="id" property="id" />
        <result column="scale_id" property="scaleId" />
        <result column="question_id" property="questionId" />
        <result column="type" property="type" />
        <result column="strategy" property="strategy" />
        <result column="reminder" property="reminder" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, scale_id, question_id, type, strategy, reminder, create_time, update_time, deleted, create_by, update_by, tenant_id
    </sql>

    <!-- 校验量表题目跳转逻辑是否存在 -->
    <select id="vaildRelationDataIntegrity">
        select count(1)
        from scale_question_relation
        where scale_id = #{scaleId}
          and deleted = 0 limit 1
    </select>

    <!-- 根据量表ID检索量表跳转逻辑列表数据 -->
    <select id="getList" resultType="com.wftk.scale.biz.dto.scale.ScaleQuestionRelationDTO">
        select r.*,q.question_number as questionNumber, q.sub_number as questionSubNumber
        from scale_question_relation r left join scale_question q on r.scale_id = q.scale_id and r.question_id = q.id and q.deleted = 0
        where r.scale_id = #{scaleId}
        and r.deleted = 0
    </select>

    <!-- 根据量表ID和题目ID获取题目逻辑跳转信息 -->
    <select id="findByScaleIdAndQuestionId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from scale_question_relation
        where deleted=0
        <if test="scaleId != null and scaleId != ''">
            and scale_id = #{scaleId}
        </if>
        <if test="questionId != null and questionId != ''">
            and question_id = #{questionId}
        </if>
    </select>
</mapper>
