<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleType">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="description" property="description"/>
        <result column="sort" property="sort"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , name, code, description, sort, create_time, update_time, deleted, create_by, update_by, tenant_id
    </sql>

    <!-- 查询量表分类名称是否已经存在 -->
    <select id="validScaleTypeNameExist" resultType="java.lang.Boolean">
        select count(id)
        from scale_type
        where deleted = 0
        <if test="id != null and id != ''">
            and id &lt;> #{id}
        </if>
        <if test="scaleTypeName != null and scaleTypeName != ''">
            and name=#{scaleTypeName}
        </if>
        limit 1
    </select>

    <!-- 查询量表分类编号是否已经存在 -->
    <select id="validScaleTypeCodeExist" resultType="java.lang.Boolean">
        select count(id)
        from scale_type
        where deleted = 0
        <if test="id != null and id != ''">
            and id &lt;> #{id}
        </if>
        <if test="scaleTypeCode != null and scaleTypeCode != ''">
            and code=#{scaleTypeCode}
        </if>
        limit 1
    </select>

    <!-- 根据条件检索量表分类列表数据 -->
    <select id="getList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scale_type
        where deleted = 0
        <if test="scaleTypeName != null and scaleTypeName != ''">
            and name like concat('%',#{scaleTypeName},'%')
        </if>
        <if test="scaleTypeName != null and scaleTypeName != ''">
            and name like concat('%',#{scaleTypeName},'%')
        </if>
        order by sort,id
    </select>

    <!-- 根据量表分类ID获取分类量表的数量 -->
    <select id="getNumOfScale" resultType="java.lang.Integer">
        select count(s.id)
        from scale s
                 inner join scale_latest v on s.id = v.scale_id
        where s.deleted = 0
          and s.type = #{scaleTypeId} limit 1
    </select>

    <select id="getListByTerminalCode" resultType="com.wftk.scale.biz.entity.ScaleType">
        select distinct st.* from scale s
        inner join scale_listing sl
        on s.id = sl.target_id and sl.type = 1 and sl.deleted = 0
        inner join scale_latest v on s.id = v.scale_id
        inner join scale_type st on st.id = s.type and st.deleted = 0
        <where>
            s.deleted = 0 and sl.deleted = 0 and sl.status = 1
            and sl.terminal_code = #{terminalCode}
        </where>
    </select>

    <select id="ownerScaleTypes" resultType="com.wftk.scale.biz.entity.ScaleType">
        select distinct st.id, st.code, st.name from scale_listing sl
            inner join scale s on s.id = sl.target_id and s.deleted = 0
            inner join scale_type st on st.id = s.type and st.deleted = 0
        <where>
            sl.type = 1 and sl.show_type = 1 and sl.deleted = 0 and sl.terminal_code = #{terminalCode} and sl.status = 1
        </where>
    </select>

</mapper>
