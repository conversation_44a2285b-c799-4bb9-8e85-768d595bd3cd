<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleWarnSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleWarnSetting">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="scale_id" property="scaleId" />
        <result column="level" property="level" />
        <result column="scale_warn_conf_id" property="scaleWarnConfId" />
        <result column="evaluate_myself" property="evaluateMyself" />
        <result column="scope" property="scope" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, deleted, tenant_id, create_by, update_by, scale_id, level, scale_warn_conf_id, evaluate_myself, scope
    </sql>
    <delete id="deleteById">
        update scale_warn_setting set deleted = 1 where id = #{id}
    </delete>
    <resultMap id="scaleScaleFactor" type="com.wftk.scale.biz.output.ScaleScaleFactorOutput">
        <id column="scaleId" property="scaleId" />
        <result column="scaleName" property="scaleName" />
        <result column="scaleCode" property="scaleCode" />
        <collection property="scaleWarnConfOutputList" javaType="java.util.List" ofType="com.wftk.scale.biz.output.ScaleScaleFactorOutput$ScaleWarnConfFactorOutput">
            <id column="scaleFactorId" property="scaleFactorId" />
            <result column="scaleFactorName" property="scaleFactorName" />
            <result column="scaleWarnType" property="scaleWarnType" />
            <result column="scaleWarnConfId" property="scaleWarnConfId" />
        </collection>
    </resultMap>
    <select id="getByScaleIdAndScaleWarnConfId" resultType="com.wftk.scale.biz.entity.ScaleWarnSetting">
        select * from scale_warn_setting where FIND_IN_SET(#{scaleWarnConfId},scale_warn_conf_id) and scale_id = #{scaleId} and deleted = 0
    </select>
    <select id="query" resultType="com.wftk.scale.biz.output.ScaleWarnSettingPageQueryOutput">
        select
            sws.id as id ,
            sws.create_time as createTime,
            sws.scale_id as scaleId,
            s.name as scaleName,
            s.code as scaleCode,
            sws.level as level,
            sws.evaluate_myself as evaluateMyself,
            sws.scope as scope,
            group_concat(distinct sf.name) as scaleFactorNames,
            group_concat(distinct u1.name) as receivingScaleWarnUserNames,
            group_concat(distinct if(sws.scope = 1, u2.name, d.name)) as scaleWarnScopeNames
        from scale_warn_setting sws
        join scale_warn_conf swc on swc.id in (sws.scale_warn_conf_id)
        join scale_warn_scope swsp on sws.id = swsp.scale_warn_setting_id
        join scale_warn_user swu on swu.scale_warn_setting_id = sws.id
        join scale_factor sf on sf.id = swc.factor_id
        join scale s on s.id = sws.scale_id
        join user u1 on u1.id = swu.user_id
        left join user u2 on u2.id = swsp.scope_id and sws.scope = 1 and u2.deleted = 0
        left join department d on d.id = swsp.scope_id and sws.scope = 2 and d.deleted = 0
        where
            sws.deleted = 0
          and swc.deleted = 0
          and swsp.deleted = 0
          and swu.deleted = 0
          and sf.deleted = 0
          and s.deleted = 0
          and u1.deleted = 0
        <if test="input.scaleName != null and input.scaleName != ''">
            and s.name like concat('%',#{input.scaleName},'%')
        </if>
        <if test="input.level != null">
            and sws.level = #{input.level}
        </if>
        <if test="input.evaluateMyself != null">
            and sws.evaluate_myself = #{input.evaluateMyself}
        </if>
        <if test="input.scope != null">
            and sws.scope = #{input.scope}
        </if>
        <if test="input.scaleFactorName != null and input.scaleFactorName != ''">
            and sf.name like concat('%',#{input.scaleFactorName},'%')
        </if>
        group by sws.id
        order by sws.id desc
    </select>
    <select id="queryScaleScaleFactor" resultMap="scaleScaleFactor">
        select
            s.id as scaleId,
            s.code as scaleCode,
            s.name as scaleName,
            sf.id as scaleFactorId,
            sf.name as scaleFactorName,
            swc.warn_type as scaleWarnType,
            swc.id as scaleWarnConfId
        from
            scale s
        join scale_latest sl on sl.scale_id = s.id
        join scale_warn_conf swc on s.id = swc.scale_id
        join scale_factor sf on sf.id = swc.factor_id
        where
            s.deleted = 0
          and swc.deleted = 0
          and sf.deleted = 0
          and sl.deleted = 0
    </select>
    <select id="getById" resultType="com.wftk.scale.biz.entity.ScaleWarnSetting">
        select * from scale_warn_setting where id = #{id} and deleted = 0
    </select>


</mapper>
