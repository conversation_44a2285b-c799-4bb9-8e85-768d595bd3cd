<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleReportConfSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleReportConfSetting">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="report_conf_id" property="reportConfId" />
        <result column="type" property="type" />
        <result column="report_type" property="reportType" />
        <result column="chart_type" property="chartType" />
        <result column="value" property="value" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, deleted, tenant_id,report_conf_id, type, report_type, chart_type, value, create_by, update_by
    </sql>
    <delete id="delByReportConfId">
        update
            scale_report_conf_setting
        set deleted = 1
        where report_conf_id = #{reportConfId}
          and deleted = 0
    </delete>
    <select id="selectByReportConfId" resultType="com.wftk.scale.biz.entity.ScaleReportConfSetting">
        select * from scale_report_conf_setting
        where report_conf_id = #{reportConfId}
          and deleted = 0
    </select>
    <select id="selectScaleReportConfSettingByReportConfId"
            resultType="com.wftk.scale.biz.entity.ScaleReportConfSetting">
        select * from scale_report_conf_setting
        <where>
            report_conf_id = #{reportConfId}
            and type = #{type}
            and deleted = 0
            <if test="reportType != null">
                and report_type = #{reportType}
            </if>
        </where>
    </select>

</mapper>
