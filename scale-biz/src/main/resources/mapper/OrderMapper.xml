<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.OrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.Order">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="type" property="type" />
        <result column="target_id" property="targetId" />
        <result column="target_name" property="targetName" />
        <result column="scale_listing_id" property="scaleListingId" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="user_account" property="userAccount" />
        <result column="terminal_serial_no" property="terminalSerialNo" />
        <result column="terminal_code" property="terminalCode" />
        <result column="phone" property="phone" />
        <result column="amount" property="amount" />
        <result column="source" property="source" />
        <result column="status" property="status" />
        <result column="pay_channel" property="payChannel" />
        <result column="expir_time" property="expirTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
        <result column="original_amount" property="originalAmount" />
        <result column="complete_time" property="completeTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, type, target_id,target_name,scale_listing_id, user_id, user_name,user_account, terminal_code, phone, amount, source, status, pay_channel, create_time, update_time, deleted, create_by, update_by, tenant_id, original_amount,terminal_serial_no,expir_time,complete_time
    </sql>
    <update id="updateStatus">
        update `order` set status = #{status} where order_no = #{orderNo} and status = #{oldStatus} and deleted = 0
    </update>
    <select id="getByTerminalSerialNo" resultType="com.wftk.scale.biz.entity.Order">
        select
            <include refid="Base_Column_List"/>
        from `order`
          <where>
            deleted = 0
            <if test="terminalCode != null">
                and terminal_code = #{terminalCode}
            </if>
              <if test="terminalSerialNo != null">
                  and terminal_serial_no = #{terminalSerialNo}
              </if>
          </where>
    </select>
    <select id="getByTerminalSerialNoAndOrderNo" resultType="com.wftk.scale.biz.entity.Order">
        select
        <include refid="Base_Column_List"/>
        from `order`
        <where>
            deleted = 0
            <if test="terminalCode != null">
                and terminal_code = #{terminalCode}
            </if>
            <if test="terminalSerialNo != null and terminalSerialNo != ''">
                and terminal_serial_no = #{terminalSerialNo}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
        </where>
    </select>
    <select id="getList"
            parameterType="com.wftk.scale.biz.dto.order.AdminOrderQueryDto"
            resultType="com.wftk.scale.biz.dto.order.OrderQueryDTO">
        select a.id, a.user_account userAccount, a.user_name userName,
               a.department_id departmentId, a.phone, a.target_name scaleName, a.type scaleType,
               a.terminal_code terminalCode, a.source,
               case when ifnull(a.amount, 0) = 0 then 0 else 1 end amountType,
               a.pay_channel payMethod, a.amount, a.status status, a.update_time updateTime
        from `order` a
        <where>
            a.deleted = 0
            <if test="userAccount != null">
                and a.user_account = #{userAccount}
            </if>
            <if test="userName != null">
                and a.user_name like concat('%',#{userName},'%')
            </if>
            <if test="departmentId != null">
                and a.department_id = #{departmentId}
            </if>
            <if test="phone != null">
                and a.phone = #{phone}
            </if>
            <if test="scaleName != null">
                and a.target_name like concat('%',#{scaleName},'%')
            </if>
            <if test="terminalCode != null">
                and a.terminal_code = #{terminalCode}
            </if>
            <if test="id != null">
                and a.id = #{id}
            </if>
        </where>
        order by a.update_time desc
    </select>

    <select id="getExtOrderList" resultType="com.wftk.scale.biz.entity.Order">
        select
        <include refid="Base_Column_List"/>
        from `order`
        <where>
            deleted = 0
            <if test="terminalCode != null">
                and terminal_code = #{terminalCode}
            </if>
            <if test="terminalSerialNos != null and terminalSerialNos.size > 0">
                and terminal_serial_no in
                <foreach collection="terminalSerialNos" open="(" close=")" separator="," item="terminalSerialNo">
                    #{terminalSerialNo}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getByOrderNo" resultType="com.wftk.scale.biz.entity.Order">
        select
          <include refid="Base_Column_List"/>
        from `order`
        <where>
            deleted = 0 and order_no = #{orderNo}
        </where>
    </select>

</mapper>
