<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleListingUserRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleListingUserRecord">
        <id column="id" property="id"/>
        <result column="scale_listing_id" property="scaleListingId"/>
        <result column="target_id" property="targetId"/>
        <result column="target_name" property="targetName"/>
        <result column="target_type" property="targetType"/>
        <result column="user_conf_id" property="userConfId"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="department_id" property="departmentId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, scale_listing_id, user_conf_id, user_id, user_name, department_id, create_time, update_time, deleted, create_by, update_by, tenant_id
    </sql>

    <!-- 获取量表分发记录（根据type类型区分单个量表分发记录和组合量表分发记录）建议将用户信息部门信息进行冗余,减少查询关联!!! -->
    <select id="getScaleDistributeRecord" resultType="com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordQueryDTO">
        select u.id as userId,
        u.account as userAccount,
        u.`name` as userName,
        u.phone as phone,
        r.id as id,
        r.scale_listing_id as scaleListingId,
        r.target_id as targetId,
        r.target_name as targetName,
        r.target_type as targetType,
        t.name as targetTypeName,
        r.department_id as departmentId,
        -- r.order_no as orderNo,
        r.type as type,
        d.name as departmentName,
        l.terminal_code as terminalCode,
        n.name as terminalName,
        r.update_by as updateBy,
        r.update_time as updateTime
        from scale_listing_user_record r
        left join scale_listing l on l.id = r.scale_listing_id and r.deleted = 0
        left join terminal n on l.terminal_code = n.code and n.deleted = 0
        left join scale_type t on r.target_type = t.id and t.deleted = 0
        left join user u on r.user_id = u.id and u.deleted = 0
        left join (
            select d.*,m.name from user_department d left join department m on d.department_id = m.id
            where d.deleted = 0
        ) d on u.id = d.user_id
        where r.deleted = 0
        <if test="param.type != null and param.type != ''">
            and r.type = #{param.type}
        </if>
        <if test="param.targetName != null and param.targetName != ''">
            and r.target_name like concat('%',#{param.targetName},'%')
        </if>
        <if test="param.terminalCode != null and param.terminalCode != ''">
            and n.code = #{param.terminalCode}
        </if>
        <if test="param.userAccount != null and param.userAccount != ''">
            and u.account like concat('%',#{param.userAccount},'%')
        </if>
        <if test="param.userName != null and param.userName != ''">
            and u.name like concat('%',#{param.userName},'%')
        </if>
        <if test="param.userId != null and param.userId != ''">
            and u.id = #{param.userId}
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone like concat('%',#{param.phone},'%')
        </if>
        <if test="param.deptId != null and param.deptId != ''">
            and d.department_id = ${param.deptId}
        </if>
        order by r.create_time desc, r.id desc
    </select>

    <select id="findByListingIdAndTargetId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from scale_listing_user_record
        where scale_listing_id = #{listingId} and

    </select>



    <select id="getScaleDistributeRecordByUserId" resultType="com.wftk.scale.biz.entity.ScaleListingUserRecord">
         select * from scale_listing_user_record
         <where>
             deleted = 0
             <if test="userId != null">
                 and user_id = #{userId}
             </if>
             <if test="scaleListingId != null">
                 and scale_listing_id = #{scaleListingId}
             </if>
         </where>
    </select>
</mapper>
