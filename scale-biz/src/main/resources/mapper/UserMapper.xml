<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.User">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="account" property="account" />
        <result column="password" property="password" />
        <result column="name" property="name" />
        <result column="sex" property="sex" />
        <result column="birthday" property="birthday" />
        <result column="phone" property="phone" />
        <result column="id_card" property="idCard" />
        <result column="email" property="email" />
        <result column="remark" property="remark" />
        <result column="enable" property="enable" />
        <result column="terminal_code" property="terminalCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, account, password, name, sex, birthday, phone, id_card, email, remark, enable, create_time, update_time, deleted, create_by, update_by, tenant_id,terminal_code
    </sql>
    <select id="selectByCodeAndDepartmentId" resultType="com.wftk.scale.biz.entity.User">
        select
        u.*
        from user u
        inner join user_department ud on u.id = ud.user_id and ud.deleted = 0
        inner join department d on d.id = ud.department_id and d.deleted = 0
        <where>
            u.deleted = 0
            <if test="departmentId != null">
                and d.id = #{departmentId}
            </if>
            <if test="code != null and code != ''">
                and u.code = #{code}
            </if>
        </where>
    </select>

    <select id="selectByAccountAndDepartmentId" resultType="com.wftk.scale.biz.entity.User">
        select
        u.*
        from user u
        inner join user_department ud on u.id = ud.user_id and ud.deleted = 0
        inner join department d on d.id = ud.department_id and d.deleted = 0
        <where>
            u.deleted = 0
            <if test="departmentId != null">
                and d.id = #{departmentId}
            </if>
            <if test="account != null and account != ''">
                and u.account = #{account}
            </if>
        </where>
    </select>

    <select id="findDistributionUser" resultType="com.wftk.scale.biz.dto.user.DistributableUserDTO">
        select
            distinct
            u.id userId ,u.name userName, u.code userCode,d.id departmentId
        from user u
            inner join user_department ud on u.id = ud.user_id and ud.deleted = 0
            inner join department d on d.id = ud.department_id and d.deleted = 0 and d.enable = 1
            left join department_terminal dt on dt.department_id = d.id and dt.deleted = 0
            left join terminal t on t.code = dt.terminal_code and t.deleted = 0
        <where>
          u.deleted = 0 and u.enable = 1
          <if test="terminalCode != null and terminalCode != ''">
              and t.code = #{terminalCode}
          </if>
          and find_in_set(#{departmentCode}, replace(d.parent_path,'|',','))
        </where>
    </select>
    <select id="selectByAccount" resultType="com.wftk.scale.biz.entity.User">
        select * from user
        <where>
            deleted = 0
            <if test="account != null and account != ''">
                and account = #{account}
            </if>
            <if test="enable != null">
                and enable = #{enable}
            </if>
        </where>
    </select>
    <select id="selectUserQueryList" parameterType="com.wftk.scale.biz.dto.user.UserSearchDTO"
            resultType="com.wftk.scale.biz.dto.user.UserQueryDTO">
        select
            u.id userId ,u.account userAccount, u.name userName, u.code userCode,d.name departmentName,
            u.phone userPhone,u.enable ,u.create_by createBy, u.update_time updateTime,
            u.create_time createTime, u.update_by updateBy
        from user u
                 inner join user_department ud on u.id = ud.user_id and ud.deleted = 0
                 inner join department d on d.id = ud.department_id and d.deleted = 0
        <where>
             u.deleted = 0
            <if test="terminalCode != null and terminalCode != ''">
                and u.terminal_code = #{terminalCode}
            </if>
            <if test="departmentId != null and departmentId != ''">
                and d.id = #{departmentId}
            </if>
            <if test="enable != null">
                and u.enable = #{enable}
            </if>
            <if test="phone != null and phone != ''">
                and u.phone = #{phone}
            </if>
            <if test="userName != null and userName != ''">
                and u.name like concat('%',#{userName},'%')
            </if>
            <if test="userAccount != null and userAccount != ''">
                and u.account = #{userAccount}
            </if>
            <if test="departmentName != null and departmentName != ''">
                and d.name like concat('%',#{departmentName},'%')
            </if>
        </where>
        order by u.create_time desc
    </select>

    <select id="queryList" parameterType="com.wftk.scale.biz.dto.user.UserSearchDTO"
            resultType="com.wftk.scale.biz.dto.user.UserExcelConvertDTO">
        select
        u.account, u.name, u.code, u.birthday, u.sex, u.phone, u.email, d.name departmentName,
        d.id departmentId, u.id_card idCard
        from user u
        inner join user_department ud on u.id = ud.user_id and ud.deleted = 0
        inner join department d on d.id = ud.department_id and d.deleted = 0
        <where>
            u.deleted = 0
            <if test="terminalCode != null and terminalCode != ''">
                and u.terminal_code = #{terminalCode}
            </if>
            <if test="departmentId != null and departmentId != ''">
                and d.id = #{departmentId}
            </if>
            <if test="enable != null">
                and u.enable = #{enable}
            </if>
            <if test="phone != null and phone != ''">
                and u.phone = #{phone}
            </if>
            <if test="userName != null and userName != ''">
                and u.name like concat('%',#{userName},'%')
            </if>
            <if test="userAccount != null and userAccount != ''">
                and u.account = #{userAccount}
            </if>
        </where>
        order by u.create_time desc
    </select>

    <select id="queryTerminalCodeByUserDeptIds" resultType="java.lang.String">
        select t.code from department d
           inner join department_terminal dt on dt.department_id = d.id and dt.deleted = 0
           inner join terminal t on t.code = dt.terminal_code and t.deleted = 0
        where d.deleted = 0 and d.enable = 1 and d.id in
        <foreach collection="userDeptIds" item="userDeptId" open="(" close=")" separator=",">
            #{userDeptId}
        </foreach>
    </select>

    <select id="getEnableDeptIdsByUserIds" resultType="java.lang.Long">
        select d.id from user u
          join user_department ud on u.id = ud.user_id and ud.deleted = 0
          join department d on d.id = ud.department_id and d.deleted = 0 and d.enable = 1
        where u.deleted = 0 and u.enable = 1 and u.id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>

    <select id="selectByUserId" resultType="com.wftk.scale.biz.vo.UserVO">
        select u.id, u.code, u.account, u.name,
               d.name departmentName, d.id departmentId, u.sex, u.birthday, u.phone, u.id_card idCard, u.entry_date entryDate,
               u.email, u.remark, u.enable
        from user u
           inner join user_department ud on u.id = ud.user_id and ud.deleted = 0
           inner join department d on d.id = ud.department_id and d.deleted = 0
        where u.id = #{userId}
    </select>

    <select id="countStatusUser" resultType="java.lang.Integer">
        select count(*) from user t1
        inner join user_department t2 on t1.id = t2.user_id and t2.deleted = 0
        inner join department t3 on t3.id = t2.department_id and t3.deleted = 0
        <where>
            t1.deleted = 0
            <if test="enable != null">
                and t1.enable = #{enable}
            </if>
            <if test="orgIds != null">
                and t3.id in
                <foreach collection="orgIds" item="orgId" separator="," open="(" close=")">
                    #{orgId}
                </foreach>
            </if>
        </where>
     </select>

    <select id="selectUserToDepartment" resultType="com.wftk.scale.biz.vo.UserToDepartmentVO">
        select a.code, a.account, CONCAT(c.code, '-', c.name) concatName
        from user a
        left join user_department b on a.id = b.user_id and b.deleted = 0
        left join department c on b.department_id = c.id and c.deleted = 0
        where a.deleted = 0
    </select>
    <select id="getById" resultType="com.wftk.scale.biz.entity.User">
        select * from user where id = #{id} and deleted = 0 and enable = 1
    </select>
    <select id="getByIds" resultType="com.wftk.scale.biz.entity.User">
        select * from user where deleted = 0 and enable = 1 and id in
        <foreach item="id" collection="ids" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <select id="getUserDepartmentByIds" resultType="com.wftk.scale.biz.vo.UserVO">
        select u.id,
               u.code,
               u.account,
               u.name,
               d.name departmentName,
               d.id departmentId,
               u.sex,
               u.birthday,
               u.phone,
               u.id_card idCard,
               u.entry_date entryDate,
               u.email,
               u.remark,
               u.enable
        from
            user u
            join user_department ud on u.id = ud.user_id and ud.deleted = 0
            join department d on d.id = ud.department_id and d.deleted = 0
        where
            u.deleted = 0
            and u.enable = 1
            and ud.deleted = 0
            and d.deleted = 0
            and d.enable = 1
            and u.id in
        <foreach item="id" collection="ids" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
</mapper>
