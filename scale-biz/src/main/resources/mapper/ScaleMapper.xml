<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.Scale">
        <id column="id" property="id"/>
        <result column="provider" property="provider"/>
        <result column="provider_name" property="providerName"/>
        <result column="type" property="type"/>
        <result column="code" property="code"/>
        <result column="cover" property="cover"/>
        <result column="name" property="name"/>
        <result column="version" property="version"/>
        <result column="intro" property="intro"/>
        <result column="guideline" property="guideline"/>
        <result column="sort" property="sort"/>
        <result column="min_time_limit" property="minTimeLimit"/>
        <result column="max_time_limit" property="maxTimeLimit"/>
        <result column="description" property="description"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , provider, provider_name, type, code, cover, name, version, intro, guideline, sort, min_time_limit, max_time_limit, description, status, remark, create_time, update_time, deleted, tenant_id, create_by, update_by
    </sql>

    <!-- 查询量表名称是否已经存在 -->
    <select id="validScaleNameExists" resultType="java.lang.Boolean">
        select count(s.id)
        from scale s inner join scale_latest v on s.id = v.scale_id and v.deleted = 0
        where s.deleted = 0
        <if test="scaleId != null and scaleId != ''">
            and s.id &lt;> #{scaleId}
        </if>
        <if test="scaleCode != null and scaleCode != ''">
            and s.code &lt;> #{scaleCode}
        </if>
        <if test="scaleName != null and scaleName != ''">
            and s.name = #{scaleName}
        </if>
        limit 1
    </select>

    <!-- 获取单个量表最新版本数据 -->
    <select id="selectListByScaleNameAndCompleteStatus" resultType="com.wftk.scale.biz.dto.scale.ScaleQueryDTO">
        select s.*,st.name typeName from scale s
            inner join scale_latest v
                on s.id = v.scale_id and v.deleted = 0
            inner join scale_type st
                on s.type = st.id and st.deleted = 0
        where s.deleted = 0
        <if test="scaleName != null and scaleName != ''">
            and s.name like concat('%',#{scaleName},'%')
        </if>
        <if test="complateStatus != null and complateStatus != ''">
            and s.status = #{complateStatus}
        </if>
        order by s.create_time desc, s.sort desc,s.id desc
    </select>

    <select id="getLatestScaleByCode" resultType="com.wftk.scale.biz.entity.Scale">
        select s.* from scale s inner join scale_latest v on s.id = v.scale_id
        where s.deleted = 0 and v.deleted = 0 and s.code = #{scaleCode}
    </select>

    <select id="getListedScale" resultType="com.wftk.scale.biz.dto.scale.ScaleDTO">
        select
            s.id id ,sl.id listingId,sl.show_type listingShowType,s.provider,s.provider_name, s.type type ,st.name typeName,
            s.code code,s.cover, s.name name,s.version,s.intro,s.guideline,s.sort,
            s.min_time_limit minTimeLimit,s.max_time_limit maxTimeLimit,
            s.description,s.status,s.remark,sl.create_time createTime,sl.update_time updateTime,
            sl.create_by createBy,sl.update_by updateBy
        from scale s
        inner join scale_listing sl
        on s.id = sl.target_id and sl.type = 1 and sl.deleted = 0
        inner join scale_type st on st.id = s.type and st.deleted = 0
        <where>
            s.deleted = 0 and sl.status = 1 and sl.enable = 1
            and sl.terminal_code = #{terminalCode}
            <if test="scaleCode != null and scaleCode != ''">
                and s.code = #{scaleCode}
            </if>
            <if test="scaleId != null ">
                and s.id = #{scaleId}
            </if>
            <if test="scaleName != null and scaleName != ''">
                and s.name like concat('%',#{scaleName},'%')
            </if>
            <if test="scaleType != null ">
                and s.type = #{scaleType}
            </if>
            <if test="listingShowType != null ">
                and sl.show_type = #{listingShowType}
            </if>
        </where>
    </select>
    <select id="getListedScaleDetail" resultType="com.wftk.scale.biz.dto.scale.ScaleListingDetailDTO">
        select
            s.id id ,s.name scaleName, sl.id listingId, s.type type , s.code code, s.name name,sl.enable
            sl.original_price originalPrice,sl.price price ,sl.terminal_code terminalCode
        from scale s
        inner join scale_listing sl
        on s.id = sl.target_id and sl.type = 1 and sl.deleted = 0
        inner join scale_type st on st.id = s.type and st.deleted = 0
        <where>
            s.deleted = 0 and sl.deleted = 0 and sl.status = 1
            and sl.terminal_code = #{terminalCode} and s.code = #{scaleCode} and sl.id = #{scaleListingId}
        </where>
    </select>
    <select id="getLatestScaleListByCodes" resultType="com.wftk.scale.biz.entity.Scale">
        select s.* from scale s inner join scale_latest v on s.id = v.scale_id
        where s.deleted = 0 and v.deleted = 0 and s.code in
        <foreach collection="scaleCodes" item="scaleCode" open="(" close=")" separator="," >
           #{scaleCode}
        </foreach>
    </select>

    <select id="getScaleListingDetailDTO" resultType="com.wftk.scale.biz.dto.scale.ScaleListingDetailDTO">
        SELECT
         s.id id ,sl.id listingId, s.type type , s.code code, s.name name,
         sl.original_price originalPrice,sl.price price ,sl.terminal_code terminalCode
        FROM
            scale s
                INNER JOIN scale_listing sl ON s.id = sl.target_id
                AND sl.type = 1
                AND sl.deleted = 0
        WHERE
            s.deleted = 0
          AND sl.STATUS = 1
          AND ( s.CODE, sl.id ) IN
          <foreach collection="scaleSerials" item="scaleSerial" separator="," open="(" close=")">
              (#{scaleSerial.scaleCode},#{scaleSerial.listingId})
          </foreach>
          and sl.terminal_code = #{terminalCode}
    </select>
    <select id="getById" resultType="com.wftk.scale.biz.entity.Scale">
        select * from scale where id = #{id} and deleted = 0
    </select>

    <!-- 根据ID更新量表完成状态 -->
    <update id="updateComplateStatus">
        update scale
        set status = #{status},
            update_by = #{opUser}
        where id = #{scaleId}
    </update>

</mapper>
