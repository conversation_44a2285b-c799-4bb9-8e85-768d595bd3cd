<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleUserReportMapper">


    <select id="getByResultId" resultType="com.wftk.scale.biz.entity.ScaleUserReport">
        select
           *
        from scale_user_report
        <where>
            deleted = 0 and result_id = #{resultId}
        </where>
    </select>

    <select id="queryPage" parameterType="com.wftk.scale.biz.dto.report.UserReportQueryDTO"
            resultType="com.wftk.scale.biz.vo.ScaleUserReportVO">
        select a.id, c.account, c.name userName, c.sex, c.phone,
               d.name departmentName, d.id departmentId, e.target_name scaleName,
               1 as valid, case when a.positive_count > 0 then 1 else 0 end as result,
               e.terminal_code terminalCode, f.name terminalName, a.create_time reportTime
        from scale_user_report a
        join scale_user_result b on b.deleted = 0 and a.result_id = b.id
        left join user c on c.deleted = 0 and c.id = b.user_id
        left join (select t1.id, t1.name, t2.user_id from department t1
            left join user_department t2 on t2.deleted = 0 and t2.department_id = t1.id
                            where t1.deleted = 0) d on d.user_id = c.id
        left join scale_listing e on e.deleted = 0 and e.id = b.scale_listing_id
        left join terminal f on f.deleted = 0 and f.code = e.terminal_code
        where
            a.deleted = 0
        <if test="userAccount != null and userAccount != ''">
            and c.account = #{userAccount}
        </if>
        <if test="userName != null and userName != ''">
            and c.name like concat('%',#{userName},'%')
        </if>
        <if test="deptId != null and deptId != ''">
            and d.id = #{deptId}
        </if>
        <if test="phone != null and phone != ''">
            and c.phone = #{phone}
        </if>
        <if test="scaleName != null and scaleName != ''">
            and e.target_name like concat('%',#{scaleName},'%')
        </if>
        order by a.create_time desc
    </select>

</mapper>
