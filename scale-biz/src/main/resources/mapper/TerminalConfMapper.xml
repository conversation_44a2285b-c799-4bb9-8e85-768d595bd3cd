<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.TerminalConfMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.TerminalConf">
        <id column="id" property="id"/>
        <result column="terminal_id" property="terminalId"/>
        <result column="item_code" property="itemCode"/>
        <result column="item_value" property="itemValue"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="enable" property="enable"/>
        <result column="deleted" property="deleted"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, terminal_id, item_code, item_value, create_time, update_time, create_by, update_by, enable, deleted, tenant_id
    </sql>

    <!-- 校验终端信息是否已经存在 -->
    <select id="validTerminalCodeAndValueExist" resultType="java.lang.Boolean">
        select count(id)
        from terminal_conf
        where deleted = 0
        <if test="id != null and id != ''">
            and id &lt;> #{id}
        </if>
        <if test="itemCode != null and itemCode != ''">
            and item_code = #{itemCode}
        </if>
        <if test="itemValue != null and itemValue != ''">
            and item_value = #{itemValue}
        </if>
        limit 1
    </select>

    <!-- 更新数据启用状态 -->
    <update id="updateEnable">
        update terminal_conf
        set enable = #{enable}
        where id = #{id}
    </update>

    <!-- 根据条件检索终端配置列表数据 -->
    <select id="getList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from terminal_conf
        where deleted = 0
    </select>
    <select id="selectByTerminalCodeAndItemCode" resultType="java.lang.String">
        select item_value
        from terminal_conf tc
                 join terminal t on tc.terminal_id = t.id
        <where>
            t.deleted = 0
            and t.enable = 1
            and tc.deleted = 0
            <if test="itemCode != null and itemCode != ''">
                and tc.item_code = #{itemCode}
            </if>
            <if test="enable != null">
                and tc.enable = #{enable}
            </if>
            <if test="terminalCode != null and terminalCode != ''">
                and t.code = #{terminalCode}
            </if>
        </where>
    </select>
    <select id="selectByTerminalIdAndItemCode" resultType="java.lang.String">
        select item_value
        from terminal_conf
        <where>
            deleted = 0
            <if test="terminalId != null">
                and terminal_id = #{terminalId}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and item_code = #{itemCode}
            </if>
            <if test="enable != null">
                and enable = #{enable}
            </if>
        </where>
    </select>

</mapper>
