<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleQuestionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleQuestion">
        <id column="id" property="id"/>
        <result column="scale_id" property="scaleId"/>
        <result column="sort" property="sort"/>
        <result column="question" property="question"/>
        <result column="data_type" property="dataType"/>
        <result column="type" property="type"/>
        <result column="require_answer" property="requireAnswer"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="file_path" property="filePath"/>
        <result column="offset" property="offset"/>
        <result column="scoring_type" property="scoringType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , scale_id, question_number, sub_number,sort, question, data_type, type,require_answer, create_time, update_time, deleted, create_by, update_by, tenant_id, file_path,`offset`,scoring_type
    </sql>

    <!-- 校验量表题目内容是否存在 -->
    <select id="vaildQuestionDataIntegrity" resultType="java.lang.Boolean">
        select count(1)
        from scale_question
        where scale_id = #{scaleId}
          and deleted = 0 limit 1
    </select>

    <!-- 校验量表题目是否存在子题数据 -->
    <select id="vaildQuestionChildrenExists" resultType="java.lang.Boolean">
        select count(1)
        from scale_question
        where scale_id = #{scaleId}
          and question_number = #{questionNum}
          and deleted = 0
          and id &lt;> #{questionId}
    </select>


    <!-- 校验量表题目编号是否存在 -->
    <select id="vaildQuestionNumberExists" resultType="java.lang.Boolean">
        select count(1)
        from scale_question
        where deleted = 0
        <if test="param.id != null and param.id != ''">
            and id &lt;> #{param.id}
        </if>
        <if test="param.scaleId != null and param.scaleId != ''">
            and scale_id = #{param.scaleId}
        </if>
        <if test="(param.questionNumber != null and param.questionNumber != '') and (param.subNumber != null and param.subNumber != '')">
            and question_number = #{param.questionNumber} and sub_number = #{param.subNumber}
        </if>
        <if test="(param.questionNumber != null and param.questionNumber != '') and (param.subNumber == null or param.subNumber == '')">
            and question_number = #{param.questionNumber} and sub_number is null
        </if>
        limit 1
    </select>

    <!-- 校验量表上层题目编号是否已经存在 -->
    <select id="vaildQuestionUpperNumberExists" resultType="java.lang.Boolean">
        select count(1)
        from scale_question
        where scale_id = #{param.scaleId}
        and deleted = 0
        <if test="param.id != null and param.id != ''">
            and id &lt;> #{param.id}
        </if>
        <if test="param.questionNumber != null and param.questionNumber != ''">
            and question_number = #{param.questionNumber}
        </if>
        and sub_number is null
        limit 1
    </select>

    <!-- 根据量表ID获取关联的题目信息 -->
    <select id="findByScaleId" resultType="com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO">
        select
        <include refid="Base_Column_List"/>
        from scale_question
        where scale_id = #{scaleId}
        and deleted = 0
        order by cast(question_number as signed), cast(sub_number as signed), sort, id
    </select>

    <!-- 根据量表ID和题目ID获取题目详情 -->
    <select id="findByScaleIdAndQuestionId" resultType="com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO">
        select
        <include refid="Base_Column_List"/>
        from scale_question
        where scale_id = #{scaleId} and id = #{questionId}
        and deleted = 0
        limit 1
    </select>

    <!-- 根据量表ID删除量表题目信息 -->
    <update id="deleteByScaleId">
        update scale_question
        set deleted = 1
        where scale_id = #{scaleId}
    </update>

    <!-- 根据量表ID获取量表题目数量 -->
    <select id="getNumOfQuestion" resultType="java.lang.Integer">
        select count(id)
        from scale_question
        where scale_id = #{scaleId} and sub_number is null
          and deleted = 0
    </select>
    <select id="findByQuestionId" resultType="com.wftk.scale.biz.entity.ScaleQuestion">
        select * from scale_question where deleted = 0 and id = #{questionId}
    </select>
    <select id="findListByScaleId" resultType="com.wftk.scale.biz.entity.ScaleQuestion">
        select * from scale_question where deleted = 0 and scale_id = #{scaleId}
    </select>
    <select id="getQuestionByIds" resultType="com.wftk.scale.biz.entity.ScaleQuestion">
        select * from scale_question sq where deleted = 0 and id in
        <foreach item="item" collection="ids" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
    </select>
</mapper>
