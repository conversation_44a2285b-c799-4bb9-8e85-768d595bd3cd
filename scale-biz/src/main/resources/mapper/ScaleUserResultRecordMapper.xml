<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleUserResultRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.ScaleUserResultRecord">
        <id column="id" property="id"/>
        <result column="result_id" property="resultId"/>
        <result column="scale_id" property="scaleId"/>
        <result column="question_id" property="questionId"/>
        <result column="option_id" property="optionId"/>
        <result column="answer" property="answer"/>
        <result column="content" property="content"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="score" property="score"/>
        <result column="result" property="result"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , result_id, scale_id, question_id, option_id, answer, content, create_time, update_time, deleted, create_by, update_by, tenant_id, score, result
    </sql>
    <delete id="deletedByIds">
        delete from scale_user_result_record where id in
        <foreach item="item" collection="ids" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <!--根据测评记录ID获取测评答案信息-->
    <select id="findByResultId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scale_user_result_record
        where result_id = #{resultId} and deleted = 0
    </select>

    <!-- findOneByResultIdAndQuestionId --> 

    <select id="findOneByResultIdAndQuestionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from scale_user_result_record
        where result_id = #{resultId} and question_id = #{questionId} and deleted = 0
    </select>
</mapper>
