<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.SystemTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.SystemTag">
        <id column="id" property="id" />
        <result column="tag" property="tag" />
        <result column="code" property="code" />
        <result column="type" property="type" />
        <result column="enable" property="enable" />
        <result column="description" property="description" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tag, code, type, enable, description, deleted, create_time, update_time, create_by, update_by, tenant_id
    </sql>

    <!-- 查询预警标签名称是否已经存在 -->
    <select id="validSystemTagCodeExist" resultType="java.lang.Boolean">
        select count(id)
        from system_tag
        where deleted = 0
        <if test="tagId != null and tagId != ''">
            and id &lt;> #{tagId}
        </if>
        <if test="tagCode != null and tagCode != ''">
            and code = #{tagCode}
        </if>
        limit 1
    </select>

    <!-- 更新数据启用状态 -->
    <update id="updateEnable">
        update system_tag
        set enable = #{enable},update_by = #{opUser}
        where id = #{tagId}
    </update>

    <!-- 根据条件检索预警标签列表数据 -->
    <select id="getList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from system_tag
        where deleted = 0
        <if test="tagName != null and tagName != ''">
            and tag like concat('%',#{tagName},'%')
        </if>
        <if test="enable != null and enable != ''">
            and enable = #{enable}
        </if>
        order by id desc
    </select>

</mapper>
