<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.OrderRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wftk.scale.biz.entity.OrderRefund">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="order_no" property="orderNo" />
        <result column="trader_no" property="traderNo" />
        <result column="refund_no" property="refundNo" />
        <result column="refund_extra_no" property="refundExtraNo" />
        <result column="refund_status" property="refundStatus" />
        <result column="refund_amount" property="refundAmount" />
        <result column="refund_time" property="refundTime" />
        <result column="remark" property="remark" />
        <result column="refund_result_time" property="refundResultTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, deleted, tenant_id, order_no, trader_no, refund_no, refund_extra_no, refund_status, refund_amount, refund_time, remark, refund_result_time
    </sql>

</mapper>
