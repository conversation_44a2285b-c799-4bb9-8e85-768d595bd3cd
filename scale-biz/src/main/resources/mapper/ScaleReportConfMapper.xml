<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wftk.scale.biz.mapper.ScaleReportConfMapper">



    <!-- 根据量表ID获取报告配置信息 -->
    <select id="findReportConfByScaleId" resultType="com.wftk.scale.biz.entity.ScaleReportConf">
        select
          *
        from scale_report_conf
        where scale_id = #{scaleId} and deleted = 0
        limit 1
    </select>

    <select id="validReportConfDataIntegrity" resultType="java.lang.Boolean">
        select
            count(1)
        from scale_report_conf
        where scale_id = #{scaleId} and deleted = 0
    </select>

</mapper>
