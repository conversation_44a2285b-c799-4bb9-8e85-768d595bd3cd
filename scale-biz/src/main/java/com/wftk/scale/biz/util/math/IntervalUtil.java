package com.wftk.scale.biz.util.math;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/3/10 10:47
 */
public class IntervalUtil {

    public static boolean hasOverlap(List<Interval> intervals, Interval newInterval) {
        for (Interval interval : intervals) {
            // 判断是否重叠
            if (interval.getStart() <= newInterval.getEnd() && newInterval.getStart() <= interval.getEnd()) {
                return true;
            }
        }
        return false;
    }

}
