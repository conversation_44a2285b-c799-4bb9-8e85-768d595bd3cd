package com.wftk.scale.biz.ext.evaluator.function.scale.question;

import java.util.List;
import java.util.Map;

import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.wftk.scale.biz.entity.ScaleQuestion;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;
import cn.hutool.core.util.StrUtil;

/**
 * 选择量表题目函数(多个题目编号用逗号分隔)
 * 示例: q_select("1,2,3"), 表示选择当前量表中编号为1,2,3题
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public class SelectScaleQuestionFunction extends BaseScaleQuestionFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Question.Q_SELECT;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env, final AviatorObject arg1) {
        String questionSeqs = (String) arg1.getValue(env);
        logger.info("q_select: questionSeqs: {}", questionSeqs);
        if (StrUtil.isBlank(questionSeqs)) {
            throw new IllegalArgumentException("questionSeqs must not be blank");
        }
        List<ScaleQuestionSequence> questionSequence = null;
        if (questionSeqs.contains(",")) {
            String[] questionSeqsArray = questionSeqs.split(",");
            questionSequence = buildQuestionSequence(questionSeqsArray);
        } else {
            questionSequence = buildQuestionSequence(questionSeqs);
        }
        List<ScaleQuestion> scaleQuestions = selectBySequence(env, questionSequence);
        String questionIds = getQuestionIds(scaleQuestions);

        return new AviatorString(questionIds);
    }

}
