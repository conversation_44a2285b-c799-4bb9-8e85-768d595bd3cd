package com.wftk.scale.biz.manager.report.handle;

import com.wftk.scale.biz.constant.ScaleReportConfSettingConstant;
import com.wftk.scale.biz.constant.enums.ScaleFactorTypeEnum;
import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.entity.ScaleReportConf;
import com.wftk.scale.biz.entity.ScaleUserFactorResult;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.manager.report.actuator.*;
import com.wftk.scale.biz.manager.report.dto.base.*;
import com.wftk.scale.biz.manager.report.dto.content.TotalScoreReportContentDTO;
import com.wftk.scale.biz.service.ScaleFactorService;
import com.wftk.scale.biz.service.ScaleUserFactorResultService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 总体结果解读
 * <AUTHOR>
 * @createDate 2025/9/8 16:02
 */
@Slf4j
@Component
public class TotalScoreResultsHandle {

    private final Integer FACTOR_TYPE = ScaleFactorTypeEnum.TOTAL.getType();

    @Resource
    ScaleFactorService scaleFactorService;

    @Resource
    ScaleUserFactorResultService scaleUserFactorResultService;

    @Resource
    ResultIntroActuator resultIntroActuator;

    @Resource
    SuggestionActuator suggestionActuator;

    @Resource
    AudioActuator audioActuator;

    @Resource
    VideoActuator videoActuator;

    @Resource
    ChartActuator chartActuator;

    @Resource
    FormActuator formActuator;

    @Resource
    WordageActuator wordageActuator;

    @Resource
    RemarkActuator remarkActuator;

    public TotalScoreReportContentDTO buildReportContent(ScaleUserResult scaleUserResult, ScaleReportConf scaleReportConf) {
        if (!scaleReportConf.getTotalScoreEnable()) {
            log.info("totalScoreResults enable is close. resultId is {}",scaleUserResult.getId());
            return null;
        }
        // 查找总分因子
        Long scaleId = scaleUserResult.getScaleId();
        ScaleFactor factor = scaleFactorService.getFactorByScaleIdAndType(scaleId, FACTOR_TYPE);
        if(factor == null){
            log.info("totalScoreResults total factor is null. resultId is {}, scaleId is {}"
                    ,scaleUserResult.getId(),scaleUserResult.getScaleId());
            return null;
        }

        // 查找结果解读和因子结果
        ReultIntroDTO reultIntroDTO = null;
        if(scaleReportConf.getResultIntroEnable() ) {
            reultIntroDTO = resultIntroActuator.doResultIntro(scaleUserResult.getId(), factor.getId(), factor.getName());
        }

        // 获取建议
        SuggestionDTO suggestionDTO = null;
        if(scaleReportConf.getImprovementSuggestionEnable()){
            BigDecimal score = scaleUserFactorResultService.getScoreByResultIdAndFactorId(scaleUserResult.getId(), factor.getId());
            suggestionDTO = suggestionActuator.doSuggestion(scaleReportConf.getId(), score, factor.getName(), factor.getId());
        }

        // 获取音频
        AudioDTO audioDTO = null;
        VideoDTO videoDTO = null;
        if(scaleReportConf.getAudioAndVideoNarrateEnable()){
            BigDecimal score = scaleUserFactorResultService.getScoreByResultIdAndFactorId(scaleUserResult.getId(), factor.getId());
            audioDTO = audioActuator.doAudio(scaleReportConf.getId(), score, factor.getName(), factor.getId());
            videoDTO = videoActuator.doVideo(scaleReportConf.getId(), score, factor.getName(), factor.getId());
        }

        Integer reportType = ScaleReportConfSettingConstant.ReportType.TOTAL;

        // 获取图表
        ChartDTO chartDTO = null;
        if(scaleReportConf.getTotalScoreChartEnable()){
            chartDTO = chartActuator.doChart(scaleReportConf.getId(),reportType, scaleUserResult.getId(), List.of(factor.getId()));
        }

        // 获取表格
        FormDTO formDTO = null;
        if(scaleReportConf.getTotalScoreFormEnable()){
            formDTO = formActuator.doForm(scaleUserResult.getId(),List.of(factor.getId()), scaleReportConf.getId(), reportType);
        }

        // 获取文字
        String wordage = null;
        if(scaleReportConf.getTotalScoreWordageEnable()){
            List<FactorResultDTO> factorResults = new ArrayList<>();
            List<ScaleUserFactorResult> scaleUserFactorResults = scaleUserFactorResultService.getListByResultId(scaleUserResult.getId(), List.of(factor.getId()));
            for (ScaleUserFactorResult factorResult:scaleUserFactorResults) {
                FactorResultDTO factorResultDTO = new FactorResultDTO();
                factorResultDTO.setFactorName(factorResult.getFactorName());
                factorResultDTO.setScore(factorResult.getScore());
                factorResults.add(factorResultDTO);
            }
            wordage = wordageActuator.doWordage(factorResults, reportType);
        }

        String remark = null;
        if(scaleReportConf.getRemarkEnable()){
            remark = remarkActuator.doRemark(scaleReportConf.getId(),reportType);
        }

        TotalScoreReportContentDTO totalScoreReportContent = new TotalScoreReportContentDTO();
        totalScoreReportContent.setRemark(remark);
        totalScoreReportContent.setForm(formDTO);
        totalScoreReportContent.setResultIntro(reultIntroDTO);
        totalScoreReportContent.setChart(chartDTO);
        totalScoreReportContent.setSuggestion(suggestionDTO);
        totalScoreReportContent.setWordage( wordage);
        totalScoreReportContent.setAudio(audioDTO);
        totalScoreReportContent.setVideo(videoDTO);

        return  totalScoreReportContent;
    }

}
