package com.wftk.scale.biz.ext.evaluator.engine;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.Expression;
import com.googlecode.aviator.lexer.ExpressionLexer;
import com.googlecode.aviator.lexer.token.Token;
import com.googlecode.aviator.runtime.type.AviatorFunction;
import com.wftk.scale.biz.ext.evaluator.constant.EnvConstant;
import com.wftk.scale.biz.ext.evaluator.core.BaseEvaluatorEngine;
import com.wftk.scale.biz.ext.evaluator.core.EvaluatorContext;
import com.wftk.scale.biz.ext.evaluator.core.ExpressionTemplate;
import com.wftk.scale.biz.ext.evaluator.exception.FunctionNotFoundException;
import com.wftk.scale.biz.ext.evaluator.exception.TemplateOperationException;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

/**
 * 
 * 基于AviatorScript的计算引擎
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public class AviatorEvaluatorEngine extends BaseEvaluatorEngine implements ApplicationContextAware {

    private final TemplateCache templateCache;
    private final Configuration configuration;
    private final AviatorEvaluatorInstance aviatorEvaluator;
    private ApplicationContext applicationContext;

    public AviatorEvaluatorEngine(AviatorEvaluatorInstance aviatorEvaluator) {
        this(aviatorEvaluator, 100);
    }

    public AviatorEvaluatorEngine(AviatorEvaluatorInstance aviatorEvaluator, int cacheSize) {
        this.templateCache = new TemplateCache(cacheSize);
        this.configuration = new Configuration(Configuration.VERSION_2_3_28);
        initTemplate(configuration);
        this.aviatorEvaluator = aviatorEvaluator;
    }

    protected void initTemplate(Configuration configuration) {
        configuration.setDefaultEncoding(StandardCharsets.UTF_8.name());
    }

    @Override
    protected Object doEvaluate(String expression, @Nullable EvaluatorContext context) {
        logger.info("doEvaluate {} with context [{}]", expression, context);
        configureContext(context);
        Expression expr = aviatorEvaluator.compile(expression);
        if (context == null || context.isEmpty()) {
            return expr.execute();
        }
        return expr.execute(context.getAllVariables());
    }

    @Override
    public String bindingExpressionTemplate(String expression, EvaluatorContext context) {
        String expressionDigest = SecureUtil.md5(expression);
        Template template = templateCache.get(expressionDigest);
        if (template == null) {
            try {
                template = new Template(expressionDigest, expression, configuration);
            } catch (IOException e) {
                throw new TemplateOperationException("Failed to create template.", e);
            }
            templateCache.put(expressionDigest, template);
        }
        StringWriter templateOut = new StringWriter();
        try {
            template.process(context.getAllVariables(), templateOut);
        } catch (TemplateException | IOException e) {
            throw new TemplateOperationException(expressionDigest + " bindingExpressionTemplate error.", e);
        }
        String result = templateOut.toString();
        logger.info("bindingExpressionTemplate: {} -> {}", expression, result);
        return result;
    }


    @Override
    public ExpressionTemplate expressionToTemplate(String expression) {
        if (StrUtil.isBlank(expression)) {
            return null;
        }
        ExpressionLexer lexer = new ExpressionLexer(AviatorEvaluator.getInstance(), expression);
        List<Token<?>> tokens = new ArrayList<>();
        Token<?> token;

        // 先收集所有tokens
        while ((token = lexer.scan()) != null) {
            tokens.add(token);
        }

        List<String> parameters = new ArrayList<>();
        StringBuilder templateBuilder = new StringBuilder();
        int placeholderIndex = 1;

        // 遍历tokens，通过上下文判断是否为函数调用
        for (int i = 0; i < tokens.size(); i++) {
            Token<?> currentToken = tokens.get(i);

            if (currentToken.getType() == Token.TokenType.Variable) {
                String varName = currentToken.getLexeme();

                // 检查下一个token是否为左括号，如果是则认为当前是函数名
                boolean isFunction = false;
                if (i + 1 < tokens.size()) {
                    Token<?> nextToken = tokens.get(i + 1);
                    // 检查是否为左括号（通过lexeme判断）
                    if ("(".equals(nextToken.getLexeme())) {
                        isFunction = true;
                    }
                }

                if (isFunction) {
                    // 函数名保持不变
                    templateBuilder.append(varName);
                } else {
                    // 变量：分配新的占位符
                    String paramName = "p" + placeholderIndex;
                    // 生成一个带有类型判断逻辑的FreeMarker模板片段
                    String placeholder = "<#if " + paramName + "?? && " + paramName + "?is_string>\"${ " + paramName + " }\"<#else>${ " + paramName + "?c}</#if>";
                    parameters.add(paramName);
                    placeholderIndex++;
                    templateBuilder.append(placeholder);
                }
            } else {
                // 其他token保持不变
                templateBuilder.append(currentToken.getLexeme());
            }
        }
        return new ExpressionTemplate(parameters, templateBuilder.toString());
    }





    /**
     * 配置上下文
     * 
     * @param context
     */
    protected void configureContext(EvaluatorContext context) {
        context.addVariable(EnvConstant.SCALE_USER_RESULT_RECORD_SERVICE,
                applicationContext.getBean(EnvConstant.SCALE_USER_RESULT_RECORD_SERVICE));
        context.addVariable(EnvConstant.SCALE_QUESTION_SERVICE,
                applicationContext.getBean(EnvConstant.SCALE_QUESTION_SERVICE));
        context.addVariable(EnvConstant.SCALE_FACTOR_SERVICE,
                applicationContext.getBean(EnvConstant.SCALE_FACTOR_SERVICE));
        context.addVariable(EnvConstant.SCALE_QUESTION_OPTION_SERVICE,
                applicationContext.getBean(EnvConstant.SCALE_QUESTION_OPTION_SERVICE));
    }


    /**
     * 获取函数
     * 
     * @param name
     * @return
     */
    public AviatorFunction getFunctionByName(String name) {
        return aviatorEvaluator.getFunction(name);
    }


    /**
     * 模板缓存
     */
    public static class TemplateCache {
        private final Map<String, Template> cache;

        public TemplateCache(int capacity) {
            this.cache = new LinkedHashMap<String, Template>(capacity, 0.75f, true) {
                @Override
                protected boolean removeEldestEntry(Map.Entry<String, Template> eldest) {
                    return size() > capacity;
                }
            };
        }

        public Template get(String key) {
            return cache.get(key);
        }

        public void put(String key, Template value) {
            cache.put(key, value);
        }

        public void clear() {
            cache.clear();
        }
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public ExpressionValidateResult validate(String expression, boolean allowSimpleExpression) {
        try {
            // 先尝试编译，如果编译不通过，表达式一定不合法
            Expression expressionObject = aviatorEvaluator.compile(expression);
            // 如果不允许简单表达式，则需要进一步判断
            if (!allowSimpleExpression && !isComplexExpression(expressionObject, expression)) {
                logger.info("illegal expression: {}", expression);
                return ExpressionValidateResult.fail("表达式校验失败");
            }
            return ExpressionValidateResult.success();
        } catch (Exception e) {
            logger.error("validate expression [{}] error", expression, e);
            @SuppressWarnings("unchecked")
            Throwable causedBy = ExceptionUtil.getCausedBy(e, FunctionNotFoundException.class);
            if (causedBy != null) {
                return ExpressionValidateResult
                        .fail("非法函数:" + ((FunctionNotFoundException) causedBy).getFunction());
            }
            return ExpressionValidateResult.fail("表达式校验失败");
        }
    }

    /**
     * 判断表达式是否为复杂表达式
     * 
     * @param expression
     * @return
     */
    private boolean isComplexExpression(Expression expression, String expressionString) {
        // 如果存在变量名，则为复杂表达式
        if (CollUtil.isNotEmpty(expression.getVariableNames())) {
            if (expression.getVariableNames().size() > 1) {
                // 多个变量，一定是负责表达式
                return true;
            }
            // 注意理解AviatorScript的变量概念（字符串带上引号才是常量，不带引号就是变量名）
            // 例如: "'abc'"是常量，"ab"是变量
            // compile("ab")后，获取到变量名为["ab"]
            // compile("'ab'")后，获取到变量名为[]
            if (!expression.getVariableNames().get(0).equals(expressionString)) {
                return true;
            }
            logger.info("simple expression: {}", expressionString);
            return false;
        }
        // 如果存在函数名，则为复杂表达式
        if (CollUtil.isNotEmpty(expression.getFunctionNames())) {
            return true;
        }
        
        // 如果不存在变量，则说明可能是一个能直接运算的表达式（例如: 1+2或者函数sum(1,2)）
        // 此种情况运算后的结果一定跟运算前不一致，可以通过此规则来校验
        Object result = expression.execute();
        logger.info("complex expression test: {} -> {}", expression, result);
        return !Objects.equals(result, expression);
    }

}