package com.wftk.scale.biz.dto.user;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;


/**
 * <AUTHOR>
 * @createDate 2024/12/14 10:16
 */
@Data
public class DepartmentCreateDTO {

    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空")
    @Length(max = 50, message = "部门名称不能超过50个字符")
    private String name;

    /**
     * 部门编码
     */
    @NotBlank
    @Length(max = 50, message = "部门编码不能超过50个字符")
    private String code;

    /**
     * 父级路径
     */
    private String parentPath;

    /**
     * 负责人名字
     */
    @Length(max = 50, message = "负责人名字不能超过50个字符")
    private String managerName;

    /**
     * 负责人联系电话
     */
    @Pattern(regexp = "^(1[3-9])\\d{9}$", message = "负责人联系电话格式有误")
    private String managerPhone;

    /**
     * 负责人邮箱
     */
    @Pattern(regexp = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@[a-zA-Z0-9-]+\\.[a-zA-Z]{2,}$", message = "邮箱格式错误")
    private String managerEmail;

    /**
     * 父级部门 最高级默认为0
     */
    private Long parentId;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 1开启，0关闭
     */
    private Boolean enable;

    /**
     * 备注
     */
    @Length(max = 255, message = "备注不能超过255个字符")
    private String remark;

    /**
     * 终端编码
     */
    private List<String> terminalCode;

}
