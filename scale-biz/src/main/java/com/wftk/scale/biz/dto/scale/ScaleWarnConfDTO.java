package com.wftk.scale.biz.dto.scale;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: ScaleWarnConfDTO
 * @Description: 量表预警阈值信息
 * @Author: mq
 * @Date: 2024-11-05 13:55
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleWarnConfDTO implements Serializable {

    private Long id;


    /**
     * 量表ID
     */
    private Long scaleId;

    /**
     * 因子ID
     */
    private Long factorId;

    /**
     * 因子名称
     */
    private String factorName;

    /**
     * 预警逻辑，1大于，2等于，3小于,4区间
     */
    private Integer type;

    /**
     * 预警阈值值
     */
    private Integer threshold;


    /**
     * 预警阈值区间
     */
    private String interval;

    /**
     * 标识标签
     */
    private Long tagId;

    /**
     * 标识标签
     */
    private String tag;

    /**
     * 预警方式，1短信，2邮件,可多个，逗号分隔。
     */
    private String warnType;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 创建人
     */
    private String createBy;

}
