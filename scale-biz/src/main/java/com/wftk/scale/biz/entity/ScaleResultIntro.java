package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 结果解读表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Data
@TableName("scale_result_intro")
public class ScaleResultIntro implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private Long scaleId;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 结果解读
     */
    private String intro;

    /**
     * 量表因子ID
     */
    private Long factorId;

    /**
     * 量表因子所得分数（可能是个区间）
     */
    private String score;

    /**
     * 因子分值转换方式: 1.等比转换  2.固定值转换 （如果是等比转换，则转换后的值是计算出来的区间）
     */
    private Integer scoreConvertType;

    /**
     * 转换分值
     */
    private String convertScore;

    /**
     * 结果: 0.阴; 1.阳;
     */
    private Boolean result;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 展示类型(文字、图表、表格), 通过逗号隔开
     */
    private String showType;

    /**
     * 图表类型(横向分段、柱状图、圆饼图)
     */
    private Integer chartType;

    /**
     * 租户ID
     */
    private String tenantId;



    @Override
    public String toString() {
        return "ScaleResultIntro{" +
            "id = " + id +
            ", scaleId = " + scaleId +
            ", sort = " + sort +
            ", intro = " + intro +
            ", factorId = " + factorId +
            ", score = " + score +
            ", scoreConvertType = " + scoreConvertType +
            ", convertScore = " + convertScore +
            ", result = " + result +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
            ", deleted = " + deleted +
            ", createBy = " + createBy +
            ", updateBy = " + updateBy +
            ", tenantId = " + tenantId +
        "}";
    }
}
