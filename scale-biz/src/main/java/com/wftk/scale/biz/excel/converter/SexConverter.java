package com.wftk.scale.biz.excel.converter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.wftk.scale.biz.constant.enums.UserSexEnum;

/**
 * <AUTHOR>
 */
public class SexConverter implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadConverterContext<?> context) {
        return UserSexEnum.convertNameToValue(context.getReadCellData().getStringValue());
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) {
        if(context.getValue() == null){
            return new WriteCellData<String>();
        }
        String sexVal = UserSexEnum.valueOf(context.getValue());
        return new WriteCellData<String>(StrUtil.isBlank(sexVal) ? "" : sexVal);
    }
}
