package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.scale.ScaleUserResultDetailDTO;
import com.wftk.scale.biz.dto.scale.ScaleUserResultParamDTO;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.vo.ScaleUserResultVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户测评记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleUserResultMapper extends BaseMapper<ScaleUserResult> {

    /*
     * @Author: mq
     * @Description: 校验量表是否存在测评记录
     * @Date: 2024/11/7 13:58
     * @Param: userId-用户ID
     * @Param: scaleId-量表ID
     * @return: boolean
     **/
    boolean vaildScaleUserResultRecord(@Param("userId") Long userId, @Param("scaleId") Long scaleId);

    /*
     * @Author: mq
     * @Description: 根据条件获取用户测评记录
     * @Date: 2024/11/7 17:53
     * @Param: paramDTO
     * @return: java.util.List<com.wftk.scale.biz.dto.scale.ScaleUserResultQueryDTO>
     **/
    List<ScaleUserResultVO> getList(ScaleUserResultParamDTO paramDTO);

    /*
     * @Author: mq
     * @Description: 根据测评记录ID更新测评结束时间
     * @Date: 2024/11/7 18:13
     * @Param: resultId-测评记录ID
     * @Param: endTime-测评结束时间
     * @return: void
     **/
    void updateEndTime(@Param("resultId") Long resultId, @Param("endTime") LocalDateTime endTime);

    /*
     * @Author: mq
     * @Description: 根据量表ID、用户ID和上架ID获取用户最后一条测评记录
     * @Date: 2024/11/7 18:32
     * @Param: scaleId-量表ID
     * @Param: userId-用户ID
     * @Param: listingId-上架ID
     * @return: com.wftk.scale.biz.entity.ScaleUserResult
     **/
    ScaleUserResult findLastUserResult(@Param("listingId") Long listingId, @Param("scaleId") Long scaleId, @Param("userId") Long userI,@Param("orderNo")String orderNo);

    /*
    * @Author: mq
    * @Description: 根据测评记录ID获取用户测评记录详情
    * @Date: 2024/11/25 18:02
    * @Param: resultId-测评记录ID
    * @return: ScaleUserResultDetailDTO
    **/
    ScaleUserResultDetailDTO findDetailByResultId(@Param("resultId") Long resultId);


    List<ScaleUserResult> selectByOrderNo(@Param("orderNo")String orderNo);
}
