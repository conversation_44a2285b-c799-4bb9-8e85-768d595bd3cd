package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 量表主表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public class Scale implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 量表提供商代码: SYSTEM代表本系统
     */
    private String provider;

    /**
     * 量表厂商名称
     */
    private String providerName;

    /**
     * 量表类型，来源于scale_type表的主键
     */
    private Long type;

    /**
     * 量表编码
     */
    private String code;

    /**
     * 封面图url
     */
    private String cover;

    /**
     * 量表名称
     */
    private String name;

    /**
     * 版本号(hash值，实现过程中通过指定内容生成hash，hash值有变动认为数据有修改，此时新写入1条记录，量表编码不变)
     */
    private String version;

    /**
     * 简介
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String intro;

    /**
     * 指导语
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String guideline;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 限时秒数最小值
     */
    private Integer minTimeLimit;

    /**
     * 限时秒数最大值
     */
    private Integer maxTimeLimit;

    /**
     * 介绍
     */
    private String description;

    /**
     * 0未完成，1已完成，问题，因子，解读等没完成之前是未完成
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getGuideline() {
        return guideline;
    }

    public void setGuideline(String guideline) {
        this.guideline = guideline;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getMinTimeLimit() {
        return minTimeLimit;
    }

    public void setMinTimeLimit(Integer minTimeLimit) {
        this.minTimeLimit = minTimeLimit;
    }

    public Integer getMaxTimeLimit() {
        return maxTimeLimit;
    }

    public void setMaxTimeLimit(Integer maxTimeLimit) {
        this.maxTimeLimit = maxTimeLimit;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public String toString() {
        return "Scale{" +
            "id = " + id +
            ", provider = " + provider +
            ", providerName = " + providerName +
            ", type = " + type +
            ", code = " + code +
            ", cover = " + cover +
            ", name = " + name +
            ", version = " + version +
            ", intro = " + intro +
            ", guideline = " + guideline +
            ", sort = " + sort +
            ", minTimeLimit = " + minTimeLimit +
            ", maxTimeLimit = " + maxTimeLimit +
            ", description = " + description +
            ", status = " + status +
            ", remark = " + remark +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
            ", deleted = " + deleted +
            ", tenantId = " + tenantId +
            ", createBy = " + createBy +
            ", updateBy = " + updateBy +
        "}";
    }
}
