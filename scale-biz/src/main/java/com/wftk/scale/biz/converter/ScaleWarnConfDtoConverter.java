package com.wftk.scale.biz.converter;

import com.wftk.scale.biz.dto.scale.ScaleWarnConfCreateDTO;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfDTO;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfModifyDTO;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfQueryDTO;
import com.wftk.scale.biz.entity.ScaleWarnConf;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @createDate 2025/3/10 14:38
 */
@Mapper(componentModel = "spring")
public interface ScaleWarnConfDtoConverter {

    /*
     * @Author: mq
     * @Description: 将查询量表预警阈值信息实体转换为数据实体
     * @Date: 2024/11/5 17:08
     * @Param: input
     * @return: com.wftk.scale.biz.entity.ScaleWarnConf
     **/
    ScaleWarnConfDTO scaleWarnConfQueryToDto(ScaleWarnConfQueryDTO scaleWarnConfQueryDTO);

    /*
     * @Author: mq
     * @Description: 将创建量表预警阈值信息实体转换为数据实体
     * @Date: 2024/11/5 17:08
     * @Param: input
     * @return: com.wftk.scale.biz.entity.ScaleWarnConf
     **/
    ScaleWarnConfDTO scaleWarnConfCreateToDto(ScaleWarnConfCreateDTO scaleWarnConfCreateDTO);

    /*
     * @Author: mq
     * @Description: 将创建量表预警阈值信息实体转换为数据实体
     * @Date: 2024/11/5 17:08
     * @Param: input
     * @return: com.wftk.scale.biz.entity.ScaleWarnConf
     **/
    ScaleWarnConf scaleWarnConfCreateToEntity(ScaleWarnConfCreateDTO scaleWarnConfCreateDTO);

    /*
     * @Author: mq
     * @Description: 将修改量表预警阈值信息实体转换为数据实体
     * @Date: 2024/11/5 17:08
     * @Param: input
     * @return: com.wftk.scale.biz.entity.ScaleWarnConf
     **/
    ScaleWarnConfDTO scaleWarnConfModifyToDto(ScaleWarnConfModifyDTO scaleWarnConfModifyDTO);

    /*
     * @Author: mq
     * @Description: 将创建量表预警阈值信息实体转换为数据实体
     * @Date: 2024/11/5 17:08
     * @Param: input
     * @return: com.wftk.scale.biz.entity.ScaleWarnConf
     **/
    ScaleWarnConf scaleWarnConfModifyToEntity(ScaleWarnConfModifyDTO scaleWarnConfModifyDTO);

}
