package com.wftk.scale.biz.manager.job.action;

import com.wftk.exception.core.exception.BusinessException;
import com.wftk.file.biz.spring.boot.autoconfigure.common.ApplicationContextHolder;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.DefaultFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.FileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.UploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.manager.ResourceManager;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.jackson.core.JSONObject;
import com.wftk.jackson.exception.JSONException;
import com.wftk.scale.biz.constant.FileConstant;
import com.wftk.scale.biz.constant.TerminalConfConstant;
import com.wftk.scale.biz.constant.enums.HttpResultStatusEnum;
import com.wftk.scale.biz.dto.httpjob.HttpResult;
import com.wftk.scale.biz.dto.notify.OrderStautsNotifyInput;
import com.wftk.scale.biz.dto.notify.UserReportNotifyInput;
import com.wftk.scale.biz.entity.HttpJob;
import com.wftk.scale.biz.entity.Order;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.service.NotifyService;
import com.wftk.scale.biz.service.OrderService;
import com.wftk.scale.biz.service.ScaleUserResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2025/1/6 11:23
 */
@Component(TerminalConfConstant.ORDER_USER_REPORT_NOTIFY)
@Slf4j
public class UserReportAction implements RequestAction{
    @Autowired
    NotifyService notifyService;

    @Autowired
    OrderService orderService;

    @Autowired
    ScaleUserResultService scaleUserResultService;

    @Override
    public void doRequestAction(HttpJob httpJob) {
        JSONObject instance = JSONObject.getInstance();
        try{
            UserReportNotifyInput userReportNotifyInput = instance.parseObject(httpJob.getParams(), UserReportNotifyInput.class);
            ScaleUserResult scaleUserResult = scaleUserResultService.getById(Long.parseLong(userReportNotifyInput.getEvaluationRecordNo()));
            Order order = orderService.getByOrderNo(scaleUserResult.getOrderNo());
            // 内部取oss文件
            FileMeta fileMeta = new DefaultFileMeta(new File(scaleUserResult.getReportUrl()), null);
            ResourceManager manager = (ResourceManager) ApplicationContextHolder.applicationContext.getBean(OSSConstant.BeanName.SIGNED);
            UploadedFileMeta uploadedFileMeta = manager.get(FileConstant.FILE_SCALE_SIGN_ROLE, fileMeta);
            userReportNotifyInput.setReportUrl(uploadedFileMeta.getUrl().toString());
            HttpResult httpResult = notifyService.userReportNotify(userReportNotifyInput, order.getTerminalCode());

            if(!Objects.equals(httpResult.getStatus(), HttpResultStatusEnum.SUCCESS.getStatus())) {
                throw new BusinessException(httpResult.getMsg());
            }
        }catch (BusinessException e){
            throw e;
        }catch (JSONException e){
            log.error("user report notify job json convert error.",e);
            throw new BusinessException("params format error .");
        }catch (Exception e){
            log.error("user report notify job execute error",e);
            throw new BusinessException("http request error .");
        }
    }
}
