package com.wftk.scale.biz.ext.evaluator.function.scale;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.ext.evaluator.constant.EnvConstant;
import com.wftk.scale.biz.service.ScaleUserResultRecordService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 量表结果评估
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
public abstract class BaseScaleEvaluationFunction extends BaseScaleFunction {

    /**
     * 根据评测结果ID和题目ID获取量表题目答案
     * 
     * @param env
     * @param questionId
     * @return
     */
    protected ScaleUserResultRecord getUserResultOption(Map<String, Object> env, Long questionId) {
        ScaleUserResultRecordService scaleUserResultRecordService = getFromEnv(env,
                EnvConstant.SCALE_USER_RESULT_RECORD_SERVICE, ScaleUserResultRecordService.class, false);
        Long resultId = getFromEnv(env, EnvConstant.RESULT_ID, Long.class, false);
        return scaleUserResultRecordService.findByResultIdAndQuestionId(resultId, questionId);
    }

    /**
     * 根据评测结果ID获取量表题目答案
     * 
     * @param env
     * @param questionIds
     * @return
     */
    protected List<ScaleUserResultRecord> getUserResultOptions(Map<String, Object> env, String questionIds, boolean allowScoreNull) {
        ScaleUserResultRecordService scaleUserResultRecordService = getFromEnv(env,
                EnvConstant.SCALE_USER_RESULT_RECORD_SERVICE, ScaleUserResultRecordService.class, false);
        Long resultId = getFromEnv(env, EnvConstant.RESULT_ID, Long.class, false);
        List<Long> questionIdsList = StrUtil.split(questionIds, ",").stream()
                .map(String::trim)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(questionIdsList)) {
            throw new IllegalArgumentException("questionIds must not be empty");
        }
        return scaleUserResultRecordService.findByResultId(resultId).stream()
                .filter(item -> questionIdsList.contains(item.getQuestionId()))
                .filter(item -> allowScoreNull || item.getScore() != null)
                .collect(Collectors.toList());
    }

    /**
     * 计算总分
     * 
     * @param userResultOptions
     * @return
     */
    protected double sumScore(List<ScaleUserResultRecord> userResultOptions) {
        return userResultOptions.stream()
                .filter(item -> item.getScore() != null)
                .map(ScaleUserResultRecord::getScore)
                .mapToDouble(BigDecimal::doubleValue)
                .sum();
    }
}
