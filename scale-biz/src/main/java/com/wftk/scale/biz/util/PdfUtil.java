package com.wftk.scale.biz.util;
import com.itextpdf.text.pdf.BaseFont;
import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.wftk.scale.biz.config.UserReportPdfPathConfig;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;

/**
 * 通过FreeMarker生成PDF
 */
@Slf4j
public class PdfUtil {


    public static String contractHandler(UserReportPdfPathConfig userReportPdfPathConfig, String templateName,
                                         Map<String, Object> paramMap, String fileName) throws Exception {
        String contractPath= userReportPdfPathConfig.getTempPath();
        // 组装html和pdf合同的全路径URL
        String localHtmlUrl = contractPath +"/"+ fileName + ".html";
        String localPdfPath = contractPath ;

        // 判断本地路径是否存在如果不存在则创建
        File localFile = new File(localPdfPath);
        if (!localFile.exists()) {
            localFile.mkdirs();
        }
        String localPdfUrl = localFile +"/"+ fileName + ".pdf";
        templateName = templateName + ".ftl";
        htmHandler(userReportPdfPathConfig, templateName, localHtmlUrl, paramMap);// 生成html合同
        pdfHandler(userReportPdfPathConfig,localHtmlUrl, localPdfUrl);// 根据html合同生成pdf合同
        deleteFile(localHtmlUrl);// 删除html格式合同
        return localPdfUrl;
    }

    public static String ftltoHtml(UserReportPdfPathConfig userReportPdfPathConfig, String templateName,
                                         Map<String, Object> paramMap, String fileName) throws Exception {
        String contractPath= userReportPdfPathConfig.getTempPath();
        // 组装html和pdf合同的全路径URL
        String localHtmlUrl = contractPath +"/"+ fileName + ".html";

        templateName = templateName + ".ftl";
        htmHandler(userReportPdfPathConfig, templateName, localHtmlUrl, paramMap);// 生成html合同
        return localHtmlUrl;
    }

    /**
     * 生成html合同
     * @param templateName
     * @param htmUrl
     * @param paramMap
     * @throws Exception
     */
    private static void htmHandler(UserReportPdfPathConfig userReportPdfPathConfig, String templateName,
                                   String htmUrl, Map<String, Object> paramMap) throws Exception {

        Configuration cfg = new Configuration(Configuration.VERSION_2_3_23);
        cfg.setDefaultEncoding("UTF-8");
        String templates = userReportPdfPathConfig.getTemplatesPath();
        cfg.setDirectoryForTemplateLoading(new File(templates));
        Template template = cfg.getTemplate(templateName);
        template.setEncoding("UTF-8");
        File outHtmFile = new File(htmUrl);
        Writer out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outHtmFile)));
        template.process(paramMap, out);
        out.close();
    }

    /**
     * 根据html合同生成pdf合同
     * @param htmUrl
     * @param pdfUrl
     * @throws Exception
     */
    private static void pdfHandler(UserReportPdfPathConfig userReportPdfPathConfig, String htmUrl, String pdfUrl)
            throws Exception {
        File htmFile = new File(htmUrl);
        File pdfFile = new File(pdfUrl);
        // 读取HTML文件内容并进行必要的处理
        String htmlContent = Files.readString(Paths.get(htmFile.getPath()));

        // 使用正则表达式匹配并修复可能有问题的URL参数
        if(htmlContent.contains("&x-oss-signature-version=")){
            htmlContent = htmlContent.replaceAll("&x-oss-signature-version=([^&]+)", "&amp;x-oss-signature-version=$1");
            htmlContent = htmlContent.replaceAll("&x-oss-access-key-id=([^&]+)", "&amp;x-oss-access-key-id=$1");
            htmlContent = htmlContent.replaceAll("&x-oss-expires=([^&]+)", "&amp;x-oss-expires=$1");
            htmlContent = htmlContent.replaceAll("&x-oss-signature=([^&]+)", "&amp;x-oss-signature=$1");
        }else {
            htmlContent = htmlContent.replaceAll("&OSSAccessKeyId=([^&]+)", "&amp;OSSAccessKeyId=$1");
            htmlContent = htmlContent.replaceAll("&Expires=([^&]+)", "&amp;Expires=$1");
            htmlContent = htmlContent.replaceAll("&Signature=([^&]+)", "&amp;Signature=$1");
        }

        // 创建ITextRenderer对象
        ITextRenderer renderer = new ITextRenderer();

        // 设置字体解析器以支持中文
        ITextFontResolver fontResolver = renderer.getFontResolver();
        String fontPath = userReportPdfPathConfig.getTemplatesPath()+"/simsun.ttc";
        try {
            fontResolver.addFont(fontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            log.error("Failed to load font: " + fontPath, e);
            throw e;
        }

        // 将处理后的内容设置到renderer中
        try {
            renderer.setDocumentFromString(htmlContent); // 使用处理后的HTML内容
        } catch (Exception e) {
            log.error("Failed to set document from HTML content", e);
            throw e;
        }

        // 布局和渲染PDF
        try {
            renderer.layout();
        } catch (Exception e) {
            log.error("Failed to layout the document", e);
            throw e;
        }

        // 创建输出流，将PDF内容写入文件
        try (FileOutputStream outputStream = new FileOutputStream(pdfFile)) {
            try {
                renderer.createPDF(outputStream);
            } catch (Exception e) {
                log.error("Failed to create PDF", e);
                throw e;
            }
        } catch (IOException e) {
            log.error("Failed to write PDF to file: {}", pdfFile.getAbsolutePath(), e);
            throw e;
        }
    }

    /**
     * 删除html格式合同
     * @param fileUrl
     */
    private static void deleteFile(String fileUrl) {
        File file = new File(fileUrl);
        file.delete();
    }

    public static String convertPdf(String htmlPath,UserReportPdfPathConfig userReportPdfPathConfig,String fileName) {
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch();
            BrowserContext context = browser.newContext();
            Page page = context.newPage();

            // 设置较大的视口
            page.setViewportSize(1200, 1000);

            // 加载本地 HTML 文件或在线 URL
            // 方法1: 加载本地文件
            page.navigate(htmlPath);

            // 等待图表容器可见
//            page.waitForSelector(".chart-container", new Page.WaitForSelectorOptions().setState(WaitForSelectorState.VISIBLE));

            // 确保 ECharts 完全渲染 - 执行 JavaScript 等待
            page.evaluate("() => new Promise((resolve) => {"
                    + "if (window.myChart && typeof window.myChart.on === 'function') {"
                    + "  window.myChart.on('finished', () => setTimeout(resolve, 300));"
                    + "} else {"
                    + "  setTimeout(resolve, 10000);"
                    + "}"
                    + "})");

            // 组装pdf的全路径URL
            String contractPath= userReportPdfPathConfig.getTempPath();

            // 判断本地路径是否存在如果不存在则创建
            File localFile = new File(contractPath);
            if (!localFile.exists()) {
                localFile.mkdirs();
            }
            String localPdfUrl = localFile +"/"+ fileName + ".pdf";

            // 设置 PDF 选项
            Page.PdfOptions pdfOptions = new Page.PdfOptions()
                    .setPath(Paths.get(localPdfUrl)) // 输出路径
                    .setPrintBackground(true)         // 打印背景
                    .setPreferCSSPageSize(true);

            // 生成 PDF
            page.pdf(pdfOptions);
            browser.close();

            log.info("PDF 生成成功: {}",localPdfUrl);
            return localPdfUrl;
        } catch (Exception e) {
            log.error("PDF 生成失败",e);
        }
        return null;
    }

}