package com.wftk.scale.biz.constant.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @createDate 2024/11/19 19:54
 */
@Getter
public enum PayChannelEnum {

    // 1支付宝2微信3现金4他人代付5.终端线下结算
    ALIPAY(1,"支付宝"),
    WECHAT(2,"微信"),
    CASH(3,"现金"),
    PAY_FOR_OTHERS(4,"他人代付"),
    OFFLINE_SETTLE(5,"线下结算");



    private Integer value;

    private String desc;

    PayChannelEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDesc(Integer status) {
        return Arrays.stream(PayChannelEnum.values()).filter(e -> e.value.equals(status))
                .map(PayChannelEnum::getDesc).findFirst().orElse(null);
    }

}
