package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.*;
import com.wftk.scale.biz.entity.ScaleCombination;
import com.wftk.scale.biz.entity.ScaleCombinationDetail;

import java.util.List;

/**
 * <p>
 * 组合量表(量表每次修改后均在此表写入一条数据) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleCombinationService extends IService<ScaleCombination> {

    /*
     * @Author: mq
     * @Description: 校验量表名称是否存在
     * @Date: 2024/11/6 16:33
     * @Param: scaleCombinationId-组合量表ID
     * @Param: scaleName-组合量表名称
     * @return: boolean
     **/
    boolean validScaleName(Long scaleCombinationId, String scaleName);

    /*
     * @Author: mq
     * @Description: 校验是否有设置量表详情数据
     * @Date: 2024/12/5 20:15
     * @Param: details-关联的量表详情信息
     * @return: boolean
     **/
    boolean vaildScaleConfDetails(List<ScaleCombinationDetail> details);

    /*
     * @Author: mq
     * @Description: 校验量表是否是完成状态
     * @Date: 2024/11/6 18:39
     * @Param: scaleCombinationId
     * @return: boolean
     **/
    boolean vaildCompletedStatus(Long scaleCombinationId);

    /*
     * @Author: mq
     * @Description: 创建组合量表基本信息
     * @Date: 2024/11/6 16:26
     * @Param: scaleCombination-组合量表基本信息
     * @Param: details-组合量表详情信息
     * @return: void
     **/
    void create(ScaleCombination scaleCombination, List<ScaleCombinationDetail> details);

    /*
     * @Author: mq
     * @Description: 修改组合量表基本信息
     * @Date: 2024/11/6 16:27
     * @Param: scaleCombination-组合量表信息
     * @Param: details-组合量表详情信息
     * @return: void
     **/
    void modify(ScaleCombination scaleCombination, List<ScaleCombinationDetail> details);

    /*
     * @Author: mq
     * @Description: 根据ID删除组合量表信息
     * @Date: 2024/11/6 16:27
     * @Param: scaleCombinationId-组合量表ID
     * @return: void
     **/
    void delete(Long scaleCombinationId);

    /*
     * @Author: mq
     * @Description: 获取组合量表最新版本数据
     * @Date: 2024/11/6 16:32
     * @Param: scaleName-量表名称
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.scale.ScaleCombinationQueryDTO>
     **/
    Page<ScaleCombinationQueryDTO> selectScalePage(String scaleName);

    /*
     * @Author: mq
     * @Description: 获取已完成的量表数据
     * @Date: 2024/11/7 11:34
     * @return: List<ScaleQueryDTO>
     **/
    List<ScaleQueryDTO> selectScaleList(Long combinationId);

    /*
     * @Author: mq
     * @Description: 根据组合量表ID获取量表详情信息
     * @Date: 2024/11/20 14:50
     * @Param: scaleCombinationId-组合量表ID
     * @return: ScaleCombinationQueryDTO
     **/
    ScaleCombinationQueryDTO findByScaleId(Long scaleCombinationId);

    /*
     * @Author: mq
     * @Description: 根据组合量表编号获取量表详情信息（最新版本）
     * @Date: 2024/11/20 14:53
     * @Param: scaleCode
     * @return: ScaleCombinationQueryDTO
     **/
    ScaleCombinationQueryDTO findByScaleCode(String scaleCode);

    /*
     * @Author: mq
     * @Description: 更新量表完成状态
     * @Date: 2024/11/6 18:41
     * @Param: scaleCombinationId-组合量表ID
     * @Param: complateStatus-完成状态(0未完成，1已完成)
     * @return: void
     **/
    void updateComplateStatus(Long scaleCombinationId, Integer complateStatus);

    /*
     * @Author: mq
     * @Description: 根据ID复制量表信息
     * @Date: 2024/11/6 16:32
     * @Param: scaleCombinationId-组合量表ID
     * @return: java.lang.Long
     **/
    Long copyScale(Long scaleCombinationId);

    /**
     * @param code
     * @return
     */
    ScaleCombination getLatestScaleByCode(String code);

    /**
     * 查询已上架组合量表
     *
     * @param scaleCode
     * @param terminalCode
     * @param scaleName
     * @param type
     * @param listingShowType
     * @return
     */
    List<ScaleCombinationDTO> getListedScaleCombination(String scaleCode, String terminalCode, String scaleName, Integer type,Integer listingShowType);

    List<ScaleDTO> getListedScaleCombinationDetail(Long scaleCombinationId, String terminalCode);

    List<ScaleListingDetailDTO> getScaleListingDetailDTO(List<ScaleSerialDTO> scaleSerials, String terminalCode);

    ScaleListingDetailDTO getScaleListingDetail(String scaleCode, Long listingId, String terminalCode);

}
