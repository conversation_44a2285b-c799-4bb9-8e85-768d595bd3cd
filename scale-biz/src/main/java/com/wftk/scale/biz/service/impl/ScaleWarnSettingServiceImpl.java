package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.ScaleWarnSettingScopeEnum;
import com.wftk.scale.biz.converter.ScaleWarnSettingConverter;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfQueryDTO;
import com.wftk.scale.biz.entity.*;
import com.wftk.scale.biz.input.ScaleWarnSettingCreateInput;
import com.wftk.scale.biz.input.ScaleWarnSettingPageQueryInput;
import com.wftk.scale.biz.input.ScaleWarnSettingUpdateInput;
import com.wftk.scale.biz.mapper.*;
import com.wftk.scale.biz.output.ScaleScaleFactorOutput;
import com.wftk.scale.biz.output.ScaleWarnSettingDetailOutput;
import com.wftk.scale.biz.output.ScaleWarnSettingPageQueryOutput;
import com.wftk.scale.biz.service.ScaleWarnSettingService;
import com.wftk.scale.biz.vo.UserVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04 16:56:01
 */
@Service
public class ScaleWarnSettingServiceImpl extends ServiceImpl<ScaleWarnSettingMapper, ScaleWarnSetting> implements ScaleWarnSettingService {

    @Autowired
    private ScaleWarnSettingConverter scaleWarnSettingConverter;

    @Autowired
    private ScaleWarnConfMapper scaleWarnConfMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ScaleWarnUserMapper scaleWarnUserMapper;

    @Autowired
    private ScaleWarnScopeMapper scaleWarnScopeMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private ScaleMapper scaleMapper;

    @Override
    public Page<ScaleWarnSettingPageQueryOutput> pageQuery(ScaleWarnSettingPageQueryInput input) {
        return Page.doSelectPage(input.getPageNum(),input.getPageSize(),()->baseMapper.query(input));
    }

    @Override
    public List<ScaleScaleFactorOutput> queryScaleScaleFactor() {
        return baseMapper.queryScaleScaleFactor();
    }

    @Override
    public ScaleWarnSettingDetailOutput getDetailById(Long id) {
        ScaleWarnSetting scaleWarnSetting = baseMapper.getById(id);
        if (scaleWarnSetting == null) {
            return null;
        }
        ScaleWarnSettingDetailOutput scaleWarnSettingDetailOutput = scaleWarnSettingConverter.entityToDetailOutput(scaleWarnSetting);
        Scale scale = scaleMapper.getById(scaleWarnSetting.getScaleId());
        if (scale != null) {
            scaleWarnSettingDetailOutput.setScaleName(scale.getName());
            scaleWarnSettingDetailOutput.setScaleCode(scale.getCode());
        }
        // 预警因子
        String scaleWarnConfIds = scaleWarnSetting.getScaleWarnConfId();
        List<String> scaleWarnConfIdList = StrUtil.split(scaleWarnConfIds, ",");
        if (CollUtil.isNotEmpty(scaleWarnConfIdList)) {
            List<ScaleWarnConfQueryDTO> scaleWarnConfigList = scaleWarnConfMapper.getByIds(scaleWarnSetting.getScaleId(),scaleWarnConfIdList);
            if (CollUtil.isNotEmpty(scaleWarnConfigList)) {
                List<ScaleWarnSettingDetailOutput.ScaleWarnConfOutput> list = scaleWarnConfigList.stream().map(it -> {
                    ScaleWarnSettingDetailOutput.ScaleWarnConfOutput scaleWarnConfOutput = new ScaleWarnSettingDetailOutput.ScaleWarnConfOutput();
                    scaleWarnConfOutput.setId(it.getId());
                    scaleWarnConfOutput.setScaleFactorId(it.getFactorId());
                    scaleWarnConfOutput.setScaleFactorName(it.getFactorName());
                    return scaleWarnConfOutput;
                }).toList();
                scaleWarnSettingDetailOutput.setScaleWarnConfOutputList( list);
            }
        }
        // 预警范围
        Integer scope = scaleWarnSetting.getScope();
        List<ScaleWarnScope> scaleWarnScopes = scaleWarnScopeMapper.queryByScaleIdAndScaleWarnSettingId(scaleWarnSetting.getId(), scaleWarnSetting.getScaleId());
        if (CollUtil.isNotEmpty(scaleWarnScopes)) {
            ScaleWarnSettingScopeEnum scaleWarnSettingScopeEnum = ScaleWarnSettingScopeEnum.getScaleWarnSettingScopeEnumByValue(scope);
            Set<Long> scopeIds = scaleWarnScopes.stream().map(ScaleWarnScope::getScopeId).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(scopeIds)) {
                switch (scaleWarnSettingScopeEnum) {
                    case PERSON -> {
                        List<UserVO> userList = userMapper.getUserDepartmentByIds(scopeIds);
                        List<ScaleWarnSettingDetailOutput.ScaleWarnScopeOutput> list = userList.stream().map(it -> {
                            ScaleWarnSettingDetailOutput.ScaleWarnScopeOutput scaleWarnScopeOutput = new ScaleWarnSettingDetailOutput.ScaleWarnScopeOutput();
                            scaleWarnScopeOutput.setScopeId(it.getId());
                            scaleWarnScopeOutput.setScopeName(it.getName());
                            scaleWarnScopeOutput.setDepartmentId(it.getDepartmentId());
                            return scaleWarnScopeOutput;
                        }).toList();
                        scaleWarnSettingDetailOutput.setScaleWarnScopeOutputList(list);
                    }
                    case DEPARTMENT -> {
                        List<Department> departmentList = departmentMapper.getByIds(scopeIds);
                        List<ScaleWarnSettingDetailOutput.ScaleWarnScopeOutput> list = departmentList.stream().map(it -> {
                            ScaleWarnSettingDetailOutput.ScaleWarnScopeOutput scaleWarnScopeOutput = new ScaleWarnSettingDetailOutput.ScaleWarnScopeOutput();
                            scaleWarnScopeOutput.setScopeId(it.getId());
                            scaleWarnScopeOutput.setScopeName(it.getName());
                            return scaleWarnScopeOutput;
                        }).toList();
                        scaleWarnSettingDetailOutput.setScaleWarnScopeOutputList(list);
                    }
                }
            }
        }

        // 预警接收人
        Set<Long> scaleWarnUserIds = scaleWarnUserMapper.getUserIdByScaleWarnSettingId(scaleWarnSetting.getId(), scaleWarnSetting.getScaleId());
        if (CollUtil.isNotEmpty(scaleWarnUserIds)) {
            List<UserVO> userList = userMapper.getUserDepartmentByIds(scaleWarnUserIds);
            List<ScaleWarnSettingDetailOutput.ReceiveScaleWarnUserOut> list = userList.stream().map(it -> {
                ScaleWarnSettingDetailOutput.ReceiveScaleWarnUserOut receiveScaleWarnUserOut = new ScaleWarnSettingDetailOutput.ReceiveScaleWarnUserOut();
                receiveScaleWarnUserOut.setUserId(it.getId());
                receiveScaleWarnUserOut.setUserName(it.getName());
                receiveScaleWarnUserOut.setDepartmentId(it.getDepartmentId());
                return receiveScaleWarnUserOut;
            }).toList();
            scaleWarnSettingDetailOutput.setReceiveScaleWarnUserOutList(list);
        }
        return scaleWarnSettingDetailOutput;
    }

    @Override
    @Transactional
    public void create(ScaleWarnSettingCreateInput createInput) {
        ScaleWarnSetting scaleWarnSetting = scaleWarnSettingConverter.inputToEntity(createInput);
        scaleWarnSetting.setScaleWarnConfId(StrUtil.join(",",createInput.getScaleWarnConfIds()));
        save(scaleWarnSetting);
        Set<Long> receiveScaleWarnUserIds = createInput.getReceiveScaleWarnUserIds();
        receiveScaleWarnUserIds.forEach(it -> {
            ScaleWarnUser scaleWarnUser = new ScaleWarnUser();
            scaleWarnUser.setUserId(it);
            scaleWarnUser.setScaleId(scaleWarnSetting.getScaleId());
            scaleWarnUser.setScaleWarnSettingId(scaleWarnSetting.getId());
            scaleWarnUserMapper.insert(scaleWarnUser);
        });
        Set<Long> scaleWarnScopeIds = createInput.getScaleWarnScopeIds();
        scaleWarnScopeIds.forEach(it -> {
            ScaleWarnScope scaleWarnScope = new ScaleWarnScope();
            scaleWarnScope.setScopeId(it);
            scaleWarnScope.setScopeType(createInput.getScope());
            scaleWarnScope.setScaleId(scaleWarnSetting.getScaleId());
            scaleWarnScope.setScaleWarnSettingId(scaleWarnSetting.getId());
            scaleWarnScopeMapper.insert(scaleWarnScope);
        });
    }

    @Override
    public void update(ScaleWarnSettingUpdateInput updateInput) {
        ScaleWarnSetting scaleWarnSetting = scaleWarnSettingConverter.updateInputToEntity(updateInput);
        scaleWarnSetting.setScaleWarnConfId(StrUtil.join(",",updateInput.getScaleWarnConfIds()));
        updateById(scaleWarnSetting);

        Set<Long> receiveScaleWarnUserIds = updateInput.getReceiveScaleWarnUserIds();
        scaleWarnUserMapper.deleteByScaleWarnSettingId(scaleWarnSetting.getId(), scaleWarnSetting.getScaleId());
        receiveScaleWarnUserIds.forEach(it -> {
            ScaleWarnUser scaleWarnUser = new ScaleWarnUser();
            scaleWarnUser.setUserId(it);
            scaleWarnUser.setScaleId(scaleWarnSetting.getScaleId());
            scaleWarnUser.setScaleWarnSettingId(scaleWarnSetting.getId());
            scaleWarnUserMapper.insert(scaleWarnUser);
        });

        Set<Long> scaleWarnScopeIds = updateInput.getScaleWarnScopeIds();
        scaleWarnScopeMapper.deleteByByScaleIdAndScaleWarnSettingId(scaleWarnSetting.getId(), scaleWarnSetting.getScaleId());
        scaleWarnScopeIds.forEach(it -> {
            ScaleWarnScope scaleWarnScope = new ScaleWarnScope();
            scaleWarnScope.setScopeId(it);
            scaleWarnScope.setScaleId(scaleWarnSetting.getScaleId());
            scaleWarnScope.setScaleWarnSettingId(scaleWarnSetting.getId());
            scaleWarnScope.setScopeType(updateInput.getScope());
            scaleWarnScopeMapper.insert(scaleWarnScope);
        });
    }

    @Override
    public void delete(Long id) {
        baseMapper.deleteById(id);
    }
}
