package com.wftk.scale.biz.dto.user.role;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SysRoleCreateDTO implements Serializable {

    @NotBlank(message = "角色名称不能为空")
    @Length(max = 50, message = "角色名称最大输入50个字符")
    private String name;

    @NotBlank(message = "角色标识不能为空")
    @Length(max = 50, message = "角色标识最大输入50个字符")
    private String code;

    @NotNull(message = "角色顺序不能为空")
    private Integer sort;

    @Length(max = 255, message = "备注最大输入255个字符")
    private String remark;
}
