package com.wftk.scale.biz.dto.notify;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/12/18 19:40
 */
@Data
public class EvaluationRecordNotifyInput {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 终端订单号
     */
    private String terminalSerialNo;

    /**
     * 记录号
     */
    private String evaluationRecordNo;

    /**
     * 量表编码
     */
    private String scaleCode;

    /**
     * 量表名称
     */
//    private String scaleName;

    /**
     * 开始测评时间
     */
    private LocalDateTime startTime;

    /**
     * 结束测评时间
     */
    private LocalDateTime endTime;

}
