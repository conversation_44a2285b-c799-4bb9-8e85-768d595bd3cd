package com.wftk.scale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.dto.dict.DictDTO;
import com.wftk.scale.biz.entity.Dict;
import com.wftk.scale.biz.mapper.DictMapper;
import com.wftk.scale.biz.service.DictService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 数据字典主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class DictServiceImpl extends ServiceImpl<DictMapper, Dict> implements DictService {

    @Override
    public List<DictDTO> findByCode(String code) {
        return baseMapper.findByCode(code);
    }
}
