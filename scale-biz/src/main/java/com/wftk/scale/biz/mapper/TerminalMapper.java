package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.scale.TerminalDTO;
import com.wftk.scale.biz.entity.Terminal;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 终端信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface TerminalMapper extends BaseMapper<Terminal> {

    /*
     * @Author: mq
     * @Description: 校验终端编号是否已经存在
     * @Date: 2024/11/6 15:57
     * @Param: terminalId-终端ID
     * @Param: terminalCode-终端编号
     * @return: boolean
     **/
    boolean vaildTerminalCodeExists(@Param("terminalId") Long terminalId, @Param("terminalCode") String terminalCode);

    /**
     *
     * @param terminalCode
     * @return
     */
    Terminal getByCode(@Param("terminalCode") String terminalCode);


    /*
     * @Author: mq
     * @Description: 根据ID更新终端信息数据的状态
     * @Date: 2024/11/6 16:06
     * @Param: terminalId-终端ID
     * @Param: enable-是否禁用
     * @Param: opUser-操作用户
     * @return: void
     **/
    void updateEnable(@Param("terminalId") Long terminalId, @Param("enable") Boolean enable, @Param("opUser") String opUser);

    /*
     * @Author: mq
     * @Description: 根据条件检索终端信息
     * @Date: 2024/11/6 16:02
     * @Param: terminalName-终端名称
     * @return: java.util.List<com.wftk.scale.biz.entity.Terminal>
     **/
    List<TerminalDTO> getList(@Param("terminalName") String terminalName);

    /*
     * @Author: mq
     * @Description: 获取未绑定机构的终端
     * @Date: 2024/11/6 16:02
     * @Param: terminalName-终端名称
     * @return: java.util.List<com.wftk.scale.biz.entity.Terminal>
     **/
    List<TerminalDTO> getNotBindingDepartmentList();
}
