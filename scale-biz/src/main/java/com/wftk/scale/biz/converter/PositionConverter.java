package com.wftk.scale.biz.converter;

import com.wftk.scale.biz.dto.user.position.PositionAddDTO;
import com.wftk.scale.biz.dto.user.position.PositionUpdateDTO;
import com.wftk.scale.biz.entity.Position;
import com.wftk.scale.biz.excel.model.PositionExcelDataDTO;
import com.wftk.scale.biz.vo.PositionVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface PositionConverter {

    PositionVO positionToPositionVO(Position position);

    Position positionAddDtoToPosition(PositionAddDTO dto);

    Position positionUpdateDtoToPosition(PositionUpdateDTO dto);

    PositionExcelDataDTO positionToPositionExcelDataDTO(Position position);
}
