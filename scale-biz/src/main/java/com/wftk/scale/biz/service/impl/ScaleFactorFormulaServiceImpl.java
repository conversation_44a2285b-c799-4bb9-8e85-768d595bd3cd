package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.EnableEnum;
import com.wftk.scale.biz.entity.ScaleFactorFormula;
import com.wftk.scale.biz.ext.evaluator.core.EvaluatorEngine;
import com.wftk.scale.biz.ext.evaluator.core.EvaluatorEngine.ExpressionValidateResult;
import com.wftk.scale.biz.mapper.ScaleFactorFormulaMapper;
import com.wftk.scale.biz.mapper.ScaleFactorMapper;
import com.wftk.scale.biz.service.ScaleFactorFormulaService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 因子公式 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class ScaleFactorFormulaServiceImpl extends ServiceImpl<ScaleFactorFormulaMapper, ScaleFactorFormula>
        implements ScaleFactorFormulaService {

    @Autowired
    private ScaleFactorFormulaMapper scaleFactorFormulaMapper;
    @Resource
    private ScaleFactorMapper scaleFactorMapper;

    @Autowired
    private EvaluatorEngine evaluatorEngine;

    @Override
    public boolean validFormulaName(Long factorFormulaId, String formulaName) {
        boolean checkResult = false;
        if (StrUtil.isEmpty(formulaName)) {
            return checkResult;
        }
        checkResult = scaleFactorFormulaMapper.validFormulaNameExist(factorFormulaId, formulaName);
        return checkResult;
    }

    @Override
    public boolean vaildFormulaEnabled(Long factorFormulaId) {
        boolean checkResult = false;
        ScaleFactorFormula formula = scaleFactorFormulaMapper.selectById(factorFormulaId);
        if (ObjectUtil.isNull(formula)) {
            return checkResult;
        }
        checkResult = formula.getEnable();
        return checkResult;
    }

    @Override
    public String checkFormulaUsed(Long factorFormulaId) {
        return scaleFactorFormulaMapper.checkFormulaUsed(factorFormulaId);
    }

    @Override
    public void create(ScaleFactorFormula scaleFactorFormula) {
        scaleFactorFormula.setEnable(EnableEnum.ENABLE.getEnable());
        String formula = scaleFactorFormula.getFormula();
        if (StrUtil.isNotBlank(formula)) {
            formula = formula.replaceAll("\\s", "").toLowerCase();
            ExpressionValidateResult validateResult = evaluatorEngine.validate(formula);
            if (!validateResult.valid()) {
                throw new BusinessException(validateResult.message());
            }
            scaleFactorFormula.setFormula(formula);
        }
        scaleFactorFormulaMapper.insert(scaleFactorFormula);
    }

    @Override
    public void delete(Long factorFormulaId) {
        // 判断因子公式是否被因子维度所引用,关联的题目和量表需未删除
//        if (scaleFactorMapper.checkFactorIsDepend(factorFormulaId)) {
//            throw new BusinessException("删除失败, 因子公式已被因子维度所引用！");
//        }
        scaleFactorFormulaMapper.deleteById(factorFormulaId);
    }

    @Override
    public void modify(ScaleFactorFormula scaleFactorFormula) {
        ScaleFactorFormula rawData = scaleFactorFormulaMapper.selectById(scaleFactorFormula.getId());
        BeanUtils.copyProperties(scaleFactorFormula, rawData);
        String formula = rawData.getFormula();
        if (StrUtil.isNotBlank(formula)) {
            formula = formula.replaceAll("\\s", "").toLowerCase();
            ExpressionValidateResult validateResult = evaluatorEngine.validate(formula);
            if (!validateResult.valid()) {
                throw new BusinessException(validateResult.message());
            }
            rawData.setFormula(formula);
        }
        scaleFactorFormulaMapper.updateById(rawData);
    }

    @Override
    public void updateEnable(Long factorFormulaId, Boolean enable) {
        String opUser = AuthenticationHolder.getAuthentication().getAuthUser().getAccount();
        scaleFactorFormulaMapper.updateEnable(factorFormulaId, enable, opUser);
    }

    @Override
    public void updateStatus(Long factorFormulaId, Boolean status) {
        scaleFactorFormulaMapper.update(new LambdaUpdateWrapper<ScaleFactorFormula>()
                .eq(ScaleFactorFormula::getId, factorFormulaId)
                .set(ScaleFactorFormula::getStatus, status));
    }

    @Override
    public Page<ScaleFactorFormula> selectPage(String formulaName) {
        List<ScaleFactorFormula> list = scaleFactorFormulaMapper.getList(formulaName, null, null);
        return Page.doSelectPage(() -> list);
    }

    @Override
    public List<ScaleFactorFormula> getListOfEnabled(String formulaName, Integer status) {
        return scaleFactorFormulaMapper.getList(formulaName, true, status);
    }
}
