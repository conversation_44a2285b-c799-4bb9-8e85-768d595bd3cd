package com.wftk.scale.biz.ext.wechat.enums;

import com.wftk.common.core.enums.BaseEnum;

/**
 * <AUTHOR>
 * @create 2023/11/7 11:40
 */
public enum NotificationStatusEnum implements BaseEnum {
    WAIT(1, "待通知"), SUCCESS(2, "通知成功"), FAILED(10, "通知失败");

    private final Integer value;
    private final String label;

    NotificationStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
