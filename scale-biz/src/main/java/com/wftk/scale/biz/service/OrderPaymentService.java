package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.entity.OrderPayment;

import java.util.Map;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface OrderPaymentService extends IService<OrderPayment> {

    Map<String, Object> doPrepay(OrderPayment orderPayment);

    Integer updateStatus(String tradeNo,Integer status,Integer oldStatus);

}
