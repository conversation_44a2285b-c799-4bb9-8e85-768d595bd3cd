package com.wftk.scale.biz.excel.service.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.IdGenerator;
import com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO;
import com.wftk.scale.biz.entity.ScaleQuestionOption;
import com.wftk.scale.biz.excel.model.ScaleQuestionExcelDataDTO;
import com.wftk.scale.biz.excel.service.IConvert;
import com.wftk.scale.biz.excel.utils.SpringContextUtil;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ScaleQuestionConvert implements IConvert<ScaleQuestionExcelDataDTO, ScaleQuestionQueryDTO> {

    private ScaleQuestionConvert() {}

    private static final class ConvertHolder {
        private static final ScaleQuestionConvert CONVERT = new ScaleQuestionConvert();

        private static final IdGenerator GENERATOR = SpringContextUtil.getBean(IdGenerator.class);
    }

    /**
     * 默认EXCEL模板答案的最大数量，每个题目最大允许10个答案
     */
    private static final Integer IMPORT_MAX_QUESTION_NUM = 10;

    /**
     * 默认EXCEL模板答案得分的标签值，题目答案对应的标签值
     */
    private static final String[] IMPORT_QUESTION_LABEL = new String[]{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J"};

    public static ScaleQuestionConvert getInstance() {
        return ConvertHolder.CONVERT;
    }

    @Override
    public ScaleQuestionQueryDTO convertData(Long scaleId, ScaleQuestionExcelDataDTO value) {
        ScaleQuestionQueryDTO question = ScaleQuestionQueryDTO.builder().build();
        BeanUtils.copyProperties(value, question);
        long questionId = ConvertHolder.GENERATOR.nextId();
        question.setId(questionId);
        question.setScaleId(scaleId);
        question.setOptions(this.convertQuestionOption(scaleId, questionId, value));
        return question;
    }

    private List<ScaleQuestionOption> convertQuestionOption(Long scaleId, Long questionId, ScaleQuestionExcelDataDTO rowData) {
        List<ScaleQuestionOption> optionList = Lists.newArrayList();
        for (int index = 1; index <= IMPORT_MAX_QUESTION_NUM; index++) {
            Object answerObj = ReflectUtil.getFieldValue(rowData, "answer" + index);
            Object answerScoreObj = ReflectUtil.getFieldValue(rowData, "answerScore" + index);
            Object property = ReflectUtil.getFieldValue(rowData, "property" + index);
            Object operateValue = ReflectUtil.getFieldValue(rowData, "operateValue" + index);
            if (ObjUtil.isNotNull(answerObj)) {
                ScaleQuestionOption option = new ScaleQuestionOption();
                option.setScaleId(scaleId);
                option.setValue(String.valueOf(answerObj));
                option.setQuestionId(questionId);
                option.setScore(ObjectUtil.isEmpty(answerScoreObj)? null : Integer.valueOf(String.valueOf(answerScoreObj)));
                option.setLabel(IMPORT_QUESTION_LABEL[index - 1]);
                //性质允许为空
                option.setResult(ObjectUtil.isEmpty(property) ? null : Boolean.valueOf(String.valueOf(property)));
                //转换值
                option.setOperateValue(ObjectUtil.isEmpty(operateValue) ? null : Integer.valueOf(String.valueOf(operateValue)));
                optionList.add(option);
            }
        }
        return optionList;
    }

    public List<ScaleQuestionOption> setQuestionId(List<ScaleQuestionQueryDTO> list){
        return list.stream()
                .filter(dto -> CollUtil.isNotEmpty(dto.getOptions()))
                .map(dto -> {
                    System.out.println(dto.getId());
                    List<ScaleQuestionOption> options = dto.getOptions();
                    options.forEach(it -> it.setQuestionId(dto.getId()));
                    return options;
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }
}
