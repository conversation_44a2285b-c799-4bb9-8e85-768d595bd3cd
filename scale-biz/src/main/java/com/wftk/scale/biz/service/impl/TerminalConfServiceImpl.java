package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.EnableEnum;
import com.wftk.scale.biz.entity.TerminalConf;
import com.wftk.scale.biz.mapper.TerminalConfMapper;
import com.wftk.scale.biz.service.TerminalConfService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 终端配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class TerminalConfServiceImpl extends ServiceImpl<TerminalConfMapper, TerminalConf> implements TerminalConfService {

    @Autowired
    private TerminalConfMapper terminalConfMapper;

    @Override
    public boolean validTerminalCodeAndValue(Long id, String itemCode, String itemValue) {
        boolean checkResult = false;
        if (StrUtil.isEmpty(itemCode) || StrUtil.isEmpty(itemValue)) {
            return checkResult;
        }
        checkResult = terminalConfMapper.validTerminalCodeAndValueExist(id, itemCode, itemValue);
        return checkResult;
    }

    @Override
    public void create(TerminalConf terminalConf) {
        terminalConf.setEnable(ObjUtil.defaultIfNull(terminalConf.getEnable(), EnableEnum.ENABLE.getEnable()));
        terminalConfMapper.insert(terminalConf);
    }

    @Override
    public void delete(Long id) {
        terminalConfMapper.deleteById(id);
    }

    @Override
    public void modify(TerminalConf terminalConf) {
        TerminalConf rawData = terminalConfMapper.selectById(terminalConf.getId());
        BeanUtils.copyProperties(terminalConf, rawData);
        terminalConfMapper.updateById(rawData);
    }

    @Override
    public void updateEnable(Long id, Boolean enable) {
        if (ObjectUtil.isNull(id)) {
            return;
        }
        terminalConfMapper.updateEnable(id, enable);
    }

    @Override
    public Page<TerminalConf> selectPage(String terminalName, Integer terminalType, Long userId) {
        return Page.doSelectPage(() -> terminalConfMapper.getList(terminalName, terminalType, userId));
    }

    @Override
    public String findByItemCode(Long terminalId, String itermCode, Boolean enable) {
        return baseMapper.selectByTerminalIdAndItemCode(terminalId, itermCode, enable);
    }

    @Override
    public String findByItemCode(String terminalCode, String itermCode, Boolean enable) {
        return baseMapper.selectByTerminalCodeAndItemCode(terminalCode, itermCode, enable);
    }

}
