package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.order.AdminOrderQueryDto;
import com.wftk.scale.biz.dto.order.OrderQueryDTO;
import com.wftk.scale.biz.entity.Order;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface OrderMapper extends BaseMapper<Order> {

    Order getByTerminalSerialNo(@Param("terminalCode")String terminalCode,@Param("terminalSerialNo")String terminalSerialNo);

    Order getByTerminalSerialNoAndOrderNo(@Param("terminalCode")String terminalCode,@Param("terminalSerialNo")String terminalSerialNo,@Param("orderNo")String orderNo);

    Order getByOrderNo(@Param("orderNo")String orderNo);

    List<OrderQueryDTO> getList(AdminOrderQueryDto queryDto);

    List<Order> getExtOrderList(@Param("terminalCode")String terminalCode,@Param("terminalSerialNos")List<String> terminalSerialNos);

    Integer updateStatus(@Param("orderNo")String orderNo, @Param("status")Integer status,@Param("oldStatus") Integer oldStatus);
}
