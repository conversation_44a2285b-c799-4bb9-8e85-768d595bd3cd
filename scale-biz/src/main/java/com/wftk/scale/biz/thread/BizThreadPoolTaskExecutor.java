package com.wftk.scale.biz.thread;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;


public class BizThreadPoolTaskExecutor {

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public BizThreadPoolTaskExecutor(ThreadPoolTaskExecutor threadPoolTaskExecutor) {
        this.threadPoolTaskExecutor = threadPoolTaskExecutor;
    }

    public ThreadPoolTaskExecutor getThreadPoolTaskExecutor() {
        return threadPoolTaskExecutor;
    }

}
