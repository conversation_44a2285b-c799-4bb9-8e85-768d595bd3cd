package com.wftk.scale.biz.constant.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @EnumName: PropertyEnum
 * @Description: 性质常量信息
 * @Author: mq
 * @Date: 2024-11-05 14:39
 * @Version: 1.0
 **/
@Getter
public enum PropertyEnum {

    POSITIVE(true, "阳"),

    NEGATIVE(false, "阴"),

    NONE(null, "无"),
    ;

    private Boolean status;

    private String value;

    PropertyEnum(Boolean status, String value) {
        this.status = status;
        this.value = value;
    }

    public static String getValue(Boolean result) {
        return Arrays.stream(PropertyEnum.values())
                .filter(e -> Objects.equals(e.getStatus(), result))
                .findFirst()
                .map(PropertyEnum::getValue)
                .orElse(null);
    }

    public static Boolean getStatus(String value) {
        return Arrays.stream(PropertyEnum.values())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .map(PropertyEnum::getStatus)
                .orElse(null);
    }
}
