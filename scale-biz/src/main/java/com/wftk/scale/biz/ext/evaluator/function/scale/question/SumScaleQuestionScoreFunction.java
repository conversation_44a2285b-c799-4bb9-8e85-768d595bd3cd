package com.wftk.scale.biz.ext.evaluator.function.scale.question;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.googlecode.aviator.runtime.type.AviatorDouble;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;
import com.wftk.scale.biz.ext.evaluator.function.scale.BaseScaleEvaluationFunction;

import java.util.List;
import java.util.Map;

/**
 * 根据量表题目ID算分
 * 示例: q_sum("1,2,3"), 表示计算当前量表中ID为1,2,3题的总分
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
public class SumScaleQuestionScoreFunction extends BaseScaleEvaluationFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Question.Q_SUM;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env, final AviatorObject arg1) {
        Object questionIdsObj = arg1.getValue(env);
        if (questionIdsObj == null) {
            throw new IllegalArgumentException("arg1 get value is null");
        }
        String questionIds = questionIdsObj.toString();
        logger.info("q_sum: questionIds: {}", questionIds);
        if (StrUtil.isBlank(questionIds)) {
            throw new IllegalArgumentException("questionIds must not be blank");
        }
        List<ScaleUserResultRecord> resultItemDetailByQuestionIds = getUserResultOptions(env, questionIds, false);
        if (CollUtil.isEmpty(resultItemDetailByQuestionIds)) {
            throw new IllegalArgumentException("questionIds not found");
        }
        double score = sumScore(resultItemDetailByQuestionIds);
        return new AviatorDouble(score);
    }

}
