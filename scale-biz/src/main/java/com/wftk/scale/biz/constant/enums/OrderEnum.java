package com.wftk.scale.biz.constant.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @createDate 2024/11/19 17:31
 */
@Getter
public enum OrderEnum {

    WAIT_PAY(1,"待支付"),
    WAIT_COMPLETE(2,"待完成"),
    COMPLETED(3,"已完成"),
    CANCLE(4,"已取消");



    private Integer status;

    private String desc;

    OrderEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static String getDesc(Integer status) {
        return Arrays.stream(OrderEnum.values()).filter(e -> e.status.equals(status))
                .map(OrderEnum::getDesc).findFirst().orElse(null);
    }

}
