package com.wftk.scale.biz.constant.enums;

import com.wftk.common.core.enums.BaseEnum;

public enum ScaleWarnSettingLevelEnum implements BaseEnum {
    LIGHI(1,"轻度"),MODERATE(2,"中度"),HEAVY(3,"重度");

    private Integer value;
    private String label;

    ScaleWarnSettingLevelEnum(Integer value, String label){
        this.value = value;
        this.label = label;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
