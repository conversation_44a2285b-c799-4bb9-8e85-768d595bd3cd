package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.TerminalConf;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @InterfaceName: TerminalConfMapper
 * @Description: 终端配置信息数据操作
 * @Author: mq
 * @Date: 2024-10-24 17:54
 * @Version: 1.0
 **/
public interface TerminalConfMapper extends BaseMapper<TerminalConf> {

    /*
     * @Author: mq
     * @Description: 校验终端配置编码是否已经存在
     * @Date: 2024/10/24 17:57
     * @Param: itemCode-配置编码
     * @return: boolean
     **/
    boolean validTerminalCodeAndValueExist(@Param("id") Long id, @Param("itemCode") String itemCode, @Param("itemValue") String itemValue);

    /*
     * @Author: mq
     * @Description: 根据ID更新终端配置数据的状态
     * @Date: 2024/10/24 17:25
     * @Param: id-主键ID
     * @Param: enable-启用状态(false 未开启 true 已开启)
     * @return: void
     **/
    void updateEnable(Long id, Boolean enable);

    /*
     * @Author: mq
     * @Description: 根据条件查询终端配置列表数据
     * @Date: 2024/10/24 17:43
     * @Param: terminalName
     * @Param: terminalType
     * @Param: userId
     * @return: java.util.List<com.wftk.scale.biz.entity.TerminalConf>
     **/
    List<TerminalConf> getList(@Param("terminalName") String terminalName, @Param("terminalType") Integer terminalType, @Param("userId") Long userId);

    /**
     *
     * @param terminalId
     * @param itemCode
     * @param enable
     * @return
     */
    String selectByTerminalIdAndItemCode(@Param("terminalId") Long terminalId, @Param("itemCode") String itemCode, @Param("enable") Boolean enable);


    /**
     *
     * @param terminalCode
     * @param itemCode
     * @param enable
     * @return
     */
    String selectByTerminalCodeAndItemCode(@Param("terminalCode") String terminalCode, @Param("itemCode") String itemCode, @Param("enable") Boolean enable);
}
