package com.wftk.scale.biz.dto.scale;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: ScaleWarnConfQueryDTO
 * @Description: 量表预警阈值信息
 * @Author: mq
 * @Date: 2024-11-05 13:55
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleWarnConfQueryDTO implements Serializable {

    /**
     * 预警阈值ID
     */
    private Long id;

    /**
     * 量表ID
     */
    private Long scaleId;

    /**
     * 因子ID
     */
    private Long factorId;

    /**
     * 因子名称
     */
    private String factorName;

    /**
     * 预警逻辑，1大于，2等于，3小于
     */
    private Integer type;

    /**
     * 预警阈值
     */
    private Integer threshold;

    /**
     * 标识标签
     */
    private String tag;

    /**
     * 标识标签
     */
    private String tagId;

    /**
     * 预警方式，1短信，2邮件,可多个，逗号分隔。
     */
    private String warnType;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 批次号
     */
    private String batchNo;

}
