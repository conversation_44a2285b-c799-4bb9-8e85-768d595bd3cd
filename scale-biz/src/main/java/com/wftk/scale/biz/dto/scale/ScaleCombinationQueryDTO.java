package com.wftk.scale.biz.dto.scale;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ScaleCombinationQueryDTO
 * @Description: 组合量表
 * @Author: mq
 * @Date: 2024-11-06 16:28
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleCombinationQueryDTO implements Serializable {

    /**
     * 量表id
     */
    private Long id;

    /**
     * 上下架ID
     */
    private Long scalelistingId;

    /**
     * 量表名称
     */
    private String name;

    /**
     * 量表编号
     */
    private String code;

    /**
     * 测评方式，1依次测评，2选择测评
     */
    private Integer type;

    /**
     * 量表数量
     */
    private Integer numOfScale;

    /**
     * 量表排序
     */
    private String sort;

    /**
     * 0未完成，1已完成，问题，因子，解读等没完成之前是未完成
     */
    private Integer status;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 版本号（hash值）
     */
    private String version;

    /**
     * 量表创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 量表更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 详情
     */
    private List<ScaleQueryDTO> details;

    /**
     * 能够复测
     */
    private Boolean allowRepeat;

    /**
     * 量表上架项目配置开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime listingStartTime;

    /**
     * 量表上架项目配置结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime listingEndTime;
}
