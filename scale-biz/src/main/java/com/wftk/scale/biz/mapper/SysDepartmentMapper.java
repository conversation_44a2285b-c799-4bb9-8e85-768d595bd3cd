package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.SysDepartment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SysDepartmentMapper extends BaseMapper<SysDepartment> {

    List<String> concatCodeAndName();

    boolean countSysUserByIdAndEnable(@Param("id") Long id, @Param("enabled") Boolean enable);

    List<Long> selectChildIdList(@Param("id") Long id);
}
