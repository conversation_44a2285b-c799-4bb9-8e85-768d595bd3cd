package com.wftk.scale.biz.constant.enums;


import lombok.Getter;

import java.util.Arrays;

/**
 * @EnumName: QuestionScoringTypeEnum
 * @Description:
 * @Author: funian
 * @Date: 2025/09/15
 * @Version: 1.0
 **/
@Getter
public enum QuestionScoringTypeEnum {

    SELECT_SINGLE(1, "正向计分"),

    SELECT_OTHER(2, "反向计分");
    private Integer type;

    private String value;

    QuestionScoringTypeEnum(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public static Integer getType(String value) {
        return Arrays.stream(QuestionScoringTypeEnum.values())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .map(QuestionScoringTypeEnum::getType)
                .orElse(null);
    }

    public static String getValue(Integer type) {
        return Arrays.stream(QuestionScoringTypeEnum.values())
                .filter(e -> e.getType().equals(type))
                .findFirst()
                .map(QuestionScoringTypeEnum::getValue)
                .orElse(null);
    }
}
