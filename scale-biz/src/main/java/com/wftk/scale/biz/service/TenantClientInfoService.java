package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.entity.TenantClientInfo;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * <p>
 * 租户下的client信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27 15:50:09
 */
public interface TenantClientInfoService extends IService<TenantClientInfo> {

    /**
     * 根据clientId查询tenant信息
     * @param clientId
     * @param enable
     * @return
     */
    TenantClientInfo findOneByClientId(@NonNull String clientId, @Nullable Boolean enable);
}
