package com.wftk.scale.biz.ext.evaluator.core;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2025-09-03
 */
public class ExpressionTemplate {

    /**
     * 模板化后的参数
     */
    private final Collection<String> parameters;
    
    /**
     * 模板
     */
    private final String template;

    public ExpressionTemplate(Collection<String> parameters, String template) {
        this.parameters = parameters;
        this.template = template;
    }

    public Collection<String> getParameters() {
        return parameters;
    }

    public String getTemplate() {
        return template;
    }

}
