package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.TerminalConfConstant;
import com.wftk.scale.biz.constant.enums.HttpJobStatusEnum;
import com.wftk.scale.biz.constant.enums.OrderEnum;
import com.wftk.scale.biz.constant.enums.PaymentEnum;
import com.wftk.scale.biz.constant.enums.ScaleEnum;
import com.wftk.scale.biz.converter.NotifyConvert;
import com.wftk.scale.biz.converter.OrderConverter;
import com.wftk.scale.biz.dto.notify.OrderStautsNotifyInput;
import com.wftk.scale.biz.dto.order.AdminOrderQueryDto;
import com.wftk.scale.biz.dto.order.BuyOrderDTO;
import com.wftk.scale.biz.dto.order.OrderPayDTO;
import com.wftk.scale.biz.dto.order.OrderQueryDTO;
import com.wftk.scale.biz.dto.order.PlaceOrderDTO;
import com.wftk.scale.biz.entity.Department;
import com.wftk.scale.biz.entity.HttpJob;
import com.wftk.scale.biz.entity.Order;
import com.wftk.scale.biz.entity.OrderPayment;
import com.wftk.scale.biz.entity.OrderRefund;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.entity.Terminal;
import com.wftk.scale.biz.manager.job.executor.RequestExecutor;
import com.wftk.scale.biz.mapper.OrderMapper;
import com.wftk.scale.biz.service.DepartmentService;
import com.wftk.scale.biz.service.HttpJobService;
import com.wftk.scale.biz.service.OrderPaymentService;
import com.wftk.scale.biz.service.OrderRefundService;
import com.wftk.scale.biz.service.OrderService;
import com.wftk.scale.biz.service.ScaleCombinationDetailService;
import com.wftk.scale.biz.service.ScaleUserResultService;
import com.wftk.scale.biz.service.TerminalService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
@Slf4j
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {


    @Autowired
    OrderMapper orderMapper;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    ScaleCombinationDetailService scaleCombinationDetailService;

    @Autowired
    ScaleUserResultService scaleUserResultService;

    @Autowired
    OrderConverter orderConverter;
    @Resource
    private DepartmentService departmentService;
    @Resource
    private TerminalService terminalService;
    @Resource
    private OrderRefundService orderRefundService;

    @Autowired
    HttpJobService httpJobService;

    @Autowired
    private RequestExecutor requestExecutor;

    @Autowired
    NotifyConvert notifyConvert;


    @Transactional
    @Override
    public OrderPayment createScaleOrderAndPayOrder(BuyOrderDTO buyOrderDTO) {
        // 创建订单
        Order saveOrder = orderConverter.buyOrderToEntity(buyOrderDTO);
        saveOrder.setOrderNo(IdUtil.getSnowflakeNextIdStr());
        saveOrder.setStatus(OrderEnum.WAIT_PAY.getStatus());
        save(saveOrder);
        // 创建支付单
        OrderPayment orderPayment = new OrderPayment();
        orderPayment.setOrderNo(saveOrder.getOrderNo());
        orderPayment.setTradeAmount(saveOrder.getAmount());
        orderPayment.setPayChannel(saveOrder.getPayChannel());
        orderPayment.setTradeNo(IdUtil.getSnowflakeNextIdStr());
        orderPayment.setStatus(PaymentEnum.WAIT.getStatus());
        orderPayment.setPaymentTime(LocalDateTime.now());
        orderPaymentService.save(orderPayment);
        return orderPayment;
    }

    @Transactional
    @Override
    public OrderPayDTO placeScaleOrder(PlaceOrderDTO placeOrder) {
        OrderPayDTO orderPayDTO = new OrderPayDTO();
        // step1 校验订单
        Order order = orderMapper.getByTerminalSerialNo(placeOrder.getTerminalCode(), placeOrder.getTerminalSerialNo());
        if(order != null){
            orderPayDTO.setOrder(order);
            return orderPayDTO;
        }

        // step2 创建订单
        Order saveOrder = orderConverter.placeOrderToEntity(placeOrder);
        saveOrder.setOrderNo(IdUtil.getSnowflakeNextIdStr());
        saveOrder.setStatus(OrderEnum.WAIT_PAY.getStatus());
        save(saveOrder);

        // step3 创建支付单
        // 创建支付单
        OrderPayment orderPayment = new OrderPayment();
        orderPayment.setOrderNo(saveOrder.getOrderNo());
        orderPayment.setTradeAmount(saveOrder.getAmount());
        orderPayment.setPayChannel(saveOrder.getPayChannel());
        orderPayment.setTradeNo(IdUtil.getSnowflakeNextIdStr());
        orderPayment.setStatus(PaymentEnum.WAIT.getStatus());
        orderPayment.setPaymentTime(LocalDateTime.now());
        orderPaymentService.save(orderPayment);

        orderPayDTO.setOrder(saveOrder);
        orderPayDTO.setOrderPayment(orderPayment);
        return orderPayDTO;
    }

    @Override
    public Order getByTerminalSerialNoAndOrderNo(String terminalCode, String terminalSerialNo, String orderNo) {
        return orderMapper.getByTerminalSerialNoAndOrderNo(terminalCode,terminalSerialNo,orderNo);
    }

    @Override
    public Order getByOrderNo(String orderNo) {
        return orderMapper.getByOrderNo(orderNo);
    }

    @Override
    public Page<OrderQueryDTO> getList(AdminOrderQueryDto queryDto) {
        Page<OrderQueryDTO> page = Page.doSelectPage(() -> orderMapper.getList(queryDto));
        //避免join，单表查询终端及机构名称
        setDepartmentNameAndTerminalName(page.getList());
        return page;
    }

    @Override
    public List<Order> getExtOrderList(String terminalCode, List<String> terminalSerialNos) {
        return orderMapper.getExtOrderList(terminalCode,terminalSerialNos);
    }

    @Override
    public Integer updateStatus(String orderNo, Integer status, Integer oldStatus) {
        log.info("order status change. status: {} -> {}. orderNo: {}",oldStatus,status,orderNo);
        return orderMapper.updateStatus(orderNo,status,oldStatus);
    }

    @Override
    public boolean validOrderCompleted(String orderNo) {
        if(StrUtil.isBlank(orderNo)){
            return false;
        }

        Order order = getByOrderNo(orderNo);
        if(Objects.equals(order.getType(), ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode())){
            return true;
        }else if(Objects.equals(order.getType(), ScaleEnum.COMBINATION_SCALE_LISTING_TYPE.getCode())) {
            // 查询量表详情是否都有测评记录
            List<Long> scaleId = scaleCombinationDetailService.findScaleIdByCombinationId(order.getTargetId());
            List<ScaleUserResult> scaleUserResults = scaleUserResultService.selectByOrderNo(orderNo);
            // 去除重复量表测评记录
            Set<Long> seenScaleIds = new HashSet<>();
            List<ScaleUserResult> deduplicatedList = scaleUserResults.stream()
                    .filter(scaleUserResult -> seenScaleIds.add(scaleUserResult.getScaleId()))
                    .toList();
            return scaleId.size() == deduplicatedList.size();
        }else {
            log.error("order type invlid. orderNo: {}",orderNo);
        }
        return false;
    }

    @Override
    public String getOrderNoByListingIdAndUserId(Long listingId, Long userId) {
        //获取最新的一条订单
        Order order = orderMapper.selectOne(new LambdaQueryWrapper<Order>()
                .eq(Order::getScaleListingId, listingId)
                .eq(Order::getUserId, userId)
                .orderByDesc(Order::getCreateTime)
                .last("limit 1")
        );
        return order == null ? null : order.getOrderNo();
    }

    @Override
    public OrderQueryDTO detailById(AdminOrderQueryDto queryDto) {
        List<OrderQueryDTO> list = orderMapper.getList(queryDto);
        setDepartmentNameAndTerminalName(list);
        return CollUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrder(Long id) {
        //删除order、order_payment、order_refund的关联数据
        Order order = baseMapper.selectById(id);
        if(order == null){
            throw new BusinessException("订单信息不存在");
        }
        String orderNo = order.getOrderNo();
        baseMapper.deleteById(id);
        orderPaymentService.remove(new LambdaQueryWrapper<OrderPayment>().eq(OrderPayment::getOrderNo, orderNo));
        orderRefundService.remove(new LambdaQueryWrapper<OrderRefund>().eq(OrderRefund::getOrderNo, orderNo));
    }


    @Override
    public Order notifyComplete(String orderNo) {
        Order order = getByOrderNo(orderNo);
        if(order == null){
            throw new IllegalArgumentException("illgeal orderNo: " + orderNo);
        }
        try {
            // 判断订单是否已完成测评
            boolean validOrderCompleted = validOrderCompleted(orderNo);
            if (validOrderCompleted) {
                updateStatus(order.getOrderNo(), OrderEnum.COMPLETED.getStatus(), OrderEnum.WAIT_COMPLETE.getStatus());
                // 设置完成时间
                Order updateOrder = new Order();
                updateOrder.setId(order.getId());
                updateOrder.setCompleteTime(LocalDateTime.now());
                updateById(updateOrder);
                if (StrUtil.isNotBlank(order.getTerminalSerialNo())) {
                    order.setStatus(OrderEnum.COMPLETED.getStatus());
                    OrderStautsNotifyInput orderStautsNotifyInput = notifyConvert.orderToOrderStautsNotifyInput(order);
                    String bizId = orderStautsNotifyInput.getOrderNo();
                    HttpJob httpJob = httpJobService.createJob(bizId,
                            TerminalConfConstant.ORDER_STATUS_NOTIFY,
                            HttpJobStatusEnum.WAIT_SCHEDULE.getStatus(),
                            JSONObject.getInstance().toJSONString(orderStautsNotifyInput));
                    requestExecutor.execute(httpJob);
                }
            }
        } catch (Exception e) {
            log.error("推送订单状态异常.", e);
        }
        return order;
    }



    private void setDepartmentNameAndTerminalName(List<OrderQueryDTO> list){
        if(CollUtil.isNotEmpty(list)){
            Set<Long> orgIdSet = list.stream().map(OrderQueryDTO::getDepartmentId).filter(Objects::nonNull).collect(Collectors.toSet());
            Set<String> terminalCodeSet = list.stream().map(OrderQueryDTO::getTerminalCode).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Long, String> orgIdToNameMap = new HashMap<>(4);
            if(CollUtil.isNotEmpty(orgIdSet)){
                orgIdToNameMap = departmentService.listByIds(orgIdSet).stream().collect(Collectors.toMap(Department::getId, Department::getName));
            }
            Map<String, String> terminalCodeToNameMap = new HashMap<>(4);
            if(CollUtil.isNotEmpty(terminalCodeSet)){
                terminalCodeToNameMap = terminalService.list(
                                new LambdaQueryWrapper<Terminal>().in(Terminal::getCode, terminalCodeSet))
                        .stream().collect(Collectors.toMap(Terminal::getCode, Terminal::getName));
            }
            for (OrderQueryDTO dto : list){
                dto.setDepartmentName(orgIdToNameMap.get(dto.getDepartmentId()));
                dto.setTerminalName(terminalCodeToNameMap.get(dto.getTerminalCode()));
            }
        }
    }
}
