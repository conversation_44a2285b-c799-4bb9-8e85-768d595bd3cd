package com.wftk.scale.biz.converter;

import com.wftk.scale.biz.dto.user.sysuser.SysUserCreateDTO;
import com.wftk.scale.biz.dto.user.sysuser.SysUserUpdateDTO;
import com.wftk.scale.biz.entity.SysUser;
import com.wftk.scale.biz.excel.model.SysUserExcelDataDTO;
import com.wftk.scale.biz.vo.SysUserVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface SysUserConverter {

    SysUser sysUserCreateDtoToSysUser(SysUserCreateDTO dto);

    SysUser sysUserUpdateDtoToSysUser(SysUserUpdateDTO dto);

    List<SysUserExcelDataDTO> sysUserVOListToSysUserExcelDataDTOList(List<SysUserVO> list);
}
