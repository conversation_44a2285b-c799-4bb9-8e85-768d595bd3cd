package com.wftk.scale.biz.ext.notice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum NoticeTypeEnum {

    /**
     * 短信
     */
    SMS(1),

    /**
     * 邮件
     */
    EMAIL(2);

    private final Integer code;

    public static NoticeTypeEnum getNoticeTypeEnumByCode(Integer code) {
        return Arrays.stream(NoticeTypeEnum.values())
                .filter(noticeTypeEnum -> noticeTypeEnum.getCode().equals(code)).
                findFirst()
                .orElse(null);
    }

    public static NoticeTypeEnum getByName(String name) {
        return Arrays.stream(NoticeTypeEnum.values())
                .filter(noticeTypeEnum -> noticeTypeEnum.name().equals(name)).
                findFirst()
                .orElse(null);
    }
}
