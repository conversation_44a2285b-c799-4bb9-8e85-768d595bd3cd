package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.ScaleWarn;
import com.wftk.scale.biz.vo.ScaleWarnVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ScaleWarnMapper extends BaseMapper<ScaleWarn> {

    List<ScaleWarnVO> queryList(@Param("account") String account, @Param("userName") String userName,
                                @Param("departmentId") Long departmentId, @Param("phone") String phone,
                                @Param("scaleName") String scaleName, @Param("terminalCode") String terminalCode);
}
