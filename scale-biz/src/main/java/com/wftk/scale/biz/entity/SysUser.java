package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统管理员
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22 13:38:30
 */
@TableName("sys_user")
@Data
public class SysUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 系统用户ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 账号
     */
    private String account;
    /**
     * 加密密码串
     */
    private String password;
    /**
     * 名称
     */
    private String name;
    /**
     * 性别 1男 2女
     */
    private Integer sex;
    /**
     * 手机号码
     */
    @TableField(fill = FieldFill.UPDATE)
    private String tel;
    /**
     * 出生日期
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDate birthday;
    /**
     * 邮箱
     */
    @TableField(fill = FieldFill.UPDATE)
    private String email;
    /**
     * 入学年份
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDate entryDate;
    /**
     * 身份证号
     */
    @TableField(fill = FieldFill.UPDATE)
    private String idCard;
    /**
     * 备注
     */
    @TableField(fill = FieldFill.UPDATE)
    private String remark;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     * 0.未启用; 1.启用;
     */
    private Boolean enable;
    /**
     * 逻辑删除0-不删除1-删除
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
}
