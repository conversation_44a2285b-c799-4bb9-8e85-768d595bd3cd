package com.wftk.scale.biz.dto.listing;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ScaleListedQueryDTO
 * @Description: 量表可上架信息返回列表传输实体
 * @Author: mq
 * @Date: 2024-10-29 16:05
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleListingQueryDTO implements Serializable {

    /**
     * 封面图url
     */
    @RFile(role = FileConstant.FILE_SCALE_PUB_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    private String cover;

    /**
     * 可能是量表ID，也可能是组合量表ID
     */
    private Long targetId;

    /**
     * 可能是量表名称，也可能是组合量表名称
     */
    private String targetName;

    /**
     * 量表类型
     */
    private Long targetType;

    /**
     * 测评方式 1依次测评 2选择测评
     */
    private Integer evaluationType;

    /**
     * 量表编号
     */
    private String targetCode;

    /**
     * 量表类型名称
     */
    private String targetTypeName;

    /**
     * 上架终端数量
     */
    private Integer numOfListed;

    /**
     * 详情
     */
    private List<ScaleQueryDTO> details;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String updateBy;
}
