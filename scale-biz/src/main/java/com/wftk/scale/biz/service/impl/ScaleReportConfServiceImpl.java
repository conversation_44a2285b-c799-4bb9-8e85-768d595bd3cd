package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.event.ScaleCreateEvent;
import com.wftk.scale.biz.event.ScaleDelEvent;
import com.wftk.scale.biz.entity.ScaleReportConf;
import com.wftk.scale.biz.mapper.ScaleReportConfMapper;
import com.wftk.scale.biz.service.ScaleReportConfItemService;
import com.wftk.scale.biz.service.ScaleReportConfService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 报告设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class ScaleReportConfServiceImpl extends ServiceImpl<ScaleReportConfMapper, ScaleReportConf> implements ScaleReportConfService {

    @Autowired
    private ScaleReportConfMapper scaleReportConfMapper;
    @Resource
    private ScaleReportConfItemService scaleReportConfItemService;


    @EventListener
    public void onoApplication(ScaleCreateEvent event) {
        Long oldScaleId = event.getOldScaleId();
        Long newScaleId = event.getNewScaleId();
        ScaleReportConf reportConf = scaleReportConfMapper.findReportConfByScaleId(oldScaleId);
        if (ObjectUtil.isNotNull(reportConf)) {
            Long oldReportConfId = reportConf.getId();
            reportConf.setId(null);
            reportConf.setScaleId(newScaleId);
            this.save(reportConf);
            scaleReportConfItemService.saveCopyReportItem(oldReportConfId, reportConf.getId(), newScaleId, event.getOpUser());
        }
    }

    @EventListener
    public void onoApplication(ScaleDelEvent event) {
        scaleReportConfMapper.delete(new LambdaQueryWrapper<ScaleReportConf>().eq(ScaleReportConf::getScaleId, event.getOldScaleId()));
    }

    @Override
    public Long create(ScaleReportConf reportConf) {
        scaleReportConfMapper.insert(reportConf);
        return reportConf.getId();
    }

    @Override
    public void modify(ScaleReportConf reportConf) {
        Long reportConfId = reportConf.getId();
        ScaleReportConf rawData = scaleReportConfMapper.selectById(reportConfId);
        BeanUtils.copyProperties(reportConf, rawData);
        scaleReportConfMapper.updateById(rawData);
    }

    @Override
    public ScaleReportConf detail(Long scaleId) {
        return scaleReportConfMapper.findReportConfByScaleId(scaleId);
    }

    @Override
    public boolean validReportConfDataIntegrity(Long scaleId) {
        return scaleReportConfMapper.validReportConfDataIntegrity(scaleId);
    }
}
