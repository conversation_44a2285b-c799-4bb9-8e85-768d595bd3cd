package com.wftk.scale.biz.excel.model;


import com.alibaba.excel.annotation.ExcelProperty;
import com.wftk.scale.biz.excel.converter.PropertyConverter;
import com.wftk.scale.biz.excel.converter.QuestionScoringTypeConverter;
import com.wftk.scale.biz.excel.converter.QuestionTypeConverter;
import com.wftk.scale.biz.excel.converter.WhetherConverter;
import com.wftk.scale.biz.excel.model.base.BaseExcelDataDTO;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @ClassName: ScaleQuestionExcelDataDTO
 * @Description: 量表题目数据导入导出数据模型
 * @Author: mq
 * @Date: 2024/11/22
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ScaleQuestionExcelDataDTO extends BaseExcelDataDTO {

    @ExcelProperty("题号")
    @NotNull(message = "题号不能为空")
    private String questionNumber;

    @ExcelProperty("子题号")
    private String subNumber;

    @ExcelProperty(value = "必填", converter = WhetherConverter.class)
    private Boolean requireAnswer;

    @ExcelProperty("排序")
    private String sort;

    @ExcelProperty("问题")
    private String question;

    @ExcelProperty(value = "题型", converter = QuestionTypeConverter.class)
    private Integer type;

    @ExcelProperty(value = "计分类型", converter = QuestionScoringTypeConverter.class)
    private Integer scoringType;

    @ExcelProperty("答案1")
    private String answer1;

    @ExcelProperty("答案2")
    private String answer2;

    @ExcelProperty("答案3")
    private String answer3;

    @ExcelProperty("答案4")
    private String answer4;

    @ExcelProperty("答案5")
    private String answer5;

    @ExcelProperty("答案6")
    private String answer6;

    @ExcelProperty("答案7")
    private String answer7;

    @ExcelProperty("答案8")
    private String answer8;

    @ExcelProperty("答案9")
    private String answer9;

    @ExcelProperty("答案10")
    private String answer10;

    @ExcelProperty("答案1得分")
    private String answerScore1;

    @ExcelProperty("答案2得分")
    private String answerScore2;

    @ExcelProperty("答案3得分")
    private String answerScore3;

    @ExcelProperty("答案4得分")
    private String answerScore4;

    @ExcelProperty("答案5得分")
    private String answerScore5;

    @ExcelProperty("答案6得分")
    private String answerScore6;

    @ExcelProperty("答案7得分")
    private String answerScore7;

    @ExcelProperty("答案8得分")
    private String answerScore8;

    @ExcelProperty("答案9得分")
    private String answerScore9;

    @ExcelProperty("答案10得分")
    private String answerScore10;

    @ExcelProperty(value = "性质1", converter = PropertyConverter.class)
    private Boolean property1;

    @ExcelProperty(value = "性质2", converter = PropertyConverter.class)
    private Boolean property2;

    @ExcelProperty(value = "性质3", converter = PropertyConverter.class)
    private Boolean property3;

    @ExcelProperty(value = "性质4", converter = PropertyConverter.class)
    private Boolean property4;

    @ExcelProperty(value = "性质5", converter = PropertyConverter.class)
    private Boolean property5;

    @ExcelProperty(value = "性质6", converter = PropertyConverter.class)
    private Boolean property6;

    @ExcelProperty(value = "性质7", converter = PropertyConverter.class)
    private Boolean property7;

    @ExcelProperty(value = "性质8", converter = PropertyConverter.class)
    private Boolean property8;

    @ExcelProperty(value = "性质9", converter = PropertyConverter.class)
    private Boolean property9;

    @ExcelProperty(value = "性质10", converter = PropertyConverter.class)
    private Boolean property10;

    @ExcelProperty("答案1转换值")
    private String operateValue1;

    @ExcelProperty("答案2转换值")
    private String operateValue2;

    @ExcelProperty("答案3转换值")
    private String operateValue3;

    @ExcelProperty("答案4转换值")
    private String operateValue4;

    @ExcelProperty("答案5转换值")
    private String operateValue5;

    @ExcelProperty("答案6转换值")
    private String operateValue6;

    @ExcelProperty("答案7转换值")
    private String operateValue7;

    @ExcelProperty("答案8转换值")
    private String operateValue8;

    @ExcelProperty("答案9转换值")
    private String operateValue9;

    @ExcelProperty("答案10转换值")
    private String operateValue10;
}
