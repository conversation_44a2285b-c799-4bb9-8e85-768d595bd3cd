package com.wftk.scale.biz.ext.evaluator.function.scale.factor;

import java.util.Map;

import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.ext.evaluator.constant.EnvConstant;
import com.wftk.scale.biz.ext.evaluator.function.scale.BaseScaleEvaluationFunction;
import com.wftk.scale.biz.service.ScaleFactorService;

import cn.hutool.core.util.StrUtil;

/**
 * 量表因子相关函数
 * <AUTHOR>
 * @date 2025-09-05
 */
public abstract class BaseScaleFactorFunction extends BaseScaleEvaluationFunction {

    /**
     * 根据因子ID获取因子
     * @param env
     * @param factorId
     * @return
     */
    protected ScaleFactor getFactorById(Map<String, Object> env, Long factorId) {
        ScaleFactorService scaleFactorService = getFromEnv(env, EnvConstant.SCALE_FACTOR_SERVICE, ScaleFactorService.class, false);
        ScaleFactor scaleFactor = scaleFactorService.getById(factorId);
        if (scaleFactor == null) {
            throw new IllegalArgumentException("factor not found, id: " + factorId);
        }
        return scaleFactor;
    }

    /**
     * 获取因子问题ID
     * @param scaleFactor
     * @return
     */
    protected String getQuestionIds(ScaleFactor scaleFactor) {
        //数据库中使用的是"|"分隔的字符串，需要转换成逗号分隔的字符串
        String questionIds = scaleFactor.getQuestionId();
        if (StrUtil.isBlank(questionIds)) {
            logger.warn("questionIds is blank, factorId: {}, scaleId: {}", scaleFactor.getId(), scaleFactor.getScaleId());
            return null;
        }
        questionIds = questionIds.replace("|", ",");
        return questionIds;
    }

}
