package com.wftk.scale.biz.manager.report.actuator;

import com.wftk.scale.biz.constant.ScaleReportConfSettingConstant;
import com.wftk.scale.biz.entity.ScaleReportConfSetting;
import com.wftk.scale.biz.service.ScaleReportConfSettingService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/9/10 20:39
 */
@Slf4j
@Component
public class RemarkActuator {

    private final Integer REMARK = ScaleReportConfSettingConstant.Type.REMARK;

    @Resource
    ScaleReportConfSettingService scaleReportConfSettingService;

    public String doRemark(Long reportConfId,Integer reportType) {
        List<ScaleReportConfSetting> settingList = scaleReportConfSettingService.selectScaleReportConfSettingByReportConfId(reportConfId, REMARK, reportType);
        if(settingList.isEmpty()){
            log.warn("scaleReportConfSetting is null. reportConfId: {} type: {}",reportConfId,REMARK);
            return null;
        }
        if (settingList.size() > 1) {
            log.warn("scaleReportConfSetting data error. reportConfId: {} type: {} data: {}", reportConfId, REMARK, settingList);
            return null;
        }

        return settingList.get(0).getValue();
    }

}
