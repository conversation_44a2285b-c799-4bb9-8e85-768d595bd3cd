package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.ScaleWarnConfTypeEnum;
import com.wftk.scale.biz.constant.enums.ScaleWarnSettingScopeEnum;
import com.wftk.scale.biz.constant.enums.ScaleWarnStatusEnum;
import com.wftk.scale.biz.converter.ScaleWarnConfDtoConverter;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfCreateDTO;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfDTO;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfModifyDTO;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfQueryDTO;
import com.wftk.scale.biz.entity.*;
import com.wftk.scale.biz.event.ScaleCreateRelationEvent;
import com.wftk.scale.biz.event.ScaleDelEvent;
import com.wftk.scale.biz.event.ScaleEvaluationIntroEvent;
import com.wftk.scale.biz.ext.notice.NoticeResult;
import com.wftk.scale.biz.mapper.*;
import com.wftk.scale.biz.service.*;
import com.wftk.scale.biz.thread.BizThreadPoolTaskExecutor;
import com.wftk.scale.biz.util.math.Interval;
import com.wftk.scale.biz.util.math.IntervalUtil;
import com.wftk.scale.biz.vo.UserVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警阈值表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Slf4j
@Service
public class ScaleWarnConfServiceImpl extends ServiceImpl<ScaleWarnConfMapper, ScaleWarnConf> implements ScaleWarnConfService {

    @Resource
    private ScaleWarnConfMapper scaleWarnConfMapper;
    @Resource
    private ScaleService scaleService;
    @Resource
    private ScaleFactorService scaleFactorService;
    @Resource
    private ScaleUserFactorResultService scaleUserFactorResultService;
    @Resource
    private UserService userService;
    @Resource
    private ScaleUserResultService scaleUserResultService;
    @Resource
    private ScaleListingUserConfService scaleListingUserConfService;
    @Resource
    private ScaleListingService scaleListingService;
    @Resource
    private ScaleWarnDetailService scaleWarnDetailService;
    @Resource
    private TerminalService terminalService;
    @Resource
    private ScaleWarnConfDtoConverter scaleWarnConfConverter;

    @Autowired
    private ScaleWarnSettingMapper scaleWarnSettingMapper;

    @Autowired
    private ScaleWarnScopeMapper scaleWarnScopeMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private ScaleWarnUserMapper scaleWarnUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private NoticeMessageService noticeMessageService;

    @Autowired
    private BizThreadPoolTaskExecutor bizThreadPoolTaskExecutor;

    @Override
    public boolean validWarnConfFactor(Long id, Long scaleId, Long factorId) {
        return scaleWarnConfMapper.validWarnConFactorExists(id, scaleId, factorId);
    }

    @Override
    public String checkFactorRelationWarnConf(Long factorId) {
        return scaleWarnConfMapper.checkFactorRelationWarnConf(factorId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ScaleWarnConfCreateDTO scaleWarnConfCreateDTO) {
        // 参数校验
        validParams(scaleWarnConfCreateDTO.getType(), scaleWarnConfCreateDTO.getThreshold(),
                scaleWarnConfCreateDTO.getStart(), scaleWarnConfCreateDTO.getEnd());
        // 批次号
        String batchNo = IdUtil.getSnowflakeNextIdStr();

        String interval = generateInterval(scaleWarnConfCreateDTO.getType(), scaleWarnConfCreateDTO.getThreshold(),
                scaleWarnConfCreateDTO.getStart(), scaleWarnConfCreateDTO.getEnd());

        ScaleWarnConfDTO scaleWarnConfDTO = scaleWarnConfConverter.scaleWarnConfCreateToDto(scaleWarnConfCreateDTO);
        scaleWarnConfDTO.setInterval(interval);

        // 校验是否有重叠,逻辑：因子、逻辑、标识一致，创建失败
        // 两种情况 1.预警逻辑与阈值一样 2.预警逻辑与标识一样
        checkWarnValOverlap(scaleWarnConfDTO);

        // 区间生成两条数据 同一批次
        List<ScaleWarnConf> warnConfs = new ArrayList<>();
        if (Objects.equals(scaleWarnConfCreateDTO.getType(), ScaleWarnConfTypeEnum.INTERVAL.getType())) {
            ScaleWarnConf scaleWarnConf1 = scaleWarnConfConverter.scaleWarnConfCreateToEntity(scaleWarnConfCreateDTO);
            scaleWarnConf1.setType(ScaleWarnConfTypeEnum.GREATER.getType());
            scaleWarnConf1.setThreshold(scaleWarnConfCreateDTO.getStart() - 1);

            ScaleWarnConf scaleWarnConf2 = scaleWarnConfConverter.scaleWarnConfCreateToEntity(scaleWarnConfCreateDTO);
            scaleWarnConf2.setType(ScaleWarnConfTypeEnum.LESS.getType());
            scaleWarnConf2.setThreshold(scaleWarnConfCreateDTO.getEnd() + 1);
            warnConfs.add(scaleWarnConf1);
            warnConfs.add(scaleWarnConf2);
        } else {
            ScaleWarnConf scaleWarnConf1 = scaleWarnConfConverter.scaleWarnConfCreateToEntity(scaleWarnConfCreateDTO);
            warnConfs.add(scaleWarnConf1);
        }
        for (ScaleWarnConf scaleWarnConf : warnConfs) {
            scaleWarnConf.setBatchNo(batchNo);
            scaleWarnConfMapper.insert(scaleWarnConf);
        }
    }

    /**
     * 校验判断表示，区间是否有重叠
     */
    private void checkWarnValOverlap(ScaleWarnConfDTO warnConf) {
        String errorMsg = "存在重叠的预警阈值配置";

        // 校验这个因子下的标签是否有重叠
        boolean exist = scaleWarnConfMapper.validWarnConfFactorTagExists(warnConf.getScaleId(), warnConf.getFactorId(), warnConf.getTagId(), warnConf.getBatchNo());
        if (exist) {
            throw new BusinessException(errorMsg);
        }

        // 校验这个因子下的预警值范围是否有重叠
        // 查询数据
        List<ScaleWarnConfQueryDTO> factorList = scaleWarnConfMapper.getFactorList(warnConf.getScaleId(), warnConf.getFactorId());
        factorList = factorList.stream().filter(scaleWarnConfQueryDTO -> !scaleWarnConfQueryDTO.getBatchNo().equals(warnConf.getBatchNo())).collect(Collectors.toList());
        // 合并两个值的区间
        List<ScaleWarnConfDTO> scaleWarnConfDTOList = getScaleWarnConfDTOList(factorList);
        List<Interval> scaleWarnConfIntervalList = getScaleWarnConfIntervalList(scaleWarnConfDTOList);
        Interval newInterval = convertInterval(warnConf.getInterval());
        boolean hasOverlap = IntervalUtil.hasOverlap(scaleWarnConfIntervalList, newInterval);
        if (hasOverlap) {
            throw new BusinessException(errorMsg);
        }

    }


    /**
     * 校验判断表示，是否有重叠
     */
    private void checkWarnValOverlap1(Long scaleId, Long factorId, Integer type, Integer threshold, Long tagId, Long id) {
        boolean exist = scaleWarnConfMapper.exists(
                new LambdaQueryWrapper<ScaleWarnConf>()
                        .eq(ScaleWarnConf::getScaleId, scaleId)
                        .eq(ScaleWarnConf::getFactorId, factorId)
                        .eq(ScaleWarnConf::getType, type)
                        .eq(ScaleWarnConf::getThreshold, threshold));
        String errorMsg = "存在重叠的预警阈值配置";
        if (exist) {
            throw new BusinessException(errorMsg);
        }
        exist = scaleWarnConfMapper.exists(
                new LambdaQueryWrapper<ScaleWarnConf>()
                        .eq(ScaleWarnConf::getScaleId, scaleId)
                        .eq(ScaleWarnConf::getFactorId, factorId)
                        .eq(ScaleWarnConf::getType, type)
                        .eq(ScaleWarnConf::getTagId, tagId));
        if (exist) {
            throw new BusinessException(errorMsg);
        }
        //排序 大于 等于 小于 值从小到大
        List<ScaleWarnConf> list = baseMapper.selectList(new LambdaQueryWrapper<ScaleWarnConf>()
                .eq(ScaleWarnConf::getScaleId, scaleId)
                .eq(ScaleWarnConf::getFactorId, factorId)
                .orderByAsc(ScaleWarnConf::getType)
                .orderByAsc(ScaleWarnConf::getThreshold)
        );
        //不同的判断分组 1大于 2等于 3小于
        Map<Integer, List<Integer>> map = new HashMap<>(8);
        list.forEach(conf -> {
            //移除编辑时当前的本身判断
            if (id != null && id.equals(conf.getId())) {
                return;
            }
            Integer confType = conf.getType();
            List<Integer> valList = map.computeIfAbsent(confType, k -> new ArrayList<>());
            valList.add(conf.getThreshold());
        });

        List<Integer> typeList = map.computeIfAbsent(type, k -> new ArrayList<>(8));
        typeList.add(threshold);
        //从小到大排序
        typeList.sort(Integer::compareTo);

        //校验下范围是否有重叠 小于的最大值 等于的最小值 等于的最大值 大于的最小值
        Integer lessMax = getVal(map.get(3), 3);
        Integer equalMin = getVal(map.get(2), 2);
        Integer equalMax = getVal(map.get(2), 3);
        Integer greaterMin = getVal(map.get(1), 1);

        if (lessMax != null) {
            if (equalMin != null && equalMin < lessMax) {
                throw new BusinessException(errorMsg);
            }
            if (equalMax != null && equalMax < lessMax) {
                throw new BusinessException(errorMsg);
            }
            if (greaterMin != null && greaterMin < lessMax) {
                throw new BusinessException(errorMsg);
            }
        }
        if (equalMax != null) {
            if (greaterMin != null && greaterMin < equalMax) {
                throw new BusinessException(errorMsg);
            }
        }
    }

    private Integer getVal(List<Integer> list, Integer type) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        switch (type) {
            case 1, 2 -> {
                return list.get(0);
            }
            case 3 -> {
                return list.get(list.size() - 1);
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(ScaleWarnConfModifyDTO warnConfModifyDTO) {
        // 参数校验
        validParams(warnConfModifyDTO.getType(), warnConfModifyDTO.getThreshold(),
                warnConfModifyDTO.getStart(), warnConfModifyDTO.getEnd());

        String interval = generateInterval(warnConfModifyDTO.getType(), warnConfModifyDTO.getThreshold(),
                warnConfModifyDTO.getStart(), warnConfModifyDTO.getEnd());

        ScaleWarnConfDTO scaleWarnConfDTO = scaleWarnConfConverter.scaleWarnConfModifyToDto(warnConfModifyDTO);
        scaleWarnConfDTO.setInterval(interval);

        // 校验是否有重叠,逻辑：因子、逻辑、标识一致，创建失败
        // 两种情况 1.预警逻辑与阈值一样 2.预警逻辑与标识一样
        checkWarnValOverlap(scaleWarnConfDTO);

        // 先删除 再新增
        scaleWarnConfMapper.deleteByBatchNo(warnConfModifyDTO.getBatchNo());

        // 批次号
        String batchNo = IdUtil.getSnowflakeNextIdStr();
        // 区间生成两条数据 同一批次
        List<ScaleWarnConf> warnConfs = new ArrayList<>();
        if (Objects.equals(warnConfModifyDTO.getType(), ScaleWarnConfTypeEnum.INTERVAL.getType())) {
            ScaleWarnConf scaleWarnConf1 = scaleWarnConfConverter.scaleWarnConfModifyToEntity(warnConfModifyDTO);
            scaleWarnConf1.setType(ScaleWarnConfTypeEnum.GREATER.getType());
            scaleWarnConf1.setThreshold(warnConfModifyDTO.getStart() - 1);

            ScaleWarnConf scaleWarnConf2 = scaleWarnConfConverter.scaleWarnConfModifyToEntity(warnConfModifyDTO);
            scaleWarnConf2.setType(ScaleWarnConfTypeEnum.LESS.getType());
            scaleWarnConf2.setThreshold(warnConfModifyDTO.getEnd() + 1);
            warnConfs.add(scaleWarnConf1);
            warnConfs.add(scaleWarnConf2);
        } else {
            ScaleWarnConf scaleWarnConf1 = scaleWarnConfConverter.scaleWarnConfModifyToEntity(warnConfModifyDTO);
            warnConfs.add(scaleWarnConf1);
        }
        for (ScaleWarnConf scaleWarnConf : warnConfs) {
            scaleWarnConf.setBatchNo(batchNo);
            scaleWarnConfMapper.insert(scaleWarnConf);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String batchNo) {
        scaleWarnConfMapper.deleteByBatchNo(batchNo);
    }

    @Override
    public ScaleWarnConfDTO detail(String batchNo) {
        List<ScaleWarnConfQueryDTO> listByBatchNo = scaleWarnConfMapper.getListByBatchNo(batchNo);
        return getScaleWarnConfDTOList(listByBatchNo).get(0);
    }

    @Override
    public Page<ScaleWarnConfQueryDTO> selectPage(Long scaleId) {
        return Page.doSelectPage(() -> scaleWarnConfMapper.getList(scaleId));
    }

    public void sendWarnMsgByFactorIdAndScaleId(Long factorId, BigDecimal finalScore, UserVO userVO, ScaleUserResult scaleUserResult) {
        ScaleFactor scaleFactor = scaleFactorService.getById(factorId);
        if (scaleFactor == null) {
            log.error("量表因子查询为空,factorId:{}", factorId);
            return;
        }
        Scale scale = scaleService.getById(scaleFactor.getScaleId());
        if (scale == null) {
            log.error("量表查询为空,factorId:{}", factorId);
            return;
        }
        //订单号关联查询，是否是组合量表下
        Long scaleListingId = scaleUserResult.getScaleListingId();
        if (scaleListingId == null) {
            log.error("量表上下架查询为空");
            return;
        }
        ScaleListing scaleListing = scaleListingService.getById(scaleListingId);
        if (scaleListing == null) {
            log.error("量表上下架查询为空，,scaleListingId:{}", scaleListingId);
            return;
        }
        String terminalName;
        if (StrUtil.isNotBlank(scaleListing.getTerminalCode())) {
            Terminal terminal = terminalService.getByCode(scaleListing.getTerminalCode());
            terminalName = terminal == null ? null : terminal.getName();
        } else {
            terminalName = null;
        }

        List<ScaleWarnConfQueryDTO> factorList = scaleWarnConfMapper.getFactorList(scale.getId(), factorId);
        List<ScaleWarnConfDTO> scaleWarnConfDTOList = getScaleWarnConfDTOList(factorList);
        for (ScaleWarnConfDTO scaleWarnConfDTO : scaleWarnConfDTOList) {
            dealWith(scale, scaleFactor, finalScore, scaleWarnConfDTO,
                    userVO, scaleUserResult, scaleListing, terminalName);
        }

    }

    @Override
    public void saveBatchByFactorId(ScaleCreateRelationEvent event, Map<Long, Long> map) {
        List<ScaleWarnConf> list = scaleWarnConfMapper.selectList(new LambdaQueryWrapper<ScaleWarnConf>()
                .eq(ScaleWarnConf::getScaleId, event.getOldScaleId()));
        if (CollUtil.isEmpty(list)) {
            return;
        }
        list.forEach(conf -> {
            conf.setId(null);
            conf.setScaleId(event.getNewScaleId());
            conf.setFactorId(map.get(conf.getFactorId()));
        });
        scaleWarnConfMapper.insert(list);
    }

    @Override
    public List<ScaleWarnConfQueryDTO> getFactorList(Long scaleId, Long factorId) {
        return scaleWarnConfMapper.getFactorList(scaleId, factorId);
    }

    @Override
    public List<ScaleWarnConfDTO> getScaleWarnConfDTOList(List<ScaleWarnConfQueryDTO> scaleWarnConfQueryDTOS) {
        if (scaleWarnConfQueryDTOS.isEmpty()) {
            return new ArrayList<>();
        }
        List<ScaleWarnConfDTO> scaleWarnConfDTOS = new ArrayList<>();
        // 先根据因子分组
        Map<Long, List<ScaleWarnConfQueryDTO>> groupedByFactor = scaleWarnConfQueryDTOS.stream()
                .collect(Collectors.groupingBy(ScaleWarnConfQueryDTO::getFactorId));

        // 再根据批次号分组
        for (List<ScaleWarnConfQueryDTO> value : groupedByFactor.values()) {
            Map<String, List<ScaleWarnConfQueryDTO>> groupedByBatchNo = value.stream()
                    .collect(Collectors.groupingBy(ScaleWarnConfQueryDTO::getBatchNo));

            // 合并区间
            for (List<ScaleWarnConfQueryDTO> confQueryDTOS : groupedByBatchNo.values()) {
                if (confQueryDTOS.size() > 1) {
                    AtomicReference<Integer> start = new AtomicReference<>(0);
                    AtomicReference<Integer> end = new AtomicReference<>(0);
                    confQueryDTOS.forEach(scaleWarnConfQueryDTO -> {
                        if (Objects.equals(scaleWarnConfQueryDTO.getType(), ScaleWarnConfTypeEnum.GREATER.getType())) {
                            // 大于设置开始值
                            start.set(scaleWarnConfQueryDTO.getThreshold() + 1);
                        } else if (Objects.equals(scaleWarnConfQueryDTO.getType(), ScaleWarnConfTypeEnum.LESS.getType())) {
                            // 小于设置结束值
                            end.set(scaleWarnConfQueryDTO.getThreshold() - 1);
                        } else {
                            // 合并区间没有`等于`类型
                            throw new BusinessException("预警阈值数据查询异常");
                        }
                    });
                    ScaleWarnConfDTO scaleWarnConfDTO = scaleWarnConfConverter.scaleWarnConfQueryToDto(confQueryDTOS.get(0));
                    scaleWarnConfDTO.setInterval(start.get() + "," + end.get());
                    scaleWarnConfDTO.setType(ScaleWarnConfTypeEnum.INTERVAL.getType());
                    scaleWarnConfDTO.setThreshold(null);
                    scaleWarnConfDTOS.add(scaleWarnConfDTO);
                } else {
                    ScaleWarnConfDTO scaleWarnConfDTO = scaleWarnConfConverter.scaleWarnConfQueryToDto(confQueryDTOS.get(0));
                    scaleWarnConfDTO.setInterval(String.valueOf(confQueryDTOS.get(0).getThreshold()));
                    scaleWarnConfDTOS.add(scaleWarnConfDTO);
                }
            }
        }

        return scaleWarnConfDTOS;
    }

    private void validParams(Integer type, Integer threshold, Integer start, Integer end) {
        ScaleWarnConfTypeEnum scaleWarnConfTypeEnum = ScaleWarnConfTypeEnum.getScaleWarnConfTypeEnumByType(type);
        switch (Objects.requireNonNull(scaleWarnConfTypeEnum)) {
            case GREATER, LESS, EQUAL -> {
                if (threshold == null) {
                    throw new BusinessException("预警阈值不能为空");
                }
                if (threshold < 1) {
                    throw new BusinessException("预警阈值不能小于1");
                }
            }
            case INTERVAL -> {
                if (start == null) {
                    throw new BusinessException("预警区间起始值不能为空");
                }
                if (end == null) {
                    throw new BusinessException("预警区间结束值不能为空");
                }
                if (start > end) {
                    throw new BusinessException("预警区间起始值不能大于结束值");
                }
                if (start < 1) {
                    throw new BusinessException("预警区间起始值不能小于1");
                }
            }
            default -> {
            }
        }
    }

    private List<Interval> getScaleWarnConfIntervalList(List<ScaleWarnConfDTO> scaleWarnConfDTOS) {
        if (scaleWarnConfDTOS.isEmpty()) {
            return List.of();
        }
        List<Interval> list = new ArrayList<>();
        for (ScaleWarnConfDTO scaleWarnConfDTO : scaleWarnConfDTOS) {
            String intervalStr;
            if (Objects.equals(scaleWarnConfDTO.getType(), ScaleWarnConfTypeEnum.INTERVAL.getType())) {
                intervalStr = scaleWarnConfDTO.getInterval();
            } else {
                intervalStr = generateInterval(scaleWarnConfDTO.getType(), scaleWarnConfDTO.getThreshold(), null, null);
            }
            Interval interval = convertInterval(intervalStr);
            list.add(interval);
        }

        return list;
    }

    private Interval convertInterval(String intervalStr) {
        String[] intervalArray = ObjectUtil.defaultIfNull(intervalStr.split(","), new String[0]);
        if (intervalArray.length != 2) {
            throw new BusinessException("区间数值解析失败");
        }
        Interval interval = new Interval();
        interval.setStart(Integer.parseInt(intervalArray[0]));
        interval.setEnd(Integer.parseInt(intervalArray[1]));
        return interval;
    }

    private String generateInterval(Integer type, Integer threshold, Integer start, Integer end) {
        Integer startValue;
        Integer endValue;

        ScaleWarnConfTypeEnum scaleWarnConfTypeEnum = ScaleWarnConfTypeEnum.getScaleWarnConfTypeEnumByType(type);
        switch (Objects.requireNonNull(scaleWarnConfTypeEnum)) {
            case GREATER -> {
                startValue = threshold + 1;
                endValue = Integer.MAX_VALUE;
            }
            case EQUAL -> {
                startValue = threshold;
                endValue = threshold;
            }
            case LESS -> {
                startValue = 1;
                endValue = threshold - 1;
            }
            case INTERVAL -> {
                startValue = start;
                endValue = end;

            }
            default -> throw new BusinessException("未知的预警阈值类型");
        }
        return startValue + "," + endValue;
    }


    private void dealWith(Scale scale, ScaleFactor factor, BigDecimal finalScore, ScaleWarnConfDTO scaleWarnConf,
                          UserVO userVO, ScaleUserResult scaleUserResult, ScaleListing scaleListing, String terminalName) {

        // 判断是否配置了预警设置
        List<ScaleWarnSetting> scaleWarnSettingList = scaleWarnSettingMapper.getByScaleIdAndScaleWarnConfId(scaleWarnConf.getId(), scale.getId());
        if (CollUtil.isEmpty(scaleWarnSettingList)) {
            log.info("未配置预警设置。{}",scaleWarnConf);
            return;
        }

        if (!userVO.getEnable()) {
            log.info("用户被禁用。{}",userVO);
            return;
        }

        String scaleName = scale.getName();
        String warnMsgTemplate = "%s量表测评因子%s解析得分：%s，已%s, 结果为：%s";

        Integer type = scaleWarnConf.getType();
        //1大于 2等于 3小于
        String operateName = null;
        String tagName = null;
        String warnTypeStr = null;
        switch (type) {
            case 1 -> {
                if(finalScore.compareTo(new BigDecimal(scaleWarnConf.getThreshold())) > 0){
                    operateName = "大于"+scaleWarnConf.getInterval();
                    warnTypeStr = scaleWarnConf.getWarnType();
                    tagName = scaleWarnConf.getTag();
                }
            }
            case 2 -> {
                if(finalScore.compareTo(new BigDecimal(scaleWarnConf.getThreshold())) == 0) {
                    operateName = "等于"+scaleWarnConf.getInterval();
                    warnTypeStr = scaleWarnConf.getWarnType();
                    tagName = scaleWarnConf.getTag();
                }
            }
            case 3 -> {
                if(finalScore.compareTo(new BigDecimal(scaleWarnConf.getThreshold())) < 0) {
                    operateName = "小于"+scaleWarnConf.getInterval();
                    warnTypeStr = scaleWarnConf.getWarnType();
                    tagName = scaleWarnConf.getTag();
                }
            }
            case 4 -> {
                String interval = scaleWarnConf.getInterval();
                if (interval.startsWith("[") && interval.endsWith("]")) {
                    interval = interval.substring(1, interval.length() - 1);
                }
                String[] strScores = ObjectUtil.defaultIfNull(interval.split(","), new String[0]);
                BigDecimal[] valueArray = new BigDecimal[strScores.length];
                for (int i = 0; i < strScores.length; i++) {
                    valueArray[i] = new BigDecimal(strScores[i].trim());
                }
                List<BigDecimal> scores = Arrays.asList(valueArray);
                BigDecimal minLimit = scores.get(0);
                BigDecimal maxLimit = scores.get(scores.size() - 1);
                if (minLimit == null || maxLimit == null) {
                    return ;
                }
                if (finalScore.compareTo(minLimit) >= 0 && finalScore.compareTo(maxLimit) <= 0) {
                    operateName = "在["+scaleWarnConf.getInterval()+"]区间";
                    warnTypeStr = scaleWarnConf.getWarnType();
                    tagName = scaleWarnConf.getTag();
                }
            }
            default -> {
            }
        }
//        String factorName = factor.getName();
        final String finalTagName = tagName;
        if (StrUtil.isNotBlank(operateName) && StrUtil.isNotBlank(warnTypeStr)) {
            //补充短信通知所需要的参数
            List<Integer> warnTypeSpilt = Arrays.stream(warnTypeStr.split(",")).map(Integer::parseInt).toList();
//            LinkedHashMap<String, Object> map = new LinkedHashMap<>(4);
//            map.put(SmsKeyConstant.SCALE_NAME, scaleName);
//            map.put(SmsKeyConstant.FACTOR_NAME, factorName);
//            map.put(SmsKeyConstant.FINAL_SCORE, finalScore);
//            map.put(SmsKeyConstant.OPERATE_NAME, operateName);
//            map.put(SmsKeyConstant.TAG_NAME, tagName);
            for (ScaleWarnSetting scaleWarnSetting : scaleWarnSettingList) { // 预警设置
                List<ScaleWarnScope> scaleWarnScopes = scaleWarnScopeMapper.queryByScaleIdAndScaleWarnSettingId(scaleWarnSetting.getId(), scaleWarnSetting.getScaleId());
                if (CollUtil.isEmpty(scaleWarnScopes)) {
                    log.info("未配置预警范围。{}",scaleWarnSetting);
                    continue;
                }
                // 判断该人是否在被预警的范围内
                if (!validScaleWarnScopeUser(scaleWarnScopes, userVO) && (scaleWarnSetting.getEvaluateMyself() != null && !scaleWarnSetting.getEvaluateMyself())) {
                    log.info("用户不在预警范围内。{}",scaleWarnSetting);
                    continue;
                }

                Set<Long> scaleNotifyUserIds = getScaleNotifyUserId(scaleWarnSetting, scaleUserResult);
                if (CollUtil.isEmpty(scaleNotifyUserIds)) {
                    log.info("未配置预警用户。{}",scaleWarnSetting);
                    continue;
                }
                for (Long notifyUserId : scaleNotifyUserIds) {
                    for (Integer warnType : warnTypeSpilt) {
                        CompletableFuture.runAsync( () -> {
                            //保存预警信息
                            ScaleWarn scaleWarn = scaleWarnDetailService.saveByWarnNotice(scaleListing, userVO, scale, scaleUserResult, finalTagName, warnType, factor, terminalName, scaleWarnSetting,notifyUserId);
                            // 发送通知
                            NoticeResult noticeResult = noticeMessageService.sendScaleWarnNoticeByMessage(scaleWarn);
                            if (noticeResult.isOk()) {
                                scaleWarn.setStatus(ScaleWarnStatusEnum.VALID.getValue());
                                scaleWarnDetailService.updateById(scaleWarn);
                            }
                        },bizThreadPoolTaskExecutor.getThreadPoolTaskExecutor());
                    }
                }
            }
        }
    }

    /**
     * 判断用户是否在被告警的范围内
     **/
    boolean validScaleWarnScopeUser(List<ScaleWarnScope> scaleWarnScopeList, UserVO userVO) {
        if (CollUtil.isEmpty(scaleWarnScopeList)) {
            return false;
        }
        for (ScaleWarnScope scaleWarnScope : scaleWarnScopeList) {
            Integer scopeType = scaleWarnScope.getScopeType();
            ScaleWarnSettingScopeEnum scopeEnum = ScaleWarnSettingScopeEnum.getScaleWarnSettingScopeEnumByValue(scopeType);
            switch (scopeEnum) {
                // 范围为个人，则判断用户id是否一致
                case PERSON -> {
                    if (scaleWarnScope.getScopeId().equals(userVO.getId())) {
                        return true;
                    }
                }
                // 范围为部门，则判断用户部门id是否一致（包含下级部门）
                case DEPARTMENT -> {
                    List<Department> childDepartment = departmentMapper.getChildDepartmentById(scaleWarnScope.getScopeId());
                    if (CollUtil.isEmpty(childDepartment)) {
                        continue;
                    }
                    boolean matchDepartment = childDepartment.stream().anyMatch(department -> department.getId().equals(userVO.getDepartmentId()));
                    if (matchDepartment) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 获取通知用户
     **/
    Set<Long> getScaleNotifyUserId(ScaleWarnSetting scaleWarnSetting,ScaleUserResult scaleUserResult){
        Set<Long> notifyUserIdSet = CollUtil.newHashSet();
        if (scaleWarnSetting.getEvaluateMyself()) {
            notifyUserIdSet.add(scaleUserResult.getUserId());
        }
        Set<Long> notifyUserIds = scaleWarnUserMapper.getUserIdByScaleWarnSettingId(scaleWarnSetting.getId(), scaleWarnSetting.getScaleId());
        if (CollUtil.isNotEmpty(notifyUserIds)) {
            notifyUserIdSet.addAll(notifyUserIds);
        }

        // 过滤掉无效用户
        if (CollUtil.isNotEmpty(notifyUserIdSet)) {
            List<Long> enableUserIdList = userMapper.getEnableDeptIdsByUserIds(CollUtil.newArrayList(notifyUserIdSet));
            if (CollUtil.isNotEmpty(enableUserIdList)) {
                notifyUserIdSet = CollUtil.newHashSet(enableUserIdList);
            }
        }
        return notifyUserIdSet;
    }


    @EventListener(ScaleEvaluationIntroEvent.class)
    @Async
    public void onApplication(ScaleEvaluationIntroEvent event) {
        Long resultId = event.getResultId();
        Long userId = event.getUserId();
        log.info("send warn event, resultId:{}, userId:{}", resultId, userId);
        if (resultId == null || userId == null) {
            return;
        }
        List<ScaleUserFactorResult> list = scaleUserFactorResultService.list(
                new LambdaQueryWrapper<ScaleUserFactorResult>().eq(ScaleUserFactorResult::getResultId, resultId));
        if (CollUtil.isNotEmpty(list)) {
            //判断是否需要发送通知
            ScaleUserResult scaleUserResult = scaleUserResultService.getById(resultId);
            if (scaleUserResult == null || scaleUserResult.getScaleListingId() == null) {
                log.error("未查询到关联结果或上架信息,resultId:{}", resultId);
                return;
            }
            ScaleListingUserConf scaleListingUserConf = scaleListingUserConfService.findByListingUserIdOrOrderNo(scaleUserResult.getListingUserId(), scaleUserResult.getOrderNo());
            //页面分发的默认发送
            if (scaleListingUserConf != null && !scaleListingUserConf.getAllowNotify()) {
                log.debug("上架信息配置的不发送预警信息");
                return;
            }
            UserVO userVO = userService.selectByUserId(userId);
            list.forEach(factorResult -> sendWarnMsgByFactorIdAndScaleId(factorResult.getFactorId(), factorResult.getScore(), userVO, scaleUserResult));
        }
    }

    @EventListener
    public void onoApplication(ScaleDelEvent event) {
        scaleWarnConfMapper.delete(new LambdaQueryWrapper<ScaleWarnConf>().eq(ScaleWarnConf::getScaleId, event.getOldScaleId()));
    }
}