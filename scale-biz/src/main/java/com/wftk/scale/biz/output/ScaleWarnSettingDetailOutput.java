package com.wftk.scale.biz.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class ScaleWarnSettingDetailOutput implements Serializable {
    @Serial
    private static final long serialVersionUID = 7021016619953716336L;

    /**
     * 量表设置ID
     */
    @Schema(description = "量表设置ID")
    private Long id;

    /**
     * 量表ID
     */
    @Schema(description = "量表ID")
    private Long scaleId;

    @Schema(description = "量表编码")
    private String scaleCode;

    /**
     * 量表名称
     */
    @Schema(description = "量表名称")
    private String scaleName;

    /**
     * 预警等级
     */
    @Schema(description = "预警等级")
    private Integer level;

    /**
     * 是否预警自己
     */
    @Schema(description = "是否预警自己")
    private Boolean evaluateMyself;

    /**
     * 预警范围
     */
    @Schema(description = "预警范围")
    private Integer scope;

    /**
     * 预警因子
     */
    @Schema(description = "预警因子")
    private List<ScaleWarnConfOutput> scaleWarnConfOutputList;

    /**
     * 预警部门或人员
     */
    @Schema(description = "预警部门或人员")
    private List<ScaleWarnScopeOutput> scaleWarnScopeOutputList;

    /**
     * 接收预警用户
     */
    @Schema(description = "接收预警用户")
    private List<ReceiveScaleWarnUserOut> receiveScaleWarnUserOutList;

    @Data
    public static class ScaleWarnConfOutput{

        @Schema(description = "id")
        private Long id;


        /**
         * 预警因子ID
         */
        @Schema(description = "预警因子ID")
        private Long scaleFactorId;

        /**
         * 预警因子名称
         */
        @Schema(description = "预警因子名称")
        private String scaleFactorName;
    }



    @Data
    public static class ReceiveScaleWarnUserOut{

        /**
         * 接收预警用户ID
         */
        @Schema(description = "接收预警用户ID")
        private Long userId;

        /**
         * 接收预警用户名称
         */
        @Schema(description = "接收预警用户名称")
        private String userName;

        /**
         * 接收预警用户所属部门ID
         */
        private Long departmentId;

    }

    @Data
    public static class ScaleWarnScopeOutput{

        /**
         * 预警范围ID
         */
        @Schema(description = "预警范围ID")
        private Long scopeId;

        /**
         * 预警范围名称
         */
        @Schema(description = "预警范围名称")
        private String scopeName;

        /**
         * 预警范围类型
         */
        @Schema(description = "如果范围是个人，此字段则返回个人所属的部门ID")
        private Long departmentId;

    }

}
