package com.wftk.scale.biz.excel.utils;


import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * @ClassName: ExcelContextUtil
 * @Description: excel导入导出工具类
 * @Author: mq
 * @Date: 2024/11/29
 * @Version: 1.0
 **/
public class ExcelContextUtil {

    private static final String SUFFIX = ".xlsx";

    private static final String DEFAULT_FILE_NAME = "excel.xlsx";

    /*
     * @Author: mq
     * @Description: 未下载文件设置响应头
     * @Date: 2024/11/29 10:19
     * @Param: response-响应
     * @Param: fileName-文件名
     * @return: void
     **/
    public static void setExportHeader(HttpServletResponse response, String fileName) {
        fileName = StrUtil.isEmpty(fileName)?  DEFAULT_FILE_NAME : fileName;
        fileName = fileName.endsWith(SUFFIX) ? fileName : fileName + SUFFIX;
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replace("\\+", "%20");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.setHeader("Content-Disposition", "attachment;filename*=" + fileName);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    /*
     * @Author: mq
     * @Description: 返回导入文件的最后一行行号
     * @Date: 2024/11/29 10:49
     * @Param: file
     * @Param: sheetNum
     * @return: Integer
     **/
    public static Integer getLastNumber(File file, Integer sheetNum) {
        sheetNum = sheetNum == null ? 0 : sheetNum;
        try {
            Workbook wb = WorkbookFactory.create(file);
            Sheet sheet = wb.getSheetAt(sheetNum);
            return sheet.getLastRowNum();
        } catch (Exception e) {
        }
        return 0;
    }

}
