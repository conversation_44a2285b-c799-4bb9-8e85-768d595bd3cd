package com.wftk.scale.biz.event.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.DefaultFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.FileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.UploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.manager.OSSServerManager;
import com.wftk.jackson.core.JSONObject;
import com.wftk.scale.biz.config.UserReportPdfPathConfig;
import com.wftk.scale.biz.constant.FileConstant;
import com.wftk.scale.biz.constant.TerminalConfConstant;
import com.wftk.scale.biz.constant.enums.*;
import com.wftk.scale.biz.converter.NotifyConvert;
import com.wftk.scale.biz.dto.notify.UserReportNotifyInput;
import com.wftk.scale.biz.dto.report.*;
import com.wftk.scale.biz.entity.*;
import com.wftk.scale.biz.event.ScaleEvaluationIntroEvent;
import com.wftk.scale.biz.manager.job.executor.RequestExecutor;
import com.wftk.scale.biz.manager.report.ReportBuildManager;
import com.wftk.scale.biz.manager.report.dto.ReportDTO;
import com.wftk.scale.biz.service.*;
import com.wftk.scale.biz.util.PdfUtil;
import jakarta.annotation.Resource;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ScaleEvaluationIntroListener implements ApplicationListener<ScaleEvaluationIntroEvent> {

    @Autowired
    ScaleUserResultService scaleUserResultService;

    @Autowired
    ScaleUserResultRecordService scaleUserResultRecordService;

    @Autowired
    ScaleService scaleService;

    @Autowired
    ScaleCombinationService scaleCombinationService;

    @Autowired
    ScaleUserReportService scaleUserReportService;

    @Autowired
    ScaleFactorService scaleFactorService;

    @Autowired
    ScaleReportConfService scaleReportConfService;

    @Autowired
    ScaleReportConfItemService scaleReportConfItemService;

    @Autowired
    ScaleResultIntroService scaleResultIntroService;

    @Autowired
    ScaleWarnConfService scaleWarnConfService;

    @Autowired
    UserService userService;

    @Autowired
    ScaleUserFactorResultService scaleUserFactorResultService;

    @Resource
    private ScaleListingUserConfService scaleListingUserConfService;

    @Autowired
    ScaleQuestionOptionService scaleQuestionOptionService;

    @Autowired
    OrderService orderService;

    @Autowired
    NotifyService notifyService;

    @Autowired
    NotifyConvert notifyConvert;

    @Autowired
    HttpJobService httpJobService;

    @Autowired
    RequestExecutor requestExecutor;

    @Autowired
    ReportBuildManager reportBuildManager;

    @Autowired
    private UserReportPdfPathConfig userReportPdfPathConfig;

    @Autowired
    private OSSServerManager ossServerManager;


    @Async
    @Override
    public void onApplicationEvent(@NonNull ScaleEvaluationIntroEvent event) {
        log.info("scale evaluation intro event, source is {}", event);
        Long resultId = event.getResultId();
        // step1 获取所有记录
        ScaleUserResult scaleUserResult = scaleUserResultService.getById(resultId);
        if (scaleUserResult == null) {
            log.info("scaleUserResult is null. resultId is {}", resultId);
            return;
        }
        // 查询是否需要生成报告 默认需要
        boolean report = true;
        Long listingUserId = scaleUserResult.getListingUserId();
        String orderNo = scaleUserResult.getOrderNo();
        ScaleListingUserConf scaleListingUserConf = scaleListingUserConfService.findByListingUserIdOrOrderNo(listingUserId, orderNo);
        if (scaleListingUserConf != null) {
            report = scaleListingUserConf.getAllowGetReport();
        }
        if (!report) {
            log.info("report enable is close. listingUserId: {} orderNo: {}", listingUserId, orderNo);
            return;
        }


        JSONObject jsonObject = JSONObject.getInstance();
        ReportDTO reportDTO = reportBuildManager.buildReportResult(scaleUserResult);

        // 保存报告数据
        ScaleUserReport scaleUserReport = new ScaleUserReport();
        scaleUserReport.setOrderNo(scaleUserResult.getOrderNo());
        scaleUserReport.setResultId(resultId);
        scaleUserReport.setScaleName(reportDTO.getScaleName());
        if(reportDTO.getUserInfoReportContent() != null){
            scaleUserReport.setUserInfo(jsonObject.toJSONString(reportDTO.getUserInfoReportContent()));
        }
        if(reportDTO.getFactorAnalyseReportContent() != null){
            scaleUserReport.setFactorAnalysis(jsonObject.toJSONString(reportDTO.getFactorAnalyseReportContent()));
        }
        if(reportDTO.getTotalScoreReportContent() != null){
            scaleUserReport.setTotalScore(jsonObject.toJSONString(reportDTO.getTotalScoreReportContent()));
        }
        if(reportDTO.getAvgScoreReportContent() != null){
            scaleUserReport.setAvgScore(jsonObject.toJSONString(reportDTO.getAvgScoreReportContent()));
        }
        if(reportDTO.getFactorScoreContentDTO() != null){
            scaleUserReport.setFactorScore(jsonObject.toJSONString(reportDTO.getFactorScoreContentDTO()));
        }
        if(reportDTO.getPositiveCountReportContent() != null){
            scaleUserReport.setPositiveCount(jsonObject.toJSONString(reportDTO.getPositiveCountReportContent()));
        }
        if(reportDTO.getReadingInstructionsContent() != null){
            scaleUserReport.setReadingInstructions(jsonObject.toJSONString(reportDTO.getReadingInstructionsContent()));
        }
        scaleUserReportService.save(scaleUserReport);

        if (reportDTO != null) {
            String templateName = "userReport20250911";
            String fileName = IdUtil.getSnowflakeNextIdStr();
            String jsonString = jsonObject.toJSONString(reportDTO);
            Map<String, Object> data = jsonObject.parseMap(jsonString, String.class, Object.class);
            data.put("jsonData", jsonObject.toJSONString(data));
            log.info("pdf data: {}", data);
            try {
                String htmlPath = PdfUtil.ftltoHtml(userReportPdfPathConfig, templateName, data, fileName);
                String pdf = PdfUtil.convertPdf(htmlPath, userReportPdfPathConfig, fileName);
                assert pdf != null;
                File pdfFile = new File(pdf);
                FileMeta fileMeta = new DefaultFileMeta(pdfFile, null);
                UploadedFileMeta save = ossServerManager.save(FileConstant.FILE_SCALE_SIGN_ROLE, fileMeta, true);
                //删除本地pdf流
//                pdfFile.delete();
                // step4 写入报告地址
                if (StrUtil.isNotBlank(save.getFileName())) {
                    ScaleUserResult modifyUserResult = new ScaleUserResult();
                    modifyUserResult.setId(scaleUserResult.getId());
                    modifyUserResult.setReportTime(LocalDateTime.now());
                    modifyUserResult.setReportUrl(save.getFileName());
                    scaleUserResultService.updateById(modifyUserResult);
                    log.info("user scale report url save success. {}", modifyUserResult);

                    // 推送报告
                    pushUserReport(scaleUserResult, modifyUserResult.getReportTime());
                }
                log.info("user scale report create success.");
            } catch (Exception e) {
                log.error("pdf create fail.", e);
            }
        }

    }

    private void pushUserReport(ScaleUserResult scaleUserResult, LocalDateTime reportTime) {
        Order order = orderService.getByOrderNo(scaleUserResult.getOrderNo());
        if (StrUtil.isNotBlank(order.getTerminalSerialNo())) {
            UserReportNotifyInput userReportNotifyInput = notifyConvert.orderToUserReportNotifyInput(order);
            userReportNotifyInput.setScaleCode(scaleService.getById(scaleUserResult.getScaleId()).getCode());
            userReportNotifyInput.setEvaluationRecordNo(scaleUserResult.getId().toString());
            userReportNotifyInput.setReportTime(reportTime);
            UserReportDTO scaleUserReport = scaleUserReportService.getUserReportByResultId(scaleUserResult.getId());
            if (scaleUserReport != null) {
                userReportNotifyInput.setTotalScore(scaleUserReport.getTotalScore());
                if (scaleUserReport.getWarnTag() != null) {
                    userReportNotifyInput.setWarnTag(scaleUserReport.getWarnTag().getWarnTag());
                }
            }

            HttpJob job = httpJobService.createJob(userReportNotifyInput.getEvaluationRecordNo(),
                    TerminalConfConstant.ORDER_USER_REPORT_NOTIFY,
                    HttpJobStatusEnum.INIT.getStatus(),
                    JSONObject.getInstance().toJSONString(userReportNotifyInput));
            if (Objects.equals(order.getType(), ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode())) {
                // 单个量表
                job.setStatus(HttpJobStatusEnum.WAIT_SCHEDULE.getStatus());
                httpJobService.updateStatusByBizCodeAndBizId(job);
                requestExecutor.execute(job);
            } else {
                // 组合量表 判断测评方式
                ScaleCombination scaleCombination = scaleCombinationService.getById(order.getTargetId());
                if (Objects.equals(scaleCombination.getType(), ScaleEnum.COMBINATION_SCALE_TYPE_CHOOSE.getCode())) {
                    if (Objects.equals(order.getStatus(), OrderEnum.COMPLETED.getStatus())) {
                        // 修改任务为待调度 等待定时任务触发
                        List<ScaleUserResult> scaleUserResults = scaleUserResultService.selectByOrderNo(order.getOrderNo());
                        List<Long> bizIds = scaleUserResults.stream().map(ScaleUserResult::getId).toList();
                        LocalDateTime nextTime = DateUtil.offsetSecond(new Date(), ScheduledEnum.one.getSecond()).toLocalDateTime();
                        httpJobService.updateStatusByBizCodeAndBizIds(TerminalConfConstant.ORDER_USER_REPORT_NOTIFY, nextTime,
                                HttpJobStatusEnum.WAIT_SCHEDULE.getStatus(), bizIds);
                    }
                } else {
                    job.setStatus(HttpJobStatusEnum.WAIT_SCHEDULE.getStatus());
                    httpJobService.updateStatusByBizCodeAndBizId(job);
                    requestExecutor.execute(job);
                }
            }
        }
    }


}
