package com.wftk.scale.biz.dto.scale;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: ScaleUserResultQueryDTO
 * @Description: 测评记录信息
 * @Author: mq
 * @Date: 2024/11/20
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleUserResultQueryDTO implements Serializable {

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 量表名称
     */
    private String scaleName;

    /**
     * 量表ID
     */
    private Long scaleId;

    /**
     * 上下架id，来自scale_listing表的主键
     */
    private Long scaleListingId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 如果是页面展示的，该字段无值来自scale_listing_user_record表的主键
     */
    private Long listingUserId;

    /**
     * 测评开始时间
     */
    private LocalDateTime startTime;

    /**
     * 测评结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 报告生成时间
     */
    private LocalDateTime reportTime;

    /**
     * 报告地址
     */
    private String reportUrl;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 终端ID
     */
    private String terminalId;
}
