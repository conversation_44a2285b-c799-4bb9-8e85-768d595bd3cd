package com.wftk.scale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.ScaleWarnStatusEnum;
import com.wftk.scale.biz.converter.ScaleWarnDetailConvert;
import com.wftk.scale.biz.dto.scale.ScaleWarnDetailDTO;
import com.wftk.scale.biz.entity.*;
import com.wftk.scale.biz.excel.service.handler.ScaleWarnDetailHandler;
import com.wftk.scale.biz.mapper.ScaleWarnMapper;
import com.wftk.scale.biz.mapper.UserMapper;
import com.wftk.scale.biz.service.ScaleTypeService;
import com.wftk.scale.biz.service.ScaleWarnDetailService;
import com.wftk.scale.biz.vo.ScaleWarnVO;
import com.wftk.scale.biz.vo.UserVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ScaleWarnDetailServiceImpl extends ServiceImpl<ScaleWarnMapper, ScaleWarn> implements ScaleWarnDetailService {

    @Resource
    private ScaleWarnMapper scaleWarnMapper;
    @Resource
    private ScaleWarnDetailConvert scaleWarnDetailConvert;
    @Resource
    private ScaleTypeService scaleTypeService;
    @Resource
    private ScaleWarnDetailHandler scaleWarnDetailHandler;

    @Autowired
    private UserMapper userMapper;

    @Override
    public Page<ScaleWarnVO> queryList(String account, String userName, Long departmentId, String phone, String scaleName, String terminalCode) {
        return Page.doSelectPage(()-> scaleWarnMapper.queryList(account, userName, departmentId, phone, scaleName, terminalCode));
    }

    @Override
    public ScaleWarnVO detail(Long id) {
        ScaleWarn scaleWarn = baseMapper.selectById(id);
        return scaleWarn == null ? null : scaleWarnDetailConvert.scaleWarnToVO(scaleWarn);
    }

    @Override
    public void deleteById(Long id) {
        baseMapper.deleteById(id);
    }

    @Override
    public ScaleWarn saveByWarnNotice(ScaleListing scaleListing, UserVO userVO, Scale scale, ScaleUserResult scaleUserResult,
                                 String tagName, int noticeType, ScaleFactor scaleFactor, String terminalName, ScaleWarnSetting scaleWarnSetting, Long receivingWarnUserId) {
        ScaleWarn scaleWarn = new ScaleWarn();
        if(scaleListing != null){
            //根据上下架的type确定答题是单量表还是组合量表下的单量表
            scaleWarn.setType(scaleListing.getType())
                    .setScaleListingId(scaleListing.getId())
                    .setContactMode(scaleListing.getShowType())
                    //终端
                    .setTerminalCode(scaleListing.getTerminalCode())
                    .setTerminalName(terminalName);;
        }
        if(userVO != null) {
            //用户信息
            scaleWarn.setPhone(userVO.getPhone()).setSex(userVO.getSex())
                    .setAccount(userVO.getAccount()).setUserName(userVO.getName())
                    //机构信息
                    .setDepartmentId(userVO.getDepartmentId()).setDepartmentName(userVO.getDepartmentName());
        }
        //关联量表数据
        if(scale != null){
            Long typeId = scale.getType();
            String typeName = null;
            if(typeId != null){
                ScaleType scaleType = scaleTypeService.getById(typeId);
                typeName = scaleType == null ? null : scaleType.getName();
            }
            scaleWarn.setScaleId(scale.getId())
                    .setScaleName(scale.getName())
                    .setScaleTypeId(typeId)
                    .setScaleType(typeName);
        }
        //答题信息
        if(scaleUserResult != null){
            scaleWarn.setAssessmentTime(scaleUserResult.getStartTime())
                    .setFactorId(scaleFactor == null ? null : scaleFactor.getId())
                    .setCost(getCastSeconds(scaleUserResult.getStartTime(), scaleUserResult.getEndTime()))
                    .setResultId(scaleUserResult.getId());
        }
        //风险信息
        scaleWarn.setTagName(tagName).setNoticeType(noticeType).setStatus(ScaleWarnStatusEnum.INVALID.getValue());
        //风险预警等级 未知
        scaleWarn.setRiskLevel(scaleWarnSetting.getLevel());
        if (receivingWarnUserId != null) {
            User user = userMapper.getById(receivingWarnUserId);
            if (user != null) {
                scaleWarn.setReceivingWarnUserId(user.getId());
                scaleWarn.setReceivingWarnUserName(user.getName());
            }
        }
        this.save(scaleWarn);
        return scaleWarn;
    }

    @Override
    public void export(HttpServletResponse response, ScaleWarnDetailDTO dto) {
        List<ScaleWarnVO> scaleWarnList = scaleWarnMapper.queryList(dto.getAccount(), dto.getUserName(), dto.getDepartmentId(), dto.getPhone(),
                dto.getScaleName(), dto.getTerminalCode());
        scaleWarnDetailHandler.exportExcel(response, "预警列表导出", scaleWarnDetailConvert.scaleWarnVoListToExcelDataDTOList(scaleWarnList));
    }

    private int getCastSeconds(LocalDateTime start, LocalDateTime end){
        if(start == null || end == null) {
            return -1;
        }
        return (int) Duration.between(start, end).getSeconds();
    }
}
