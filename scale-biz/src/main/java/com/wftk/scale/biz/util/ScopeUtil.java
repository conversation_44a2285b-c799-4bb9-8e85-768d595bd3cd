package com.wftk.scale.biz.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/9/11 11:23
 */
@Slf4j
public class ScopeUtil {

    /**
     * 区间解析
     * 区间 以,隔开 格式：[0,1]
     * @param sectionStr
     * @return
     */
    public static List<BigDecimal> parseSectionStr(String sectionStr) {
        try {
            if (StrUtil.isEmpty(sectionStr)) {
                return List.of();
            }
            if (sectionStr.startsWith("[") && sectionStr.endsWith("]")) {
                sectionStr = sectionStr.substring(1, sectionStr.length() - 1);
            }
            String[] strScores = ObjectUtil.defaultIfNull(sectionStr.split(","), new String[0]);
            BigDecimal[] valueArray = new BigDecimal[strScores.length];
            for (int i = 0; i < strScores.length; i++) {
                valueArray[i] = new BigDecimal(strScores[i].trim());
            }
            return Arrays.asList(valueArray);
        } catch (Exception e) {
            log.error("区间解析错误,区间值:{}", sectionStr, e);
            return List.of();
        }
    }


    /**
     * 判断是否在范围内
     *
     * @param scopes
     * @param totalScore
     * @return
     */
    public static Boolean inScope(List<BigDecimal> scopes, BigDecimal totalScore) {
        try {
            BigDecimal minLimit = scopes.get(0);
            BigDecimal maxLimit = scopes.get(scopes.size() - 1);
            if (minLimit == null || maxLimit == null) {
                return false;
            }
            if (totalScore.compareTo(minLimit) >= 0 && totalScore.compareTo(maxLimit) <= 0) {
                return true;
            }
        } catch (Exception e) {
            log.error("区间判断异常,区间:{},分值：{}", scopes, totalScore, e);
            return false;
        }
        return false;
    }

}
