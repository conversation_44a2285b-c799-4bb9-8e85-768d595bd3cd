package com.wftk.scale.biz.jackson.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import cn.hutool.core.util.StrUtil;

import java.io.IOException;

/**
 * 将逗号分隔的字符串反序列化为数组
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
public class StringToStringArraySerializer extends JsonSerializer<String> {

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (StrUtil.isBlank(value)) {
            gen.writeStartArray();
            gen.writeEndArray();
            return;
        }
        String[] values = value.split(",");
        gen.writeStartArray();
        for (String v : values) {
            gen.writeString(v.trim());
        }
        gen.writeEndArray();
    }

}
