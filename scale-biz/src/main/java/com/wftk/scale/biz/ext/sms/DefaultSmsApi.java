package com.wftk.scale.biz.ext.sms;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.scale.biz.ext.notice.sms.SmsNoticeRequest;
import com.wftk.scale.biz.ext.sms.request.VerifyCodeRequest;
import com.wftk.scale.biz.ext.sms.scene.SmsSceneEnum;
import com.wftk.sms.client.spring.boot.autoconfigure.core.client.SmsClient;
import com.wftk.sms.client.spring.boot.autoconfigure.core.request.builder.send.DefaultSendSmsRequestBuilder;
import com.wftk.sms.client.spring.boot.autoconfigure.core.request.send.SendSmsRequest;
import com.wftk.sms.client.spring.boot.autoconfigure.core.response.SmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Map;

@Slf4j
public class DefaultSmsApi implements SmsApi, ApplicationContextAware {

    private final SmsClient smsClient;
    private final String productionProfile;
    private String[] activeProfiles;

    public DefaultSmsApi(SmsClient smsClient, String productionProfile) {
        this.smsClient = smsClient;
        this.productionProfile = productionProfile;
    }


    @Override
    public boolean sendVerificationCode(VerifyCodeRequest request) {
        return sendSmsMsg(request.getPhone(), SmsSceneEnum.VERIFY_CODE.name(), BeanUtil.beanToMap(request));
    }

    boolean sendSmsMsg(String tel, String scene, Map<String, Object> templateParams) {
        if (StrUtil.isBlank(tel)) {
            log.error("send sms is error: tel is empty");
            return false;
        }
        if (StrUtil.isBlank(scene)) {
            log.error("send sms is error: scene is empty");
            return false;
        }
        if (CollUtil.isEmpty(templateParams)) {
            log.error("send sms is error: templateParams is empty");
            return false;
        }
        try {
            SendSmsRequest smsRequest = new DefaultSendSmsRequestBuilder()
                    .scene(scene)
                    .tel(tel)
                    .templateParams(templateParams)
                    .build();
            log.info("send sms tel: {}, scene: {}, params: [{}]", tel, scene, templateParams);
            //非生产环境不真实发送短信
            SmsResponse smsResponse;
            if (ArrayUtil.isNotEmpty(activeProfiles) && ArrayUtil.contains(activeProfiles, productionProfile)) {
                smsResponse = smsClient.sendSms(smsRequest);
            } else {
                smsResponse = () -> true;
            }
            log.info("send sms result: [{}], tel: {}, scene: {}", smsResponse.isSuccess(), tel, scene);
            return smsResponse.isSuccess();
        } catch (Exception e) {
            log.error("send sms is error: ", e);
        }
        return false;
    }

    @Override
    public boolean sendSmsMsg(SmsNoticeRequest smsNoticeRequest) {
        return sendSmsMsg(smsNoticeRequest.getTel(), smsNoticeRequest.getScene(), smsNoticeRequest.getParams());
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.activeProfiles = applicationContext.getEnvironment().getActiveProfiles();
    }
}
