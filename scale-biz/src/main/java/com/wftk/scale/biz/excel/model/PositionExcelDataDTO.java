package com.wftk.scale.biz.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wftk.scale.biz.excel.converter.EnableConverter;
import com.wftk.scale.biz.excel.model.base.BaseExcelDataDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PositionExcelDataDTO extends BaseExcelDataDTO {
    @ExcelProperty("岗位编号")
    private String code;
    @ExcelProperty("岗位名称")
    private String name;
    @ExcelProperty(value = "岗位状态", converter = EnableConverter.class)
    private Boolean enable;
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;
    @ExcelProperty("备注")
    private String remark;
}
