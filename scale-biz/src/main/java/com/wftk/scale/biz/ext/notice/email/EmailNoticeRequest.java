package com.wftk.scale.biz.ext.notice.email;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.scale.biz.ext.notice.NoticeRequest;
import lombok.Getter;

import java.io.Serial;
import java.io.Serializable;

@Getter
public abstract class EmailNoticeRequest<T> implements NoticeRequest, Serializable {
    @Serial
    private static final long serialVersionUID = 6809134629329807783L;

    /**
     * 内容
     */
    private String content;

    /**
     * 邮件地址
     */
    private String address;

    /**
     * 邮件标题
     */
    private String subject;

    public EmailNoticeRequest(String address, String subject, T t){
        this.address = address;
        this.subject = subject;
        this.content = StrUtil.format(getTemplate(), BeanUtil.beanToMap(t));
    }
    protected abstract String getTemplate();
}
