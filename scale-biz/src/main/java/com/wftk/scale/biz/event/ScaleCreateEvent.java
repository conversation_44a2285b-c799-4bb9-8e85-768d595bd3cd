package com.wftk.scale.biz.event;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * @ClassName: ScaleCreateEventDTO
 * @Description: 创建量表事件
 * @Author: mq
 * @Date: 2024-11-04 10:22
 * @Version: 1.0
 **/
@Getter
@Setter
@Builder
public class ScaleCreateEvent extends ApplicationEvent{

    private Long newScaleId;

    private Long oldScaleId;

    private String opUser;

    private String tenantId="system";

    public ScaleCreateEvent(Long newScaleId, Long oldScaleId, String opUser, String tenantId) {
        super(newScaleId);
        this.newScaleId = newScaleId;
        this.oldScaleId = oldScaleId;
        this.opUser = opUser;
        this.tenantId = tenantId;
    }
}
