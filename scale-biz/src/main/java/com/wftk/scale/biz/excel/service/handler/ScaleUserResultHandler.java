package com.wftk.scale.biz.excel.service.handler;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.excel.model.ScaleUserResultExcelDataDTO;
import com.wftk.scale.biz.excel.utils.ExcelUtil;
import com.wftk.scale.biz.mapper.ScaleUserResultMapper;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ScaleUserResultHandler extends ServiceImpl<ScaleUserResultMapper, ScaleUserResult> {

    public void exportExcel(HttpServletResponse response, String fileName, List<ScaleUserResultExcelDataDTO> list) {
        ExcelUtil.write(response, ScaleUserResultExcelDataDTO.class, fileName, list);
    }
}
