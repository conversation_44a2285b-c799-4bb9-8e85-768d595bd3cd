package com.wftk.scale.biz.input;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Set;

@Data
public class ScaleWarnSettingCreateInput implements Serializable {
    @Serial
    private static final long serialVersionUID = -4650303727547827873L;

    /**
     * 量表ID
     */
    @Schema(description = "量表ID")
    @NotNull
    private Long scaleId;

    /**
     * 预警等级
     */
    @Schema(description = "预警等级")
    @NotNull
    private Integer level;

    /**
     * 是否预警自己
     */
    @Schema(description = "是否预警自己")
    @NotNull
    private Boolean evaluateMyself;

    /**
     * 预警范围(1：个人；2：部门)
     */
    @Schema(description = "预警范围(1：个人；2：部门)")
    @NotNull
    private Integer scope;

    /**
     * 预警因子
     */
    @Schema(description = "预警因子")
    @NotEmpty
    private Set<Long> scaleWarnConfIds;

    /**
     * 预警部门或人员ID
     */
    @Schema(description = "预警部门或人员ID")
    @NotEmpty
    private Set<Long> scaleWarnScopeIds;

    /**
     * 接收预警用户ID
     */
    @Schema(description = "接收预警用户ID")
    @NotEmpty
    private Set<Long> receiveScaleWarnUserIds;

}
