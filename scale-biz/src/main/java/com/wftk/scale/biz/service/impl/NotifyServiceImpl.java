package com.wftk.scale.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.scale.biz.constant.TerminalConfConstant;
import com.wftk.scale.biz.constant.enums.HttpResultStatusEnum;
import com.wftk.scale.biz.constant.enums.OrderNotifyTypeEnum;
import com.wftk.scale.biz.dto.httpjob.HttpResult;
import com.wftk.scale.biz.dto.notify.EvaluationRecordNotifyInput;
import com.wftk.scale.biz.dto.notify.OrderStautsNotifyInput;
import com.wftk.scale.biz.dto.notify.UserReportNotifyInput;
import com.wftk.scale.biz.ext.notifier.NotificationClient;
import com.wftk.scale.biz.service.HttpJobService;
import com.wftk.scale.biz.service.NotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/10/18 15:11
 */
@Slf4j
@Service
public class NotifyServiceImpl implements NotifyService {

    @Autowired
    HttpJobService httpJobService;


    @Autowired
    private NotificationClient notificationClient;

    @Override
    public HttpResult userReportNotify(UserReportNotifyInput userReportNotifyInput, String terminalCode) {
        Map<String, Object> userReportMap = BeanUtil.beanToMap(userReportNotifyInput);
        return notify(terminalCode,OrderNotifyTypeEnum.ORDER_USER_REPORT,userReportMap);
    }

    @Override
    public HttpResult evaluationRecordNotify(EvaluationRecordNotifyInput evaluationRecordNotifyInput, String terminalCode) {
        Map<String, Object> evaluationRecordMap = BeanUtil.beanToMap(evaluationRecordNotifyInput);
        return notify(terminalCode,OrderNotifyTypeEnum.ORDER_EVALUATION_RECORD,evaluationRecordMap);
    }

    @Override
    public HttpResult orderStautsNotify(OrderStautsNotifyInput orderStautsNotifyInput, String terminalCode) {
        Map<String, Object> orderStatusMap = BeanUtil.beanToMap(orderStautsNotifyInput);
        return notify(terminalCode,OrderNotifyTypeEnum.ORDER_STAUTS,orderStatusMap);
    }

    @Override
    public HttpResult notify(String terminalCode, OrderNotifyTypeEnum notifyType, Map<String, Object> dataMap) {
        String terminalConfCode;
        switch (notifyType) {
            case ORDER_STAUTS -> terminalConfCode = TerminalConfConstant.ORDER_STATUS_NOTIFY;
            case ORDER_EVALUATION_RECORD -> terminalConfCode = TerminalConfConstant.ORDER_EVALUATION_RECORD_NOTIFY;
            case ORDER_USER_REPORT -> terminalConfCode = TerminalConfConstant.ORDER_USER_REPORT_NOTIFY;
            default -> {
                log.error("invalid notifyType[{}].",notifyType);
                return HttpResult.builder()
                        .status(HttpResultStatusEnum.FAIL.getStatus())
                        .msg("invalid notifyType["+notifyType+"]").build();
            }
        }
        try {
            log.info("terminalCode: {}. type: {}. notify start. param: {}.",terminalCode,terminalConfCode,dataMap);
            ApiResult<Void> result = notificationClient.notify(terminalCode, terminalConfCode, dataMap);
            if (result.getCode() == 200) {
                log.info("notify type: {}. terminalCode: {}. notify success",terminalConfCode,terminalCode);
                return HttpResult.builder()
                        .status(HttpResultStatusEnum.SUCCESS.getStatus())
                        .msg("成功").build();
            }else {
                log.error("notify fail. msg is {}", result.getMessage());
                return HttpResult.builder()
                        .status(HttpResultStatusEnum.FAIL.getStatus())
                        .msg(result.getMessage()).build();
            }
        } catch (Exception e) {
            log.error("notify error.", e);
            return HttpResult.builder()
                    .status(HttpResultStatusEnum.FAIL.getStatus())
                    .msg(e.getMessage()).build();
        }
    }

}
