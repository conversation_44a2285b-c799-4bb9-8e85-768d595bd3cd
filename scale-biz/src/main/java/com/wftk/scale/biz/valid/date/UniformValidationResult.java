package com.wftk.scale.biz.valid.date;

import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2025/8/26 10:19
 */
@Data
public class UniformValidationResult {

    private final boolean valid;
    private final String format;
    private final String message;

    public UniformValidationResult(boolean valid, String format, String message) {
        this.valid = valid;
        this.format = format;
        this.message = message;
    }


    @Override
    public String toString() {
        return String.format("验证结果: %s, 格式: %s, 消息: %s",
                valid ? "成功" : "失败", format, message);
    }

}
