package com.wftk.scale.biz.excel.converter;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;

import java.time.LocalDateTime;

public class LocalDateTimeConverter implements Converter<LocalDateTime> {

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<LocalDateTime> context) {
        if(context.getValue() == null){
            return new WriteCellData<String>();
        }
        DateTime dateTime = new DateTime(context.getValue());
        String dateTimeStr = DateUtil.format(dateTime, "yyyy-MM-dd HH:mm:ss");
        return new WriteCellData<String>(dateTimeStr);
    }

}
