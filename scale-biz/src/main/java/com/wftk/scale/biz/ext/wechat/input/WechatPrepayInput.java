package com.wftk.scale.biz.ext.wechat.input;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023/10/13 09:50
 */
@Data
public class WechatPrepayInput {

    /**
     * 三方支付平台应用ID
     */
    private String appId;

    /**
     * 三方支付平台商户ID
     */
    private String mchId;


    /**
     * 外部商户订单号(主订单号)
     */
    private String orderNo;


    /**
     * 用户IP地址
     */
    private String ip;

    /**
     * 订单标题
     */
    private String subject;

    /**
     * 支付金额(单位: 分)
     */
    private Integer amount;


    /**
     * 三方支付平台openId
     */
    private String openId;

    /**
     * 通知应用方(client)地址(如果字段为空，则通过应用方client的配置项来获取)
     */
    private String notifyUrl;

    /**
     * 商品信息
     */
    private String productInfo;


    /**
     * 微信自定义参数，回调的时候微信会回传，此处保存tenantId及clientId
     */
    private String attach;

    /**
     * 微信支付单有效时间
     */
    private LocalDateTime expireAt;

    /**
     * 场景类型 iOS Android
     */
    private String type;
}
