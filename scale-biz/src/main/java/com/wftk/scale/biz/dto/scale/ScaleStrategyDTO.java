package com.wftk.scale.biz.dto.scale;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: ScaleStrategyDTO
 * @Description:
 * @Author: mq
 * @Date: 2024/11/26
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleStrategyDTO {

    private Long  questionId;

    private String questionNumber;

    private String subNumber;

    private String lable;
}
