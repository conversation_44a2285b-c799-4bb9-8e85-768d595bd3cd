package com.wftk.scale.biz.mapper;

import com.wftk.scale.biz.entity.HttpJob;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02 15:21:14
 */
public interface HttpJobMapper extends BaseMapper<HttpJob> {

    HttpJob selectByBizCodeAndBizId(@Param("bizId") String bizId,@Param("bizCode")String bizCode);

    Integer updateHttpJobResultByBizCodeAndBizId(HttpJob httpJob);

    Integer updateStatusByBizCodeAndBizId(HttpJob httpJob);

    Integer updateStatusByBizCodeAndBizIds(@Param("bizCode")String bizCode,
                                           @Param("nextTime")LocalDateTime nextTime,
                                           @Param("status")Integer status,
                                           @Param("bizIds")List<Long> bizIds);

    List<HttpJob> selectNotEndList();

}
