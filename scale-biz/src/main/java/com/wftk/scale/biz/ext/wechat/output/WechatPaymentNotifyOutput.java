package com.wftk.scale.biz.ext.wechat.output;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023/10/13 17:07
 */
@Data
public class WechatPaymentNotifyOutput {


    /**
     * 商户系统内部订单号
     */
    private String outTradeNo;

    /**
     * 微信支付系统生成的订单号
     */
    private String transactionId;

    /**
     * 交易状态
     */
    private String tradeState;

    /**
     * 支付成功事件
     */
    private LocalDateTime successTime;
}
