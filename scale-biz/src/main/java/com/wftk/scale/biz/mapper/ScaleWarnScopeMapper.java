package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.ScaleWarnScope;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04 16:56:01
 */
public interface ScaleWarnScopeMapper extends BaseMapper<ScaleWarnScope> {

    /**
     * 获取告警范围
     * @param scaleWarnSettingId
     * @param scaleId
     * @return
     */
    List<ScaleWarnScope> queryByScaleIdAndScaleWarnSettingId(@Param("scaleWarnSettingId") Long scaleWarnSettingId, @Param("scaleId") Long scaleId);


    int deleteByByScaleIdAndScaleWarnSettingId(@Param("scaleWarnSettingId") Long scaleWarnSettingId, @Param("scaleId") Long scaleId);

}
