package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.ScaleEnum;
import com.wftk.scale.biz.constant.enums.ScaleListingShowTypeEnum;
import com.wftk.scale.biz.dto.listing.*;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.entity.ScaleCombination;
import com.wftk.scale.biz.entity.ScaleListing;
import com.wftk.scale.biz.mapper.ScaleListingMapper;
import com.wftk.scale.biz.service.OrderService;
import com.wftk.scale.biz.util.QrImgUtils;
import com.wftk.scale.biz.service.ScaleCombinationDetailService;
import com.wftk.scale.biz.service.ScaleCombinationService;
import com.wftk.scale.biz.service.ScaleListingService;
import com.wftk.scale.biz.service.ScaleQuestionService;
import com.wftk.scale.biz.service.ScaleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 量表上下架表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Slf4j
@Service
public class ScaleListingServiceImpl extends ServiceImpl<ScaleListingMapper, ScaleListing> implements ScaleListingService {

    @Autowired
    private ScaleListingMapper scaleListingMapper;

    @Autowired
    private ScaleCombinationDetailService scaleCombinationDetailService;

    @Autowired
    private ScaleQuestionService scaleQuestionService;
    @Resource
    private ScaleCombinationService scaleCombinationService;
    @Resource
    private OrderService orderService;

    @Autowired
    private ScaleService scaleService;
    @Value("${config.portal.access.url:}")
    private String portalAccessUrl;

    @Override
    public boolean vaildShowTypeOfDistribution(Long scaleListingId) {
        boolean checkResult = false;
        if (ObjUtil.isNull(scaleListingId)) {
            return checkResult;
        }
        ScaleListing scaleListing = scaleListingMapper.selectById(scaleListingId);
        if (ObjUtil.isNull(scaleListing)) {
            return checkResult;
        }
        Integer showType = ObjUtil.defaultIfNull(scaleListing.getShowType(), ScaleListingShowTypeEnum.PAGE_SHOW.getValue());
        if (ScaleListingShowTypeEnum.USER_DISTRIBUTION.getValue().equals(showType)) {
            checkResult = true;
        }
        return checkResult;
    }

    @Override
    public boolean vaildScaleStatusOnShell(Long scaleListingId, Long scaleId) {
        ScaleListing scaleListing = scaleListingMapper.selectById(scaleListingId);
        return ObjectUtil.isNull(scaleListing) ? Boolean.FALSE : ScaleEnum.SCALE_LISTING_ON_SHELL_STATUS.getCode().equals(scaleListing.getStatus());
    }

    @Override
    public Page<ScaleListingQueryDTO> getScaleListing(ScaleListingParamDTO scaleListingParamDTO) {
        //获取单个可上架量表列表信息（版本最新且已完成的量表）
        return Page.doSelectPage(() -> scaleListingMapper.getScaleListingList(scaleListingParamDTO))
                .toPage(this::buildScaleListingQueryDTO);
    }

    private List<ScaleListingQueryDTO> buildScaleListingQueryDTO(List<ScaleListingQueryDTO> list) {
        list = CollUtil.isEmpty(list) ? Collections.emptyList() : list;
        return list.stream().peek(dto -> {
            //已上架终端数量(暂定:统计一个量表不同版本的所有上架数量)
            Integer numOfListed = scaleListingMapper.getNumOfScaleListed(ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode(), dto.getTargetId());
            dto.setNumOfListed(ObjectUtil.defaultIfNull(numOfListed, 0));
        }).collect(Collectors.toList());
    }

    @Override
    public Page<ScaleListingQueryDTO> getScaleCombinationListing(ScaleCombinationListingParamDTO combinationListingParamDTO) {
        //获取组合可上架量表列表信息
        return Page.doSelectPage(() -> scaleListingMapper.getScaleCombinationListingList(combinationListingParamDTO))
                .toPage(this::buildScaleCombinationQueryDTO);
    }

    private List<ScaleListingQueryDTO> buildScaleCombinationQueryDTO(List<ScaleListingQueryDTO> list) {
        list = CollUtil.isEmpty(list) ? Collections.emptyList() : list;
        return list.stream().peek(dto -> {
            //上架终端数量 取量表所有版本的上架数量
            Integer numOfListed = scaleListingMapper.getNumOfScaleListed(ScaleEnum.COMBINATION_SCALE_LISTING_TYPE.getCode(), dto.getTargetId());
            dto.setNumOfListed(ObjectUtil.defaultIfNull(numOfListed, 0));
            //根据组合量表ID查询关联的量表列表数据
            List<ScaleQueryDTO> scales = scaleCombinationDetailService.findByCombinationId(dto.getTargetId());
            dto.setDetails(scales);
        }).collect(Collectors.toList());
    }

    @Override
    public Page<ScaleListedQueryDTO> getScaleListed(ScaleListedParamDTO scaleListedParamDTO) {
        //获取单个量表已上架信息
        scaleListedParamDTO.setType(ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode());
        return Page.doSelectPage(() -> scaleListingMapper.getScaleListedList(scaleListedParamDTO))
                .toPage(this::buildScaleListedQueryDTO);
    }

    @Override
    public Page<ScaleListedQueryDTO> getScaleListedByPageDisplay(ScaleListedParamDTO scaleListedParamDTO) {
        return Page.doSelectPage(() -> scaleListingMapper.getScaleListedByPageDisplay(scaleListedParamDTO))
                .toPage(this::buildScaleListedQueryDTO);
    }

    private List<ScaleListedQueryDTO> buildScaleListedQueryDTO(List<ScaleListedQueryDTO> list) {
        list = CollUtil.isEmpty(list) ? Collections.emptyList() : list;
        Long userId = AuthenticationHolder.getAuthentication().getAuthUser().getId();
        return list.stream().peek(dto -> {
            ScaleQueryDTO scale = scaleService.findByScaleId(dto.getTargetId());
            dto.setCover(ObjUtil.isNotNull(scale) ? scale.getCover() : "");
            dto.setUserId(ObjUtil.defaultIfNull(dto.getUserId(), userId));
            //页面分发每次都需要重新购买，不返回orderNo
//            dto.setOrderNo(orderService.getOrderNoByListingIdAndUserId(dto.getScaleListingId(), userId));
        }).collect(Collectors.toList());
    }

    @Override
    public Page<ScaleListedQueryDTO> getScaleCombinationListed(ScaleListedParamDTO scaleListedParamDTO) {
        //获取组合量表已上架信息
        scaleListedParamDTO.setType(ScaleEnum.COMBINATION_SCALE_LISTING_TYPE.getCode());
        //填充组合量表属性
        return Page.doSelectPage(() -> scaleListingMapper.getScaleListedList(scaleListedParamDTO))
                .toPage(this::buildCombinationListedQueryDTO);
    }

    @Override
    public Page<ScaleListedQueryDTO> getScaleCombinationListedByPageDisplay(ScaleListedParamDTO scaleListedParamDTO) {
        return Page.doSelectPage(() -> scaleListingMapper.getScaleListedByPageDisplay(scaleListedParamDTO))
                .toPage(this::buildCombinationListedQueryDTO);
    }

    private List<ScaleListedQueryDTO> buildCombinationListedQueryDTO(List<ScaleListedQueryDTO> list) {
        list = CollUtil.isEmpty(list) ? Collections.emptyList() : list;
        Long userId = AuthenticationHolder.getAuthentication().getAuthUser().getId();
        return list.stream().peek(dto -> {
            //通过上下架ID查询关联的最新订单号
//            String orderNo = orderService.getOrderNoByListingIdAndUserId(dto.getScaleListingId(), userId);
//            dto.setOrderNo(orderNo);
            ScaleCombination scaleCombination = scaleCombinationService.getById(dto.getTargetId());
            //组合量表的测评方式
            dto.setEvaluationType(scaleCombination == null ? null : scaleCombination.getType());
            //根据组合量表ID查询关联的量表列表数据
            List<ScaleQueryDTO> scales = scaleCombinationDetailService.findByCombinationId(dto.getTargetId());
//            scales.forEach(scale -> scale.setOrderNo(orderNo));
            dto.setNumOfQuestionOrScale(CollUtil.isEmpty(scales) ? 0 : scales.size());
            dto.setDetails(scales);
            dto.setCover(this.parseScaleCover(dto, scales));
            dto.setUserId(ObjUtil.defaultIfNull(dto.getUserId(), userId));
        }).collect(Collectors.toList());
    }

    private String parseScaleCover(ScaleListedQueryDTO dto, List<ScaleQueryDTO> scales) {
        String cover = dto.getCover();
        if (ObjUtil.isNotEmpty(cover)) {
            return cover;
        }
        Optional<ScaleQueryDTO> firstElementOptional = scales.stream().findFirst();
        ScaleQueryDTO scaleQueryDTO = firstElementOptional.orElse(null);
        cover = ObjUtil.isNotNull(scaleQueryDTO) ? scaleQueryDTO.getCover() : cover;
        return cover;
    }

    private List<ScaleListedQueryDTO> buildListedQueryDTO(List<ScaleListedQueryDTO> list) {
        list = CollUtil.isEmpty(list) ? Collections.emptyList() : list;
        return list.stream().peek(dto -> {
            Integer numOfQuestions = scaleQuestionService.getNumOfQuestion(dto.getTargetId());
            dto.setNumOfQuestionOrScale(ObjectUtil.defaultIfNull(numOfQuestions, 0));
            ScaleQueryDTO scale = scaleService.findByScaleId(dto.getTargetId());
            dto.setCover(ObjUtil.isNotNull(scale) ? scale.getCover() : "");
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void scaleOnShell(ScaleListing scaleListing) {
            String opUser = AuthenticationHolder.getAuthentication().getAuthUser().getAccount();
            //根据上架终端ID、触达方式和量表ID获取是否已经存在上架信息
            String targetCode = scaleListing.getTargetCode();
            String terminalCode = scaleListing.getTerminalCode();
            Integer showType = scaleListing.getShowType();
            ScaleListing rawData = scaleListingMapper.findByTerminalIdAndTargetCode(targetCode, terminalCode, showType);
            if (ObjectUtil.isNull(rawData)) {
                this.saveOnShell(scaleListing, opUser);
                return;
            }
            this.updateOnShell(rawData, scaleListing, opUser);
    }

    protected void saveOnShell(ScaleListing scaleListing, String opUser) {
        scaleListing.setStatus(ScaleEnum.SCALE_LISTING_ON_SHELL_STATUS.getCode());
        scaleListing.setPrice(ObjectUtil.defaultIfNull(scaleListing.getPrice(), 0));
        scaleListing.setOriginalPrice(ObjectUtil.defaultIfNull(scaleListing.getOriginalPrice(), 0));
        scaleListing.setEnable(ObjectUtil.defaultIfNull(scaleListing.getEnable(), true));
        scaleListingMapper.insert(scaleListing);
    }

    protected void updateOnShell(ScaleListing rawData, ScaleListing scaleListing, String opUser) {
        Long rawDataId = rawData.getId();
        BeanUtils.copyProperties(scaleListing, rawData);
        rawData.setId(rawDataId);
        scaleListing.setStatus(ObjectUtil.defaultIfNull(rawData.getStatus(), ScaleEnum.SCALE_LISTING_ON_SHELL_STATUS.getCode()));
        rawData.setPrice(ObjectUtil.defaultIfNull(rawData.getPrice(), 0));
        rawData.setOriginalPrice(ObjectUtil.defaultIfNull(rawData.getOriginalPrice(), 0));
        rawData.setEnable(ObjectUtil.defaultIfNull(rawData.getEnable(), true));
//        rawData.setReportUrl();
        scaleListingMapper.updateById(rawData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void scaleOffShell(Long scaleListingId) {
            String opUser = AuthenticationHolder.getAuthentication().getAuthUser().getAccount();
            Integer status = ScaleEnum.SCALE_LISTING_OFF_SHELL_STATUS.getCode();
            scaleListingMapper.updateScaleListingStatus(scaleListingId, status, opUser);
    }

    @Override
    public String qrcode(Long scaleListingId) {
        //TO-DO 工具类生成或者前端生成
        return QrImgUtils.generate(generateScaleUrl(scaleListingId), null);
    }

    private String generateScaleUrl(Long scaleListingId) {
        //查询量表上架信息
        ScaleListing scaleListing = scaleListingMapper.selectById(scaleListingId);
        Assert.notNull(scaleListing, "量表上架信息不能为空");
        //上架ID、量表ID、终端编码、量表分类、展示类型
        return portalAccessUrl + "?scaleListingId=" + scaleListingId +
                "&scaleId=" + scaleListing.getTargetId() +
                "&terminalCode=" + scaleListing.getTerminalCode() +
                "&scaleType=" + scaleListing.getTargetType() +
                "&showType=" + scaleListing.getShowType();
    }

    @Override
    public Page<ScaleListedQueryDTO> getScaleDistribute(ScaleListedParamDTO scaleListedParamDTO) {
        //获取单个量表可分发终端信息
        scaleListedParamDTO.setType(ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode());
        return Page.doSelectPage(() -> scaleListingMapper.getScaleDistribute(scaleListedParamDTO))
                .toPage(this::buildListedQueryDTO);
    }

    @Override
    public Page<ScaleListedQueryDTO> getScaleCombinationDistribute(ScaleListedParamDTO scaleListedParamDTO) {
        //获取组合量表可分发终端信息
        scaleListedParamDTO.setType(ScaleEnum.COMBINATION_SCALE_LISTING_TYPE.getCode());
        //填充组合量表属性
        return Page.doSelectPage(() -> scaleListingMapper.getScaleDistribute(scaleListedParamDTO))
                .toPage(this::buildCombinationListedQueryDTO);
    }

    @Override
    public String getCover(Long scaleListingId) {
        if (ObjUtil.isNull(scaleListingId)) {
            return null;
        }
        ScaleListedQueryDTO dto = scaleListingMapper.findById(scaleListingId);
        if (ObjUtil.isNull(dto)) {
            return null;
        }
        String cover = dto.getCover();
        Integer type = dto.getType();
        if (ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode().equals(type)) {
            ScaleQueryDTO scale = scaleService.findByScaleId(dto.getTargetId());
            return ObjUtil.isNotNull(scale) ? scale.getCover() : "";
        }
        if (ScaleEnum.COMBINATION_SCALE_LISTING_TYPE.getCode().equals(type)) {
            List<ScaleQueryDTO> scales = scaleCombinationDetailService.findByCombinationId(dto.getTargetId());
            return this.parseScaleCover(dto, scales);
        }
        return cover;
    }

    @Override
    public ScaleListing getByTargetIdAndType(Long targetId, Integer type, String terminalCode) {
        return scaleListingMapper.getByTargetIdAndType(targetId, type, terminalCode);
    }

    @Override
    public String getReportUrl(Long scaleListingId) {
        return baseMapper.getReportUrl(scaleListingId);
    }
}
