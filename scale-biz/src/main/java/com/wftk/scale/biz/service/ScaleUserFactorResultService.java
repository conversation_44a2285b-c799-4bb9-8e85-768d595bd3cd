package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleUserFactorResultDetailDTO;
import com.wftk.scale.biz.entity.ScaleUserFactorResult;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 测评因子得分表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleUserFactorResultService extends IService<ScaleUserFactorResult> {

    /* 
     * @Author: mq
     * @Description: 保存测评记录量表因子得分
     * @Date: 2024/11/7 14:46 
     * @Param: list-测试答案集合
     * @return: void
     **/
    void saveFactorScore(Long resultId);

    /*
    * @Author: mq
    * @Description: 根据测评记录ID获取测评因子得分列表数据
    * @Date: 2024/11/19 17:47
    * @Param: resultId-测评记录ID
    * @return: Page<ScaleUserFactorResult>
    **/
    Page<ScaleUserFactorResult> selectPage(Long resultId);

    /*
     * @Author: mq
     * @Description: 根据测评记录ID获取测评因子得分列表数据
     * @Date: 2024/11/19 17:47
     * @Param: resultId-测评记录ID
     * @return: Page<ScaleUserFactorResult>
     **/
    List<ScaleUserFactorResult> selectListByResultId(Long resultId);

    /*
    * @Author: mq
    * @Description: 根据测评记录ID获取测评因子得分详情信息
    * @Date: 2024/11/25 18:23
    * @Param: resultId-测评记录ID
    * @return: List<ScaleUserFactorResultDetailDTO>
    **/
    List<ScaleUserFactorResultDetailDTO> getDetailByResultId(Long resultId);

    /**
     * 根据因子id和结果id查询因子结果
     *
     * @param resultId
     * @param factorId
     * @param showType
     * @return
     */
    ScaleUserFactorResult getByResultIdAndFactorId( Long resultId,
                                                    Long factorId,
                                                    String showType);


    List<ScaleUserFactorResult> getListByResultId( Long resultId,
                                                   List<Long> factorIds);


    /**
     * 根据因子id和结果id查询最终分数
     *
     * @param resultId
     * @param factorId
     * @return
     */
    BigDecimal getScoreByResultIdAndFactorId(Long resultId,
                                              Long factorId);


    List<Map<String,Object>> selectReportFormData(Long resultId,
                                                  String clonwnStr,
                                                  List<Long> factorIds);

}
