package com.wftk.scale.biz.event;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * @ClassName: ScaleEventDTO
 * @Description: 删除量表事件
 * @Author: mq
 * @Date: 2024-11-04 10:22
 * @Version: 1.0
 **/
@Getter
@Setter
@Builder
public class ScaleDelEvent extends ApplicationEvent {

    /**
     * 被删除量表ID
     */
    private Long oldScaleId;

    /**
     * 操作用户
     */
    private String opUser;

    public ScaleDelEvent(Long oldScaleId, String opUser) {
        super(oldScaleId);
        this.oldScaleId = oldScaleId;
        this.opUser = opUser;
    }
}
