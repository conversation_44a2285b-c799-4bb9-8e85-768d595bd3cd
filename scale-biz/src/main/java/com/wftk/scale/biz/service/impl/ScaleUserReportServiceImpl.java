package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.DefaultFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.FileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.UploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.manager.OSSServerManager;
import com.wftk.jackson.core.JSONObject;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.config.UserReportPdfPathConfig;
import com.wftk.scale.biz.constant.FileConstant;
import com.wftk.scale.biz.dto.report.*;
import com.wftk.scale.biz.entity.ScaleUserReport;
import com.wftk.scale.biz.mapper.ScaleUserReportMapper;
import com.wftk.scale.biz.service.ScaleUserReportService;
import com.wftk.scale.biz.util.PdfUtil;
import com.wftk.scale.biz.vo.ScaleUserReportVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05 11:14:30
 */
@Service
@Slf4j
public class ScaleUserReportServiceImpl extends ServiceImpl<ScaleUserReportMapper, ScaleUserReport> implements ScaleUserReportService {

    @Autowired
    ScaleUserReportMapper scaleUserReportMapper;

    @Autowired
    private UserReportPdfPathConfig userReportPdfPathConfig;

    @Autowired
    private OSSServerManager ossServerManager;

    @Override
    public ScaleUserReport getByResultId(Long resultId) {
        return scaleUserReportMapper.getByResultId(resultId);
    }

    @Override
    public UserReportDTO getUserReportByResultId(Long resultId) {
        JSONObject jsonObject = JSONObject.getInstance();
        ScaleUserReport scaleUserReport = scaleUserReportMapper.getByResultId(resultId);
        if(scaleUserReport == null){
            throw new BusinessException("报告未生成");
        }
        UserReportDTO userReportDTO = new UserReportDTO();
        userReportDTO.setScaleName(scaleUserReport.getScaleName());

        return userReportDTO;
    }

    @Override
    public String generateReportPdf(Long resultId) {
        UserReportDTO userReport = getUserReportByResultId(resultId);
        //pdf生成
        String templateName = "userReport";
        JSONObject jsonObject = JSONObject.getInstance();
        String jsonString = jsonObject.toJSONString(userReport);
        Map<String, Object> stringObjectMap = jsonObject.parseMap(jsonString, String.class, Object.class);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userReport", stringObjectMap);
        log.info("generateReportPdf freemark params:{}",JSONObject.getInstance().toJSONString(paramMap));
        //调用具体的实现方法
        String fileName = IdUtil.getSnowflakeNextIdStr();
        try {
            String s = PdfUtil.contractHandler(userReportPdfPathConfig, templateName, paramMap, fileName);
            File pdfFile = new File(s);
            FileMeta fileMeta = new DefaultFileMeta(pdfFile, null);
            UploadedFileMeta save = ossServerManager.save(FileConstant.FILE_SCALE_SIGN_ROLE, fileMeta, true);
            //删除本地pdf流
            pdfFile.delete();
            return save.getFileName();
        } catch (Exception e) {
            log.error("pdf create fail.",e);
            return null;
        }
    }

    @Override
    public Page<ScaleUserReportVO> queryPage(UserReportQueryDTO dto) {
        //TODO 减少联查的表
        return Page.doSelectPage(() -> scaleUserReportMapper.queryPage(dto));
    }
}
