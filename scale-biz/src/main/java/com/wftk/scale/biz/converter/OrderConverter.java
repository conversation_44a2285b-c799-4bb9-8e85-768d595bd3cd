package com.wftk.scale.biz.converter;

import com.wftk.scale.biz.dto.notify.UserReportNotifyInput;
import com.wftk.scale.biz.dto.order.BuyOrderDTO;
import com.wftk.scale.biz.dto.order.OrderQueryDTO;
import com.wftk.scale.biz.dto.order.PlaceOrderDTO;
import com.wftk.scale.biz.entity.Order;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/11/18 16:18
 */
@Mapper(componentModel = "spring")
public interface OrderConverter {

    Order placeOrderToEntity(PlaceOrderDTO placeOrderDTO);

    Order buyOrderToEntity(BuyOrderDTO buyOrderDTO);

    OrderQueryDTO orderToOrderQueryDTO(Order order);

    List<OrderQueryDTO> orderToOrderQueryDTO(List<Order> order);


}
