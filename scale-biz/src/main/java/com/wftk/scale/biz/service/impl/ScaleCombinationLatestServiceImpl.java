package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.scale.biz.entity.ScaleCombinationLatest;
import com.wftk.scale.biz.mapper.ScaleCombinationLatestMapper;
import com.wftk.scale.biz.service.ScaleCombinationLatestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 组合量表最新数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class ScaleCombinationLatestServiceImpl extends ServiceImpl<ScaleCombinationLatestMapper, ScaleCombinationLatest> implements ScaleCombinationLatestService {

    @Autowired
    private ScaleCombinationLatestMapper scaleCombinationLatestMapper;

    @Override
    public void saveOrUpdateScaleLatest(Long scaleId, String code) {
        ScaleCombinationLatest scaleLatest = scaleCombinationLatestMapper.findByCode(code);
        if(ObjectUtil.isNull(scaleLatest)){
            scaleLatest = new ScaleCombinationLatest();
        }
        scaleLatest.setCombinationId(scaleId);
        scaleLatest.setCode(code);
        this.saveOrUpdate(scaleLatest);
    }
}
