package com.wftk.scale.biz.ext.wechat.core;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.StrUtil;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.util.IOUtil;
import com.wechat.pay.java.core.util.PemUtil;
import com.wftk.scale.biz.entity.WechatPaySetting;

import java.io.IOException;
import java.io.InputStream;
import java.security.PrivateKey;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @create 2023/10/13 11:21
 */
public class DefaultWechatClientFactory implements WechatClientFactory {

    private final Map<String, WechatClient> wechatClientMap = new ConcurrentHashMap<>();

    @Override
    public WechatClient get(WechatPaySetting setting) {
        String key = getKey(setting);
        WechatClient wechatClient = wechatClientMap.get(key);
        if (wechatClient != null) {
            return wechatClient;
        }

        RSAAutoCertificateConfig.Builder configBuilder = new RSAAutoCertificateConfig.Builder()
                .merchantId(setting.getMchId())
                .merchantSerialNumber(setting.getMchNo())
                .apiV3Key(setting.getApiSecret());

        RSAAutoCertificateConfig.Builder noticeConfigBuilder = new RSAAutoCertificateConfig.Builder()
                .merchantId(setting.getMchId())
                .merchantSerialNumber(setting.getMchNo())
                .apiV3Key(setting.getApiSecret());

        if (StrUtil.isBlank(setting.getCertFile())) {
            try {
                //如果数据库中没有配置证书位置，则从当前classpath获取
                InputStream stream = ResourceUtil.getStream("cert/wechat/apiclient_key.pem");
                String privateKeyStr = IOUtil.toString(stream);
                PrivateKey privateKey = PemUtil.loadPrivateKeyFromString(privateKeyStr);
                configBuilder.privateKey(privateKey);
                noticeConfigBuilder.privateKey(privateKey);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else {
            configBuilder.privateKeyFromPath(setting.getCertFile());
            noticeConfigBuilder.privateKeyFromPath(setting.getCertFile());
        }

        Config config = configBuilder.build();
        RSAAutoCertificateConfig notificationConfig = noticeConfigBuilder.build();

        wechatClient = new DefaultWechatClient(config, notificationConfig);
        wechatClientMap.put(key, wechatClient);
        return wechatClient;
    }


    private String getKey(WechatPaySetting setting) {
        return setting.getAppId() + "_" + setting.getMchId();
    }
}
