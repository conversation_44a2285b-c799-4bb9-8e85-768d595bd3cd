package com.wftk.scale.biz.service.impl;

import com.wftk.scale.biz.dto.scale.AgreementQueryDTO;
import com.wftk.scale.biz.entity.Agreement;
import com.wftk.scale.biz.mapper.AgreementMapper;
import com.wftk.scale.biz.service.AgreementService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 协议内容表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02 17:20:42
 */
@Service
public class AgreementServiceImpl extends ServiceImpl<AgreementMapper, Agreement> implements AgreementService {

    @Override
    public AgreementQueryDTO findByCode(String code) {
        return baseMapper.findByCode(code);
    }
}
