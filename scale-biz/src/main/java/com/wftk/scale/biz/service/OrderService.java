package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.order.AdminOrderQueryDto;
import com.wftk.scale.biz.dto.order.BuyOrderDTO;
import com.wftk.scale.biz.dto.order.OrderPayDTO;
import com.wftk.scale.biz.dto.order.OrderQueryDTO;
import com.wftk.scale.biz.dto.order.PlaceOrderDTO;
import com.wftk.scale.biz.entity.Order;
import com.wftk.scale.biz.entity.OrderPayment;
import java.util.List;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface OrderService extends IService<Order> {

    /**
     * 本系统购买量表接口
     *
     * @param buyOrderDTO
     * @return
     */
    OrderPayment createScaleOrderAndPayOrder(BuyOrderDTO buyOrderDTO);

    /**
     * 外部系统购买量表接口
     *
     * @param order
     * @return
     */
    OrderPayDTO placeScaleOrder(PlaceOrderDTO order);


    Order getByTerminalSerialNoAndOrderNo(String terminalCode, String terminalSerialNo, String orderNo);

    Order getByOrderNo(String orderNo);

    Page<OrderQueryDTO> getList(AdminOrderQueryDto queryDto);

    List<Order> getExtOrderList(String terminalCode, List<String> terminalSerialNos);

    Integer updateStatus(String orderNo,Integer status,Integer oldStatus);

    boolean validOrderCompleted(String orderNo);

    String getOrderNoByListingIdAndUserId(Long listingId, Long userId);

    OrderQueryDTO detailById(AdminOrderQueryDto queryDto);

    void deleteOrder(Long id);

    /**
     * 通知订单完成
     * @param orderNo
     */
    Order notifyComplete(String orderNo);
}
