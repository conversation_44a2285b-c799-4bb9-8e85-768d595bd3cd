package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.ScaleFactorFormula;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 因子公式 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleFactorFormulaMapper extends BaseMapper<ScaleFactorFormula> {

    /*
     * @Author: mq
     * @Description: 校验因子公式名称是否已经存在
     * @Date: 2024/10/29 14:41
     * @Param: factorFormulaId-因子公式主键ID
     * @Param: formulaName-因子公式名称
     * @return: boolean
     **/
    boolean validFormulaNameExist(@Param("factorFormulaId") Long factorFormulaId, @Param("formulaName") String formulaName);

    /*
    * @Author: mq
    * @Description: 校验因子公式是否被其它公式嵌套使用
    * @Date: 2024/11/25 13:58
    * @Param: factorFormulaId-因子公式主键ID
    * @return: String (嵌套公式名称)
    **/
    String checkFormulaUsed(@Param("factorFormulaId") Long factorFormulaId);

    /*
     * @Author: mq
     * @Description: 根据ID更新因子公式数据的状态
     * @Date: 2024/10/29 14:45
     * @Param: id-主键ID
     * @Param: enable-是否启用(0.未启用、 1.已启用)
     * @Param: opUser-操作人
     * @return: void
     **/
    void updateEnable(@Param("factorFormulaId") Long factorFormulaId, @Param("enable") Boolean enable, @Param("opUser") String opUser);

    /* 
     * @Author: mq
     * @Description: 根据条件查询因子公式数据
     * @Date: 2024/10/29 14:47 
     * @Param: formulaName-公式名称
     * @Praam: enable-是否启用(0.未启用、 1.已启用)
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleFactorFormula> 
     **/
    List<ScaleFactorFormula> getList(@Param("formulaName") String formulaName, @Param("enable") Boolean enable, @Param("status") Integer status);


    ScaleFactorFormula getByCode(@Param("code") String code);
}
