package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.scale.biz.converter.SysMenuConverter;
import com.wftk.scale.biz.dto.user.menu.SysCreateMenuDTO;
import com.wftk.scale.biz.dto.user.menu.SysUpdateMenuDTO;
import com.wftk.scale.biz.entity.SysMenu;
import com.wftk.scale.biz.event.SysMenuDeleteEvent;
import com.wftk.scale.biz.event.publisher.SysMenuDeletePublisher;
import com.wftk.scale.biz.mapper.SysMenuMapper;
import com.wftk.scale.biz.service.SysMenuService;
import com.wftk.scale.biz.vo.SysMenuVO;
import com.wftk.scale.biz.vo.SysRoleMenuVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    @Resource
    private SysMenuConverter sysMenuConverter;
    @Resource
    private SysMenuDeletePublisher publishEvent;
    @Resource
    private SysMenuMapper sysMenuMapper;

    @Override
    public List<SysMenuVO> getList() {
        return buildSysMenuVOList(listAll());
    }

    private List<SysMenu> listAll(){
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(SysMenu::getSort);
        queryWrapper.orderByAsc(SysMenu::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<SysMenuVO> getEditSelectParentList(Long id) {
        if(Objects.isNull(id)){
            throw new BusinessException("参数id不能为空");
        }
        List<SysMenu> list = listAll();
        if(CollUtil.isNotEmpty(list)){
            List<Long> sysMenuIds = baseMapper.selectChildrenByMenuId(id).stream().map(SysMenu::getId).toList();
            list = list.stream().filter(it -> !sysMenuIds.contains(it.getId())).collect(Collectors.toList());
        }
        return buildSysMenuVOList(list);
    }

    @Override
    public List<SysMenuVO> getOwnerMenus() {
        Long userId = AuthenticationHolder.getAuthentication().getAuthUser().getId();
        //查询用户关联角色，角色关联菜单
        List<SysMenu> sysMenus = sysMenuMapper.selectMenuByUserId(userId);
        return buildSysMenuVOList(sysMenus);
    }

    private List<SysMenuVO> buildSysMenuVOList(List<SysMenu> list) {
        if(CollUtil.isEmpty(list)){
            return List.of();
        }
        List<SysMenuVO> result = new ArrayList<>();
        List<SysMenuVO> sysMenuVOList = list.stream().map(vo -> sysMenuConverter.sysMenuToSysMenuVO(vo)).toList();
        Map<Long, SysMenuVO> collect = sysMenuVOList.stream().collect(Collectors.toMap(SysMenuVO::getId, vo -> vo));
        for (SysMenuVO sysMenu : sysMenuVOList) {
            //如果父节点不存在则作为父节点
            if(sysMenu.getParentId() == null || sysMenu.getParentId() == 0L || !collect.containsKey(sysMenu.getParentId())){
                result.add(sysMenu);
            }else{
                SysMenuVO parentSysMenu = collect.get(sysMenu.getParentId());
                if(parentSysMenu != null){
                    sysMenu.setParentName(parentSysMenu.getName());
                    parentSysMenu.getChildren().add(sysMenu);
                }
            }
        }
        return result;
    }

    @Override
    public void createMenu(SysCreateMenuDTO dto) {
        SysMenu sysMenu = sysMenuConverter.sysCreateMenuDtoToSysMenu(dto);
        //校验编码唯一
        validateSysMenuCodeUnique(sysMenu.getCode());
        baseMapper.insert(sysMenu);
    }

    private void validateSysMenuCodeUnique(String code) {
        boolean exists = baseMapper.exists(new LambdaQueryWrapper<SysMenu>().eq(SysMenu::getCode, code));
        if(exists){
            throw new BusinessException("菜单编码已存在!");
        }
    }

    @Override
    public void updateMenu(SysUpdateMenuDTO dto) {
        SysMenu sysMenu = sysMenuConverter.sysUpdateMenuDtoToSysMenu(dto);
        SysMenu dbSysMenu = baseMapper.selectById(sysMenu.getId());
        if(dbSysMenu == null){
            throw new BusinessException("菜单不存在");
        }
        if(!Objects.equals(dbSysMenu.getCode(), sysMenu.getCode())){
            validateSysMenuCodeUnique(sysMenu.getCode());
        }
        //编辑时，所选父机构，需要存在，且不能时当前机构的下级机构，否则会栈溢出
        validParam(sysMenu.getParentId(), sysMenu.getId());
        baseMapper.updateById(sysMenu);
    }

    private void validParam(Long parentId, Long menuId){
        if(parentId == null || parentId == 0L){
            return;
        }
        SysMenu sysMenu = baseMapper.selectById(parentId);
        if(sysMenu == null){
            throw new BusinessException("上级菜单不存在");
        }
        List<Long> ids = baseMapper.selectChildrenByMenuId(menuId).stream().map(SysMenu::getId).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(ids) && ids.contains(parentId)){
            throw new BusinessException("请勿选择当前菜单及其子菜单为上级菜单");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMenu(Long menuId) {
        //级联删除
        List<SysMenu> sysMenus = baseMapper.selectChildrenByMenuId(menuId);
        if(CollUtil.isNotEmpty(sysMenus)){
            List<Long> idList = sysMenus.stream().map(SysMenu::getId).collect(Collectors.toList());
            baseMapper.deleteByIds(idList);
            //同时删除，角色关联的菜单
            publishEvent.publishEvent(new SysMenuDeleteEvent(idList));

        }
    }

    @Override
    public List<SysRoleMenuVO> selectMenusByRoleId(Long roleId) {
        return sysMenuMapper.selectRoleMenuByRoleId(roleId);
    }
}