package com.wftk.scale.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.entity.SysUserDepartment;
import com.wftk.scale.biz.event.SysUserChangeEvent;
import com.wftk.scale.biz.event.SysUserDeleteEvent;
import com.wftk.scale.biz.mapper.SysUserDepartmentMapper;
import com.wftk.scale.biz.service.SysUserDepartmentService;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class SysUserDepartmentServiceImpl extends ServiceImpl<SysUserDepartmentMapper, SysUserDepartment> implements SysUserDepartmentService {

    @EventListener(SysUserChangeEvent.class)
    public void listen(SysUserChangeEvent event) {
        if(event.getSysDepartmentId() == null || event.getSysUserId() == null){
            return;
        }
        //先删除用户之前绑定的
        baseMapper.delete(new LambdaQueryWrapper<SysUserDepartment>().eq(SysUserDepartment::getSysUserId, event.getSysUserId()));
        //用户创建或者编辑事件
        SysUserDepartment sysUserDepartment = new SysUserDepartment();
        sysUserDepartment.setSysUserId(event.getSysUserId());
        sysUserDepartment.setSysDepartmentId(event.getSysDepartmentId());
        baseMapper.insert(sysUserDepartment);
    }

    @EventListener(SysUserDeleteEvent.class)
    public void listen(SysUserDeleteEvent event) {
        baseMapper.delete(new LambdaQueryWrapper<SysUserDepartment>().eq(SysUserDepartment::getSysUserId, event.getSysUserId()));
    }
}
