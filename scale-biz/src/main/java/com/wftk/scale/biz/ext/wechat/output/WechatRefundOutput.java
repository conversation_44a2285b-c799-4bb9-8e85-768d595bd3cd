package com.wftk.scale.biz.ext.wechat.output;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023/10/17 11:38
 */
@Data
public class WechatRefundOutput {

    /**
     * 微信支付退款号
     */
    private String refundId;

    /**
     * 商户系统内部的退款单号，商户系统内部唯一
     */
    private String outRefundNo;

    /**
     * 微信支付交易订单号
     */
    private String transactionId;

    /**
     * 商户系统订单号
     */
    private String orderNo;

    /**
     * 退款渠道
     */
    private String channel;

    /**
     * 退款入账账户
     */
    private String userReceivedAccount;

    /**
     * 退款状态
     * SUCCESS: 退款成功
     * CLOSED: 退款关闭
     * PROCESSING: 退款处理中
     * ABNORMAL: 退款异常
     */
    private String status;

    /**
     * 退款金额(单位：分)
     */
    private Integer amount;

    /**
     * 成功时间
     */
    private LocalDateTime successTime;

}
