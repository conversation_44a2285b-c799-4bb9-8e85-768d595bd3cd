package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.dto.user.DepartmentCreateDTO;
import com.wftk.scale.biz.dto.user.DepartmentQueryDTO;
import com.wftk.scale.biz.dto.user.DepartmentUpdateDTO;
import com.wftk.scale.biz.entity.Department;

import java.util.List;

/**
 * <p>
 * 部门表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface DepartmentService extends IService<Department> {

    void createDepartment(DepartmentCreateDTO departmentCreateDTO);

    void updateDepartment(DepartmentUpdateDTO departmentUpdateDTO);

    void deleteDepartment(Long id);

    List<DepartmentQueryDTO> selectTreeList(String name, Integer enable, String terminalCode, String parentName);

    /**
     * 通过终端编码获取部门
     * @param terminalCode
     * @return
     */
    Department selectByTerminalCode(String terminalCode);

}
