package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@TableName("scale_warn")
@Data
@Accessors(chain = true)
public class ScaleWarn implements Serializable {

    @TableId
    private Long id;

    /**
     * 测评记录ID
     */
    private Long resultId;

    /**
     * 用户账号
     */
    private String account;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 性别 1男 2女
     */
    private Integer sex;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 所属部门名称
     */
    private String departmentName;
    /**
     * 所属部门ID
     */
    private Long departmentId;
    /**
     * 量表ID
     */
    private Long scaleId;
    /**
     * 量表名称
     */
    private String scaleName;
    /**
     * 量表分类
     */
    private String scaleType;
    /**
     * 量表分类ID
     */
    private Long scaleTypeId;
    /**
     * 上下架ID
     */
    private Long scaleListingId;
    /**
     * 量表类型 单量表/组合量表
     */
    private Integer type;
    /**
     * 触达方式，页面展示/分发
     */
    private Integer contactMode;
    /**
     * 终端编码
     */
    private String terminalCode;
    /**
     * 终端名称
     */
    private String terminalName;
    /**
     * 测评时间
     */
    private LocalDateTime assessmentTime;
    /**
     * 测评耗时，单位:秒
     */
    private Integer cost;
    /**
     * 因子ID
     */
    private Long factorId;
    /**
     * 测评结果
     */
    private String tagName;
    /**
     * 风险等级
     */
    private Integer riskLevel;
    /**
     * 预警方式
     */
    private Integer noticeType;
    /**
     * 状态 1有效 0无效
     */
    private Integer status;

    /**
     * 接收预警用户ID
     */
    private Long receivingWarnUserId;

    /**
     * 接收预警用户名称
     */
    private String receivingWarnUserName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
}
