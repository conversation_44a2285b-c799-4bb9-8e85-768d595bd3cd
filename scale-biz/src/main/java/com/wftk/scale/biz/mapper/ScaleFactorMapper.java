package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.ScaleFactor;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 因子维度 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleFactorMapper extends BaseMapper<ScaleFactor> {

    /*
     * @Author: mq
     * @Description: 校验量表因子维度容是否有设置
     * @Date: 2024/11/4 14:40
     * @Param: scaleId-量表ID
     * @return: boolean
     **/
    boolean vaildFactorDataIntegrity(@Param("scaleId") Long scaleId);

    /*
     * @Author: mq
     * @Description: 校验因子公式是否被禁用
     * @Date: 2024/11/25 16:48
     * @Param: scaleId-量表ID
     * @return: String(被禁用公式的名称)
     **/
    String checkFactorFormulaEnable(@Param("scaleId") Long scaleId);

    /**
     * 获取不可用因子公式数量
     *
     * @Date: 2024/11/4 14:39
     * @Param: scaleId-量表ID
     * @return: boolean
     */
    Integer getFactorFormulaNotEnableCount(@Param("scaleId") Long scaleId);

    /**
     * 获取不可用因子公式数量
     *
     * @Date: 2024/11/4 14:39
     * @Param: scaleId-量表ID
     * @return: boolean
     */
    Integer getFactorFormulaNotEnableCountByScaleIds(@Param("scaleIds") List<Long> scaleIds);

    /*
     * @Author: mq
     * @Description: 校验量表因子维度名称是否已经存在
     * @Date: 2024/11/5 9:56
     * @Param: factorId-因子维度主键ID
     * @Param: scaleId-量表ID
     * @Param: factorName-因子维度名称
     * @return: boolean
     **/
    boolean vaildFactorNameExists(@Param("factorId") Long factorId, @Param("scaleId") Long scaleId, @Param("factorName") String factorName);

    /*
     * @Author: mq
     * @Description: 根据条件查询量表因子维度数据
     * @Date: 2024/11/5 10:13
     * @Param: scaleId-量表ID
     * @Param: factorName-因子维度名称
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleFactor>
     **/
    List<ScaleFactor> getList(@Param("scaleId") Long scaleId, @Param("factorName") String factorName);

    /*
     * @Author: mq
     * @Description: 根据条件查询量表因子维度数据
     * @Date: 2024/11/5 10:13
     * @Param: scaleId-量表ID
     * @Param: factorName-因子维度名称
     * @return: com.wftk.scale.biz.entity.ScaleFactor
     **/
    ScaleFactor getByScaleIdAndName(@Param("scaleId") Long scaleId, @Param("factorName") String factorName);

    /**
     * 判断因子公式是否被因子维度所引用
     * @param factorFormulaId 因子公式主键ID
     */
    boolean checkFactorIsDepend(@Param("factorFormulaId") Long factorFormulaId);

    /**
     * 判断该量表下是否存在该类型因子
     * @param scaleId 量表id
     * @param id 因子id
     */
    boolean checkFactorType(@Param("scaleId") Long scaleId,
                            @Param("id") Long id,
                            @Param("type") Integer type);


    /*
     * @Author: mq
     * @Description: 根据条件查询量表因子维度总分因子、总均分因子
     * @Date: 2024/11/5 10:13
     * @Param: scaleId-量表ID
     * @Param: type-因子维度类型
     * @return: com.wftk.scale.biz.entity.ScaleFactor
     **/
    ScaleFactor getFactorByScaleIdAndType(@Param("scaleId") Long scaleId,@Param("type") Integer type);

    /**
     *
     * @param scaleId
     * @param type
     * @return
     */
    List<ScaleFactor> getFactorListByScaleIdAndType(@Param("scaleId") Long scaleId,@Param("type") Integer type);

    ScaleFactor getById(@Param("id") Long id);
}
