package com.wftk.scale.biz.event.publisher;

import com.wftk.scale.biz.event.ScaleCreateEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createDate 2024/12/4 17:27
 */
@Component
public class ScaleCreatePublisher implements BaseEventPublisher<ScaleCreateEvent> {

    private final ApplicationEventPublisher applicationEventPublisher;

    public ScaleCreatePublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Override
    public void publishEvent(ScaleCreateEvent scaleCreateEvent) {
        applicationEventPublisher.publishEvent(scaleCreateEvent);
    }
}
