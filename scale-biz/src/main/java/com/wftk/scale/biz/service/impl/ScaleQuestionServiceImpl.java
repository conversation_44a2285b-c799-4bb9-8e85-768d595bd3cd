package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.jackson.core.TargetType;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.IdGenerator;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.SymbolConstant;
import com.wftk.scale.biz.constant.enums.JumpEnum;
import com.wftk.scale.biz.constant.enums.PropertyEnum;
import com.wftk.scale.biz.constant.enums.ScaleQuestionOptionTypeEnum;
import com.wftk.scale.biz.dto.scale.ScaleQuestionHighestOverallScoreDTO;
import com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleStrategyDTO;
import com.wftk.scale.biz.dto.scale.ScaleStrategyRelationDTO;
import com.wftk.scale.biz.entity.ScaleQuestion;
import com.wftk.scale.biz.entity.ScaleQuestionOption;
import com.wftk.scale.biz.entity.ScaleQuestionRelation;
import com.wftk.scale.biz.event.ScaleCreateEvent;
import com.wftk.scale.biz.event.ScaleDelEvent;
import com.wftk.scale.biz.excel.service.handler.ScaleQuestionHandler;
import com.wftk.scale.biz.mapper.ScaleQuestionMapper;
import com.wftk.scale.biz.service.*;
import com.wftk.scale.biz.valid.date.DateUniformValidator;
import com.wftk.scale.biz.valid.date.UniformValidationResult;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 题目答案表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Slf4j
@Service
public class ScaleQuestionServiceImpl extends ServiceImpl<ScaleQuestionMapper, ScaleQuestion>
        implements ScaleQuestionService {

    @Autowired
    private ScaleQuestionMapper scaleQuestionMapper;

    @Autowired
    private ScaleQuestionOptionService scaleQuestionOptionService;

    @Autowired
    private ScaleQuestionRelationService scaleQuestionRelationService;
    @Autowired
    private ScaleFactorService scaleFactorService;
    @Autowired
    private ScaleService scaleService;
    @Resource
    private ScaleQuestionHandler scaleQuestionHandler;

    @Autowired
    private IdGenerator idGenerator;

    @Override
    public boolean vaildQuestionDataIntegrity(Long scaleId) {
        return scaleQuestionMapper.vaildQuestionDataIntegrity(scaleId);
    }

    @Override
    public boolean vaildQuestionChildrenExists(Long scaleId, Long questionId) {
        ScaleQuestionQueryDTO question = scaleQuestionMapper.findByScaleIdAndQuestionId(scaleId, questionId);
        if (ObjUtil.isNull(question)) {
            return false;
        }
        String questionNum = question.getQuestionNumber();
        String subNum = question.getSubNumber();
        if (StrUtil.isNotEmpty(questionNum) && StrUtil.isNotEmpty(subNum)) {
            return false;
        }
        return scaleQuestionMapper.vaildQuestionChildrenExists(scaleId, questionId, questionNum);
    }

    @Override
    public boolean vaildQuestionNumber(ScaleQuestion question) {
        return scaleQuestionMapper.vaildQuestionNumberExists(question);
    }

    @Override
    public boolean vaildQuestionOptionLabel(List<ScaleQuestionOption> options) {
        return scaleQuestionOptionService.vaildQuestionOptionLabel(options);
    }

    @Override
    public boolean vaildQuestionOption(List<ScaleQuestionOption> options) {
        return !CollUtil.isEmpty(options);
    }

    @Override
    public boolean vaildQuestionNumberRelationship(ScaleQuestion question) {
        if (StrUtil.isEmpty(question.getSubNumber())) {
            return true;
        }
        // 如果子题号不为空，则判断questionNumber是否已经存在
        return scaleQuestionMapper.vaildQuestionUpperNumberExists(question);
    }

    /**
     * @param optionType
     * @param options
     * @return
     * @Description: 校验答案的值类型是否正确
     */
    @Override
    public UniformValidationResult vaildOptionTypeRight(Integer optionType, List<ScaleQuestionOption> options) {
        if (ScaleQuestionOptionTypeEnum.STRING.getCode().equals(optionType)) {
            return new UniformValidationResult(true, String.valueOf(ScaleQuestionOptionTypeEnum.STRING.getCode()), "");
        } else if (ScaleQuestionOptionTypeEnum.NUMBER.getCode().equals(optionType)) {
            for (ScaleQuestionOption scaleQuestionOption : options) {
                if (!NumberUtil.isNumber(scaleQuestionOption.getValue())) {
                    return new UniformValidationResult(false,
                            String.valueOf(ScaleQuestionOptionTypeEnum.NUMBER.getCode()), "答案请填写数值类型");
                }
            }
            return new UniformValidationResult(true, String.valueOf(ScaleQuestionOptionTypeEnum.NUMBER.getCode()), "");
        } else if (ScaleQuestionOptionTypeEnum.DATE.getCode().equals(optionType)) {
            List<String> dateStrList = options.stream().map(ScaleQuestionOption::getValue).toList();
            UniformValidationResult dateUniformValidationResult = DateUniformValidator
                    .validateUniformDates(dateStrList);
            if (dateUniformValidationResult.isValid()) {
                return dateUniformValidationResult; // 先判断日期格式
            } else {
                return DateUniformValidator.validateUniformTimes(dateStrList); // 后判断时间格式，如果都不通过说明不是date类型
            }
        }
        return new UniformValidationResult(false, null, "验证失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ScaleQuestion question, List<ScaleQuestionOption> optionList) {
        boolean result = scaleService.vaildCompletedStatus(question.getScaleId());
        if (result) {
            throw new BusinessException("创建失败,已完成的量表无法进行编辑操作!");
        }
        result = this.vaildQuestionOption(optionList);
        if (!result) {
            throw new BusinessException("创建失败,题目答案选项未设置!");
        }
        result = this.vaildQuestionOptionLabel(optionList);
        if (result) {
            throw new BusinessException("创建失败,题目答案选项标签不允许重复!");
        }
        result = this.vaildQuestionNumber(question);
        if (result) {
            throw new BusinessException("创建失败,题号或子题号已经存在!");
        }
        result = this.vaildQuestionNumberRelationship(question);
        if (!result) {
            throw new BusinessException("创建失败,父题号信息不存在!");
        }

        question.setSort(ObjUtil.defaultIfNull(question.getSort(), 0));
        question.setRequireAnswer(ObjUtil.defaultIfNull(question.getRequireAnswer(), false));
        scaleQuestionMapper.insert(question);
        scaleQuestionOptionService.create(question.getScaleId(), question.getId(), optionList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(ScaleQuestion question, List<ScaleQuestionOption> optionList) {
        boolean result = scaleService.vaildCompletedStatus(question.getScaleId());
        if (result) {
            throw new BusinessException("修改失败,已完成的量表无法进行编辑操作!");
        }
        result = this.vaildQuestionOption(optionList);
        if (!result) {
            throw new BusinessException("修改失败,题目答案选项未设置!");
        }
        result = this.vaildQuestionOptionLabel(optionList);
        if (result) {
            throw new BusinessException("修改失败,题目答案选项标签不允许重复!");
        }
        result = this.vaildQuestionNumber(question);
        if (result) {
            throw new BusinessException("修改失败,题号或子题号已经存在!");
        }
        result = this.vaildQuestionNumberRelationship(question);
        if (!result) {
            throw new BusinessException("修改失败, 父题号信息不存在!");
        }
        ScaleQuestion rawData = scaleQuestionMapper.selectById(question.getId());
        BeanUtils.copyProperties(question, rawData);
        scaleQuestionMapper.updateById(rawData);
        scaleQuestionOptionService.modify(question.getScaleId(), question.getId(), optionList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long scaleId, Long questionId) {
        validDeleteItem(scaleId, questionId);
        scaleQuestionMapper.deleteById(questionId);
        scaleQuestionOptionService.deleteByScaleIdAndQuestionId(scaleId, questionId);
    }

    private void validDeleteItem(Long scaleId, Long questionId) {
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("删除失败,量表scaleId不允许为空!");
        }
        if (ObjUtil.isNull(questionId)) {
            throw new BusinessException("删除失败,题目questionId不允许为空!");
        }
        boolean result = scaleService.vaildCompletedStatus(scaleId);
        if (result) {
            throw new BusinessException("删除失败,已完成的量表无法进行删除操作!");
        }
        result = this.vaildQuestionChildrenExists(scaleId, questionId);
        if (result) {
            throw new BusinessException("删除失败,当前题目存在子题数据,请先删除子题数据!");
        }
        String factorName = scaleFactorService.checkFactorRelationQuestion(scaleId, questionId);
        if (StrUtil.isNotEmpty(factorName)) {
            throw new BusinessException("删除失败,题目存在关联的因子维度数据!(" + factorName + ")");
        }
        result = scaleQuestionRelationService.checkRelationQuestion(scaleId, questionId);
        if (result) {
            throw new BusinessException("删除失败,题目存在关联的逻辑跳转数据!");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delAll(Long scaleId) {
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("删除失败,量表scaleId不允许为空!");
        }
        // 校验量表下的所有题目
        List<ScaleQuestionQueryDTO> byScaleId = scaleQuestionMapper.findByScaleId(scaleId);
        if (CollUtil.isNotEmpty(byScaleId)) {
            byScaleId.forEach(vo -> validDeleteItem(scaleId, vo.getId()));
            scaleQuestionMapper.deleteByScaleId(scaleId);
        }
    }

    @Override
    public Page<ScaleQuestionQueryDTO> selectPage(Long scaleId) {
        List<ScaleQuestionOption> optionList = scaleQuestionOptionService.findByScaleId(scaleId);
        return Page.doSelectPage(() -> scaleQuestionMapper.findByScaleId(scaleId))
                .toPage(datas -> this.buildScaleQuestion(scaleId, datas, optionList));
    }

    @Override
    public List<ScaleQuestionQueryDTO> findByScaleId(Long scaleId) {
        List<ScaleQuestionQueryDTO> dtoList;
        try {
            // 获取量表关联的题目信息
            List<ScaleQuestionQueryDTO> list = scaleQuestionMapper.findByScaleId(scaleId);
            // 获取量表关联的选项信息,此处一次性查出量表关联的选项，避免在题目list中循环去获取选项信息
            List<ScaleQuestionOption> optionList = scaleQuestionOptionService.findByScaleId(scaleId);
            // 封装返回数据结果
            dtoList = this.buildScaleQuestion(scaleId, list, optionList);
        } catch (Exception e) {
            dtoList = Lists.newArrayList();
            log.error("根据量表ID获取题目列表发生异常,量表:{}", scaleId, e);
        }
        return dtoList;
    }

    private List<ScaleQuestionQueryDTO> buildScaleQuestion(Long scaleId, List<ScaleQuestionQueryDTO> list,
            List<ScaleQuestionOption> optionList) {
        if (CollUtil.isEmpty(list)) {
            return List.of();
        }
        List<ScaleQuestionRelation> relationList = scaleQuestionRelationService.findByScaleIdAndQuestionId(scaleId,
                null);
        return list.stream().peek(question -> {
            Long questionId = question.getId();
            question.setAnswer(this.getAnswerText(scaleId, questionId, optionList));
            question.setAnswerScore(this.getAnswerScoreText(scaleId, questionId, optionList));
            question.setProperty(this.getPropertyText(scaleId, questionId, optionList));
            question.setOptions(this.filterQuestionOption(scaleId, questionId, optionList));
            question.setStrategyResult(this.getStrategy(questionId, relationList));
            question.setToQuestionId(null);
        }).collect(Collectors.toList());
    }

    private List<ScaleStrategyRelationDTO> getStrategy(Long questionId, List<ScaleQuestionRelation> relationList) {
        List<ScaleStrategyRelationDTO> result = new ArrayList<>();
        // 处理每个题目的跳转逻辑(建议优化跳转逻辑、题号、子题号的整体设计)
        for (JumpEnum jumpEnum : JumpEnum.values()) {
            Integer strategyType = jumpEnum.getCode();
            List<ScaleStrategyRelationDTO> jumpLogics = this.parseStrategy(strategyType, questionId, relationList);
            if (CollUtil.isEmpty(jumpLogics)) {
                continue;
            }
            result.addAll(jumpLogics);
        }
        return result;
    }

    private List<ScaleStrategyRelationDTO> parseStrategy(Integer strategyType, Long fromQuestionId,
            List<ScaleQuestionRelation> relationList) {
        List<ScaleStrategyRelationDTO> results = new ArrayList<>();
        for (ScaleQuestionRelation relation : relationList) {
            if (!strategyType.equals(relation.getType())) {
                continue;
            }
            // 将符合当前题目fromQuestionId的跳转策略进行封装返回。
            ScaleStrategyRelationDTO result = this.filterStrategy(relation, fromQuestionId);
            if (ObjUtil.isNotNull(result)) {
                results.add(result);
            }
        }
        return results;
    }

    private ScaleStrategyRelationDTO filterStrategy(ScaleQuestionRelation relation, Long fromQuestionId) {
        // 策略格式:[{"questionId":621917490251525,"lable":"A","subNumber":null,"questionNumber":"2"}]
        String strategyStr = relation.getStrategy();
        Long toQuestionId = relation.getQuestionId();
        Integer strategyType = relation.getType();
        String reminder = relation.getReminder();
        List<ScaleStrategyDTO> list = JSONObject.getInstance().parseObject(strategyStr, new TargetType<>() {
        });
        list = CollUtil.isEmpty(list) ? List.of() : list;
        List<ScaleStrategyDTO> dtoList = list.stream()
                .filter(strategy -> fromQuestionId.equals(strategy.getQuestionId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(dtoList)) {
            return null;
        }
        return ScaleStrategyRelationDTO.builder()
                .type(strategyType)
                .condition(list)
                .reminder(reminder)
                .toQuestionId(toQuestionId).build();
    }

    private String getAnswerText(Long scaleId, Long questionId, List<ScaleQuestionOption> optionList) {
        optionList = CollUtil.isEmpty(optionList) ? List.of() : optionList;
        // 拼接问题答案格式:很好|一般|不舒服|差
        return optionList.stream()
                .filter(option -> option.getScaleId().equals(scaleId) && option.getQuestionId().equals(questionId))
                .map(ScaleQuestionOption::getValue).collect(Collectors.joining(SymbolConstant.DELIMITER));
    }

    private String getAnswerScoreText(Long scaleId, Long questionId, List<ScaleQuestionOption> optionList) {
        optionList = CollUtil.isEmpty(optionList) ? List.of() : optionList;
        // 拼接答案得分: 4|3|2|1
        return optionList.stream()
                .filter(option -> option.getScaleId().equals(scaleId) && option.getQuestionId().equals(questionId))
                .map(option -> String.valueOf(option.getScore())).collect(Collectors.joining(SymbolConstant.DELIMITER));
    }

    private String getPropertyText(Long scaleId, Long questionId, List<ScaleQuestionOption> optionList) {
        optionList = CollUtil.isEmpty(optionList) ? List.of() : optionList;
        // 拼接答案性质：阳|阴|阴|阴
        return optionList.stream()
                .filter(option -> option.getScaleId().equals(scaleId) && option.getQuestionId().equals(questionId))
                .map(option -> PropertyEnum.getValue(option.getResult()))
                .collect(Collectors.joining(SymbolConstant.DELIMITER));
    }

    private List<ScaleQuestionOption> filterQuestionOption(Long scaleId, Long questionId,
            List<ScaleQuestionOption> optionList) {
        optionList = CollUtil.isEmpty(optionList) ? List.of() : optionList;
        return optionList.stream()
                .filter(option -> option.getScaleId().equals(scaleId) && option.getQuestionId().equals(questionId))
                .collect(Collectors.toList());
    }

    @Override
    public ScaleQuestionQueryDTO detail(Long scaleId, Long questionId) {
        ScaleQuestionQueryDTO question = scaleQuestionMapper.findByScaleIdAndQuestionId(scaleId, questionId);
        question.setOptions(scaleQuestionOptionService.findByScaleIdAndQuestionId(scaleId, questionId));
        return question;
    }

    @Override
    public Integer getNumOfQuestion(Long scaleId) {
        return scaleQuestionMapper.getNumOfQuestion(scaleId);
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        scaleQuestionHandler.downloadTemplate(response);
    }

    @Override
    public void exportScaleQuestion(Long scaleId, HttpServletResponse response) {
        // 根据量表ID获取题目信息（如果后期题目数据量多大,可考虑分页查询，多线程进行导出进行优化）
        List<ScaleQuestionQueryDTO> questionList = this.findByScaleId(scaleId);
        scaleQuestionHandler.exportExcel(response, "题目答案导出", questionList);
    }

    @Override
    public void importScaleQuestion(Long scaleId, MultipartFile file) {
        if (ObjUtil.isNull(scaleId)) {
            throw new BusinessException("导入失败,量表ID不允许为空!");
        }
        boolean result = scaleService.vaildCompletedStatus(scaleId);
        if (result) {
            throw new BusinessException("导入失败,已完成的量表无法进行编辑操作!");
        }
        try {
            scaleQuestionHandler.importExcel(scaleId, file);
        } catch (Exception e) {
            log.error("导入量表题目失败", e);
            throw new BusinessException("导入失败:" + e.getMessage());
        }
    }

    @Override
    public ScaleQuestion findByQuestionId(Long questionId) {
        return baseMapper.findByQuestionId(questionId);
    }

    @Override
    public void copy(Long scaleId, Long questionId) {
        List<ScaleQuestion> scaleQuestionList = baseMapper.findListByScaleId(scaleId);
        if (ObjectUtil.isEmpty(scaleQuestionList)) {
            throw new BusinessException("量表下没有题目!");
        }
        ScaleQuestion scaleQuestion = findByQuestionId(questionId);
        if (scaleQuestion == null) {
            throw new BusinessException("题目复制失败!");
        }
        // 获取最大题号
        Integer questionMaxNo = getMaxQuestionNumber(scaleQuestionList);
        if (questionMaxNo == null) {
            throw new BusinessException("未找到有效题目!");
        }

        ScaleQuestion rawData = new ScaleQuestion();
        BeanUtils.copyProperties(scaleQuestion, rawData);
        rawData.setId(idGenerator.nextId());
        // 复制最大的题号+1=新题号
        rawData.setQuestionNumber(String.valueOf((questionMaxNo + 1)));
        // 判断是否子题号
        save(rawData);
    }

    private static Integer getMaxQuestionNumber(List<ScaleQuestion> questions) {
        if (questions == null || questions.isEmpty()) {
            return null;
        }

        // 找到最大的数字值
        Optional<Integer> maxNum = questions.stream()
                .filter(q -> isNumeric(q.getQuestionNumber()))
                .map(q -> Integer.parseInt(q.getQuestionNumber()))
                .max(Integer::compare);
        return maxNum.orElse(null);
    }

    /**
     * 判断字符串是否为纯数字
     * 
     * @param str 待判断的字符串
     * @return 是纯数字返回true，否则false
     */
    private static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        // 正则匹配数字（支持正负数和零）
        return str.matches("-?\\d+");
    }

    @EventListener
    public void onoApplication(ScaleDelEvent event) {
        scaleQuestionMapper.deleteByScaleId(event.getOldScaleId());
    }

    @EventListener
    public void onoApplication(ScaleCreateEvent event) {
        Long oldScaleId = event.getOldScaleId();
        Long newScaleId = event.getNewScaleId();
        List<ScaleQuestionQueryDTO> list = scaleQuestionMapper.findByScaleId(oldScaleId);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        // 设置新老题号的对应关系
        Map<Long, Long> oldToNewIdMap = new HashMap<>(8);
        // 题目选项和关联的跳转逻辑可通过批量插入进行优化（延期处理）
        list.forEach(item -> {
            ScaleQuestion question = new ScaleQuestion();
            BeanUtils.copyProperties(item, question);
            Long oldQuestionId = item.getId();
            question.setId(null);
            question.setScaleId(newScaleId);
            scaleQuestionMapper.insert(question);
            Long newQuestionId = question.getId();
            oldToNewIdMap.put(oldQuestionId, newQuestionId);
        });
        scaleQuestionOptionService.saveScaleQuestionOption(event, oldToNewIdMap);
        scaleQuestionRelationService.saveScaleQuestionRelation(event, oldToNewIdMap);
    }

    @Override
    public List<ScaleQuestion> findEntitysByScaleId(Long scaleId) {
        return baseMapper.findListByScaleId(scaleId);
    }
}
