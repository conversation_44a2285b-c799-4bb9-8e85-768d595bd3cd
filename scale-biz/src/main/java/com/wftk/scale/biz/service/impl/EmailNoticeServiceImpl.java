//package com.wftk.scale.biz.service.impl;
//
//import com.wftk.scale.biz.ext.notice.enums.NoticeTypeEnum;
//import com.wftk.scale.biz.ext.notice.dto.EmailDto;
//import com.wftk.scale.biz.service.NoticeService;
//import org.springframework.stereotype.Service;
//import org.springframework.util.Assert;
//
///**
// * <AUTHOR>
// */
//@Service
//public class EmailNoticeServiceImpl implements NoticeService<EmailDto> {
//
//    @Override
//    public NoticeTypeEnum getNoticeType() {
//        return NoticeTypeEnum.EMAIL;
//    }
//
//    @Override
//    public void sendNotice(EmailDto email) {
//        Assert.hasLength(email.getUserIds(), "接收用户ID不能为空");
//        Assert.hasLength(email.getContent(), "发送内容不能为空");
//    }
//}
