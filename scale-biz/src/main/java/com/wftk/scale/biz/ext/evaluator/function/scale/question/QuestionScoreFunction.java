package com.wftk.scale.biz.ext.evaluator.function.scale.question;

import java.util.Map;

import com.googlecode.aviator.runtime.type.AviatorNumber;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;
import com.wftk.scale.biz.ext.evaluator.function.scale.BaseScaleEvaluationFunction;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 根据量表题目ID获取所选答案的分数
 * 示例: q_score("1"), 代表获取ID为1的选项的分数（注意：参数是ID，所以可能需要结合选择题目的函数配合使用）
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
public class QuestionScoreFunction extends BaseScaleEvaluationFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Question.Q_SCORE;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env, final AviatorObject arg1) {
        String questionId = (String) arg1.getValue(env);
        logger.info("q_score: questionId: {}", questionId);
        if (StrUtil.isBlank(questionId)) {
            throw new IllegalArgumentException("questionId must not be blank");
        }
        if (!NumberUtil.isNumber(questionId)) {
            throw new IllegalArgumentException("questionId must be a number");
        }
        ScaleUserResultRecord record = getUserResultOption(env, Long.valueOf(questionId));
        if (record == null) {
            throw new IllegalArgumentException("questionId not found");
        }
        return AviatorNumber.valueOf(record.getScore());
    }

}
