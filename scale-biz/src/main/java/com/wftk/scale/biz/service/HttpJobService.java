package com.wftk.scale.biz.service;

import com.wftk.scale.biz.entity.HttpJob;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02 15:21:14
 */
public interface HttpJobService extends IService<HttpJob> {

    HttpJob createJob(String bizId,String bizCode,Integer status,String params);

    HttpJob getJobByBizIdAndBizCoce(String bizId,String bizCode);

    Integer updateHttpJobResultByBizCodeAndBizId(HttpJob httpJob);

    Integer updateStatusByBizCodeAndBizId(HttpJob httpJob);

    Integer updateStatusByBizCodeAndBizIds(String bizCode, LocalDateTime nextTime,
                                           Integer status, List<Long> bizIds);

    List<HttpJob> selectNotEndList();

}
