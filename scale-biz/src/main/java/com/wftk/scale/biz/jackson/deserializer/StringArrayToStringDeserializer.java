package com.wftk.scale.biz.jackson.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

/**
 * 将数组反序列化为逗号分隔的字符串
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
public class StringArrayToStringDeserializer extends JsonDeserializer<String> {

    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        if (p.currentToken() == JsonToken.START_ARRAY) {
            String[] strings = p.readValueAs(String[].class);
            return String.join(",", strings);
        }
        return p.getValueAsString();
    }

}
