package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleTypeDetailDTO;
import com.wftk.scale.biz.entity.ScaleType;

import java.util.List;

/**
 * <p>
 * 量表类型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleTypeService extends IService<ScaleType> {
    
    /* 
     * @Author: mq
     * @Description: 校验量表分类名称是否已经存在
     * @Date: 2024/10/25 11:05
     * @Param: scaleTypeId-主键ID(修改量表分类)
     * @Param: scaleTypeName-量表分类名称
     * @return: boolean 
     **/
    boolean validScaleTypeName(Long scaleTypeId, String scaleTypeName);

    /*
     * @Author: mq
     * @Description: 校验量表分类名称是否已经存在
     * @Date: 2024/10/25 11:05
     * @Param: scaleTypeId-主键ID(修改量表分类)
     * @Param: scaleTypeCode-量表分类编号
     * @return: boolean
     **/
    boolean validScaleTypeCode(Long scaleTypeId, String scaleTypeCode);

    /* 
     * @Author: mq
     * @Description: 创建量表分类信息
     * @Date: 2024/10/25 10:14 
     * @Param: scaleType-量表分类信息
     * @return: void 
     **/
    void create(ScaleType scaleType);

    /*
     * @Author: mq
     * @Description: 根据ID删除量表信息
     * @Date: 2024/10/25 10:17
     * @Param: scaleTypeId-量表类型主键ID
     * @return: void
     **/
    void delete(Long scaleTypeId);

    /* 
     * @Author: mq
     * @Description: 修改量表分类信息
     * @Date: 2024/10/25 10:16
     * @Param: scaleType-量表分类信息
     * @return: void 
     **/
    void modify(ScaleType scaleType);

    /*
     * @Author: mq
     * @Description: 根据条件检索量表分类信息
     * @Date: 2024/10/25 10:24
     * @Param: scaleTypeName-量表分类名称
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.scale.ScaleTypeDetailDTO>
     **/
    Page<ScaleTypeDetailDTO> selectPage(String scaleTypeName);

    /*
    * @Author: mq
    * @Description: 获取可用的量表分类列表
    * @Date: 2024/11/26 13:47
    * @Param: scaleTypeName
    * @return: List<ScaleTypeDetailDTO>
    **/
    List<ScaleTypeDetailDTO> getListOfEnabled(String scaleTypeName);


    List<ScaleType> getListByTerminalCode(String terminalCode);

    List<ScaleTypeDetailDTO> ownerScaleTypes(String terminalCode);
}
