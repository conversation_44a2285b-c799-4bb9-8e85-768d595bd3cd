package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.scale.*;
import com.wftk.scale.biz.entity.ScaleCombination;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 组合量表(量表每次修改后均在此表写入一条数据) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleCombinationMapper extends BaseMapper<ScaleCombination> {

    /* 
     * @Author: mq
     * @Description: 校验量表编码是否存在
     * @Date: 2024/11/6 16:38 
     * @Param: scaleCombinationId-组合量表ID
     * @Param: scaleCode-组合量表ID
     * @Param: scaleName-组合量表名称
     * @return: boolean
     **/
    boolean validScaleNameExists(@Param("scaleCombinationId") Long scaleCombinationId, @Param("scaleCode") String scaleCode, @Param("scaleName") String scaleName);

    /* 
     * @Author: mq
     * @Description: 根据条件检索量表信息
     * @Date: 2024/11/6 17:53 
     * @Param: scaleName-量表名称
     * @return: java.util.List<com.wftk.scale.biz.dto.scale.ScaleCombinationQueryDTO> 
     **/
    List<ScaleCombinationQueryDTO> getList(@Param("scaleName") String scaleName);

    /*
     * @Author: mq
     * @Description: 根据ID更新量表完成状态
     * @Date: 2024/11/7 9:59
     * @Param: scaleCombinationId-组合量表ID
     * @Param: status-完成状态
     * @Param: opUser-操作人
     * @return: void
     **/
    void updateComplateStatus(@Param("scaleCombinationId") Long scaleCombinationId, @Param("status") Integer status, @Param("opUser") String opUser);

    /**
     * 获取最新版本量表
     * @param code
     * @return
     */
    ScaleCombination getLatestScaleByCode(@Param("code")String code);

    /**
     * 查询已上架组合量表
     *
     * @param scaleCode
     * @param terminalCode
     * @param scaleName
     * @param type
     * @param listingShowType
     * @return
     */
    List<ScaleCombinationDTO> getListedScaleCombination(@Param("scaleCode")String scaleCode, @Param("terminalCode")String terminalCode,
                                                        @Param("scaleName")String scaleName, @Param("type")Integer type,
                                                        @Param("listingShowType")Integer listingShowType);

    /**
     * 查询详情 （单个量表列表） 需要修改
     * @param scaleCombinationId
     * @param terminalCode
     * @return
     */
    List<ScaleDTO> getListedScaleCombinationDetail(@Param("scaleCombinationId")Long scaleCombinationId, @Param("terminalCode")String terminalCode);

    List<ScaleListingDetailDTO> getScaleListingDetailDTO(@Param("scaleSerials")List<ScaleSerialDTO> scaleSerials, @Param("terminalCode")String terminalCode);

    ScaleListingDetailDTO getScaleListingDetail(@Param("scaleCode")String scaleCode,@Param("listingId")Long listingId,@Param("terminalCode")String terminalCode);
}
