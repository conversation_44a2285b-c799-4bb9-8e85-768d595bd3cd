package com.wftk.scale.biz.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 生成二维码工具类
 */
@Slf4j
public class QrImgUtils {

    // 二维码尺寸
    private static final int QRCODE_SIZE = 210;

    private static final String FORMAT = "png";

    private static final int WHITE = Color.WHITE.getRGB();

    private static final int GRAY = Color.GRAY.getRGB();

    /**
     * 用于设置QR二维码参数
     */
    private static final Map<EncodeHintType, Object> HINTS = new HashMap<>() {
        {
            // 设置QR二维码的纠错级别（H为最高级别）具体级别信息
            put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            // 设置编码方式
            put(EncodeHintType.CHARACTER_SET, StandardCharsets.UTF_8);
            put(EncodeHintType.MARGIN, 0);
        }
    };

    /**
     * 返回图像的base64字符串,前端格式 <img src="data:image/png;base64,xxxx" />
     * @param scaleUrl 量表访问地址
     * @param logoFile log文件，可为空
     */
    public static String generate(String scaleUrl, File logoFile) {
        try(ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
            // 参数顺序分别为：编码内容，编码类型，生成图片宽度，生成图片高度，设置参数
            BitMatrix bm = multiFormatWriter.encode(scaleUrl, BarcodeFormat.QR_CODE, QRCODE_SIZE, QRCODE_SIZE, HINTS);
            BufferedImage image = new BufferedImage(QRCODE_SIZE, QRCODE_SIZE, BufferedImage.TYPE_INT_ARGB);
            // 开始利用二维码数据创建Bitmap图片，分别设为黑（0xFFFFFFFF）白（0xFF000000）两色
            for (int x = 0; x < QRCODE_SIZE; x++) {
                for (int y = 0; y < QRCODE_SIZE; y++) {
                    image.setRGB(x, y, bm.get(x, y) ? GRAY : WHITE);
                }
            }
            addLogo(image, logoFile);
            ImageIO.write(image, FORMAT, outputStream);
            return Base64.getEncoder().encodeToString(outputStream.toByteArray());
        }catch (Exception e){
            log.error("生成二维码失败", e);
            return "";
        }
    }

    private static void addLogo(BufferedImage image, File logoFile) throws IOException {
        if(logoFile == null || !logoFile.exists()){
            return;
        }
        BufferedImage logo = ImageIO.read(logoFile);
        Graphics2D graph = image.createGraphics();
        graph.drawImage(logo, image.getWidth() * 3 / 8, image.getHeight() * 3 / 8
                , image.getWidth() / 4, image.getHeight() / 4, null);
        graph.dispose();
    }
}
