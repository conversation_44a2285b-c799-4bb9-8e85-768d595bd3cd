package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleQuestionHighestOverallScoreDTO;
import com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO;
import com.wftk.scale.biz.entity.ScaleQuestion;
import com.wftk.scale.biz.entity.ScaleQuestionOption;
import com.wftk.scale.biz.valid.date.UniformValidationResult;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 题目答案表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleQuestionService extends IService<ScaleQuestion> {

    /*
     * @Author: mq
     * @Description: 校验量表题目内容是否有设置
     * @Date: 2024/11/4 14:35
     * @Param: scaleId-量表ID
     * @return: boolean
     **/
    boolean vaildQuestionDataIntegrity(Long scaleId);

    /*
    * @Author: mq
    * @Description: 校验量表题目是否存在子题数据
    * @Date: 2024/12/16 10:24
    * @Param: scaleId
     * @Param: questionId
    * @return: boolean
    **/
    boolean vaildQuestionChildrenExists(Long scaleId, Long questionId);

    /*
     * @Author: mq
     * @Description: 校验量表题目编号是否存在
     * @Date: 2024/11/5 11:31
     * @Param: question-题目信息
     * @return: boolean
     **/
    boolean vaildQuestionNumber(ScaleQuestion question);

    /*
     * @Author: mq
     * @Description: 校验量表题目选项标签是否有重复
     * @Date: 2024/12/3 18:11
     * @Param: options-题目答题选项信息
     * @return: boolean
     **/
    boolean vaildQuestionOptionLabel(List<ScaleQuestionOption> options);

    /*
     * @Author: mq
     * @Description: 校验量表题目选项是否有设置
     * @Date: 2024/12/3 18:44
     * @Param: options-题目答题选项信息
     * @return: boolean
     **/
    boolean vaildQuestionOption(List<ScaleQuestionOption> options);

    /*
     * @Author: mq
     * @Description: 校验量表题目题号和子题号关系是否设置正确（有子题号时必父题号必须存在）
     * @Date: 2024/12/6 13:38
     * @Param: question-题目答题选项信息
     * @return: boolean
     **/
    boolean vaildQuestionNumberRelationship(ScaleQuestion question);


    /**
     * @param optionType
     * @param options
     * @return
     * @Description: 校验答案的值类型是否正确
     */
    UniformValidationResult vaildOptionTypeRight(Integer optionType, List<ScaleQuestionOption> options);

    /*
     * @Author: mq
     * @Description: 创建量表关联的题目信息
     * @Date: 2024/11/4 17:31
     * @Param: scaleQuestion-量表题目信息
     * @Param: options-题目答题选项信息
     * @return: void
     **/
    void create(ScaleQuestion question, List<ScaleQuestionOption> optionList);

    /*
     * @Author: mq
     * @Description: 修改量表关联的题目信息
     * @Date: 2024/11/4 17:34
     * @Param: scaleQuestion-量表题目信息
     * @return: void
     **/
    void modify(ScaleQuestion question, List<ScaleQuestionOption> optionList);

    /*
     * @Author: mq
     * @Description: 根据题目ID删除题目信息
     * @Date: 2024/11/5 9:15
     * @Param: questionId-题目ID
     * @return: void
     **/
    void delete(Long scaleId, Long questionId);

    /*
     * @Author: mq
     * @Description: 根据量表ID删除全部题目信息
     * @Date: 2024/11/5 9:09
     * @Param: scaleId-量表ID
     * @return: void
     **/
    void delAll(Long scaleId);

    /*
     * @Author: mq
     * @Description: 根据条件检索量表题目信息
     * @Date: 2024/11/5 14:05
     * @Param: scaleId-量表ID
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO>
     **/
    Page<ScaleQuestionQueryDTO> selectPage(Long scaleId);

    /*
     * @Author: mq
     * @Description: 根据量表ID获取题目列表信息
     * @Date: 2024/11/20 18:47
     * @Param: scaleId-量表ID
     * @return: List<ScaleQuestionQueryDTO>
     **/
    List<ScaleQuestionQueryDTO> findByScaleId(Long scaleId);

    /*
     * @Author: mq
     * @Description: 获取题目详情信息
     * @Date: 2024/11/5 15:16
     * @Param: scaleId-量表ID
     * @Param: questionId-题目ID
     * @return: com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO
     **/
    ScaleQuestionQueryDTO detail(Long scaleId, Long questionId);

    /*
     * @Author: mq
     * @Description: 统计量表包含的题目数量
     * @Date: 2024/11/7 9:37
     * @Param: scaleId-量表ID
     * @return: java.lang.Integer
     **/
    Integer getNumOfQuestion(Long scaleId);

    /*
     * @Author: mq
     * @Description:下载题目模板
     * @Date: 2024/11/6 13:32
     * @Param: response-响应
     * @return: void
     **/
    void downloadTemplate(HttpServletResponse response);

    /*
     * @Author: mq
     * @Description: 导出题目数据
     * @Date: 2024/11/6 13:32
     * @Param: scaleId-量表ID
     * @Param: response-响应
     * @return: void
     **/
    void exportScaleQuestion(Long scaleId, HttpServletResponse response);

    /*
     * @Author: mq
     * @Description: 导入题目数据
     * @Date: 2024/11/6 13:32
     * @Param: file-导入文件
     * @Param: response-响应
     * @return: void
     **/
    void importScaleQuestion(Long scaleId, MultipartFile file);


    /*
     * @Author: funian
     * @Description: 题目信息
     * @Date: 2025/08/26 19:20
     * @Param: questionId-题目ID
     * @Param: response-响应
     * @return: ScaleQuestion
     **/
    ScaleQuestion findByQuestionId(Long questionId);

    /*
     * 复制量表题目
     * @param scaleId
     * @param questionId
     * @return
     */
    void copy(Long scaleId, Long questionId);


    /**
     * 根据量表ID获取题目列表信息
     * @param scaleId
     * @return
     */
    List<ScaleQuestion> findEntitysByScaleId(Long scaleId);
}
