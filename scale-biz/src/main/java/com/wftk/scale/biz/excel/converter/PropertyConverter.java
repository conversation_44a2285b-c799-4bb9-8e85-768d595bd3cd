package com.wftk.scale.biz.excel.converter;


import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.wftk.scale.biz.constant.enums.PropertyEnum;

/**
 * @ClassName: PropertyConverter
 * @Description: 将阴阳性质数据对象转换为Excel单元格的值
 * @Author: mq
 * @Date: 2024/11/28
 * @Version: 1.0
 **/
public class PropertyConverter implements Converter<Boolean> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Boolean.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Boolean convertToJavaData(ReadConverterContext<?> context) {
        return PropertyEnum.getStatus(context.getReadCellData().getStringValue());
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Boolean> context) {
        return new WriteCellData<String>(PropertyEnum.getValue(context.getValue()));
    }
}
