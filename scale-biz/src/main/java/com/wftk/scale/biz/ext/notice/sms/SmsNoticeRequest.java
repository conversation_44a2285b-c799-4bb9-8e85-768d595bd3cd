package com.wftk.scale.biz.ext.notice.sms;

import cn.hutool.core.bean.BeanUtil;
import com.wftk.scale.biz.ext.notice.NoticeRequest;
import lombok.Getter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

@Getter
public abstract class SmsNoticeRequest<T> implements NoticeRequest, Serializable {

    @Serial
    private static final long serialVersionUID = 5499440494461967584L;

    /**
     * 模板参数
     */
    private Map<String, Object> params;

    /**
     * 场景
     */
    private String scene;

    /**
     * 手机号
     */
    private String tel;

    public SmsNoticeRequest(String tel, String scene, T t) {
        this.tel = tel;
        this.scene = scene;
        this.params = BeanUtil.beanToMap(t);
    }
}
