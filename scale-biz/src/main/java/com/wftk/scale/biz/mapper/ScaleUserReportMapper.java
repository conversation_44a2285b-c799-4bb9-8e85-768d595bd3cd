package com.wftk.scale.biz.mapper;

import com.wftk.scale.biz.dto.report.UserReportQueryDTO;
import com.wftk.scale.biz.entity.ScaleUserReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.vo.ScaleUserReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05 11:14:30
 */
public interface ScaleUserReportMapper extends BaseMapper<ScaleUserReport> {

    ScaleUserReport getByResultId(@Param("resultId")Long resultId);

    List<ScaleUserReportVO> queryPage(UserReportQueryDTO dto);
}
