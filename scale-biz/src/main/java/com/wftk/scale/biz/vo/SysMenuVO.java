package com.wftk.scale.biz.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SysMenuVO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 上级菜单ID
     */
    private Long parentId;
    /**
     * 上级菜单名称
     */
    private String parentName;
    /**
     * 菜单类型 1 模块 2 菜单 3 按钮
     */
    private Integer type;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 路由名称
     */
    private String routeName;
    /**
     * 路由地址
     */
    private String routePath;
    /**
     * 组件名称
     */
    private String componentName;
    /**
     * 组件地址
     */
    private String componentPath;
    /**
     * 权限字符
     */
    private String permissionCode;
    /**
     * 显示排序
     */
    private Integer sort;
    /**
     * 图标
     */
    private String icon;
    /**
     * 菜单描述
     */
    private String description;
    /**
     * 1为隐藏，0不隐藏
     */
    private Boolean hidden;
    /**
     * 菜单状态
     */
    private Boolean enable;
    /**
     * 是否缓存
     */
    private Boolean cache;
    /**
     * 子菜单
     */
    private List<SysMenuVO> children = new ArrayList<>(10);
}