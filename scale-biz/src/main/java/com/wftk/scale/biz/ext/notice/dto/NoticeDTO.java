//package com.wftk.scale.biz.ext.notice.dto;
//
//import com.wftk.scale.biz.ext.notice.enums.NoticeTypeEnum;
//import lombok.Data;
//
//import java.util.LinkedHashMap;
//
///**
// * <AUTHOR>
// */
//@Data
//public class NoticeDTO {
//
//    /**
//     * 通知类型
//     */
//    private NoticeTypeEnum noticeType;
//    /**
//     * 通知对象，多个用逗号隔开
//     */
//    private String userIds;
//
//    /**
//     * 构建邮件或者短信实体，如需扩展，可自行添加
//     * @param noticeType  通知类型 SMS或EMAIL
//     * @param userIds     通知对象，多个用逗号隔开
//     * @param content     通知类容，通知类型为邮件才需要
//     * @param subject     邮件主题，通知类型为邮件才需要
//     * @param scene       短信场景，通知类型为短信才需要
//     * @param params      参数，通知类型为短信才需要
//     */
//    public static NoticeDTO buildNotice(NoticeTypeEnum noticeType,
//                                        String userIds,
//                                        String content,
//                                        String subject,
//                                        String scene,
//                                        LinkedHashMap<String, Object> params) {
//        switch (noticeType) {
//            case EMAIL -> {
//                return new EmailDto(content, subject, userIds);
//            }
//            case SMS -> {
//                return new SmsDTO(params, scene, userIds);
//            }
//        }
//        return null;
//    }
//}
