package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.SystemTag;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface SystemTagMapper extends BaseMapper<SystemTag> {

    /*
     * @Author: mq
     * @Description: 校验预警标签编号是否已经存在
     * @Date: 2024/10/25 14:32
     * @Param: tagId-标签ID
     * @Param: tagCode-标签编号
     * @return: boolean
     **/
    boolean validSystemTagCodeExist(@Param("tagId") Long tagId, @Param("tagCode") String tagCode);

    /*
     * @Author: mq
     * @Description: 根据ID更新预警设置数据的状态
     * @Date: 2024/10/25 14:55
     * @Param: tagId-主键ID
     * @Param: enable-是否禁用
     * @return: void
     **/
    void updateEnable(@Param("tagId") Long tagId, @Param("enable") Boolean enable, @Param("opUser") String opUser);

    /*
     * @Author: mq
     * @Description: 根据条件查询预警标签数据
     * @Date: 2024/10/25 14:33
     * @Param: tagName-标签名称
     * @Param: enable-是否启用(0.未启用、 1.已启用)
     * @return: java.util.List<com.wftk.scale.biz.entity.SystemTag>
     **/
    List<SystemTag> getList(@Param("tagName") String tagName,@Param("enable") Boolean enable);
}
