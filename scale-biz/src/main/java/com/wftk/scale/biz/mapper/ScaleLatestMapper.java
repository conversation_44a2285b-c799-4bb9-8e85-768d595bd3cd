package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.ScaleLatest;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 量表最新版本 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleLatestMapper extends BaseMapper<ScaleLatest> {

    /* 
     * @Author: mq
     * @Description: 根据量表编码获取最新版本信息
     * @Date: 2024/11/1 15:28 
     * @Param: code-量表编码
     * @return: com.wftk.scale.biz.entity.ScaleLatest 
     **/
    ScaleLatest findByCode(@Param("code") String code);

}
