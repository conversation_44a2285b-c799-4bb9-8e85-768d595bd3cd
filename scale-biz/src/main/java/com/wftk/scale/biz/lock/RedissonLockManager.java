package com.wftk.scale.biz.lock;

import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DReadWriteLock;
import com.wftk.lock.spring.boot.autoconfigure.core.factory.DLockFactory;
import com.wftk.lock.spring.boot.autoconfigure.core.factory.DReadWriteLockFactory;

public class RedissonLockManager implements LockManager{

    private final DLockFactory<?> dLockFactory;
    private final DReadWriteLockFactory<?> dReadWriteLockFactory;

    public RedissonLockManager(DLockFactory<?> dLockFactory, DReadWriteLockFactory<?> dReadWriteLockFactory) {
        this.dLockFactory = dLockFactory;
        this.dReadWriteLockFactory = dReadWriteLockFactory;
    }

    @Override
    public DLock getLock(String key) {
        return dLockFactory.get(key);
    }

    @Override
    public DReadWriteLock getReadWriteLock(String key) {
        return dReadWriteLockFactory.get(key);
    }



}
