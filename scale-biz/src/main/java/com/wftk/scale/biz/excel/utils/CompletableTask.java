package com.wftk.scale.biz.excel.utils;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class CompletableTask implements Runnable {

    private final Authentication authentication;

    private final Runnable runnable;

    public CompletableTask(Authentication authentication, Runnable runnable) {
        this.authentication = authentication;
        this.runnable = runnable;
    }

    @Override
    public void run() {
        try {
            if(authentication != null){
                log.info("task currentAuthUserId:{}", authentication.getAuthUser().getId());
                AuthenticationHolder.setAuthentication(authentication);
            }
            runnable.run();
        }finally {
            AuthenticationHolder.clear();
        }
    }
}
