package com.wftk.scale.biz.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class UserVO {

    /**
     * 用户ID
     */
    private Long id;
    /**
     * 用户编码
     */
    private String code;
    /**
     * 登录账号
     */
    private String account;
    /**
     * 用户姓名
     */
    private String name;
    /**
     * 归属部门
     */
    private String departmentName;
    /**
     * 归属部门
     */
    private Long departmentId;
    /**
     * 1男，2女
     */
    private Integer sex;
    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;
    /**
     * 手机号码
     */
    private String phone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 备注
     */
    private String remark;

    /**
     * 入学年份
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate entryDate;

    /**
     * 1启用，0禁用
     */
    private Boolean enable;
}
