package com.wftk.scale.biz.excel.service.handler;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.entity.SysRole;
import com.wftk.scale.biz.excel.model.SysRoleExcelDataDTO;
import com.wftk.scale.biz.excel.utils.ExcelUtil;
import com.wftk.scale.biz.mapper.SysRoleMapper;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class SysRoleHandler extends ServiceImpl<SysRoleMapper, SysRole> {

    public void exportExcel(HttpServletResponse response, String fileName, List<SysRoleExcelDataDTO> list) {
        ExcelUtil.write(response, SysRoleExcelDataDTO.class, fileName, list);
    }
}
