package com.wftk.scale.biz.converter;

import com.wftk.scale.biz.dto.user.menu.SysCreateMenuDTO;
import com.wftk.scale.biz.dto.user.menu.SysUpdateMenuDTO;
import com.wftk.scale.biz.entity.SysMenu;
import com.wftk.scale.biz.vo.SysMenuVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface SysMenuConverter {

    SysMenuVO sysMenuToSysMenuVO(SysMenu sysMenu);

    SysMenu sysCreateMenuDtoToSysMenu(SysCreateMenuDTO dto);

    SysMenu sysUpdateMenuDtoToSysMenu(SysUpdateMenuDTO dto);
}