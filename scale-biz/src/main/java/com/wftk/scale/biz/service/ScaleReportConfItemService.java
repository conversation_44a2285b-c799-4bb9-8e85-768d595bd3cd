package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.entity.ScaleReportConfItem;

import java.util.List;

/**
 * <p>
 * 报告设置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04 14:29:16
 */
public interface ScaleReportConfItemService extends IService<ScaleReportConfItem> {


    List<ScaleReportConfItem> findByReportConfId(Long reportConfId,List<String> itemCodes);

    List<ScaleReportConfItem> findByReportConfIdAndFactorId( Long reportConfId,
                                                            List<String> itemCodes,
                                                            Long factorId);

    void create(ScaleReportConfItem scaleReportConfItem);

    void modify(ScaleReportConfItem scaleReportConfItem);

    void delete(Long scaleReportConfItemId);

    void saveCopyReportItem(Long oldReportConfId, Long newReportConfId, Long newScaleId, String opUser);

    ScaleReportConfItem getById(Long id);
}
