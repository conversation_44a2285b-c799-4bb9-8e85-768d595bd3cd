package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 支付接入方设置(微信)
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22 14:48:13
 */
@TableName("wechat_pay_setting")
public class WechatPaySetting implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 微信应用ID
     */
    private String appId;

    /**
     * 微信应用秘钥
     */
    private String appSecret;

    /**
     * 应用名
     */
    private String appName;

    /**
     * 微信商户号
     */
    private String mchId;

    /**
     * 商户序列号
     */
    private String mchNo;

    /**
     * API秘钥
     */
    private String apiSecret;

    /**
     * 支持的场景编码(以逗号分隔)
     */
    private String sceneCode;

    /**
     * 是否启用: 0.未启用; 1.已启用;
     */
    private Boolean enabled;

    /**
     * 是否设为默认: 0.非默认; 1.默认;
     */
    private Integer isDefault;

    /**
     * 回调地址(如果接口没传，则会使用此地址)
     */
    private String notifyUrl;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    private String tenantId;

    /**
     * 微信支付API版本号
     */
    private String apiVersion;

    @TableLogic
    private Boolean deleted;

    /**
     * 微信支付证书文件
     */
    private String certFile;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getMchNo() {
        return mchNo;
    }

    public void setMchNo(String mchNo) {
        this.mchNo = mchNo;
    }

    public String getApiSecret() {
        return apiSecret;
    }

    public void setApiSecret(String apiSecret) {
        this.apiSecret = apiSecret;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getApiVersion() {
        return apiVersion;
    }

    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getCertFile() {
        return certFile;
    }

    public void setCertFile(String certFile) {
        this.certFile = certFile;
    }

    @Override
    public String toString() {
        return "WechatPaySetting{" +
            "id = " + id +
            ", appId = " + appId +
            ", appSecret = " + appSecret +
            ", appName = " + appName +
            ", mchId = " + mchId +
            ", mchNo = " + mchNo +
            ", apiSecret = " + apiSecret +
            ", sceneCode = " + sceneCode +
            ", enabled = " + enabled +
            ", isDefault = " + isDefault +
            ", notifyUrl = " + notifyUrl +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
            ", createBy = " + createBy +
            ", updateBy = " + updateBy +
            ", tenantId = " + tenantId +
            ", apiVersion = " + apiVersion +
            ", deleted = " + deleted +
            ", certFile = " + certFile +
        "}";
    }
}
