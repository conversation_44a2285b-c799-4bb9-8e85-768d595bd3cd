package com.wftk.scale.biz.excel.service.handler;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.core.password.PasswordEncoder;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.IdGenerator;
import com.wftk.scale.biz.entity.SysDepartment;
import com.wftk.scale.biz.entity.SysRole;
import com.wftk.scale.biz.entity.SysUser;
import com.wftk.scale.biz.entity.SysUserDepartment;
import com.wftk.scale.biz.entity.SysUserRole;
import com.wftk.scale.biz.excel.model.SelectDataListBO;
import com.wftk.scale.biz.excel.model.SysUserExcelDataDTO;
import com.wftk.scale.biz.excel.utils.AsyncTaskUtils;
import com.wftk.scale.biz.excel.utils.CompletableTask;
import com.wftk.scale.biz.excel.utils.ExcelUtil;
import com.wftk.scale.biz.mapper.SysDepartmentMapper;
import com.wftk.scale.biz.mapper.SysRoleMapper;
import com.wftk.scale.biz.mapper.SysUserDepartmentMapper;
import com.wftk.scale.biz.mapper.SysUserMapper;
import com.wftk.scale.biz.mapper.SysUserRoleMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SysUserHandler extends ServiceImpl<SysUserMapper, SysUser> {

    @Resource
    private SysDepartmentMapper sysDepartmentMapper;
    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private IdGenerator idGenerator;
    @Resource
    private SysUserDepartmentMapper sysUserDepartmentMapper;
    @Resource
    private SysUserRoleMapper sysUserRoleMapper;
    @Resource
    private PasswordEncoder passwordEncoder;

    public void exportExcel(HttpServletResponse response, String fileName, List<SysUserExcelDataDTO> list) {
        ExcelUtil.write(response, SysUserExcelDataDTO.class, fileName, list);
    }

    public void downloadTemplate(HttpServletResponse response, String fileName){
        //机构code-name
        List<String> selectDataList = sysDepartmentMapper.concatCodeAndName();
        //角色名称
        List<String> roleNameList = sysRoleMapper.selectList(new LambdaQueryWrapper<>()).stream().map(SysRole::getName).collect(Collectors.toList());
        ExcelUtil.write(
                response,
                SysUserExcelDataDTO.class,
                fileName,
                List.of(SysUserExcelDataDTO.builder().build()),
                List.of(
                        SelectDataListBO.builder().selectDataIndex(3).selectDataList(List.of("男", "女")).sheetName("dicSexSheet").build(),
                        SelectDataListBO.builder().selectDataIndex(7).selectDataList(selectDataList).sheetName("dicDepartmentSheet").build(),
                        SelectDataListBO.builder().selectDataIndex(8).selectDataList(roleNameList).sheetName("roleNameSheet").build()
                ));
    }

    public void importExcelData(MultipartFile file) throws Exception {
        List<SysUserExcelDataDTO> list = ExcelUtil.readContainsHeader(file, SysUserExcelDataDTO.class, this::checkUserExist);
        if(CollUtil.isEmpty(list)){
            return;
        }
        List<SysUserDepartment> sysUserDepartments = new ArrayList<>(10);
        List<SysUserRole> sysUserRoles = new ArrayList<>(10);
        List<SysUser> userList = list.stream().map(vo -> {
            SysUser user = new SysUser();
            BeanUtils.copyProperties(vo, user);
            long id = idGenerator.nextId();
            user.setId(id);
            //密码暂时统一设置
            user.setPassword(getPassword(user.getAccount()));
            SysUserDepartment sysUserDepartment = new SysUserDepartment();
            sysUserDepartment.setSysUserId(id);
            sysUserDepartment.setSysDepartmentId(vo.getDepartmentId());
            sysUserDepartments.add(sysUserDepartment);

            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(id);
            sysUserRole.setRoleId(vo.getRoleId());
            sysUserRoles.add(sysUserRole);
            return user;
        }).toList();

        Authentication authentication = AuthenticationHolder.getAuthentication();
        AsyncTaskUtils.execute(
                List.of(
                        new CompletableTask(authentication, () -> baseMapper.insert(userList)),
                        new CompletableTask(authentication, () -> sysUserDepartmentMapper.insert(sysUserDepartments)),
                        new CompletableTask(authentication, () -> sysUserRoleMapper.insert(sysUserRoles))
                )
        );
    }

    private String getPassword(String account){
        return passwordEncoder.encode("zxlb" + account);
    }

    private void checkUserExist(List<SysUserExcelDataDTO> dataList) {
        //校验code是否重复
        Map<String, Long> departmentNameToIdMap = sysDepartmentMapper.selectList(new LambdaQueryWrapper<SysDepartment>()
                        .eq(SysDepartment::getEnable, 1)).stream()
                .collect(Collectors.toMap(department -> department.getCode() + "-" + department.getName(), SysDepartment::getId));
        Map<String, Long> roleNameToIdMap = sysRoleMapper.selectList(new LambdaQueryWrapper<SysRole>().eq(SysRole::getEnable, true)).stream()
                .collect(Collectors.toMap(SysRole::getName, SysRole::getId));
        Set<String> sysUserList = baseMapper.selectList(new LambdaQueryWrapper<>()).stream().map(SysUser::getAccount).collect(Collectors.toSet());
        //校验机构是否存在，状态是否有效
        int i = 2;
        for (SysUserExcelDataDTO dto : dataList){
            Long departmentId = departmentNameToIdMap.get(dto.getDepartmentName());
            if(departmentId == null){
                throw new BusinessException("第" + i + "行数据机构不存在");
            }
            dto.setDepartmentId(departmentId);
            Long roleId = roleNameToIdMap.get(dto.getRoleNames());
            if(roleId == null){
                throw new BusinessException("第" + i + "行数据角色不存在");
            }
            dto.setRoleId(roleId);
            if(sysUserList.contains(dto.getAccount())){
                throw new BusinessException("第" + i + "行数据登录账号已存在");
            }
            sysUserList.add(dto.getAccount());
            i++;
        }
    }
}
