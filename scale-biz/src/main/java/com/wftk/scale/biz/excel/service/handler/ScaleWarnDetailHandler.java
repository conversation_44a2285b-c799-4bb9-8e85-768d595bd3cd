package com.wftk.scale.biz.excel.service.handler;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.entity.ScaleWarn;
import com.wftk.scale.biz.excel.model.ScaleWarnDetailExcelDataDTO;
import com.wftk.scale.biz.excel.utils.ExcelUtil;
import com.wftk.scale.biz.mapper.ScaleWarnMapper;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ScaleWarnDetailHandler extends ServiceImpl<ScaleWarnMapper, ScaleWarn> {

    public void exportExcel(HttpServletResponse response, String fileName, List<ScaleWarnDetailExcelDataDTO> list) {
        ExcelUtil.write(response, ScaleWarnDetailExcelDataDTO.class, fileName, list);
    }
}
