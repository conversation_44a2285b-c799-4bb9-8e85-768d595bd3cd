package com.wftk.scale.biz.event.publisher;

import com.wftk.scale.biz.event.ScaleCreateRelationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createDate 2024/12/4 17:27
 */
@Component
public class ScaleCreateRelationPublisher implements BaseEventPublisher<ScaleCreateRelationEvent> {

    private final ApplicationEventPublisher applicationEventPublisher;

    public ScaleCreateRelationPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Override
    public void publishEvent(ScaleCreateRelationEvent scaleCreateRelationEvent) {
        applicationEventPublisher.publishEvent(scaleCreateRelationEvent);
    }
}
