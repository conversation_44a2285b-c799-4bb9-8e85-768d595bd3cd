package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleDTO;
import com.wftk.scale.biz.dto.scale.ScaleListingDetailDTO;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleSerialDTO;
import com.wftk.scale.biz.entity.Scale;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 量表主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleService extends IService<Scale> {

    /*
     * @Author: mq
     * @Description: 校验量表名称是否存在
     * @Date: 2024/11/1 14:46
     * @Param: scaleId-量表ID
     * @Param: scaleName-量表名称
     * @return: boolean
     **/
    boolean validScaleName(Long scaleId, String scaleName);

    /*
     * @Author: mq
     * @Description: 校验量表限时区间是否设置合理
     * @Date: 2024/11/19 15:09
     * @Param: minTimeLimit-限时最小值
     * @Param maxTimeLimit-限时最大值
     * @return: boolean
     **/
    boolean vaildScaleTimeRange(Integer minTimeLimit, Integer maxTimeLimit);

    /*
     * @Author: mq
     * @Description: 判断量表是否完成
     * @Date: 2024/11/4 14:20
     * @Param: scaleId-量表ID
     * @return: boolean
     **/
    boolean vaildCompletedStatus(Long scaleId);

    /*
     * @Author: mq
     * @Description: 判断折扣价格是否小于等于原价
     * @Date: 2024/12/16 16:31
     * @Param: originalPrice
     * @Param: price
     * @return: boolean
     **/
    boolean vaildPriceLessThanOriginalPrice(BigDecimal originalPrice, BigDecimal price);

    /*
     * @Author: mq
     * @Description: 创建量表基本信息
     * @Date: 2024/11/1 14:44
     * @Param: scale-量表基本信息
     * @return: void
     **/
    void create(Scale scale);

    /*
     * @Author: mq
     * @Description: 修改量表基本信息
     * @Date: 2024/11/1 15:13
     * @Param: scale-量表基本信息
     * @return: void
     **/
    void modify(Scale scale);

    /*
     * @Author: mq
     * @Description: 根据ID删除量表信息
     * @Date: 2024/11/4 14:11
     * @Param: scaleId-量表ID
     * @return: void
     **/
    void delete(Long scaleId);

    /*
     * @Author: mq
     * @Description: 更新量表完成状态
     * @Date: 2024/11/6 11:21
     * @Param: scaleId-量表ID
     * @Param: complateStatus-完成状态
     * @return: void
     **/
    void updateComplateStatus(Long scaleId, Integer complateStatus);

    /*
    * @Author: mq
    * @Description: 获取单个量表最新版本数据
    * @Date: 2024/12/18 20:15
    * @Param: name
     * @Param: complateStatus
    * @return: List<ScaleQueryDTO>
    **/
    List<ScaleQueryDTO> selectListByScaleNameAndCompleteStatus(String name, Integer complateStatus, Long combinationId);
    
    /*
     * @Author: mq
     * @Description: 获取单个量表最新版本数据
     * @Date: 2024/11/4 14:03
     * @Param: name-量表名称
     * @Param: complateStatus-量表状态
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.scale.ScaleQueryDTO>
     **/
    Page<ScaleQueryDTO> selectScalePage(String name, Integer complateStatus);

    /**
     * @Author: mq
     * @Description: 根据量表ID获取量表详情信息
     * @Date: 2024/11/20 14:33
     * @Param: scaleId-量表ID
     * @return: ScaleQueryDTO
     **/
    ScaleQueryDTO findByScaleId(Long scaleId);

    /**
     * @Author: mq
     * @Description: 根据量表编号获取量表详情信息（最新版本）
     * @Date: 2024/11/20 14:33
     * @Param: scaleId-量表ID
     * @return: ScaleQueryDTO
     **/
    ScaleQueryDTO findByScaleCode(String scaleCode);

    /*
    * @Author: mq
    * @Description: 构建量表详情信息
    * @Date: 2024/12/16 20:35
    * @Param: scale
    * @return: ScaleQueryDTO
    **/
    ScaleQueryDTO buildScaleData(Scale scale);

    /*
    * @Author: mq
    * @Description: 批量构建量表详情信息
    * @Date: 2024/12/16 20:36
    * @Param: list
    * @return: List<ScaleQueryDTO>
    **/
    List<ScaleQueryDTO> buildScaleData(List<ScaleQueryDTO> list);

    /*
     * @Author: mq
     * @Description: 根据ID复制量表信息
     * @Date: 2024/11/6 11:21
     * @Param: scaleId-量表ID
     * @return: java.lang.Long
     **/
    Long copyScale(Long scaleId);

    /*
     * @Author: mq
     * @Description: 根据量表ID获取量表编号
     * @Date: 2024/12/7 16:57
     * @Param: targetId-量表ID
     * @return: String
     **/
    String getTargetCodeById(Long targetId);

    /**
     * 获取最新版本的量表
     *
     * @param scaleCode
     * @return
     */
    Scale getLatestScaleByCode(String scaleCode);

    /**
     * 获取最新版本的量表
     *
     * @param scaleCode
     * @return
     */
    List<Scale> getLatestScaleListByCodes(List<String> scaleCode);

    /**
     * 获取已上架量表
     *
     * @return
     */
    List<ScaleDTO> getListedScale(Long scaleId, String scaleCode, String scaleName, Long scaleType, Integer listingShowType,String terminalCode);


    /**
     * 获取已上架量表详情
     *
     * @return
     */
    ScaleListingDetailDTO getListedScaleDetail(String scaleCode, String terminalCode,Long scaleListingId);


    List<ScaleListingDetailDTO> getScaleListingDetailDTO(List<ScaleSerialDTO> scaleSerials, String terminalCode);

}
