package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleUserResultParamDTO;
import com.wftk.scale.biz.entity.Order;
import com.wftk.scale.biz.entity.ScaleListing;
import com.wftk.scale.biz.entity.ScaleListingUserConf;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.vo.ScaleUserResultVO;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 用户测评记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleUserResultService extends IService<ScaleUserResult> {

    /*
     * @Author: mq
     * @Description: 校验量表是否存在测评记录
     * @Date: 2024/11/7 13:57
     * @Param: scaleId-量表ID
     * @return: boolean
     **/
    boolean vaildScaleUserResultRecord(Long userId, Long scaleId);


    boolean validScaleUserResultComplete(String orderNo,Long scaleId);

    /*
     * @Author: mq
     * @Description: 校验提交的测评答案信息是否为空
     * @Date: 2024/12/16 17:31
     * @Param: recordList
     * @return: boolean
     **/
    boolean vaildScaleUserResultRecordEmpty(List<ScaleUserResultRecord> recordList);

    /*
     * @Author: mq
     * @Description: 校验是否可以再次做量表
     * @Date: 2024/11/7 18:21
     * @Param: scaleUserResult-测评信息
     * @return: boolean
     **/
    boolean vaildScaleEvaluationAgain(ScaleUserResult scaleUserResult, Long userId, String orderNo,ScaleListingUserConf userConf);

    /**
     * @Author: mq
     * @Description: 校验量表是否是上架状态
     * @Date: 2024/11/20 14:10
     * @Param: listingId-上架ID
     * @Param scaleId-量表ID
     * @return: boolean
     **/
    boolean vaildScaleListingEnabled(ScaleListing scaleListing);

    void validAnswerSort(Long scaleId, Long scaleListingId, String orderNo,Long userId);

    /**
     * @Author: mq
     * @Description: 校验量表是否是上架状态
     * @Date: 2024/11/20 14:10
     * @Param: userConf-分发配置ID
     * @return: boolean
     **/
    boolean vaildUserConfEvaluationTimeOut(ScaleListingUserConf userConf);

    /*
     * @Author: mq
     * @Description: 校验测评时间是否超出量表设置的时限
     * @Date: 2024/11/19 17:27
     * @Param: listingId-上架配置ID
     * @Param: scaleId-量表ID
     * @Param: userId-用户ID
     * @return: boolean
     **/
    boolean vaildScaleTimeLimit(ScaleUserResult scaleUserResult, ScaleListingUserConf userConf);

    /*
     * @Author: mq
     * @Description: 生成测评记录
     * @Date: 2024/11/7 14:18
     * @Param: scaleUserResult-测评记录信息
     * @Param: resultRecordList-测评回答答案
     * @return: ScaleUserResult
     **/
    ScaleUserResult create(ScaleUserResult scaleUserResult, List<ScaleUserResultRecord> resultRecordList);

    /*
     * @Author: mq
     * @Description: 根据ID删除用户测评记录
     * @Date: 2024/11/20 13:32
     * @Param: resultId
     * @return: void
     **/
    void delete(Long resultId);

    /*
     * @Author: mq
     * @Description: 根据条件检索用户测评记录信息
     * @Date: 2024/11/7 17:48
     * @Param: paramDTO-检索条件
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.scale.ScaleUserResultQueryDTO>
     **/
    Page<ScaleUserResultVO> selectPage(ScaleUserResultParamDTO paramDTO);

    /*
     * @Author: mq
     * @Description: 获取上架配置信息
     * @Date: 2024/12/24 13:39
     * @Param: listingId-上架配置ID
     * @return: void
     **/
    ScaleListing findByListingId(Long listingId);

    /*
     * @Author: mq
     * @Description: 获取上架配置信息
     * @Date: 2024/12/24 13:55
     * @Param: listingId-上架配置ID
     * @Param: listingUserId-分发记录ID
     * @return: void
     **/
    ScaleListingUserConf findByListingIdAndlistingUserId(Long listingId, Long listingUserId);

    /**
     * @Author: mq
     * @Description: 根据测评记录ID获取用户测评记录详情
     * @Date: 2024/11/25 18:00
     * @Param: resultId-测评记录ID
     * @return: ScaleUserResultDetailDTO
     **/
    ScaleUserResultVO detail(Long resultId);


    List<ScaleUserResult> selectByOrderNo(String orderNo);

    void export(HttpServletResponse response, ScaleUserResultParamDTO searchParam);

    /**
     * 推送用户评测结果
     * @param order
     * @param scaleUserResult
     */
    void notifyUserResult(Order order, ScaleUserResult scaleUserResult);
}
