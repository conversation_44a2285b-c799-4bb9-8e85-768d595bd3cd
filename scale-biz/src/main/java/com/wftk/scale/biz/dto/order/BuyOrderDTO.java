package com.wftk.scale.biz.dto.order;

import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2024/11/19 17:11
 */
@Data
public class BuyOrderDTO {

    /**
     * 量表类型
     */
    private Integer type;

    /**
     * 目标id
     */
    private Long targetId;

    /**
     * 量表名称
     */
    private String targetName;

    /**
     * 目标id
     */
    private Long scaleListingId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 终端编码
     */
    private String terminalCode;

    /**
     * 电话
     */
    private String phone;

    /**
     * 金额
     */
    private Integer amount;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 原价
     */
    private Integer originalAmount;

    /**
     * 支付渠道
     */
    private Integer payChannel;

}
