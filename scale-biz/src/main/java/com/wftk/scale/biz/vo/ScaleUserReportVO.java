package com.wftk.scale.biz.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ScaleUserReportVO {

    private Long id;
    /**
     * 用户账号
     */
    private String account;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 性别 1男 2女
     */
    private Integer sex;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 所属部门名称
     */
    private String departmentName;
    /**
     * 所属部门ID
     */
    private Long departmentId;
    /**
     * 量表名称
     */
    private String scaleName;
    /**
     * 是否有效 0无效 1有效
     */
    private Boolean valid;
    /**
     * 检测结果 0阴 1阳
     */
    private Integer result;
    /**
     * 测评终端
     */
    private String terminalCode;
    /**
     * 测评终端
     */
    private String terminalName;
    /**
     * 报告时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime reportTime;
}
