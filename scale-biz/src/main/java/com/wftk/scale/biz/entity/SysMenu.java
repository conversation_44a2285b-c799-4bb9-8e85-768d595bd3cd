package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@TableName("sys_menu")
@Data
public class SysMenu implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 父菜单id
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long parentId;
    /**
     * 1.模块,2.菜单,3.按钮
     */
    private Integer type;
    /**
     * 菜单名字
     */
    private String name;
    /**
     * 菜单编码
     */
    private String code;
    /**
     * 路由名称
     */
    private String routeName;
    /**
     * 路由地址
     */
    private String routePath;
    /**
     * 组件名称
     */
    @TableField(fill = FieldFill.UPDATE)
    private String componentName;
    /**
     * 组件地址
     */
    @TableField(fill = FieldFill.UPDATE)
    private String componentPath;
    /**
     * 权限字符
     */
    @TableField(fill = FieldFill.UPDATE)
    private String permissionCode;
    /**
     * 显示排序
     */
    private Integer sort;
    /**
     * 图标
     */
    @TableField(fill = FieldFill.UPDATE)
    private String icon;
    /**
     * 菜单描述
     */
    @TableField(fill = FieldFill.UPDATE)
    private String description;
    /**
     * 1为隐藏，0不隐藏
     */
    private Boolean hidden;
    /**
     * 菜单状态
     */
    private Boolean enable;
    /**
     * 是否缓存
     */
    private Boolean cache;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;
}
