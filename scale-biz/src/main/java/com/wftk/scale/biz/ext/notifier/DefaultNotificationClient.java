package com.wftk.scale.biz.ext.notifier;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.http.client.core.common.interceptor.RequestInterceptor;
import com.wftk.http.client.core.common.type.DataType;
import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.request.RequestMethod;
import com.wftk.http.client.core.request.builder.RequestBuilders;
import com.wftk.jackson.core.JSONObject;
import com.wftk.scale.biz.constant.TerminalConfConstant;
import com.wftk.scale.biz.entity.Order;
import com.wftk.scale.biz.service.OrderService;
import com.wftk.scale.biz.service.ScaleListingService;
import com.wftk.scale.biz.service.TerminalConfService;
import com.wftk.signature.builder.DefaultSignBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/10/18 14:00
 */
@Slf4j
public class DefaultNotificationClient implements NotificationClient {

    private final TerminalConfService terminalConfService;

    private final OrderService orderService;
    private final ScaleListingService scalelistingService;

    private final HttpRequestExecutor httpRequestExecutor;

    public DefaultNotificationClient(HttpRequestExecutor httpRequestExecutor, TerminalConfService terminalConfService,
            OrderService orderService, ScaleListingService scalelistingService) {
        this.httpRequestExecutor = httpRequestExecutor;
        this.terminalConfService = terminalConfService;
        this.orderService = orderService;
        this.scalelistingService = scalelistingService;
    }

    @Override
    public ApiResult<Void> notify(String terminalCode, String terminalConfCode, Map<String, Object> dataMap) {
        Collection<String> notifyUrls = getNotifyUrl(dataMap, terminalCode, terminalConfCode);
        if (CollUtil.isEmpty(notifyUrls)) {
            log.warn("terminal {} haven't configured, terminalCode: [{}]", terminalConfCode, terminalCode);
            return ApiResult.fail(500, "通知地址获取为空");
        }
        //TODO 需要通知多个地址（当前修复bug，时间太赶）
        String notifyUrl = notifyUrls.iterator().next();
        HttpRequest<Map<String, Object>, ApiResult<Void>> request = RequestBuilders
                .<Map<String, Object>, ApiResult<Void>>bodyBuilder(notifyUrl)
                .method(RequestMethod.POST)
                .json(dataMap)
                .resultType(new DataType<>() {
                })
                .build();
        String secret = getSecret(terminalCode);
        if (StrUtil.isBlank(secret)) {
            log.warn("terminal secret haven't configured, terminalCode: [{}]", terminalCode);
            return ApiResult.fail(500, "密钥获取为空");
        }
        SignatureRequestInterceptor signatureRequestInterceptor = new SignatureRequestInterceptor(terminalCode, secret);
        return httpRequestExecutor.execute(request, signatureRequestInterceptor, null, null);
    }

    private Collection<String> getNotifyUrl(Map<String, Object> dataMap, String terminalCode, String terminalConfCode) {
        String reportUrl = "";
        if (dataMap != null && dataMap.containsKey("orderNo")) {
            String orderNo = dataMap.get("orderNo").toString();
            // 如果scale_listing表有配置报告地址优先取
            Order order = orderService.getByOrderNo(orderNo);
            if (order != null && order.getScaleListingId() != null) {
                reportUrl = scalelistingService.getReportUrl(order.getScaleListingId());
            }
        }
        if (StrUtil.isBlank(reportUrl)) {
            reportUrl = terminalConfService.findByItemCode(terminalCode, terminalConfCode, true);
        }
        if (StrUtil.isBlank(reportUrl)) {
            return Collections.emptyList();
        }
        if (reportUrl.contains(",")) {
            return StrUtil.split(reportUrl, ",");
        }
        return Collections.singletonList(reportUrl);
    }

    private String getSecret(String terminalCode) {
        // 签名摘要
        return terminalConfService.findByItemCode(terminalCode, TerminalConfConstant.CLIENT_SECRET, true);
    }

    /**
     * 签名
     */
    public static class SignatureRequestInterceptor implements RequestInterceptor {

        private final String terminalCode;

        private final String terminalSecret;

        public SignatureRequestInterceptor(String terminalCode, String terminalSecret) {
            this.terminalCode = terminalCode;
            this.terminalSecret = terminalSecret;
        }

        @Override
        public <P, R> HttpRequest<P, R> pre(HttpRequest<P, R> httpRequest) {
            Map<String, Object> params = new HashMap<>();
            params.put("terminalCode", terminalCode);
            params.put("nonce", IdUtil.fastSimpleUUID());
            params.put("timestamp", System.currentTimeMillis());
            String signature = new DefaultSignBuilder(terminalSecret)
                    .addParams(params)
                    .addParam("body", JSONObject.getInstance().toJSONString(httpRequest.getHttpBody().getBody()))
                    .build();
            params.put("sign", signature);
            httpRequest.getHttpQueries().putAll(params);
            return httpRequest;
        }

    }
}
