package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.ScaleListingUserConf;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 量表上架触达用户配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleListingUserConfMapper extends BaseMapper<ScaleListingUserConf> {

    /*
     * @Author: mq
     * @Description: 根据量表上架配置ID获取用户分发配置信息
     * @Date: 2024/10/30 17:14
     * @Param: scaleListingId-上架配置ID
     * @return: com.wftk.scale.biz.entity.ScaleListingUserConf
     **/
    ScaleListingUserConf findByScaleListingId(@Param("scaleListingId") Long scaleListingId);


    ScaleListingUserConf getById(@Param("id") Long id);

}
