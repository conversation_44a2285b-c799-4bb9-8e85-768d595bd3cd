package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.user.DistributableUserDTO;
import com.wftk.scale.biz.dto.user.UserExcelConvertDTO;
import com.wftk.scale.biz.dto.user.UserQueryDTO;
import com.wftk.scale.biz.dto.user.UserSearchDTO;
import com.wftk.scale.biz.entity.User;
import com.wftk.scale.biz.vo.UserToDepartmentVO;
import com.wftk.scale.biz.vo.UserVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据编码和机构查询唯一用户
     * @param code
     * @param departmentId
     * @return
     */
    User selectByCodeAndDepartmentId(@Param("code")String code, @Param("departmentId")Long departmentId);

    /**
     * 根据登录账号和机构查询唯一用户
     */
    User selectByAccountAndDepartmentId(@Param("account")String account, @Param("departmentId")Long departmentId);

    /**
     * 根据账号查询用户
     * @param account
     * @param enable
     * @return
     */
    User selectByAccount(@Param("account") String account, @Param("enable") Boolean enable);
    List<DistributableUserDTO> findDistributionUser(@Param("departmentCode")String departmentCode,@Param("terminalCode") String terminalCode);

    List<UserQueryDTO> selectUserQueryList(UserSearchDTO dto);

    List<UserExcelConvertDTO> queryList(UserSearchDTO dto);

    List<String> queryTerminalCodeByUserDeptIds(@Param("userDeptIds") List<Long> userDeptIds);

    UserVO selectByUserId(@Param("userId") Long userId);

    List<Long> getEnableDeptIdsByUserIds(@Param("userIds") List<Long> userIds);

    int countStatusUser(@Param("orgIds") List<Long> orgIds, @Param("enable") Boolean enable);

    List<UserToDepartmentVO> selectUserToDepartment();

    User getById(@Param("id") Long id);

    List<User> getByIds(@Param("ids") Set<Long> ids);

    List<UserVO> getUserDepartmentByIds(@Param("ids") Set<Long> ids);
}
