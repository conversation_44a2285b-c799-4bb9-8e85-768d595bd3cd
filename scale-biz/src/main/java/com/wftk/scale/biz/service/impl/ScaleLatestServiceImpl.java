package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.scale.biz.entity.ScaleLatest;
import com.wftk.scale.biz.mapper.ScaleLatestMapper;
import com.wftk.scale.biz.service.ScaleLatestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 量表最新版本 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class ScaleLatestServiceImpl extends ServiceImpl<ScaleLatestMapper, ScaleLatest> implements ScaleLatestService {

    @Autowired
    private ScaleLatestMapper scaleLatestMapper;

    @Override
    public void saveOrUpdateScaleLatest(Long scaleId, String code) {
        ScaleLatest scaleLatest = scaleLatestMapper.findByCode(code);
        if(ObjectUtil.isNull(scaleLatest)){
            scaleLatest = new ScaleLatest();
        }
        scaleLatest.setScaleId(scaleId);
        scaleLatest.setCode(code);
//        scaleLatest.setTenantId("system");
        this.saveOrUpdate(scaleLatest);
    }
}
