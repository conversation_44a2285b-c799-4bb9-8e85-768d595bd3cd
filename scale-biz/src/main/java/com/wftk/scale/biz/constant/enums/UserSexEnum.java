package com.wftk.scale.biz.constant.enums;


import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @createDate 2024/12/24 16:31
 */
@Getter
public enum UserSexEnum  {

    MAN(1, "男"),
    WOMAN(2, "女");

    private Integer value;
    private String label;

    UserSexEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String valueOf(Integer value) {
        return Arrays.stream(UserSexEnum.values())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .map(UserSexEnum::getLabel)
                .orElse(null);
    }

    public static Integer convertNameToValue(String label) {
        return Arrays.stream(UserSexEnum.values())
                .filter(e -> e.getLabel().equals(label))
                .findFirst()
                .map(UserSexEnum::getValue)
                .orElse(null);
    }
}
