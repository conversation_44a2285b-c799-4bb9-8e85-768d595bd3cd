package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.dto.scale.ScaleQuestionHighestOverallScoreDTO;
import com.wftk.scale.biz.event.ScaleCreateEvent;
import com.wftk.scale.biz.event.ScaleDelEvent;
import com.wftk.scale.biz.entity.ScaleQuestionOption;
import com.wftk.scale.biz.mapper.ScaleQuestionOptionMapper;
import com.wftk.scale.biz.service.ScaleQuestionOptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * <p>
 * 问题选项表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class ScaleQuestionOptionServiceImpl extends ServiceImpl<ScaleQuestionOptionMapper, ScaleQuestionOption>
        implements ScaleQuestionOptionService {

    @Autowired
    private ScaleQuestionOptionMapper scaleQuestionOptionMapper;

    @Override
    public boolean vaildQuestionOptionLabel(List<ScaleQuestionOption> options) {
        boolean checkResult = false;
        if (CollUtil.isEmpty(options)) {
            return checkResult;
        }
        List<String> lables = options.stream().map(ScaleQuestionOption::getLabel).distinct()
                .collect(Collectors.toList());
        if (lables.size() != options.size()) {
            checkResult = true;
        }
        return checkResult;
    }

    @Override
    public void create(Long scaleId, Long questionId, List<ScaleQuestionOption> options) {
        if (CollUtil.isEmpty(options)) {
            return;
        }
        // 防止标签重复
        List<ScaleQuestionOption> unique = options.stream().filter(ObjUtil::isNotNull)
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(e -> e.getQuestionId() + "-" + e.getLabel()))),
                        ArrayList::new));
        options = unique.stream()
                // 过滤掉null元素,防止异常。
                .filter(ObjUtil::isNotNull)
                .peek(option -> {
                    option.setId(null);
                    option.setScaleId(scaleId);
                    option.setQuestionId(questionId);
                }).collect(Collectors.toList());
        baseMapper.insert(options);
    }

    @Override
    public void modify(Long scaleId, Long questionId, List<ScaleQuestionOption> options) {
        scaleQuestionOptionMapper.deleteByScaleIdAndQuestionId(scaleId, questionId);
        this.create(scaleId, questionId, options);
    }

    @Override
    public void saveScaleQuestionOption(ScaleCreateEvent scaleEventDTO, Map<Long, Long> oldToNewIdMap) {
        Long oldScaleId = scaleEventDTO.getOldScaleId();
        Long newScaleId = scaleEventDTO.getNewScaleId();
        List<ScaleQuestionOption> list = scaleQuestionOptionMapper.selectList(
                new LambdaQueryWrapper<ScaleQuestionOption>().eq(ScaleQuestionOption::getScaleId, oldScaleId));
        if (CollUtil.isEmpty(list)) {
            return;
        }
        list = list.stream()
                .filter(ObjUtil::isNotNull)
                .peek(option -> {
                    option.setId(null);
                    option.setScaleId(newScaleId);
                    option.setQuestionId(oldToNewIdMap.get(option.getQuestionId()));
                }).collect(Collectors.toList());
        baseMapper.insert(list);
    }

    @Override
    public List<ScaleQuestionOption> findByScaleIdAndQuestionId(Long scaleId, Long questionId) {
        return scaleQuestionOptionMapper.findByScaleIdAndQuestionId(scaleId, questionId);
    }

    @Override
    public List<ScaleQuestionOption> findByScaleId(Long scaleId) {
        return scaleQuestionOptionMapper.findByScaleIdAndQuestionId(scaleId, null);
    }

    @Override
    public void deleteByScaleIdAndQuestionId(Long scaleId, Long questionId) {
        scaleQuestionOptionMapper.deleteByScaleIdAndQuestionId(scaleId, questionId);
    }

    @Override
    public BigDecimal getQuestionTotalScore(Long scaleId) {
        return scaleQuestionOptionMapper.getQuestionTotalScore(scaleId);
    }

    @EventListener
    public void onoApplication(ScaleDelEvent event) {
        scaleQuestionOptionMapper.deleteByScaleIdAndQuestionId(event.getOldScaleId(), null);
    }

    @Override
    public ScaleQuestionHighestOverallScoreDTO findOverallScoreByScaleId(Long scaleId,List<Long> questionIds) {
        return baseMapper.findOverallScoreByScaleId(scaleId,questionIds);
    }
}
