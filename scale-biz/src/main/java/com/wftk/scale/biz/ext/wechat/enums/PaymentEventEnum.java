package com.wftk.scale.biz.ext.wechat.enums;


/**
 * <AUTHOR>
 * @create 2023/10/18 16:52
 */
public enum PaymentEventEnum {

    PAY_SUCCESS("PAY_SUCCESS", "支付成功"),
    REFUND_SUCCESS("REFUND_SUCCESS", "退款成功"),

    REFUND_FAIL("REFUND_FAIL", "退款失败");


    private String value;
    private String label;

    private PaymentEventEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}
