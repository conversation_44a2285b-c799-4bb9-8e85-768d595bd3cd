package com.wftk.scale.biz.constant;

import com.wftk.exception.common.ErrorCode;

/**
 * 微信支付错误消息
 * <AUTHOR>
 * @create 2023/10/12 16:34
 */
public interface PaymentErrorCode {

    ErrorCode UNSUPPORTED_SCENE = new ErrorCode(500001, "不支持当前场景编码");
    ErrorCode NOTIFY_URL_MUST_NOT_BE_NULL = new ErrorCode(500002, "通知地址不能为空");

    ErrorCode ORDER_PROCESSING = new ErrorCode(500020, "订单处理中，请勿重复发起");
    ErrorCode ORDER_EXISTS = new ErrorCode(500021, "该订单已存在");
    ErrorCode ORDER_NOT_EXISTS = new ErrorCode(500022, "订单不存在");
    ErrorCode PAID = new ErrorCode(500023, "该订单已支付");
    ErrorCode PAY_ERROR = new ErrorCode(500025, "支付失败");


    ErrorCode REFUND_AMOUNT_EXCEEDED = new ErrorCode(500031, "退款金额超过最大限额");
    ErrorCode ORDER_NOT_PAID = new ErrorCode(500032, "该订单尚未完成支付");
    ErrorCode ORDER_REFUND_EXISTS = new ErrorCode(500033, "该退款单已存在");
    ErrorCode REFUND_ERROR = new ErrorCode(500034, "退款失败");

}
