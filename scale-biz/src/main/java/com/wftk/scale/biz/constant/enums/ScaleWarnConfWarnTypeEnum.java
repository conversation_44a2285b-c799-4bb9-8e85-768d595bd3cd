package com.wftk.scale.biz.constant.enums;

import com.wftk.common.core.enums.BaseEnum;

import java.util.Arrays;

public enum ScaleWarnConfWarnTypeEnum implements BaseEnum {

    /**
     * 短信
     */
    SMS(1,"短信"),

    /**
     * 邮件
     */
    EMAIL(2, "邮件"),

    /**
     * 加红标注
     */
    RED_MARK(3, "加红标注");

    private final Integer value;
    private final String label;

    ScaleWarnConfWarnTypeEnum(Integer value, String label){
        this.value = value;
        this.label = label;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getLabel() {
        return label;
    }

    public static ScaleWarnConfWarnTypeEnum getByValue(Integer value) {
        return Arrays.stream(ScaleWarnConfWarnTypeEnum.values()).filter(v -> v.getValue().equals(value)).findFirst().orElse(null);
    }



}
