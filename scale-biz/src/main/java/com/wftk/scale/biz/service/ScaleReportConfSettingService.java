package com.wftk.scale.biz.service;

import com.wftk.scale.biz.dto.report.ChartSettingDTO;
import com.wftk.scale.biz.dto.report.FormSettingDTO;
import com.wftk.scale.biz.dto.report.RemarkSettingDTO;
import com.wftk.scale.biz.dto.scale.ScaleReportConfSettingDetailDTO;
import com.wftk.scale.biz.entity.ScaleReportConfSetting;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11 15:20:06
 */
public interface ScaleReportConfSettingService extends IService<ScaleReportConfSetting> {

    Integer delByReportConfId(Long reportConfId);


    ScaleReportConfSettingDetailDTO getDetailByReportConfId(Long reportConfId);

    List<ScaleReportConfSetting> selectScaleReportConfSettingByReportConfId(Long reportConfId,
                                                                            Integer type,
                                                                            Integer reportType);


    void create(Long reportConfId,
                String readingInstruction,
                List<RemarkSettingDTO> remarkSettings,
                List<FormSettingDTO> formSettings,
                List<ChartSettingDTO> chartSettings);


}
