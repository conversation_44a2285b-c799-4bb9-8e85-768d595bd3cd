package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.Department;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 部门表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface DepartmentMapper extends BaseMapper<Department> {


    /**
     * 通过终端编码获取部门
     * @param terminalCode
     * @return
     */
    Department selectByTerminalCode(@Param("terminalCode")String terminalCode);

    /**
     * 通过终端编码获取部门
     * @param terminalCode
     * @return
     */
    Boolean validTerminalBindDepartment(@Param("terminalCode")String terminalCode);

    /**
     * 通过终端编码获取部门
     * @param departmentId
     * @return
     */
    Boolean validDepartmentBindTerminal(@Param("departmentId")Long departmentId);


    List<Department> selectTreeList(@Param("name") String name, @Param("enable") Integer enable, @Param("terminalCode") String terminalCode);

    List<Department> findChildrenByParentId(@Param("parentId") Long parentId);

    List<String> concatCodeAndName();

    /**
     * 通过部门ID获取部门以及子部门
     * @param id
     * @return
     */
    List<Department> getChildDepartmentById(@Param("id") Long id);

    /**
     * 通过部门ID获取部门
     * @param ids
     * @return
     */
    List<Department> getByIds(@Param("ids") Set<Long> ids);
}
