package com.wftk.scale.biz.dto.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @createDate 2024/12/5 14:18
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserReportInfoDTO {

    /**
     * 用户编码
     */
    private String code;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 1男，2女
     */
    private String sex;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 身份证号
     */
    private String idCard;




}
