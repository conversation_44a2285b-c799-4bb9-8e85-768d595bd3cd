package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.user.*;
import com.wftk.scale.biz.entity.User;
import com.wftk.scale.biz.vo.UserVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface UserService extends IService<User> {

    User selectByCodeAndDepartmentId(String code, Long departmentId);

    /**
     * 查询
     * @param departmentCode
     * @param terminalCode
     * @return
     */
    List<DistributableUserDTO> findDistributionUser(String departmentCode, String terminalCode);

    /**
     * 保存
     */
    User createUser(UserCreateDTO userCreateDTO);

    String getPassword(String account);

    /**
     * 根据账号查询用户
     * @param account
     * @param enable
     * @return
     */
    User selectByAccount(String account, Boolean enable);

    Page<UserQueryDTO> selectUserQueryList(UserSearchDTO dto);

    void changePassword(Long userId, String password);

    void changeEnable(UserChangeEnableDTO userChangeEnableDTO);

    void changeUser(UserChangeDTO userChangeDTO);

    void deleteUser(Long userId);

    UserVO selectByUserId(Long userId);

    /**
     * 获取所选用户的有效机构
     */
    List<Long> getEnableDeptIdsByUserIds(List<Long> userIds);

    /**
     * 根据用户所属机构查询对应的终端编码
     */
    List<String> queryTerminalCodeByUserDeptIds(List<Long> userDeptIds);

    Set<String> getPhoneListByUserIds(Set<Long> userIds);

    Set<String> getEmailListByUserIds(Set<Long> userIds);

    boolean checkDepartmentUserStatus(List<Long> orgIds, Boolean enable);

    void exportUser(HttpServletResponse response, UserSearchDTO dto);

    void downTemplate(HttpServletResponse response);

    void importData(MultipartFile file);
}
