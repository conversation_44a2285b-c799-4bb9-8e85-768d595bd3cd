package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 量表上下架表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@TableName("scale_listing")
public class ScaleListing implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 封面图片
     */
    private String cover;

    /**
     * 可能是量表ID，也可能是组合量表ID
     */
    private Long targetId;

    /**
     * 量表名称,字段冗余
     */
    private String targetName;

    /**
     * 量表分类,字段冗余
     */
    private Long targetType;

    /**
     * 量表编号,字段冗余
     */
    private String targetCode;

    /**
     * 类型: 1.量表; 2.组合量表;
     */
    private Integer type;

    /**
     * 终端ID
     */
    private String terminalCode;

    /**
     * 呈现方式: 1.页面展示; 2.用户分发;
     */
    private Integer showType;

    /**
     * 报告地址，多个用逗号分隔
     */
    private String reportUrl;

    /**
     * 量表API路径
     */
    private String api;

    /**
     * 量表服务费原价, 单位:分
     */
    private Integer originalPrice;

    /**
     * 量表服务费优惠后价格，单位: 分
     */
    private Integer price;

    /**
     * 上架状态: 0.未上架; 1.已上架;
     */
    private Integer status;

    /**
     * 是否启用: 0.未启用; 1.已开启; 注意：此字段控制当前上架的量表是否可做业务（当值为0时用户即使买了也不能做，量表本身的enable字段不能限制用户做业务）
     */
    private Boolean enable;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @TableLogic
    private Boolean deleted;

    /**
     * 租户ID
     */
    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public Long getTargetId() {
        return targetId;
    }

    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public Long getTargetType() {
        return targetType;
    }

    public void setTargetType(Long targetType) {
        this.targetType = targetType;
    }

    public String getTargetCode() {
        return targetCode;
    }

    public void setTargetCode(String targetCode) {
        this.targetCode = targetCode;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTerminalCode() {
        return terminalCode;
    }

    public void setTerminalCode(String terminalCode) {
        this.terminalCode = terminalCode;
    }

    public Integer getShowType() {
        return showType;
    }

    public void setShowType(Integer showType) {
        this.showType = showType;
    }

    public String getReportUrl() {
        return reportUrl;
    }

    public void setReportUrl(String reportUrl) {
        this.reportUrl = reportUrl;
    }

    public String getApi() {
        return api;
    }

    public void setApi(String api) {
        this.api = api;
    }

    public Integer getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(Integer originalPrice) {
        this.originalPrice = originalPrice;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "ScaleListing{" +
            "id = " + id +
            ", targetId = " + targetId +
            ", type = " + type +
            ", terminalCode = " + terminalCode +
            ", showType = " + showType +
            ", reportUrl = " + reportUrl +
            ", api = " + api +
            ", originalPrice = " + originalPrice +
            ", price = " + price +
            ", status = " + status +
            ", enable = " + enable +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
            ", createBy = " + createBy +
            ", updateBy = " + updateBy +
            ", deleted = " + deleted +
            ", tenantId = " + tenantId +
        "}";
    }
}
