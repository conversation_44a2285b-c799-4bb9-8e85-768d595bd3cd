package com.wftk.scale.biz.ext.wechat.enums;

import com.wftk.common.core.enums.BaseEnum;

/**
 * <AUTHOR>
 * @create 2023/10/17 10:37
 */
public enum RefundStatusEnum implements BaseEnum {
    REFUND_WAIT(1, "待退款"), REFUND_SUCCESS(2, "退款成功"), REFUND_FAILED(10, "退款失败");

    private final Integer value;
    private final String label;;

    RefundStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
