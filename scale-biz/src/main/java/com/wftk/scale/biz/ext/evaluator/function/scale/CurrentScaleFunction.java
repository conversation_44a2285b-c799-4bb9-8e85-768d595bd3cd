package com.wftk.scale.biz.ext.evaluator.function.scale;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.wftk.scale.biz.entity.ScaleQuestion;
import com.wftk.scale.biz.ext.evaluator.constant.EnvConstant;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;
import com.wftk.scale.biz.service.ScaleQuestionService;

/**
 * 获取当前量表
 * 示例: s_current(), 表示获取当前量表, 返回问题id逗号分隔的字符串
 * <AUTHOR>
 * @date 2025-09-16
 */
public class CurrentScaleFunction extends BaseScaleEvaluationFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Scale.S_CURRENT;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env) {
        Long scaleId = getFromEnv(env, EnvConstant.SCALE_ID, Long.class, false);
        logger.info("s_current: scaleId: {}", scaleId);
        ScaleQuestionService scaleQuestionService = getFromEnv(env, EnvConstant.SCALE_QUESTION_SERVICE, ScaleQuestionService.class, false);
        List<ScaleQuestion> scaleQuestions = scaleQuestionService.findEntitysByScaleId(scaleId);
        return new AviatorString(scaleQuestions.stream()
                .map(ScaleQuestion::getId)
                .map(String::valueOf)
                .collect(Collectors.joining(",")));
    }

}
