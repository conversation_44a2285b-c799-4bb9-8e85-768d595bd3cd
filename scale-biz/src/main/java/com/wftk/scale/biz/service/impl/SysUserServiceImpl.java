package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.core.password.PasswordEncoder;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.converter.SysUserConverter;
import com.wftk.scale.biz.dto.user.sysuser.SysUserCreateDTO;
import com.wftk.scale.biz.dto.user.sysuser.SysUserQueryDTO;
import com.wftk.scale.biz.dto.user.sysuser.SysUserUpdateDTO;
import com.wftk.scale.biz.entity.SysRole;
import com.wftk.scale.biz.entity.SysUser;
import com.wftk.scale.biz.event.SysUserChangeEvent;
import com.wftk.scale.biz.event.SysUserDeleteEvent;
import com.wftk.scale.biz.event.publisher.SysUserChangePublisher;
import com.wftk.scale.biz.event.publisher.SysUserDeletePublisher;
import com.wftk.scale.biz.excel.service.handler.SysUserHandler;
import com.wftk.scale.biz.mapper.SysUserMapper;
import com.wftk.scale.biz.service.SysRoleService;
import com.wftk.scale.biz.service.SysUserService;
import com.wftk.scale.biz.vo.SysUserDetailVO;
import com.wftk.scale.biz.vo.SysUserVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 系统管理员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SysUserChangePublisher sysUserChangePublisher;
    @Resource
    private SysUserDeletePublisher sysUserDeletePublisher;
    @Resource
    private SysUserConverter sysUserConverter;
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private SysUserHandler sysUserHandler;
    @Resource
    private PasswordEncoder passwordEncoder;

    @Override
    public SysUser findByAccount(String account, Boolean enable) {
        return baseMapper.selectOneByAccount(account, enable);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(SysUserCreateDTO dto) {
        validAccountUnique(dto.getAccount());
        validDepartmentStatus(dto.getDepartmentId());
        List<Long> roleIds = validRoleStatusAndRemoveRepeat(dto.getRoleIdList());
        //同时保存 用户机构、用户角色关联关系
        SysUser sysUser = sysUserConverter.sysUserCreateDtoToSysUser(dto);
        //加密密码 TODO 前端密码传递需要传密文
        sysUser.setPassword(passwordEncoder.encode(sysUser.getPassword()));
        baseMapper.insert(sysUser);
        sysUserChangePublisher.publishEvent(new SysUserChangeEvent(sysUser.getId(), roleIds, dto.getDepartmentId()));
    }

    @Override
    public void update(SysUserUpdateDTO dto) {
        //查询用户信息
        SysUserDetailVO dbSysUser = sysUserMapper.detailById(dto.getId());
        if(dbSysUser == null){
            throw new BusinessException("用户不存在");
        }
        SysUser sysUser = sysUserConverter.sysUserUpdateDtoToSysUser(dto);
        if(!Objects.equals(dbSysUser.getAccount(), sysUser.getAccount())){
            validAccountUnique(sysUser.getAccount());
        }
        boolean departmentOrRoleChange = false;
        Long updateDepartmentId = null;
        if(!Objects.equals(dbSysUser.getDepartmentId(), dto.getDepartmentId())){
            validDepartmentStatus(dto.getDepartmentId());
            departmentOrRoleChange = true;
            updateDepartmentId = dto.getDepartmentId();
        }
        //校验角色是否修改 角色为必传项，忽略空校验
        List<Long> roleIdList = validRoleChange(dbSysUser.getRoleIds(), dto.getRoleIdList());
        if(CollUtil.isNotEmpty(roleIdList)){
            departmentOrRoleChange = true;
        }
        baseMapper.updateById(sysUser);
        if(departmentOrRoleChange){
            sysUserChangePublisher.publishEvent(new SysUserChangeEvent(sysUser.getId(), roleIdList, updateDepartmentId));
        }
    }

    private List<Long> validRoleChange(String dbRoleIds, List<Long> updateRoleIds){
        if(StrUtil.isBlank(dbRoleIds)){
            return validRoleStatusAndRemoveRepeat(updateRoleIds);
        }else{
            Set<Long> dbRoleIdSet = Arrays.stream(dbRoleIds.split(",")).filter(Objects::nonNull).map(Long::parseLong).collect(Collectors.toSet());
            Set<Long> updateRoleIdSet = new HashSet<>(updateRoleIds);
            if(!dbRoleIdSet.equals(updateRoleIdSet)){
                return validRoleStatusAndRemoveRepeat(updateRoleIds);
            }else{
                return null;
            }
        }
    }

    private List<Long> validRoleStatusAndRemoveRepeat(List<Long> roleIdList){
        if(CollUtil.isEmpty(roleIdList)){
            return roleIdList;
        }
        List<Long> distinctRoleIdList = roleIdList.stream().distinct().collect(Collectors.toList());
        long count = sysRoleService.count(new LambdaQueryWrapper<SysRole>().in(SysRole::getId, distinctRoleIdList).eq(SysRole::getEnable, true));
        if(count != distinctRoleIdList.size()){
            throw new BusinessException("角色不存在");
        }
        return distinctRoleIdList;
    }

    private void validDepartmentStatus(Long departmentId){
        //校验机构是否存在，状态是否启用
    }

    private void validAccountUnique(String account){
        boolean exists = baseMapper.exists(new LambdaQueryWrapper<SysUser>().eq(SysUser::getAccount, account));
        if(exists){
            throw new BusinessException("登录账号已存在");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        baseMapper.deleteById(id);
        sysUserDeletePublisher.publishEvent(new SysUserDeleteEvent(id));
    }

    @Override
    public SysUserDetailVO detailById(Long id) {
        if(Objects.isNull(id)){
            throw new BusinessException("用户id不能为空");
        }
        return sysUserMapper.detailById(id);
    }

    @Override
    public Page<SysUserVO> getList(SysUserQueryDTO dto) {
        return Page.doSelectPage(() -> sysUserMapper.queryListByQueryDto(dto));
    }

    @Override
    public void export(SysUserQueryDTO dto, HttpServletResponse response) {
        List<SysUserVO> list = sysUserMapper.queryListByQueryDto(dto);
        sysUserHandler.exportExcel(response, "系统用户导出", sysUserConverter.sysUserVOListToSysUserExcelDataDTOList(list));
    }

    @Override
    public void downTemplate(HttpServletResponse response) {
        sysUserHandler.downloadTemplate(response, "系统用户导入模板");
    }

    @Override
    public void importExcelData(MultipartFile file) {
        try {
            sysUserHandler.importExcelData(file);
        }catch (Exception e){
            log.error("导入失败", e);
            throw new BusinessException("导入失败:" + e.getMessage());
        }
    }
}
