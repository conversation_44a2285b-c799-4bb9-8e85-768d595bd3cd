package com.wftk.scale.biz.ext.notice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum NoticeEventEnum {

    SCALE_WARNING("量表预警提醒"),
    SCALE_LISTING_USER_RECORD("量表分发提醒");

    /**
     * 场景对应邮件主题
     */
    private final String subject;

    public static NoticeEventEnum getByName(String name) {
        return Arrays.stream(NoticeEventEnum.values())
                .filter(noticeEventEnum -> noticeEventEnum.name().equals(name))
                .findFirst()
                .orElse(null);
    }
}
