package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.ConvertTypeEnum;
import com.wftk.scale.biz.dto.scale.ScaleConvertResultIntro;
import com.wftk.scale.biz.dto.scale.ScaleQuestionHighestOverallScoreDTO;
import com.wftk.scale.biz.dto.scale.ScaleResultIntroDTO;
import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.entity.ScaleResultIntro;
import com.wftk.scale.biz.event.ScaleCreateRelationEvent;
import com.wftk.scale.biz.event.ScaleDelEvent;
import com.wftk.scale.biz.mapper.ScaleFactorMapper;
import com.wftk.scale.biz.mapper.ScaleQuestionOptionMapper;
import com.wftk.scale.biz.mapper.ScaleResultIntroMapper;
import com.wftk.scale.biz.service.ScaleResultIntroService;
import com.wftk.scale.biz.util.ParentPathUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 结果解读表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Slf4j
@Service
public class ScaleResultIntroServiceImpl extends ServiceImpl<ScaleResultIntroMapper, ScaleResultIntro> implements ScaleResultIntroService {


    @Autowired
    private ScaleResultIntroMapper scaleResultIntroMapper;

    @Autowired
    private ScaleQuestionOptionMapper scaleQuestionOptionMapper;

    @Autowired
    private ScaleFactorMapper scaleFactorMapper;


    @Override
    public boolean vaildResultIntroDataIntegrity(Long scaleId) {
        return scaleResultIntroMapper.vaildResultIntroDataIntegrity(scaleId);
    }

    @Override
    public boolean vaildResultIntroName(Long scaleId, Long factorId, String resultIntroName) {
        boolean checkResult = false;
        if (ObjectUtil.isNull(scaleId) || StrUtil.isEmpty(resultIntroName)) {
            return checkResult;
        }
        checkResult = scaleResultIntroMapper.vaildResultIntroNameExists(scaleId, factorId, resultIntroName);
        return checkResult;
    }

    @Override
    public boolean vaildFactorScoreRangeOverlap(ScaleResultIntro scaleResultIntro) {
        Long scaleId = scaleResultIntro.getScaleId();
        Long factorId = scaleResultIntro.getFactorId();
        List<ScaleResultIntro> resultIntroList = scaleResultIntroMapper.findByScaleIdAndFactorId(scaleId, factorId);
        boolean checkResult = this.checkScoreRangeOverlap(scaleResultIntro, resultIntroList);
        return checkResult;
    }

    @Override
    public String checkFactorRelationIntro(Long factorId) {
        return scaleResultIntroMapper.checkFactorRelationIntro(factorId);
    }

    private boolean checkScoreRangeOverlap(ScaleResultIntro fromResultIntro, List<ScaleResultIntro> toResultIntroList) {
        boolean checkResult = false;
        if (CollUtil.isEmpty(toResultIntroList)) {
            return checkResult;
        }
        Long resultIntroId = fromResultIntro.getId();
        if (ObjectUtil.isNotNull(resultIntroId)) {
            //修改时，过滤数据本身
            toResultIntroList = toResultIntroList.stream().filter(toResultIntro -> !resultIntroId.equals(toResultIntro.getId())).collect(Collectors.toList());
        }
        String rawScore = fromResultIntro.getScore();//原始得分配置
        List<BigDecimal> rawScoreInterval = parseFromFiexdScore(rawScore);
        if (CollUtil.isNotEmpty(rawScoreInterval)) {
            ScaleFactor scaleFactor = scaleFactorMapper.getById(fromResultIntro.getFactorId());
            if (scaleFactor != null && StrUtil.isNotBlank(scaleFactor.getQuestionId())) {
                ScaleQuestionHighestOverallScoreDTO scaleQuestionHighestOverallScoreDTO = scaleQuestionOptionMapper.findOverallScoreByScaleId(fromResultIntro.getScaleId(), StrUtil.split(scaleFactor.getQuestionId(), ParentPathUtils.SEPARATOR).stream().map(Long::parseLong).toList());
                Integer totalScore = scaleQuestionHighestOverallScoreDTO.getTotalScore();
                boolean overrange = rawScoreInterval.stream().anyMatch(score -> score.compareTo(BigDecimal.valueOf(totalScore)) > 0);
                if (overrange) {
                    throw new BusinessException("原始得分不能超过最大得分");
                }
            }
        }
        List<ScaleResultIntro> resultList = toResultIntroList.stream().filter(toResultIntro -> {
            //比较当前编辑数据的分值与历史数据设置的分值是否存在交叉重叠
            return this.isOverlap(fromResultIntro, toResultIntro);
        }).collect(Collectors.toList());
        checkResult = CollUtil.isEmpty(resultList) ? checkResult : Boolean.TRUE;
        return checkResult;
    }

    private boolean isOverlap(ScaleResultIntro fromResultIntro, ScaleResultIntro toResultIntro) {
        //当前编辑的结果信息：fromResultIntro, 历史存储的结果信息：toResultIntro
        Integer fromConvertType = fromResultIntro.getScoreConvertType();
        Integer toConvertType = toResultIntro.getScoreConvertType();
        //判断两个区间是否重叠
        if (ConvertTypeEnum.RATIO.getCode().equals(fromConvertType)
                && ConvertTypeEnum.RATIO.getCode().equals(toConvertType)) {
            return this.checkRatioScoreRangeOverlap(fromResultIntro, toResultIntro);
        }
        //fromResultIntro是固定值,toResultIntro是区间
        if (ConvertTypeEnum.FIEXD.getCode().equals(fromConvertType)
                && ConvertTypeEnum.RATIO.getCode().equals(toConvertType)) {
            return this.checkFiexdAndRatioMixRangeOverlap(fromResultIntro, toResultIntro);
        }
        //fromResultIntro是区间,toResultIntro是固定值
        if (ConvertTypeEnum.RATIO.getCode().equals(fromConvertType)
                && ConvertTypeEnum.FIEXD.getCode().equals(toConvertType)) {
            return this.checkFiexdAndRatioMixRangeOverlap(toResultIntro, fromResultIntro);
        }
        //fromResultIntro是固定值,toResultIntro是固定值
        if (ConvertTypeEnum.FIEXD.getCode().equals(fromConvertType)
                && ConvertTypeEnum.FIEXD.getCode().equals(toConvertType)) {
            return this.checkFiexdScoreRangeOverlap(toResultIntro, fromResultIntro);
        }
        return false;
    }

    private boolean checkRatioScoreRangeOverlap(ScaleResultIntro fromResultIntro, ScaleResultIntro toResultIntro) {
        List<BigDecimal> fromScores = this.parseFromFiexdScore(fromResultIntro.getScore());
        BigDecimal startScore1 = ObjectUtil.defaultIfNull(fromScores.get(0), BigDecimal.ZERO);
        BigDecimal endScore1 = ObjectUtil.defaultIfNull(fromScores.get(fromScores.size() - 1), BigDecimal.ZERO);
        List<BigDecimal> toScores = this.parseFromFiexdScore(toResultIntro.getScore());
        BigDecimal startScore2 = ObjectUtil.defaultIfNull(toScores.get(0), BigDecimal.ZERO);
        BigDecimal endScore2 = ObjectUtil.defaultIfNull(toScores.get(toScores.size() - 1), BigDecimal.ZERO);
        return startScore1.compareTo(endScore2) <= 0 && startScore2.compareTo(endScore1) <= 0;
    }

    private boolean checkFiexdScoreRangeOverlap(ScaleResultIntro fromResultIntro, ScaleResultIntro toResultIntro) {
        List<BigDecimal> fromScores = this.parseFromFiexdScore(fromResultIntro.getScore());
        List<BigDecimal> toScores = this.parseFromFiexdScore(toResultIntro.getScore());
        return fromScores.stream().anyMatch(fromScore -> toScores.stream().anyMatch(toScore -> toScore.compareTo(fromScore) == 0));
    }

    private boolean checkFiexdAndRatioMixRangeOverlap(ScaleResultIntro fromResultIntro, ScaleResultIntro toResultIntro) {
        //固定值
        List<BigDecimal> fromScores = this.parseFromFiexdScore(fromResultIntro.getScore());
        //区间
        List<BigDecimal> toScores = this.parseFromFiexdScore(toResultIntro.getScore());
        return fromScores.stream().anyMatch(fromScore -> {
            BigDecimal startScor = ObjectUtil.defaultIfNull(toScores.get(0), BigDecimal.ZERO);
            BigDecimal endScore = ObjectUtil.defaultIfNull(toScores.get(toScores.size() - 1), BigDecimal.ZERO);
            return fromScore.compareTo(startScor) >= 0 && fromScore.compareTo(endScore) <= 0;
        });
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ScaleResultIntro scaleResultIntro) {
            scaleResultIntroMapper.insert(scaleResultIntro);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(ScaleResultIntro scaleResultIntro) {
            ScaleResultIntro rawData = scaleResultIntroMapper.selectById(scaleResultIntro.getId());
            BeanUtils.copyProperties(scaleResultIntro, rawData);
            scaleResultIntroMapper.updateById(rawData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        scaleResultIntroMapper.deleteById(id);
    }

    @Override
    public Page<ScaleResultIntroDTO> seletePage(Long scaleId) {
        return Page.doSelectPage(() ->
                scaleResultIntroMapper.getList(scaleId).stream().peek(scaleResultIntroDTO -> {
                    //展示类型字符串转列表
                    if (scaleResultIntroDTO != null  && ObjectUtil.isNotEmpty(scaleResultIntroDTO.getOriginalShowType())) {
                        String[] showType = scaleResultIntroDTO.getOriginalShowType().split(",");
                        scaleResultIntroDTO.setShowType(Arrays.asList(showType));
                    }
                }).collect(Collectors.toList())
        );
    }

    @Override
    public ScaleResultIntroDTO findById(Long resultIntroId) {
        return scaleResultIntroMapper.findById(resultIntroId);
    }

    @Override
    public List<ScaleResultIntroDTO> seleteList(Long scaleId) {
        return scaleResultIntroMapper.getList(scaleId);
    }

    @Override
    public ScaleConvertResultIntro getConvertScore(Long scaleId, Long factorId, BigDecimal inversionScore) {
        BigDecimal convertResult = inversionScore;
        ScaleConvertResultIntro intro = new ScaleConvertResultIntro();
        intro.setConvertScore(convertResult);
        //一个因子维度可能会配置多个结果解读,因子需要根据原始得分去匹配符合分数条件的结果解读信息
        List<ScaleResultIntro> resultIntroList = scaleResultIntroMapper.findByScaleIdAndFactorId(scaleId, factorId);
        if (CollUtil.isEmpty(resultIntroList)) {
            return intro;
        }
        //根据分值匹配符合条件的结果解读信息
        for (ScaleResultIntro resultIntro : resultIntroList) {
            String originalScore = resultIntro.getScore();
            String convertScore = resultIntro.getConvertScore();
            Integer convertType = resultIntro.getScoreConvertType();
            convertResult = this.calculateConvertScore(convertType, originalScore, convertScore, inversionScore);
            if (ObjectUtil.isNotNull(convertResult)) {
                intro.setConvertScore(convertResult);
                intro.setResultIntroName(resultIntro.getIntro());
                intro.setScoreRange(resultIntro.getScore());
                break;
            }
        }
        return intro;
    }

    private BigDecimal calculateConvertScore(Integer convertType, String originalScore, String convertScore, BigDecimal inversionScore) {
        if (ConvertTypeEnum.RATIO.getCode().equals(convertType)) {
            return this.parseFromRatio(originalScore, convertScore, inversionScore);
        } else {
            return this.parseFromFiexd(originalScore, convertScore, inversionScore);
        }
    }

    private BigDecimal parseFromRatio(String originalScore, String convertScore, BigDecimal inversionScore) {
        try {
            BigDecimal currentScore = ObjectUtil.defaultIfNull(inversionScore, BigDecimal.ZERO);
            BigDecimal convertScores = new BigDecimal(ObjectUtil.defaultIfNull(convertScore, "0"));
            List<BigDecimal> scores = this.parseFromFiexdScore(originalScore);
            BigDecimal minLimit = ObjectUtil.defaultIfNull(scores.get(0), BigDecimal.ZERO);
            BigDecimal maxLimit = ObjectUtil.defaultIfNull(scores.get(scores.size() - 1), BigDecimal.ZERO);
            if (currentScore.compareTo(minLimit) >= 0 && currentScore.compareTo(maxLimit) <= 0) {
                convertScores = inversionScore.multiply(convertScores);
                return convertScores;
            }
        } catch (Exception e) {
            log.error("等比转换发生异常,原始得分区间:{}, 转换分值:{}", originalScore, convertScore, e);
            return null;
        }
        return null;
    }

    private BigDecimal parseFromFiexd(String originalScore, String convertScore, BigDecimal inversionScore) {
        try {
            BigDecimal currentScore = ObjectUtil.defaultIfNull(inversionScore, BigDecimal.ZERO);
            List<BigDecimal> scores = this.parseFromFiexdScore(originalScore);
            List<BigDecimal> convertScores = this.parseFromFiexdScore(convertScore);
            //原始分值需要与转换分值对应
            if (scores.size() != convertScores.size()) {
                return null;
            }
            for (int idx = 0; idx < scores.size(); idx++) {
                if (currentScore.compareTo(scores.get(idx)) != 0) {
                    continue;
                }
                //返回对应的转换分值
                return ObjectUtil.defaultIfNull(convertScores.get(idx), BigDecimal.ZERO);
            }
        } catch (Exception e) {
            log.error("非等比转换发生异常,原始得分区间:{}, 转换分值:{}", originalScore, convertScore, e);
            return null;
        }
        return null;
    }

    private List<BigDecimal> parseFromFiexdScore(String strScore) {
        try {
            if (StrUtil.isEmpty(strScore)) {
                return List.of();
            }
            if (strScore.startsWith("[") && strScore.endsWith("]")) {
                strScore = strScore.substring(1, strScore.length() - 1);
            }
            String[] strScores = ObjectUtil.defaultIfNull(strScore.split(","), new String[0]);
            BigDecimal[] valueArray = new BigDecimal[strScores.length];
            for (int i = 0; i < strScores.length; i++) {
                valueArray[i] = new BigDecimal(strScores[i].trim());
            }
            return Arrays.asList(valueArray);
        } catch (Exception e) {
            log.error("分数区间解析错误,得分区间:{}", strScore, e);
            return List.of();
        }
    }

    @Override
    public void saveBatchByNewFactor(ScaleCreateRelationEvent event, Map<Long, Long> map) {
        List<ScaleResultIntroDTO> list = scaleResultIntroMapper.getList(event.getOldScaleId());
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<ScaleResultIntro> resultIntroList = list.stream().map(resultIntroDTO -> {
            ScaleResultIntro resultIntro = new ScaleResultIntro();
            BeanUtils.copyProperties(resultIntroDTO, resultIntro);
            resultIntro.setId(null);
            resultIntro.setScaleId(event.getNewScaleId());
            resultIntro.setFactorId(map.get(resultIntro.getFactorId()));
            resultIntro.setShowType(resultIntroDTO.getOriginalShowType());
            return resultIntro;
        }).collect(Collectors.toList());
        scaleResultIntroMapper.insert(resultIntroList);
    }

    @EventListener
    public void onoApplication(ScaleDelEvent event) {
        scaleResultIntroMapper.delete(new LambdaQueryWrapper<ScaleResultIntro>().eq(ScaleResultIntro::getScaleId, event.getOldScaleId()));
    }
}
