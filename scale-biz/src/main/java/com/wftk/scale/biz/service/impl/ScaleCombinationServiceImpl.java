package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.ScaleEnum;
import com.wftk.scale.biz.dto.scale.*;
import com.wftk.scale.biz.entity.ScaleCombination;
import com.wftk.scale.biz.entity.ScaleCombinationDetail;
import com.wftk.scale.biz.mapper.ScaleCombinationMapper;
import com.wftk.scale.biz.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 组合量表(量表每次修改后均在此表写入一条数据) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Slf4j
@Service
public class ScaleCombinationServiceImpl extends ServiceImpl<ScaleCombinationMapper, ScaleCombination> implements ScaleCombinationService {

    /*
     * 量表复制名称标记符
     */
    private static final String SCALE_COPY_MARK = "_(复制)_";

    @Autowired
    private ScaleCombinationMapper scaleCombinationMapper;

    @Autowired
    private ScaleCombinationLatestService combinationLatestService;

    @Autowired
    private ScaleCombinationDetailService scaleCombinationDetailService;

    @Autowired
    private ScaleService scaleService;

    @Override
    public boolean validScaleName(Long scaleCombinationId, String scaleName) {
        boolean checkResult = false;
        if (StrUtil.isEmpty(scaleName)) {
            return checkResult;
        }
        ScaleCombination rawData = ObjectUtil.isNull(scaleCombinationId) ? null : scaleCombinationMapper.selectById(scaleCombinationId);
        String scaleCode = ObjectUtil.isNull(rawData) ? null : rawData.getCode();
        return scaleCombinationMapper.validScaleNameExists(scaleCombinationId, scaleCode, scaleName);
    }

    @Override
    public boolean vaildScaleConfDetails(List<ScaleCombinationDetail> details) {
        boolean checkResult = true;
        if (CollUtil.isEmpty(details)) {
            checkResult = false;
        }
        List<Long> uniqueList = details.stream().map(ScaleCombinationDetail::getScaleId).distinct().collect(Collectors.toList());
        if (details.size() != uniqueList.size()) {
            checkResult = false;
        }
        return checkResult;
    }

    @Override
    public boolean vaildCompletedStatus(Long scaleCombinationId) {
        ScaleCombination rawData = scaleCombinationMapper.selectById(scaleCombinationId);
        Integer status = ObjectUtil.isNotNull(rawData) ? rawData.getStatus() : ScaleEnum.SCALE_UN_COMPLETED.getCode();
        if (ScaleEnum.SCALE_COMPLETED.getCode().equals(status)) {
            return true;
        }
        return false;
    }

    private String createScaleCode() {
        //根据规则生成量表编码,当前简单实现
        return IdUtil.simpleUUID();
    }

    private String createVersion() {
        //根据规则生成量表版本号,当前简单实现
        return IdUtil.nanoId();
    }

    private String createScaleName(String name) {
        //根据规则生成量表复制名称,当前简单实现
        StringBuilder builder = new StringBuilder();
        builder.append(name)
                .append(SCALE_COPY_MARK)
                .append(DateUtil.formatTime(DateUtil.date()));
        return builder.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ScaleCombination scaleCombination, List<ScaleCombinationDetail> details) {
            scaleCombination.setCode(this.createScaleCode());
            scaleCombination.setVersion(this.createVersion());
            scaleCombination.setStatus(ObjectUtil.defaultIfNull(scaleCombination.getStatus(), ScaleEnum.SCALE_UN_COMPLETED.getCode()));
            scaleCombinationMapper.insert(scaleCombination);
            combinationLatestService.saveOrUpdateScaleLatest(scaleCombination.getId(), scaleCombination.getCode());
            scaleCombinationDetailService.create(scaleCombination.getId(), details);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(ScaleCombination scaleCombination, List<ScaleCombinationDetail> details) {
            Long oldScaleId = scaleCombination.getId();
            ScaleCombination rawData = scaleCombinationMapper.selectById(oldScaleId);
            BeanUtils.copyProperties(scaleCombination, rawData);
            scaleCombinationMapper.updateById(scaleCombination);
            scaleCombinationDetailService.modify(scaleCombination.getId(), details);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long scaleCombinationId) {
        scaleCombinationDetailService.deleteByCombinationId(scaleCombinationId);
        scaleCombinationMapper.deleteById(scaleCombinationId);
    }

    @Override
    public Page<ScaleCombinationQueryDTO> selectScalePage(String scaleName) {
        return Page.doSelectPage(() -> scaleCombinationMapper.getList(scaleName)).toPage(this::buildScaleData);
    }

    @Override
    public List<ScaleQueryDTO> selectScaleList(Long combinationId) {
        return scaleService.selectListByScaleNameAndCompleteStatus(null, ScaleEnum.SCALE_COMPLETED.getCode(), combinationId);
    }

    @Override
    public ScaleCombinationQueryDTO findByScaleId(Long scaleCombinationId) {
        ScaleCombination scaleCombination = scaleCombinationMapper.selectById(scaleCombinationId);
        ScaleCombinationQueryDTO dto = this.buildScaleData(scaleCombination);
        return dto;
    }

    @Override
    public ScaleCombinationQueryDTO findByScaleCode(String scaleCode) {
        ScaleCombination scaleCombination = scaleCombinationMapper.getLatestScaleByCode(scaleCode);
        ScaleCombinationQueryDTO dto = this.buildScaleData(scaleCombination);
        return dto;
    }

    private ScaleCombinationQueryDTO buildScaleData(ScaleCombination scaleCombination) {
        ScaleCombinationQueryDTO dto = ScaleCombinationQueryDTO.builder().build();
        BeanUtils.copyProperties(scaleCombination, dto);
        List<ScaleQueryDTO> details = scaleCombinationDetailService.findByCombinationId(scaleCombination.getId());
        dto.setDetails(details);
        dto.setNumOfScale(CollUtil.isEmpty(details) ? 0 : details.size());
        return dto;
    }

    private List<ScaleCombinationQueryDTO> buildScaleData(List<ScaleCombinationQueryDTO> list) {
        list = CollUtil.isEmpty(list) ? List.of() : list;
        return list.stream().map(dto -> {
            List<ScaleQueryDTO> details = scaleCombinationDetailService.findByCombinationId(dto.getId());
            dto.setDetails(details);
            dto.setNumOfScale(CollUtil.isEmpty(details) ? 0 : details.size());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateComplateStatus(Long scaleCombinationId, Integer complateStatus) {
        String opUser = AuthenticationHolder.getAuthentication().getAuthUser().getAccount();
        this.versionControl(scaleCombinationId, complateStatus, opUser);
        scaleCombinationMapper.updateComplateStatus(scaleCombinationId, complateStatus, opUser);
    }

    protected Long versionControl(Long scaleCombinationId, Integer status, String opUser) {
        Long newScaleId = null;
        //未完成状态:全量复制量表以及关联的基础数据,在复制的数据上进行修改。
        ScaleCombination rawData = scaleCombinationMapper.selectById(scaleCombinationId);
        String scaleCode = rawData.getCode();
        String scaleName = rawData.getName();
        //判断当前更新操作前量表是否处于已完成状态
        boolean isCompleted = ScaleEnum.SCALE_COMPLETED.getCode().equals(rawData.getStatus());
        //判断当前更新操作是否将量表设置为未完成状态
        boolean isUnCompleted = ScaleEnum.SCALE_UN_COMPLETED.getCode().equals(status);
        if (isUnCompleted && isCompleted) {
            //基于原始scaleCode,生成新的量表数据版本,将新版量表ID更新至版本控制表scale_combination_latest.
            newScaleId = this.generateNewScale(rawData, scaleCode, scaleName, opUser);
            scaleCombinationDetailService.copy(scaleCombinationId, newScaleId, opUser);
        }
        return newScaleId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyScale(Long scaleCombinationId) {
        ScaleCombination rawData = scaleCombinationMapper.selectById(scaleCombinationId);
        if (ObjectUtil.isNull(rawData)) {
            return null;
        }
        String opUser = AuthenticationHolder.getAuthentication().getAuthUser().getAccount();
        String scaleName = this.createScaleName(rawData.getName());
        Long newScaleId = this.generateNewScale(rawData, null, scaleName, opUser);
        scaleCombinationDetailService.copy(scaleCombinationId, newScaleId, opUser);
        return newScaleId;
    }

    @Override
    public ScaleCombination getLatestScaleByCode(String code) {
        return scaleCombinationMapper.getLatestScaleByCode(code);
    }

    @Override
    public List<ScaleCombinationDTO> getListedScaleCombination(String scaleCode, String terminalCode, String scaleName, Integer type, Integer listingShowType) {
        return scaleCombinationMapper.getListedScaleCombination(scaleCode, terminalCode, scaleName, type, listingShowType);
    }

    @Override
    public List<ScaleDTO> getListedScaleCombinationDetail(Long scaleCombinationId, String terminalCode) {
        return scaleCombinationMapper.getListedScaleCombinationDetail(scaleCombinationId, terminalCode);
    }

    @Override
    public List<ScaleListingDetailDTO> getScaleListingDetailDTO(List<ScaleSerialDTO> scaleSerials, String terminalCode) {
        return scaleCombinationMapper.getScaleListingDetailDTO(scaleSerials, terminalCode);
    }

    @Override
    public ScaleListingDetailDTO getScaleListingDetail(String scaleCode, Long listingId, String terminalCode) {
        return scaleCombinationMapper.getScaleListingDetail(scaleCode, listingId, terminalCode);
    }

    private Long generateNewScale(ScaleCombination oldScale, String scaleCode, String scaleName, String opUser) {
        ScaleCombination newScale = new ScaleCombination();
        BeanUtils.copyProperties(oldScale, newScale);
        scaleCode = StrUtil.isEmpty(scaleCode) ? this.createScaleCode() : scaleCode;
        newScale.setId(null);
        newScale.setCode(scaleCode);
        newScale.setStatus(ScaleEnum.SCALE_UN_COMPLETED.getCode());
        newScale.setVersion(this.createVersion());
        newScale.setName(scaleName);
        scaleCombinationMapper.insert(newScale);
        combinationLatestService.saveOrUpdateScaleLatest(newScale.getId(), scaleCode);
        return newScale.getId();
    }
}
