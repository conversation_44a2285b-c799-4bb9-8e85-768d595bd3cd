package com.wftk.scale.biz.dto.scale;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: ScaleFactorDTO
 * @Description:
 * @Author: mq
 * @Date: 2024/12/5
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleFactorDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 量表ID
     */
    private Long scaleId;

    /**
     * 因子名称
     */
    private String name;

    /**
     * 因子类型
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)// 前端要求转STRING
    private Integer type;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 题号，|隔开
     */
    private String questionId;

    /**
     * 题号，|隔开
     */
    private String questionNumber;

    /**
     * 量表公式名称
     */
    private String formulaLabel;

    /**
     * 量表公式
     */
    private String formula;


    /**
     * 公式值
     */
    private String formulaParam;


    /**
     * 1启用，0禁用
     */
    private Boolean enable;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 因子维度题目总分
     */
    private Integer totalScore;
}
