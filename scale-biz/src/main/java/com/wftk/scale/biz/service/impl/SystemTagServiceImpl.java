package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.EnableEnum;
import com.wftk.scale.biz.dto.export.SystemTagExportDto;
import com.wftk.scale.biz.entity.ScaleWarnConf;
import com.wftk.scale.biz.entity.SystemTag;
import com.wftk.scale.biz.excel.utils.ExcelUtil;
import com.wftk.scale.biz.mapper.SystemTagMapper;
import com.wftk.scale.biz.service.ScaleWarnConfService;
import com.wftk.scale.biz.service.SystemTagService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class SystemTagServiceImpl extends ServiceImpl<SystemTagMapper, SystemTag> implements SystemTagService {

    @Autowired
    private SystemTagMapper systemTagMapper;
    @Resource
    private ScaleWarnConfService scaleWarnConfService;

    @Override
    public boolean validSystemTagCode(Long id, String tagName) {
        boolean checkResult = false;
        if (StrUtil.isEmpty(tagName)) {
            return checkResult;
        }
        checkResult = systemTagMapper.validSystemTagCodeExist(id, tagName);
        return checkResult;
    }

    @Override
    public void create(SystemTag systemTag) {
        systemTag.setEnable(EnableEnum.ENABLE.getEnable());
        systemTagMapper.insert(systemTag);
    }

    @Override
    public void delete(Long tagId) {
        //校验下是否被预警阈值使用
        boolean exists = scaleWarnConfService.exists(new LambdaQueryWrapper<ScaleWarnConf>().eq(ScaleWarnConf::getTagId, tagId));
        if(exists){
            throw new BusinessException("当前预警设置已被量表预警阈值所引用");
        }
        systemTagMapper.deleteById(tagId);
    }

    @Override
    public void modify(SystemTag systemTag) {
        SystemTag rawData = systemTagMapper.selectById(systemTag.getId());
        BeanUtils.copyProperties(systemTag, rawData);
        systemTagMapper.updateById(rawData);
    }

    @Override
    public void updateEnable(Long tagId, Boolean enable) {
        if (ObjectUtil.isNull(tagId)) {
            return;
        }
        String opUser = AuthenticationHolder.getAuthentication().getAuthUser().getAccount();
        systemTagMapper.updateEnable(tagId, enable, opUser);
    }

    @Override
    public Page<SystemTag> selectPage(String tagName) {
        return Page.doSelectPage(() -> systemTagMapper.getList(tagName, null));
    }

    @Override
    public List<SystemTag> getListOfEnabled(String tagName) {
        return systemTagMapper.getList(tagName, EnableEnum.ENABLE.getEnable());
    }

    @Override
    public void export(HttpServletResponse response, String tagName){
        List<SystemTag> list = systemTagMapper.getList(tagName, null);
        List<SystemTagExportDto> collect = list.stream().map(systemTag -> {
            SystemTagExportDto exportDto = new SystemTagExportDto();
            BeanUtils.copyProperties(systemTag, exportDto);
            //手动设置转化字段
            exportDto.setStatus(systemTag.getEnable() ? "启用" : "禁用");
            return exportDto;
        }).collect(Collectors.toList());
        ExcelUtil.write(response, SystemTagExportDto.class, "预警设置导出", collect);
    }
}
