package com.wftk.scale.biz.converter;

import com.wftk.scale.biz.entity.ScaleWarnSetting;
import com.wftk.scale.biz.input.ScaleWarnSettingCreateInput;
import com.wftk.scale.biz.input.ScaleWarnSettingUpdateInput;
import com.wftk.scale.biz.output.ScaleWarnSettingDetailOutput;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ScaleWarnSettingConverter {


    ScaleWarnSettingDetailOutput entityToDetailOutput(ScaleWarnSetting entity);

    ScaleWarnSetting inputToEntity(ScaleWarnSettingCreateInput createInput);

    ScaleWarnSetting updateInputToEntity(ScaleWarnSettingUpdateInput updateInput);

}
