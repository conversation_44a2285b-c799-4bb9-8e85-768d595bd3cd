package com.wftk.scale.biz.converter;

import com.wftk.scale.biz.dto.user.sysdepartment.SysDepartmentCreateDTO;
import com.wftk.scale.biz.dto.user.sysdepartment.SysDepartmentUpdateDTO;
import com.wftk.scale.biz.entity.SysDepartment;
import com.wftk.scale.biz.vo.SysDepartmentVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface SysDepartmentConverter {

    SysDepartment sysDepartmentCreateDtoToSysDepartment(SysDepartmentCreateDTO dto);

    SysDepartment sysDepartmentUpdateDtoToSysDepartment(SysDepartmentUpdateDTO dto);

    SysDepartmentVO sysDepartmentToSysDepartmentVO(SysDepartment sysDepartment);
}
