package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 测评因子得分表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22 14:48:13
 */
@Data
@TableName("scale_user_factor_result")
public class ScaleUserFactorResult implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 测评记录id
     */
    private Long resultId;

    /**
     * 因子id，来自scale_factor的主键
     */
    private Long factorId;

    /**
     * 得分
     */
    private BigDecimal score;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 原始得分
     */
    private BigDecimal rawScore;

    /**
     * 结果解读
     */
    private String resultIntro;

    /**
     * 反转得分
     */
    private BigDecimal inversionScore;

    /**
     * 因子名称
     */
    private String factorName;


    /**
     * 得分区间
     */
    private String scoreSection;

    /**
     * 性质；0阴；1阳
     */
    private Boolean result;

    private String factorShowType;


}
