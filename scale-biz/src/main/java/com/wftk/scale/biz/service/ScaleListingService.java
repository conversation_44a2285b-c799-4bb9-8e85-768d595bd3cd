package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.listing.*;
import com.wftk.scale.biz.entity.ScaleListing;

/**
 * <p>
 * 量表上下架表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleListingService extends IService<ScaleListing> {

    /*
     * @Author: mq
     * @Description: 校验量表上架配置类型是否是用户分发类型
     * @Date: 2024/11/20 14:12
     * @Param: scaleListingId
     * @Param scaleId
     * @return: boolean
     **/
    boolean vaildShowTypeOfDistribution(Long scaleListingId);

    /*
    * @Author: mq
    * @Description: 校验量表上架配置信息是否处于上架状态
    * @Date: 2024/11/20 14:12
    * @Param: scaleListingId
    * @Param scaleId
    * @return: boolean
    **/
    boolean vaildScaleStatusOnShell(Long scaleListingId, Long scaleId);

    /*
     * @Author: mq
     * @Description:  根据条件检索单个量表可上架表信息
     * @Date: 2024/10/29 11:19
     * @Param: scaleListingParamDTO-检索条件
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.entity.ScaleListing>
     **/
    Page<ScaleListingQueryDTO> getScaleListing(ScaleListingParamDTO scaleListingParamDTO);

    /*
     * @Author: mq
     * @Description: 根据条件检索组合量表可上架表信息
     * @Date: 2024/10/30 10:06
     * @Param: combinationListingParamDTO-检索条件
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.listing.ScaleListingQueryDTO>
     **/
    Page<ScaleListingQueryDTO> getScaleCombinationListing(ScaleCombinationListingParamDTO combinationListingParamDTO);

    /* 
     * @Author: mq
     * @Description: 根据条件检索单个量表已上架终端信息
     * @Date: 2024/10/29 11:19
     * @scaleListedParamDTO-检索条件
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.entity.ScaleListing>
     **/
    Page<ScaleListedQueryDTO> getScaleListed(ScaleListedParamDTO scaleListedParamDTO);

    /*
    * @Author: mq
    * @Description: 根据条件检索单个量表已上架终端信息且页面展示
    * @Date: 2024/12/5 15:35
    * @Param: scaleListedParamDTO
    * @return: Page<ScaleListedQueryDTO>
    **/
    Page<ScaleListedQueryDTO> getScaleListedByPageDisplay(ScaleListedParamDTO scaleListedParamDTO);

    /*
     * @Author: mq
     * @Description: 根据条件检索组合量表已上架终端信息
     * @Date: 2024/10/29 17:08
     * @Param: scaleListedParamDTO-检索参数
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.listing.ScaleListingQueryDTO>
     **/
    Page<ScaleListedQueryDTO> getScaleCombinationListed(ScaleListedParamDTO scaleListedParamDTO);

    /*
     * @Author: mq
     * @Description: 根据条件检索组合量表已上架终端信息且页面展示
     * @Date: 2024/12/5 15:35
     * @Param: scaleListedParamDTO
     * @return: Page<ScaleListedQueryDTO>
     **/
    Page<ScaleListedQueryDTO> getScaleCombinationListedByPageDisplay(ScaleListedParamDTO scaleListedParamDTO);

    /*
     * @Author: mq
     * @Description: 量表上架
     * @Date: 2024/10/25 16:19
     * @Param: scaleListing-上架信息
     * @return: void
     **/
    void scaleOnShell(ScaleListing scaleListing);
    
    /*
     * @Author: mq
     * @Description: 量表下架
     * @Date: 2024/10/25 16:19
     * @Param: scaleListingId-上下架主键ID
     * @return: void
     **/
    void scaleOffShell(Long scaleListingId);
    
    /* 
     * @Author: mq
     * @Description: 生成二维码
     * @Date: 2024/10/29 17:26 
     * @Param: scaleListingId-上下架主键ID
     * @return: java.lang.String 
     **/
    String qrcode(Long scaleListingId);

    /*
     * @Author: mq
     * @Description: 根据条件检索单个量表可分发终端信息
     * @Date: 2024/10/30 16:49
     * @Param: scaleListedParamDTO
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO>
     **/
    Page<ScaleListedQueryDTO> getScaleDistribute(ScaleListedParamDTO scaleListedParamDTO);
    
    /* 
     * @Author: mq
     * @Description: 根据条件检索组合量表可分发终端信息
     * @Date: 2024/10/30 16:50 
     * @Param: scaleListedParamDTO  
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO> 
     **/
    Page<ScaleListedQueryDTO> getScaleCombinationDistribute(ScaleListedParamDTO scaleListedParamDTO);

    /*
    * @Author: mq
    * @Description: 根据上架ID获取量表封面
    * @Date: 2024/12/14 13:40
    * @Param: scaleListingId-上下架ID
    * @return: String
    **/
    String getCover(Long scaleListingId);

    /**
     * 根据id和类型查询
     *
     * @param targetId
     * @param type
     * @param terminalCode
     * @return
     */
    ScaleListing getByTargetIdAndType( Long targetId,  Integer type, String terminalCode);

    /**
     * 根据id查询报告地址
     * @param scaleListingId
     * @return
     */
    String getReportUrl(Long scaleListingId);
}
