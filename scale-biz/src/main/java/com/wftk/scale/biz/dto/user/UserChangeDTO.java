package com.wftk.scale.biz.dto.user;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class UserChangeDTO {

    /**
     * 用戶主鍵
     */
    @NotNull(message = "用户主键不能为空")
    private Long id;

    /**
     * 用户姓名
     */
    @NotBlank(message = "用户姓名不能为空")
    @Length(max = 50, message = "用户姓名长度不能超过50")
    private String name;

    /**
     * 性别 1男 2女
     */
    private Integer sex;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 备注
     */
    private String remark;

    /**
     * 部门id
     */
    @NotNull(message = "所属部门不能为空")
    private Long departmentId;

    /**
     * 用户类型
     */
    private Integer userType;

    /**
     * 额外信息
     */
    private String extraInfo;

    /**
     * 入学年份
     */
    private LocalDate entryDate;
}
