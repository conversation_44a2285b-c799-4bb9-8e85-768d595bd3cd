package com.wftk.scale.biz.ext.notice.email;

import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

public class ScaleWarnEmailNoticeRequest extends EmailNoticeRequest<ScaleWarnEmailNoticeRequest.ScaleWarnEmailNoticeRequestParams>{


    @Serial
    private static final long serialVersionUID = -5139720449774731877L;

    public ScaleWarnEmailNoticeRequest(String address, ScaleWarnEmailNoticeRequest.ScaleWarnEmailNoticeRequestParams params) {
        super(address, EmailSubjectEnum.SCALE_WARNING_SCENE.getEmailSubject(),params);
    }

    @Override
    public String getTemplate() {
        return "模板{}";
    }

    @Data
    @Builder
    public static class ScaleWarnEmailNoticeRequestParams implements Serializable {

        @Serial
        private static final long serialVersionUID = 5334529685734613881L;
    }

}
