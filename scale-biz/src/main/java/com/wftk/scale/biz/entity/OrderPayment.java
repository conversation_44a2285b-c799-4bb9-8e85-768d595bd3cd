package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22 14:48:13
 */
@TableName("order_payment")
public class OrderPayment implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务订单编号
     */
    private String orderNo;

    /**
     * 交易订单号
     */
    private String tradeNo;

    /**
     * 第三方交易号
     */
    private String extraTradeNo;

    /**
     * 支付状态(待支付、支付中、支付成功、支付失败、取消支付)
     */
    private Integer status;

    /**
     * 1支付宝2微信3现金4他人代付5.终端线下结算
     */
    private Integer payChannel;

    /**
     * 发起支付时间
     */
    private LocalDateTime paymentTime;

    /**
     * 支付结果时间
     */
    private LocalDateTime paymentResultTime;

    /**
     * 备注
     */
    private String memo;

    /**
     * 交易金额
     */
    private Integer tradeAmount;

    /**
     * 第三方返回编码
     */
    private String extraCode;

    /**
     * 第三方返回信息
     */
    private String extraMsg;

    /**
     * 第三方请求参数
     */
    private String extraReqJson;

    /**
     * 第三方响应参数
     */
    private String extraRespJson;

    /**
     * 第三方用户标识
     */
    private String extraUserId;

    /**
     * 支付渠道app_id
     */
    private String appId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 过期时间
     */
    private LocalDateTime expirTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getExtraTradeNo() {
        return extraTradeNo;
    }

    public void setExtraTradeNo(String extraTradeNo) {
        this.extraTradeNo = extraTradeNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(Integer payChannel) {
        this.payChannel = payChannel;
    }

    public LocalDateTime getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(LocalDateTime paymentTime) {
        this.paymentTime = paymentTime;
    }

    public LocalDateTime getPaymentResultTime() {
        return paymentResultTime;
    }

    public void setPaymentResultTime(LocalDateTime paymentResultTime) {
        this.paymentResultTime = paymentResultTime;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getTradeAmount() {
        return tradeAmount;
    }

    public void setTradeAmount(Integer tradeAmount) {
        this.tradeAmount = tradeAmount;
    }

    public String getExtraCode() {
        return extraCode;
    }

    public void setExtraCode(String extraCode) {
        this.extraCode = extraCode;
    }

    public String getExtraMsg() {
        return extraMsg;
    }

    public void setExtraMsg(String extraMsg) {
        this.extraMsg = extraMsg;
    }

    public String getExtraReqJson() {
        return extraReqJson;
    }

    public void setExtraReqJson(String extraReqJson) {
        this.extraReqJson = extraReqJson;
    }

    public String getExtraRespJson() {
        return extraRespJson;
    }

    public void setExtraRespJson(String extraRespJson) {
        this.extraRespJson = extraRespJson;
    }

    public String getExtraUserId() {
        return extraUserId;
    }

    public void setExtraUserId(String extraUserId) {
        this.extraUserId = extraUserId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public LocalDateTime getExpirTime() {
        return expirTime;
    }

    public void setExpirTime(LocalDateTime expirTime) {
        this.expirTime = expirTime;
    }

    @Override
    public String toString() {
        return "OrderPayment{" +
            "id = " + id +
            ", orderNo = " + orderNo +
            ", tradeNo = " + tradeNo +
            ", extraTradeNo = " + extraTradeNo +
            ", status = " + status +
            ", payChannel = " + payChannel +
            ", paymentTime = " + paymentTime +
            ", paymentResultTime = " + paymentResultTime +
            ", memo = " + memo +
            ", tradeAmount = " + tradeAmount +
            ", extraCode = " + extraCode +
            ", extraMsg = " + extraMsg +
            ", extraReqJson = " + extraReqJson +
            ", extraRespJson = " + extraRespJson +
            ", extraUserId = " + extraUserId +
            ", appId = " + appId +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
            ", deleted = " + deleted +
            ", createBy = " + createBy +
            ", updateBy = " + updateBy +
            ", tenantId = " + tenantId +
            ", expirTime = " + expirTime +
        "}";
    }
}
