package com.wftk.scale.biz.manager.report.handle;

import com.wftk.scale.biz.constant.ScaleReportConfSettingConstant;
import com.wftk.scale.biz.constant.enums.ScaleFactorTypeEnum;
import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.entity.ScaleReportConf;
import com.wftk.scale.biz.entity.ScaleUserFactorResult;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.manager.report.actuator.ChartActuator;
import com.wftk.scale.biz.manager.report.actuator.FormActuator;
import com.wftk.scale.biz.manager.report.actuator.RemarkActuator;
import com.wftk.scale.biz.manager.report.actuator.WordageActuator;
import com.wftk.scale.biz.manager.report.dto.base.*;
import com.wftk.scale.biz.manager.report.dto.content.FactorScoreContentDTO;
import com.wftk.scale.biz.service.ScaleFactorService;
import com.wftk.scale.biz.service.ScaleUserFactorResultService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @createDate 2025/9/8 17:42
 */
@Slf4j
@Component
public class FactorScoreHandle {

    @Resource
    ScaleFactorService scaleFactorService;

    @Resource
    ScaleUserFactorResultService scaleUserFactorResultService;

    @Resource
    ChartActuator chartActuator;

    @Resource
    FormActuator formActuator;

    @Resource
    WordageActuator wordageActuator;

    @Resource
    RemarkActuator remarkActuator;


    public FactorScoreContentDTO buildReportContent(ScaleUserResult scaleUserResult, ScaleReportConf scaleReportConf) {
        if (!scaleReportConf.getFactorAnalysisEnable()) {
            log.info("factorScore enable is close. resultId is {}",scaleUserResult.getId());
            return null;
        }

        Integer reportType = ScaleReportConfSettingConstant.ReportType.FACTOR_SCORE;

        // 查找其它因子
        List<ScaleFactor> scaleFactors = scaleFactorService.getFactorListByScaleIdAndType(scaleUserResult.getScaleId(), ScaleFactorTypeEnum.OTHER.getType());
        if(scaleFactors.isEmpty()){
            log.info("factorAnalyse factor is empty. resultId is {}",scaleUserResult.getId());
            return null;
        }
        List<Long> factorIds = scaleFactors.stream().map(ScaleFactor::getId).toList();

        // 获取图表
        ChartDTO chartDTO = null;
        if(scaleReportConf.getFactorAnalysisChartEnable()){
            chartDTO = chartActuator.doChart(scaleReportConf.getId(),reportType, scaleUserResult.getId(), factorIds);
        }

        // 获取表格
        FormDTO formDTO = null;
        if(scaleReportConf.getFactorAnalysisFormEnable()){
            formDTO = formActuator.doForm(scaleUserResult.getId(),factorIds, scaleReportConf.getId(), reportType);
        }

        // 获取文字
        String wordage = null;
        if(scaleReportConf.getFactorAnalysisWordageEnable()){
            List<FactorResultDTO> factorResults = new ArrayList<>();
            List<ScaleUserFactorResult> scaleUserFactorResults = scaleUserFactorResultService.getListByResultId(scaleUserResult.getId(), factorIds);
            for (ScaleUserFactorResult factorResult:scaleUserFactorResults) {
                FactorResultDTO factorResultDTO = new FactorResultDTO();
                factorResultDTO.setFactorName(factorResult.getFactorName());
                factorResultDTO.setScore(factorResult.getScore());
                factorResults.add(factorResultDTO);
            }
            wordage = wordageActuator.doWordage(factorResults, reportType);
        }

        String remark = null;
        if(scaleReportConf.getRemarkEnable()){
            remark = remarkActuator.doRemark(scaleReportConf.getId(),reportType);
        }

        FactorScoreContentDTO factorScoreContentDTO = new FactorScoreContentDTO();
        factorScoreContentDTO.setRemark(remark);
        factorScoreContentDTO.setForm(formDTO);
        factorScoreContentDTO.setChart(chartDTO);
        factorScoreContentDTO.setWordage( wordage);
        return factorScoreContentDTO;
    }


}
