package com.wftk.scale.biz.ext.wechat.core;

import com.wftk.scale.biz.ext.wechat.input.WechatPrepayInput;
import com.wftk.scale.biz.ext.wechat.input.WechatRefundInput;
import com.wftk.scale.biz.ext.wechat.output.WechatPaymentNotifyOutput;
import com.wftk.scale.biz.ext.wechat.output.WechatRefundOutput;
import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @create 2023/10/12 21:04
 */
public interface WechatClient {

    /**
     * 小程序支付
     * @param prepayInput
     * @return
     */
    String weappPrepay(WechatPrepayInput prepayInput);

    /**
     * H5支付
     * @param prepayInput
     * @return
     */
    String h5Prepay(WechatPrepayInput prepayInput);

    /**
     * native支付
     * @param prepayInput
     * @return
     */
    String nativePrepay(WechatPrepayInput prepayInput);


    /**
     * H5支付成功回调通知
     * @param httpServletRequest
     * @return
     */
    WechatPaymentNotifyOutput h5PaymentNotify(HttpServletRequest httpServletRequest);


    /**
     * 退款
     * @param wechatRefundInput
     * @return
     */
    WechatRefundOutput refund(WechatRefundInput wechatRefundInput);


    /**
     * 退款回调
     * @param httpServletRequest
     * @return
     */
    WechatRefundOutput refundNotify(HttpServletRequest httpServletRequest);


    /**
     * 根据三方订单号查询订单支付信息
     * @param orderNo
     * @param mchId
     * @return
     */
    WechatPaymentNotifyOutput queryPaymentInfo(String orderNo, String mchId);

    /**
     * 根据退款号查询订单退款信息
     * @param refundNo
     * @return
     */
    WechatRefundOutput queryRefundInfo(String refundNo);
}
