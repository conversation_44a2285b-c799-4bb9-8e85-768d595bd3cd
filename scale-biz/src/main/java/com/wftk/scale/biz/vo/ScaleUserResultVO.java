package com.wftk.scale.biz.vo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ScaleUserResultVO {

    private Long id;
    /**
     * 用户账号
     */
    private String account;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 性别 1男 2女
     */
    private Integer sex;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 所属部门名称
     */
    private String departmentName;
    /**
     * 所属部门ID
     */
    private Long departmentId;
    /**
     * 量表名称
     */
    private String scaleName;
    /**
     * 量表分类
     */
    private String scaleType;
    /**
     * 量表类型 单量表、组合量表
     */
    private Integer type;
    /**
     * 终端编码
     */
    private String terminalCode;
    /**
     * 终端名称
     */
    private String terminalName;
    /**
     * 测评耗时，单位:秒
     */
    private Integer cost;
    /**
     * 触达方式，页面展示/分发
     */
    private Integer contactMode;
    /**
     * 风险等级
     */
    private Integer riskLevel;
}
