package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 报告设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04 14:29:16
 */
@Data
@TableName("scale_report_conf_item")
public class ScaleReportConfItem implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 量表id。来自scale表的主键
     */
    private Long scaleId;

    /**
     * 报告配置id。来自scale_report_conf的主键
     */
    private Long reportConfId;

    /**
     * 因子id,全部代号: all
     */
    private String factorId;

    /**
     * 配置项编码
     */
    private String itemCode;

    /**
     * 配置项值
     */
    private String itemValue;

    /**
     * 配置项文件
     */
    private String itemFilePath;

    /**
     * 配置项描述
     */
    private String itemDescribe;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

}
