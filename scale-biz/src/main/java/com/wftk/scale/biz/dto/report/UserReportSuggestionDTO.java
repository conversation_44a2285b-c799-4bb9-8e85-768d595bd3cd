package com.wftk.scale.biz.dto.report;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2024/12/5 17:41
 */
@Data
public class UserReportSuggestionDTO {

    /**
     * 建议
     */
    private String suggestion;

    /**
     * 图标路径
     */
    @RFile(role = FileConstant.FILE_SCALE_SIGN_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    private String filePath;

}
