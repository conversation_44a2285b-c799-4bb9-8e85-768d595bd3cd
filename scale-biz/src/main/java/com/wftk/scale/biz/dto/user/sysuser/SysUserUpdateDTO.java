package com.wftk.scale.biz.dto.user.sysuser;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SysUserUpdateDTO implements Serializable {

    @NotNull(message = "用户id不能为空")
    private Long id;

    @NotBlank(message = "用户姓名不能为空")
    @Length(max = 50, message = "用户姓名长度不能超过50")
    private String name;

    private Integer sex;

    private LocalDate birthDay;

    @Length(min = 11, max = 11, message = "手机号格式有误")
    @Pattern(regexp = "^(1[3-9])\\d{9}$", message = "手机号格式有误")
    private String tel;

    @Pattern(regexp = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@[a-zA-Z0-9-]+\\.[a-zA-Z]{2,}$", message = "邮箱格式错误")
    @Length(max = 50, message = "邮箱长度不能超过50")
    private String email;

    private LocalDate entryDate;

    @NotNull(message = "归属部门不能为空")
    private Long departmentId;

    @NotEmpty(message = "角色不能为空")
    private List<Long> roleIdList;

    @Length(min = 18, max = 18, message = "身份证号码格式错误")
    @Pattern(regexp = "^[1-9]\\d{5}(?:19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$", message = "身份证号码格式错误")
    private String idCard;

    @Length(max = 255, message = "备注长度不能超过255")
    private String remark;
}
