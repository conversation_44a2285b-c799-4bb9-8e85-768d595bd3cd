package com.wftk.scale.biz.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wftk.scale.biz.excel.converter.*;
import com.wftk.scale.biz.excel.model.base.BaseExcelDataDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ScaleWarnDetailExcelDataDTO extends BaseExcelDataDTO {

    @ExcelProperty("用户账号")
    private String account;

    @ExcelProperty("用户姓名")
    private String userName;
    /**
     * 性别 1男 2女
     */
    @ExcelProperty(value = "性别", converter = SexConverter.class)
    private Integer sex;
    /**
     * 所属部门名称
     */
    @ExcelProperty("归属部门")
    private String departmentName;
    /**
     * 手机号
     */
    @ExcelProperty("手机号码")
    private String phone;
    /**
     * 量表名称
     */
    @ExcelProperty("量表名称")
    private String scaleName;
    /**
     * 量表分类
     */
    @ExcelProperty("量表分类")
    private String scaleType;

    /**
     * 量表类型 单量表/组合量表
     */
    @Schema(description = "量表类型 单量表/组合量表")
    @ExcelProperty(value = "量表类型", converter = ScaleTypeConverter.class)
    private Integer type;

    /**
     * 终端名称
     */
    @ExcelProperty("测评终端")
    private String terminalName;

    /**
     * 测评时间
     */
    @Schema(description = "测评时间")
    @ExcelProperty(value = "测评时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime assessmentTime;

    /**
     * 测评耗时，单位:秒
     */
    @Schema(description = "测评耗时，单位:秒")
    @ExcelProperty("测评耗时（秒）")
    private Integer cost;

    /**
     * 触达方式，页面展示/分发
     */
    @Schema(description = "触达方式，页面展示/分发")
    @ExcelProperty(value = "触达方式", converter = ScaleListingShowTypeConverter.class)
    private Integer contactMode;
    /**
     * 测评结果
     */
    @Schema(description = "测评结果")
    @ExcelProperty(value = "测评结果")
    private String tagName;
    /**
     * 风险等级
     */
    @Schema(description = "风险等级")
    @ExcelProperty(value = "风险等级", converter = ScaleWarnSettingLevelConverter.class)
    private Integer riskLevel;
    /**
     * 状态
     */
    @Schema(description = "状态")
    @ExcelProperty(value = "状态", converter = ScaleWarnStatusConverter.class)
    private Integer status;

    @Schema(description = "创建时间")
    @ExcelProperty(value = "创建时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    /**
     * 接收预警用户名称
     */
    @Schema(description = "接收预警用户名称")
    @ExcelProperty("接警用户名称")
    private String receivingWarnUserName;

    /**
     * 预警方式
     */
    @Schema(description = "预警方式 (1：短信；2：邮件；3：加红标注)")
    @ExcelProperty(value = "预警方式", converter = ScaleWarnNoticeTypeConverter.class)
    private Integer noticeType;

}
