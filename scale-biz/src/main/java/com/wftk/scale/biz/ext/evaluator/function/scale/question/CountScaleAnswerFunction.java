package com.wftk.scale.biz.ext.evaluator.function.scale.question;

import java.util.List;
import java.util.Map;

import com.googlecode.aviator.runtime.type.AviatorDouble;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;
import com.wftk.scale.biz.ext.evaluator.function.scale.BaseScaleEvaluationFunction;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 根据量表题目ID统计匹配指定答案的数量
 * 示例: q_count("1,2,3", "A"), 代表统计题目ID为1,2,3题中回答为A的数量
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
public class CountScaleAnswerFunction extends BaseScaleEvaluationFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Question.Q_COUNT;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env, final AviatorObject arg1, final AviatorObject arg2) {
        String questionIds = (String) arg1.getValue(env);
        String answer = (String) arg2.getValue(env);
        logger.info("q_count: questionIds: {}, answer: {}", questionIds, answer);
        if (StrUtil.isBlank(questionIds) || StrUtil.isBlank(answer)) {
            throw new IllegalArgumentException("questionIds or answer must not be blank");
        }
        List<ScaleUserResultRecord> resultItemDetailByQuestionIds = getUserResultOptions(env, questionIds, true);
        if (CollUtil.isEmpty(resultItemDetailByQuestionIds)) {
            return new AviatorDouble(0);
        }
        return new AviatorDouble(resultItemDetailByQuestionIds.stream()
                .filter(item -> answer.equals(item.getAnswer()))
                .count());
    }

}
