package com.wftk.scale.biz.service;

import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.entity.ScaleWarnSetting;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.input.ScaleWarnSettingCreateInput;
import com.wftk.scale.biz.input.ScaleWarnSettingPageQueryInput;
import com.wftk.scale.biz.input.ScaleWarnSettingUpdateInput;
import com.wftk.scale.biz.output.ScaleScaleFactorOutput;
import com.wftk.scale.biz.output.ScaleWarnSettingDetailOutput;
import com.wftk.scale.biz.output.ScaleWarnSettingPageQueryOutput;

import java.util.List;

/**
 * <p>
 * 预警设置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04 16:56:01
 */
public interface ScaleWarnSettingService extends IService<ScaleWarnSetting> {

    Page<ScaleWarnSettingPageQueryOutput> pageQuery(ScaleWarnSettingPageQueryInput input);

    List<ScaleScaleFactorOutput> queryScaleScaleFactor();

    ScaleWarnSettingDetailOutput getDetailById(Long id);

    void create(ScaleWarnSettingCreateInput createInput);

    void update(ScaleWarnSettingUpdateInput updateInput);

    void delete(Long id);
}
