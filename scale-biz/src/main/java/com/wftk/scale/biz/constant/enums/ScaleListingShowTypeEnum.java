package com.wftk.scale.biz.constant.enums;

import com.wftk.common.core.enums.BaseEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @createDate 2024/11/21 11:00
 */
@Getter
public enum ScaleListingShowTypeEnum implements BaseEnum {

    PAGE_SHOW(1,"页面展示"),
    USER_DISTRIBUTION(2,"用户分发");

    private Integer value;

    private String desc;

    ScaleListingShowTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @Override
    public String getLabel() {
        return desc;
    }
}
