package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.scale.ScaleQuestionHighestOverallScoreDTO;
import com.wftk.scale.biz.entity.ScaleQuestionOption;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 问题选项表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleQuestionOptionMapper extends BaseMapper<ScaleQuestionOption> {

    /*
     * @Author: mq
     * @Description: 根据量表ID和题目ID获取题目选项信息
     * @Date: 2024/11/4 17:12
     * @Param: scaleId
     * @Param: questionId
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleQuestionOption>
     **/
    List<ScaleQuestionOption> findByScaleIdAndQuestionId(@Param("scaleId") Long scaleId, @Param("questionId") Long questionId);

    /*
     * @Author: mq
     * @Description: 根据量表ID和题目ID删除选项数据
     * @Date: 2024/11/5 15:28
     * @Param: scaleId
     * @Param: questionId
     * @return: void
     **/
    void deleteByScaleIdAndQuestionId(@Param("scaleId") Long scaleId, @Param("questionId") Long questionId);

    /**
     * 获取总分
     * @param scaleId
     * @return
     */
    BigDecimal getQuestionTotalScore(@Param("scaleId") Long scaleId);

    ScaleQuestionHighestOverallScoreDTO findOverallScoreByScaleId(@Param("scaleId") Long scaleId, @Param("questionIds") List<Long> questionIds);

}
