package com.wftk.scale.biz.config;

import com.wftk.scale.biz.event.publisher.ScaleCreatePublisher;
import com.wftk.scale.biz.event.publisher.ScaleCreateRelationPublisher;
import com.wftk.scale.biz.event.publisher.ScaleDelPublisher;
import com.wftk.scale.biz.event.publisher.ScaleEvaluationIntroPublisher;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2024/4/30 14:06
 */
@Configuration
@ComponentScan("com.wftk.scale.biz.event.listener")
public class BizEventConfig {

    @Bean
    ScaleEvaluationIntroPublisher alipayDrawEventPublisher(ApplicationEventPublisher applicationEventPublisher){
        return new ScaleEvaluationIntroPublisher(applicationEventPublisher);
    }

    @Bean
    ScaleCreatePublisher scaleCreatePublisher(ApplicationEventPublisher applicationEventPublisher){
        return new ScaleCreatePublisher(applicationEventPublisher);
    }

    @Bean
    ScaleCreateRelationPublisher scaleCreateRelationPublisher(ApplicationEventPublisher applicationEventPublisher){
        return new ScaleCreateRelationPublisher(applicationEventPublisher);
    }

    @Bean
    ScaleDelPublisher scaleDelPublisher(ApplicationEventPublisher applicationEventPublisher){
        return new ScaleDelPublisher(applicationEventPublisher);
    }





}
