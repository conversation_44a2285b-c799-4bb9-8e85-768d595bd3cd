package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.user.role.SysRoleCreateDTO;
import com.wftk.scale.biz.dto.user.role.SysRoleQueryDTO;
import com.wftk.scale.biz.dto.user.role.SysRoleUpdateDTO;
import com.wftk.scale.biz.dto.user.role.SysRoleUpdateEnableDTO;
import com.wftk.scale.biz.entity.SysRole;
import com.wftk.scale.biz.vo.SysRoleVO;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <p>
 * 角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface SysRoleService extends IService<SysRole> {

    Page<SysRoleVO> getList(SysRoleQueryDTO dto);

    SysRoleVO detailById(Long id);

    void create(SysRoleCreateDTO dto);

    void update(SysRoleUpdateDTO dto);

    void updateStatus(SysRoleUpdateEnableDTO dto);

    void deleteById(Long id);

    void export(SysRoleQueryDTO dto, HttpServletResponse response);
}
