package com.wftk.scale.biz.constant.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @EnumName: ScaleWarnConfTypeEnum
 * @Description: 量表预警阈值配置类型
 * @Author: mq
 * @Date: 2024-10-31 17:09
 * @Version: 1.0
 **/
@Getter
public enum ScaleWarnConfTypeEnum {

    GREATER(1, "大于"),

    EQUAL(2, "等于"),

    LESS(3, "小于"),

    INTERVAL(4, "区间");


    private Integer type;

    private String desc;

    ScaleWarnConfTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static ScaleWarnConfTypeEnum getScaleWarnConfTypeEnumByType(Integer type) {
        for (ScaleWarnConfTypeEnum typeEnum : ScaleWarnConfTypeEnum.values()) {
            if (Objects.equals(typeEnum.getType(), type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
