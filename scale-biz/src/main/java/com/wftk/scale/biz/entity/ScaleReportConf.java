package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 报告设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04 14:29:16
 */
@Data
@TableName("scale_report_conf")
public class ScaleReportConf implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 量表id。来自scale表的主键
     */
    private Long scaleId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 用户编码开关
     */
    private Boolean userCodeEnable;

    /**
     * 用户姓名开关
     */
    private Boolean userNameEnable;

    /**
     * 用户性别开关
     */
    private Boolean userSexEnable;

    /**
     * 用户手机号开关
     */
    private Boolean userPhoneEnable;

    /**
     * 用户生日开关
     */
    private Boolean userBirthdayEnable;

    /**
     * 用户身份证号开关
     */
    private Boolean userIdCardEnable;

    /**
     * 测评时间开关
     */
    private Boolean evaluationTimeEnable;

    /**
     * 阅读须知开关
     */
    private Boolean readingInstructionsEnable;

    /**
     * 备注开关
     */
    private Boolean remarkEnable;

    /**
     * 总分开关
     */
    private Boolean totalScoreEnable;

    /**
     * 总分图表开关
     */
    private Boolean totalScoreChartEnable;

    /**
     * 总分文字开关
     */
    private Boolean totalScoreWordageEnable;

    /**
     * 总分表格开关
     */
    private Boolean totalScoreFormEnable;

    /**
     * 平均分开关
     */
    private Boolean avgScoreEnable;

    /**
     * 平均分图表开关
     */
    private Boolean avgScoreChartEnable;

    /**
     * 平均分文字开关
     */
    private Boolean avgScoreWordageEnable;

    /**
     * 平均分表格开关
     */
    private Boolean avgScoreFormEnable;

    /**
     * 阳性数量开关
     */
    private Boolean positiveCountEnable;

    /**
     * 阳性数量图表开关
     */
    private Boolean positiveCountChartEnable;

    /**
     * 阳性数量文字开关
     */
    private Boolean positiveCountWordageEnable;

    /**
     * 阳性数量表格开关
     */
    private Boolean positiveCountFormEnable;

    /**
     * 结果解读开关
     */
    private Boolean resultIntroEnable;

    /**
     * 音频视频解说开关
     */
    private Boolean audioAndVideoNarrateEnable;

    /**
     * 改善建议开关
     */
    private Boolean improvementSuggestionEnable;

    /**
     * 因子分析开关
     */
    private Boolean factorAnalysisEnable;

    /**
     * 因子分析图表开关
     */
    private Boolean factorAnalysisChartEnable;

    /**
     * 因子分析文字开关
     */
    private Boolean factorAnalysisWordageEnable;

    /**
     * 因子分析表格开关
     */
    private Boolean factorAnalysisFormEnable;


    /**
     * 因子结果解读开关
     */
    private Boolean factorResultIntroEnable;

    /**
     * 因子音频视频解说开关
     */
    private Boolean factorAudioAndVideoNarrateEnable;

    /**
     * 因子改善建议开关
     */
    private Boolean factorImprovementSuggestionEnable;


}
