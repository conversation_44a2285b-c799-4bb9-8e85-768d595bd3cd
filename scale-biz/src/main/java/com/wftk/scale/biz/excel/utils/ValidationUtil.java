package com.wftk.scale.biz.excel.utils;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.util.FieldUtils;
import jakarta.validation.ConstraintViolation;

import java.lang.reflect.Field;
import java.util.Objects;

/**
 * @ClassName: ValidationUtil
 * @Description:
 * @Author: mq
 * @Date: 2024/11/29
 * @Version: 1.0
 **/
public class ValidationUtil {

    public static String getMessage(ConstraintViolation<?> constraintViolation) {
        String message = constraintViolation.getMessage();
        if (!message.contains("{fieldTitle}")) {
            return message;
        }
        String fieldTitle = "";
        Class<?> rootBeanClass = constraintViolation.getRootBeanClass();
        if (Objects.nonNull(rootBeanClass)) {
            Field field = FieldUtils
                    .getField(rootBeanClass, constraintViolation.getPropertyPath().toString(), true);
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (Objects.nonNull(excelProperty) && excelProperty.value().length != 0) {
                fieldTitle = excelProperty.value()[0];
            }
        }
        return message.replace("{fieldTitle}", fieldTitle);
    }
}
