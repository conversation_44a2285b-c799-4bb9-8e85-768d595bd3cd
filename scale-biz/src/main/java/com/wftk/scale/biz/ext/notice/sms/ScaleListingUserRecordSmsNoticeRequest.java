package com.wftk.scale.biz.ext.notice.sms;

import com.wftk.scale.biz.ext.sms.scene.SmsSceneEnum;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;

public class ScaleListingUserRecordSmsNoticeRequest extends SmsNoticeRequest<ScaleListingUserRecordSmsNoticeRequest.ScaleListingUserRecordSmsParams>{


    @Serial
    private static final long serialVersionUID = -3639961782235749741L;

    public ScaleListingUserRecordSmsNoticeRequest(String tel, ScaleListingUserRecordSmsParams scaleListingUserRecordSmsParams) {
        super(tel, SmsSceneEnum.SCALE_LISTING_USER_RECORD.name(), scaleListingUserRecordSmsParams);
    }

    @Data
    @Builder
    public static class ScaleListingUserRecordSmsParams {



    }

}
