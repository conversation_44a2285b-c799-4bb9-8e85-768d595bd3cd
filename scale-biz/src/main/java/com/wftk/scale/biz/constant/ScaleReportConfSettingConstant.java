package com.wftk.scale.biz.constant;

/**
 * <AUTHOR>
 * @createDate 2024/12/4 15:02
 */
public interface ScaleReportConfSettingConstant {

    // 1.阅读须知 2.备注 3.表格设置 4.图表设置
    interface Type{

        /**
         * 1.阅读须知
         */
        Integer READING_INSTRUCT = 1;

        /**
         * 2.备注
         */
        Integer REMARK = 2;

        /**
         * 3.表格设置
         */
        Integer FORM_SETTING = 3;

        /**
         * 4.图表设置
         */
        Integer CHART_SETTING = 4;
    }

    // 1.总分  2.总均分 3.阳性数量 4.因子分析
    interface ReportType{
        Integer TOTAL = 1;

        Integer AVG = 2;

        Integer POSITIVE_COUNT = 3;

        Integer FACTOR_SCORE = 4;

    }

    // 1.横向分段  2.柱状图  3.折线图 4.钟表图
    interface ChartType{

        Integer SINGLE_HORIZONTAL = 1;

        Integer BAR = 2;

        Integer LINE = 3;

        Integer CLOCK = 4;
    }



}
