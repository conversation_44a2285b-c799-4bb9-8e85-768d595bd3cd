package com.wftk.scale.biz.ext.notice.sms;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.wftk.jackson.core.JSONObject;
import com.wftk.scale.biz.entity.NoticeMessage;
import com.wftk.scale.biz.ext.notice.NoticeHandler;
import com.wftk.scale.biz.ext.notice.NoticeRequest;
import com.wftk.scale.biz.ext.notice.NoticeResult;
import com.wftk.scale.biz.ext.notice.enums.NoticeTypeEnum;
import com.wftk.scale.biz.ext.sms.SmsApi;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SmsNoticeHandler extends NoticeHandler<SmsNoticeRequest> {

    private final SmsApi smsApi;

    public SmsNoticeHandler(SmsApi smsApi) {
        super(NoticeTypeEnum.SMS);
        this.smsApi = smsApi;
    }

    @Override
    public NoticeResult sendNotice(SmsNoticeRequest smsRequest) {
        try {
            Assert.notBlank(smsRequest.getScene(), "场景不能为空");
            Assert.notBlank(smsRequest.getTel(), "接收用户电话不能为空");
            Assert.notNull(smsRequest.getParams(), "模板参数不能为空");
            boolean result = smsApi.sendSmsMsg(smsRequest);
            log.info("SMS send message result is:{}", result);
            if ( result) {
                return NoticeResult.builder().ok(true).build();
            }
            return NoticeResult.builder().ok( false).build();
        } catch (Exception e) {
            log.error("SMS send error: ", e);
            return NoticeResult.builder().ok( false).message(e.getMessage()).build();

        }
    }

    @Override
    public NoticeRequest buildNoticeRequest(NoticeMessage noticeMessage) {
        String content = noticeMessage.getContent();
        if (StrUtil.isBlank(content)) {
            return null;
        }
        return JSONObject.getInstance().parseObject(content, SmsNoticeRequest.class);
    }
}
