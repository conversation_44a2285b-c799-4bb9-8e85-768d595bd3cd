package com.wftk.scale.biz.constant.enums;


import lombok.Getter;

/**
 * @EnumName: ConvertTypeEnum
 * @Description: 因子分值转换方式
 * @Author: mq
 * @Date: 2024/11/26
 * @Version: 1.0
 **/
@Getter
public enum DictCodeEnum {

    PATHTYPE("PATHTYPE", "路由类型"),
    ;
    private String code;

    private String desc;

    DictCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
