package com.wftk.scale.biz.constant.enums;

import java.util.Arrays;

public enum ScaleWarnSettingScopeEnum {

    // 个人
    PERSON(1),
    // 部门
    DEPARTMENT(2);

    private Integer value;

    ScaleWarnSettingScopeEnum(Integer value){
        this.value = value;
    }

    public Integer getValue(){
        return value;
    }

    public static ScaleWarnSettingScopeEnum getScaleWarnSettingScopeEnumByValue(Integer value){
        return Arrays.stream(ScaleWarnSettingScopeEnum.values())
                .filter(item -> item.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
}
