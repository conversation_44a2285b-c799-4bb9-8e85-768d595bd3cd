package com.wftk.scale.biz.ext.evaluator.function.scale;

import java.util.List;
import java.util.Map;

import com.googlecode.aviator.runtime.type.AviatorDouble;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.ext.evaluator.constant.EnvConstant;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;
import com.wftk.scale.biz.service.ScaleUserResultRecordService;

import cn.hutool.core.collection.CollUtil;

/**
 * 统计量表平均分
 * 示例: s_avg(), 表示统计当前量表的平均分
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
public class ScaleAvgScoreFunction extends BaseScaleEvaluationFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Scale.S_AVG;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env) {
        Long resultId = getFromEnv(env, EnvConstant.RESULT_ID, Long.class, false);
        logger.info("s_avg: resultId: {}, scaleId: {}", resultId, env.get(EnvConstant.SCALE_ID));
        ScaleUserResultRecordService scaleUserResultRecordService = getFromEnv(env,
                EnvConstant.SCALE_USER_RESULT_RECORD_SERVICE, ScaleUserResultRecordService.class, false);
        List<ScaleUserResultRecord> scaleUserResultRecords = scaleUserResultRecordService.findByResultId(resultId);
        double totalScore = 0;
        if (CollUtil.isEmpty(scaleUserResultRecords)) {
            throw new IllegalArgumentException("scaleUserResultRecords must not be empty");
        } else {
            totalScore = sumScore(scaleUserResultRecords);
        }
        double avg = (int) (totalScore / scaleUserResultRecords.size() * 100) / 100;
        return AviatorDouble.valueOf(avg);
    }

}
