package com.wftk.scale.biz.manager.report.actuator;

import com.wftk.scale.biz.constant.ScaleReportConfItemConstant;
import com.wftk.scale.biz.entity.ScaleReportConfItem;
import com.wftk.scale.biz.manager.report.dto.base.AudioDTO;
import com.wftk.scale.biz.service.ScaleReportConfItemService;
import com.wftk.scale.biz.util.ScopeUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/9/10 20:39
 */
@Slf4j
@Component
public class AudioActuator {

    private final String AUDIO_CODE = ScaleReportConfItemConstant.AUDIO_CODE;

    @Resource
    ScaleReportConfItemService scaleReportConfItemService;

    public AudioDTO doAudio(Long scaleReportConfId, BigDecimal score, String factorName,Long factorId) {
        AudioDTO audioDTO = new AudioDTO();
        // 查找音频
        // 根据因子和全部查询
        List<ScaleReportConfItem> scaleReportConfItems = scaleReportConfItemService.findByReportConfIdAndFactorId(scaleReportConfId, List.of(AUDIO_CODE),factorId);
        for (ScaleReportConfItem scaleReportConfItem : scaleReportConfItems) {
            String itemDescribe = scaleReportConfItem.getItemDescribe();
            Boolean inScope = ScopeUtil.inScope(ScopeUtil.parseSectionStr(itemDescribe), score);
            if (inScope) {
                audioDTO.setFactorName(factorName);
                audioDTO.setFilePath(scaleReportConfItem.getItemFilePath());
                break;
            }
        }
        return audioDTO;
    }

}
