package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.scale.biz.constant.ScaleReportConfSettingConstant;
import com.wftk.scale.biz.dto.report.ChartSettingDTO;
import com.wftk.scale.biz.dto.report.FormSettingDTO;
import com.wftk.scale.biz.dto.report.RemarkSettingDTO;
import com.wftk.scale.biz.dto.scale.ScaleReportConfSettingDetailDTO;
import com.wftk.scale.biz.entity.ScaleReportConfSetting;
import com.wftk.scale.biz.mapper.ScaleReportConfSettingMapper;
import com.wftk.scale.biz.service.ScaleReportConfSettingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11 15:20:06
 */
@Service
public class ScaleReportConfSettingServiceImpl extends ServiceImpl<ScaleReportConfSettingMapper, ScaleReportConfSetting> implements ScaleReportConfSettingService {

    @Resource
    ScaleReportConfSettingMapper scaleReportConfSettingMapper;

    @Override
    public Integer delByReportConfId(Long reportConfId) {
        return scaleReportConfSettingMapper.delByReportConfId(reportConfId);
    }

    @Override
    public ScaleReportConfSettingDetailDTO getDetailByReportConfId(Long reportConfId) {
        List<ScaleReportConfSetting> settingList = scaleReportConfSettingMapper.selectByReportConfId(reportConfId);
        if (settingList.isEmpty()) {
            return null;
        }
        Map<Integer, List<ScaleReportConfSetting>> byType = settingList.stream()
                .collect(Collectors.groupingBy(ScaleReportConfSetting::getType));
        ScaleReportConfSettingDetailDTO settingDetailDTO = new ScaleReportConfSettingDetailDTO();
        byType.forEach((type, scaleReportConfSettings) -> {
            // 1.阅读须知 2.备注 3.表格设置 4.图表设置
            switch (type) {
                case 1 -> {
                    if (scaleReportConfSettings.size() > 1) {
                        throw new BusinessException("阅读须知数据异常");
                    }
                    ScaleReportConfSetting scaleReportConfSetting = scaleReportConfSettings.get(0);
                    settingDetailDTO.setReadingInstruction(scaleReportConfSetting.getValue());
                }
                case 2 -> {
                    List<RemarkSettingDTO> remarkSettings = new ArrayList<>();
                    scaleReportConfSettings.forEach(scaleReportConfSetting -> {
                        // 设置备注
                        RemarkSettingDTO remarkSetting = new RemarkSettingDTO();
                        remarkSetting.setRemark(scaleReportConfSetting.getValue());
                        remarkSetting.setType(String.valueOf(scaleReportConfSetting.getReportType()));
                        remarkSettings.add(remarkSetting);
                    });
                    settingDetailDTO.setRemarkSettings(remarkSettings);
                }
                case 3 -> {
                    List<FormSettingDTO> formSettings = new ArrayList<>();
                    scaleReportConfSettings.forEach(scaleReportConfSetting -> {
                        // 设置表格设置
                        FormSettingDTO formSetting = new FormSettingDTO();
                        formSetting.setColumn(Arrays.asList(scaleReportConfSetting.getValue().split(",")));
                        formSetting.setType(String.valueOf(scaleReportConfSetting.getReportType()));
                        formSettings.add(formSetting);
                    });
                    settingDetailDTO.setFormSettings(formSettings);
                }
                case 4 -> {
                    List<ChartSettingDTO> chartSettings = new ArrayList<>();
                    scaleReportConfSettings.forEach(scaleReportConfSetting -> {
                        // 设置图表
                        ChartSettingDTO chartSetting = new ChartSettingDTO();
                        chartSetting.setChartStyle(scaleReportConfSetting.getValue());
                        chartSetting.setType(String.valueOf(scaleReportConfSetting.getReportType()));
                        chartSetting.setChartType(String.valueOf(scaleReportConfSetting.getChartType()));
                        chartSettings.add(chartSetting);
                    });
                    settingDetailDTO.setChartSettings(chartSettings);
                }
                default -> throw new BusinessException("invalid type.");
            }
        });

        return settingDetailDTO;
    }

    @Override
    public List<ScaleReportConfSetting> selectScaleReportConfSettingByReportConfId(Long reportConfId, Integer type, Integer reportType) {
        return scaleReportConfSettingMapper.selectScaleReportConfSettingByReportConfId(reportConfId, type, reportType);
    }

    @Override
    public void create(Long reportConfId, String readingInstruction, List<RemarkSettingDTO> remarkSettings, List<FormSettingDTO> formSettings, List<ChartSettingDTO> chartSettings) {
        List<ScaleReportConfSetting> settingList = new ArrayList<>();

        // 报告设置项存储
        if (StrUtil.isNotBlank(readingInstruction)) {
            // 设置阅读须知
            ScaleReportConfSetting scaleReportConfSetting = new ScaleReportConfSetting();
            scaleReportConfSetting.setReportConfId(reportConfId);
            scaleReportConfSetting.setType(ScaleReportConfSettingConstant.Type.READING_INSTRUCT);
            scaleReportConfSetting.setValue(readingInstruction);
            settingList.add(scaleReportConfSetting);
        }

        if (CollUtil.isNotEmpty(remarkSettings)) {
            remarkSettings.forEach(remarkSettingDTO -> {
                // 设置备注
                ScaleReportConfSetting scaleReportConfSetting = new ScaleReportConfSetting();
                scaleReportConfSetting.setReportConfId(reportConfId);
                scaleReportConfSetting.setType(ScaleReportConfSettingConstant.Type.REMARK);
                scaleReportConfSetting.setReportType(Integer.parseInt(remarkSettingDTO.getType()));
                scaleReportConfSetting.setValue(remarkSettingDTO.getRemark());
                settingList.add(scaleReportConfSetting);
            });
        }

        if (CollUtil.isNotEmpty(formSettings)) {
            formSettings.forEach(formSettingDTO -> {
                // 设置表格设置
                ScaleReportConfSetting scaleReportConfSetting = new ScaleReportConfSetting();
                scaleReportConfSetting.setReportConfId(reportConfId);
                scaleReportConfSetting.setType(ScaleReportConfSettingConstant.Type.FORM_SETTING);
                scaleReportConfSetting.setReportType(Integer.parseInt(formSettingDTO.getType()));
                scaleReportConfSetting.setValue(StringUtils.join(formSettingDTO.getColumn(), ","));
                settingList.add(scaleReportConfSetting);
            });
        }

        if (CollUtil.isNotEmpty(chartSettings)) {
            chartSettings.forEach(chartSettingDTO -> {
                // 设置图表
                ScaleReportConfSetting scaleReportConfSetting = new ScaleReportConfSetting();
                scaleReportConfSetting.setReportConfId(reportConfId);
                scaleReportConfSetting.setType(ScaleReportConfSettingConstant.Type.CHART_SETTING);
                scaleReportConfSetting.setReportType(Integer.parseInt(chartSettingDTO.getType()));
                scaleReportConfSetting.setChartType(Integer.parseInt(chartSettingDTO.getChartType()));
                scaleReportConfSetting.setValue(chartSettingDTO.getChartStyle());
                settingList.add(scaleReportConfSetting);
            });
        }

        if (!settingList.isEmpty()) {
            this.saveBatch(settingList);
        }

    }

}
