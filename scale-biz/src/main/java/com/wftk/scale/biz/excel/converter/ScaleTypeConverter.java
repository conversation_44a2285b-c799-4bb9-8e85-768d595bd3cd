package com.wftk.scale.biz.excel.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.wftk.scale.biz.constant.enums.ScaleEnum;

public class ScaleTypeConverter implements Converter<Integer> {

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) {
        if(context.getValue() == null){
            return new WriteCellData<String>();
        }

        Integer value = context.getValue();
        String scaleType = "";
        if (ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode().equals(value)) {
            scaleType = ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getDesc();
        } else if (ScaleEnum.COMBINATION_SCALE_LISTING_TYPE.getCode().equals(value)) {
            scaleType = ScaleEnum.COMBINATION_SCALE_LISTING_TYPE.getDesc();
        }
        return new WriteCellData<String>(scaleType);
    }

}
