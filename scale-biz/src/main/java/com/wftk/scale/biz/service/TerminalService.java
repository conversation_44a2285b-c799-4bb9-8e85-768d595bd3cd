package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.TerminalDTO;
import com.wftk.scale.biz.entity.Terminal;

import java.util.List;


public interface TerminalService extends IService<Terminal> {

    /* 
     * @Author: mq
     * @Description: 校验终端编号是否已经存在
     * @Date: 2024/11/6 15:54 
     * @Param: terminalId-终端ID
     * @Param: terminalCode-终端编号
     * @return: boolean 
     **/
    boolean vaildTerminalCode(Long terminalId, String terminalCode);

    /**
     *
     * @param terminalCode
     * @return
     */
    Terminal getByCode(String terminalCode);

    /* 
     * @Author: mq
     * @Description: 创建终端信息
     * @Date: 2024/11/6 15:53
     * @Param: terminal-终端信息
     * @return: void 
     **/
    void create(Terminal terminal);
    
    /* 
     * @Author: mq
     * @Description: 修改终端信息
     * @Date: 2024/11/6 15:53 
     * @Param: terminal-终端信息
     * @return: void 
     **/
    void modify(Terminal terminal);

    /* 
     * @Author: mq
     * @Description: 根据终端ID删除数据
     * @Date: 2024/11/6 15:53 
     * @Param: terminalId-终端ID
     * @return: void 
     **/
    void delete(Long terminalId);
    
    /* 
     * @Author: mq
     * @Description: 根据ID更新终端数据的状态
     * @Date: 2024/11/6 16:07 
     * @Param: terminalId-终端ID
     * @Param: enable-启用状态(false 未开启 true 已开启)
     * @return: void 
     **/
    void updateEnable(Long terminalId, Boolean enable);

    /* 
     * @Author: mq
     * @Description: 根据条件检索终端信息
     * @Date: 2024/11/6 15:56 
     * @Param: terminalName-终端名称
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.entity.Terminal> 
     **/
    Page<TerminalDTO> selectPage(String terminalName);

    /*
    * @Author: mq
    * @Description: 获取可用的终端信息列表
    * @Date: 2024/12/6 10:45
    * @Param: terminalName-终端名称
    * @return: List<Terminal>
    **/
    List<TerminalDTO> getListOfEnabled(String terminalName);

    /**
     * 获取未绑定机构的终端
     * @return
     */
    List<TerminalDTO> getNotBindingDepartmentList();
}