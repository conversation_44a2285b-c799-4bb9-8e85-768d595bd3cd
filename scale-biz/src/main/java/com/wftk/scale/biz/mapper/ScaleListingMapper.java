package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.listing.*;
import com.wftk.scale.biz.entity.ScaleListing;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 量表上下架表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleListingMapper extends BaseMapper<ScaleListing> {

    /*
     * @Author: mq
     * @Description: 根据量表ID和终端ID获取上架信息
     * @Date: 2024/10/25 16:23
     * @Param: targetId-量表ID
     * @Param: terminalId-终端ID
     * @Param: showType-触达用户方式
     * @return: com.wftk.scale.biz.entity.ScaleListing
     **/
    ScaleListing findByTerminalIdAndTargetCode(@Param("targetCode") String targetCode, @Param("terminalCode") String terminalCode, @Param("showType") Integer showType);

    /*
     * @Author: mq
     * @Description: 根据ID修改量表上下架状态
     * @Date: 2024/10/29 10:44
     * @Param: scaleListingId-上加下主键ID
     * @Param: status-下架状态
     * @Param: opUser-操作人
     * @return: void
     **/
    void updateScaleListingStatus(@Param("scaleListingId") Long scaleListingId, @Param("status") Integer status, @Param("opUser") String opUser);

    /*
     * @Author: mq
     * @Description: 根据条件检索单个可上架量表信息
     * @Date: 2024/10/30 9:47
     * @Param: scaleListingParamDTO-检索参数
     * @return: java.util.List<com.wftk.scale.biz.dto.listing.ScaleListingQueryDTO>
     **/
    List<ScaleListingQueryDTO> getScaleListingList(@Param("param") ScaleListingParamDTO scaleListingParamDTO);

    /*
     * @Author: mq
     * @Description: 根据条件检索组合可上架量表信息
     * @Date: 2024/10/30 10:08
     * @Param: combinationListingParamDTO-检索参数
     * @return: java.util.List<com.wftk.scale.biz.dto.listing.ScaleListingQueryDTO>
     **/
    List<ScaleListingQueryDTO> getScaleCombinationListingList(@Param("param") ScaleCombinationListingParamDTO combinationListingParamDTO);

    /*
     * @Author: mq
     * @Description:  根据条件检索量表已上架终端量信息
     * @Date: 2024/10/29 11:24
     * @Param: type-上架量表类型(1.单个量表、 2.组合量表)
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleListing>
     **/
    List<ScaleListedQueryDTO> getScaleListedList(@Param("param") ScaleListedParamDTO scaleListedParamDTO);

    /*
    * @Author: mq
    * @Description: Page.doSelectPage(
    * @Date: 2024/12/6 17:00
    * @Param: scaleListedParamDTO
    * @return: List<ScaleListedQueryDTO>
    **/
    List<ScaleListedQueryDTO> getScaleListedByPageDisplay(@Param("param") ScaleListedParamDTO scaleListedParamDTO);

    /*
     * @Author: mq
     * @Description: 根据量表ID统计已经上架终端数量
     * @Date: 2024/10/30 10:03
     * @Param: type-上架量表类型(1.单个量表、 2.组合量表)
     * @Param: targetId-量表ID(单个量表或者组合量表)
     * @return: java.lang.Integer
     **/
    Integer getNumOfScaleListed(@Param("type") Integer type, @Param("targetId") Long targetId);

    /*
     * @Author: mq
     * @Description: 根据条件检索量表可分发终端量信息
     * @Date: 2024/10/30 16:51
     * @Param: scaleListedParamDTO
     * @return: java.util.List<com.wftk.scale.biz.dto.listing.ScaleListedQueryDTO>
     **/
    List<ScaleListedQueryDTO> getScaleDistribute(@Param("param") ScaleListedParamDTO scaleListedParamDTO);

    /*
    * @Author: mq
    * @Description: TODO
    * @Date: 2024/12/14 12:03
    * @Param: scaleListingId
    * @return: ScaleListedQueryDTO
    **/
    ScaleListedQueryDTO findById(@Param("scaleListingId") Long scaleListingId);

    /**
     * 根据id和类型查询
     *
     * @param targetId
     * @param type
     * @param terminalCode
     * @return
     */
    ScaleListing getByTargetIdAndType(@Param("targetId") Long targetId,@Param("type") Integer type,@Param("terminalCode")String terminalCode);

    String getReportUrl(@Param("scaleListingId") Long scaleListingId);
}
