package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 因子维度
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02 14:11:54
 */
@Data
@TableName("scale_factor")
public class ScaleFactor implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private Long scaleId;

    /**
     * 因子名称
     */
    private String name;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 题目ID，|隔开
     */
    private String questionId;

    /**
     * 量表公式（数学公式）
     */
    private String formula;

    /**
     * 已通过编译的公式
     */
    private String formulaCompiled;

    /**
     * 中文描述，例如：因子总分*10-平均数
     */
    private String formulaLabel;

    /**
     * 参数值(多个则按照逗号分隔)
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String formulaParam;

    /**
     * 被编译后的参数名(多个参数用逗号隔开)
     */
    private String formulaParamName;

    /**
     * 1启用，0禁用
     */
    private Boolean enable;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 因子类型 1.总分因子 2.其他因子
     */
    private Integer type;

    @Override
    public String toString() {
        return "ScaleFactor{" +
                "id = " + id +
                ", scaleId = " + scaleId +
                ", name = " + name +
                ", sort = " + sort +
                ", questionId = " + questionId +
                ", formula = " + formula +
                ", formulaCompiled = " + formulaCompiled +
                ", formulaLabel = " + formulaLabel +
                ", formulaParam = " + formulaParam +
                ", enable = " + enable +
                ", createTime = " + createTime +
                ", updateTime = " + updateTime +
                ", deleted = " + deleted +
                ", createBy = " + createBy +
                ", updateBy = " + updateBy +
                ", tenantId = " + tenantId +
                "}";
    }
}
