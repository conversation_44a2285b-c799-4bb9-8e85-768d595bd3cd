package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.*;
import com.wftk.scale.biz.entity.ScaleWarnConf;
import com.wftk.scale.biz.event.ScaleCreateRelationEvent;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 预警阈值表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleWarnConfService extends IService<ScaleWarnConf> {

    /*
     * @Author: mq
     * @Description: 校验量表预警阈值信息是否已经存在
     * @Date: 2024/11/5 16:59
     * @Param: id-主键ID
     * @Param: scaleId-量表ID
     * @Param: factorId-因子维度ID
     * @return: boolean
     **/
    boolean validWarnConfFactor(Long id, Long scaleId, Long factorId);

    /*
     * @Author: mq
     * @Description: 校验因子维度关联的预警阈值信息
     * @Date: 2024/12/12 15:20
     * @Param: factorId-因子维度ID
     * @return: String
     **/
    String checkFactorRelationWarnConf(Long factorId);

    /*
     * @Author: mq
     * @Description: 创建量表预警阈值信息
     * @Date: 2024/11/5 16:41
     * @Param: warnConf-预警阈值信息
     * @return: void
     **/
    void create(ScaleWarnConfCreateDTO warnConfCreateDTO);

    /*
     * @Author: mq
     * @Description: 修改量表预警阈值信息
     * @Date: 2024/11/5 16:41
     * @Param: warnConf-预警阈值信息
     * @return: void
     **/
    void modify(ScaleWarnConfModifyDTO warnConfModifyDTO);

    /*
     * @Author: mq
     * @Description: 根据ID删除量表预警阈值信息
     * @Date: 2024/11/5 16:42
     * @Param: batchNo
     * @return: void
     **/
    void delete(String batchNo);


    /*
     * @Author: mq
     * @Description: 根据ID删除量表预警阈值信息
     * @Date: 2024/11/5 16:42
     * @Param: id
     * @return: void
     **/
    ScaleWarnConfDTO detail(String batchNo);

    /*
     * @Author: mq
     * @Description: 根据条件检索预量表警阈值信息
     * @Date: 2024/11/5 16:45
     * @Param: scaleId
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.scale.ScaleWarnConfQueryDTO>
     **/
    Page<ScaleWarnConfQueryDTO> selectPage(Long scaleId);

    void saveBatchByFactorId(ScaleCreateRelationEvent event, Map<Long, Long> map);

    List<ScaleWarnConfQueryDTO> getFactorList( Long scaleId, Long factorId);

    List<ScaleWarnConfDTO> getScaleWarnConfDTOList(List<ScaleWarnConfQueryDTO> scaleWarnConfQueryDTOS);
}
