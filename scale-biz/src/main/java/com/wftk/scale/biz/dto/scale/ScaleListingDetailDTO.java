package com.wftk.scale.biz.dto.scale;

import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2024/12/16 14:38
 */
@Data
public class ScaleListingDetailDTO {

    /**
     * 量表Id
     */
    private Long id;

    /**
     * 量表名称
     */
    private String scaleName;

    /**
     * 上架Id
     */
    private Long listingId;

    /**
     * 量表类型，来源于scale_type表的主键
     */
    private Long type;

    /**
     * 量表编码
     */
    private String code;

    /**
     * 量表名称
     */
    private String name;

    /**
     * 量表服务费原价, 单位:分
     */
    private Integer originalPrice;

    /**
     * 量表服务费优惠后价格，单位: 分
     */
    private Integer price;

    /**
     * 终端ID
     */
    private String terminalCode;

    /**
     * 禁用状态
     */
    private Boolean enable;

}
