package com.wftk.scale.biz.manager.report.actuator;

import com.wftk.scale.biz.constant.ScaleReportConfItemConstant;
import com.wftk.scale.biz.entity.ScaleReportConfItem;
import com.wftk.scale.biz.manager.report.dto.base.VideoDTO;
import com.wftk.scale.biz.service.ScaleReportConfItemService;
import com.wftk.scale.biz.util.ScopeUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/9/10 20:39
 */
@Slf4j
@Component
public class VideoActuator {

    private final String VIDEO_CODE = ScaleReportConfItemConstant.VIDEO_CODE;

    @Resource
    ScaleReportConfItemService scaleReportConfItemService;


    public VideoDTO doVideo(Long scaleReportConfId, BigDecimal score, String factorName,Long factorId) {
        VideoDTO videoDTO = new VideoDTO();
        // 查找视频
        // 根据因子和全部查询
        List<ScaleReportConfItem> scaleReportConfItems = scaleReportConfItemService.findByReportConfIdAndFactorId(scaleReportConfId, List.of(VIDEO_CODE),factorId);
        for (ScaleReportConfItem scaleReportConfItem : scaleReportConfItems) {
            String itemDescribe = scaleReportConfItem.getItemDescribe();
            Boolean inScope = ScopeUtil.inScope(ScopeUtil.parseSectionStr(itemDescribe), score);
            if (inScope) {
                videoDTO.setFactorName(factorName);
                videoDTO.setFilePath(scaleReportConfItem.getItemFilePath());
                break;
            }
        }
        return videoDTO;
    }

}
