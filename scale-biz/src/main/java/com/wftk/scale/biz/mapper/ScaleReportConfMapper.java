package com.wftk.scale.biz.mapper;

import com.wftk.scale.biz.entity.ScaleReportConf;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 报告设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04 14:29:16
 */
public interface ScaleReportConfMapper extends BaseMapper<ScaleReportConf> {

    /*
     * @Author: mq
     * @Description: 根据量表ID获取报告配置信息
     * @Date: 2024/11/6 13:58
     * @Param: scaleId
     * @return: com.wftk.scale.biz.entity.ScaleReportConf
     **/
    ScaleReportConf findReportConfByScaleId(@Param("scaleId") Long scaleId);

    boolean validReportConfDataIntegrity(@Param("scaleId") Long scaleId);

}
