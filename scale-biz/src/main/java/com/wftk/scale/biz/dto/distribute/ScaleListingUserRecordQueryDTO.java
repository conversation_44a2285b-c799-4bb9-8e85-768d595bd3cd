package com.wftk.scale.biz.dto.distribute;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ScaleListingUserRecordDTO
 * @Description: 量表分发用户信息实体
 * @Author: mq
 * @Date: 2024-10-30 16:41
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleListingUserRecordQueryDTO implements Serializable {

    /**
     * 分发用户记录ID
     */
    private Long id;

    /**
     * 上下架ID
     */
    private Long scaleListingId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 支付订单号
     */
    private String orderNo;
    /**
     * 部门id，部门表还没建，表名待补充
     */
    private Long departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 上架类型（单个量表、组合量表）
     */
    private Integer type;

    /**
     * 测评方式 1依次测评 2选择测评
     */
    private Integer evaluationType;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 封面图url
     */
    @RFile(role = FileConstant.FILE_SCALE_PUB_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    private String cover;

    /**
     * 量表ID
     */
    private Long targetId;

    /**
     * 量表名称
     */
    private String targetName;

    /**
     * 分类
     */
    private Long targetType;

    /**
     * 分类名称
     */
    private String targetTypeName;

    /**
     * 终端编号
     */
    private String terminalCode;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 详情
     */
    private List<ScaleQueryDTO> details;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}
