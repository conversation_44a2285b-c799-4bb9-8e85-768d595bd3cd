package com.wftk.scale.biz.dto.user.role;

import cn.hutool.core.util.StrUtil;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class SysRoleQueryDTO implements Serializable {

    private String name;

    private String code;

    private Boolean enable;

    @Pattern(regexp = "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$", message = "开始时间日期格式错误，需为yyyy-MM-dd")
    private String startDate;
    @Pattern(regexp = "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$", message = "截止时间日期格式错误，需为yyyy-MM-dd")
    private String endDate;

    private LocalDateTime startDateTime;

    private LocalDateTime endDateTime;

    public void setStartDate(String startDate) {
        this.startDate = startDate;
        if(StrUtil.isNotBlank(startDate)){
            this.startDateTime = LocalDate.parse(startDate).atStartOfDay();
        }
    }

    private void setEndDate(String endDate) {
        this.endDate = endDate;
        if(StrUtil.isNotBlank(endDate)){
            this.endDateTime = LocalDate.parse(endDate).atTime(23, 59, 59);
        }
    }
}
