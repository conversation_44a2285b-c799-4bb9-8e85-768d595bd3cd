package com.wftk.scale.biz.constant.enums;


import lombok.Getter;

/**
 * @EnumName: JumpEnum
 * @Description:
 * @Author: mq
 * @Date: 2024/12/11
 * @Version: 1.0
 **/
@Getter
public enum JumpEnum {

    SINGLE_CONDITION(1, "单条件触发跳转"),
    ALL_MULTIPLE_CONDITION(2,"多条件同时满足触发跳转"),
    ONE_MULTIPLE_CONDITION(3,"多条件之一满足触发跳转")
    ;
    private Integer code;

    private String desc;

    JumpEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
