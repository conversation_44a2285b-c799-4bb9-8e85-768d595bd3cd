package com.wftk.scale.biz.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class ScaleWarnSettingPageQueryInput implements Serializable {
    @Serial
    private static final long serialVersionUID = -4676417926711707373L;

    /**
     * 当前页
     */
    @Schema(description = "当前页")
    private int pageNum;

    /**
     * 每页数量
     */
    @Schema(description = "每页数量")
    private int pageSize;

    /**
     * 量表名称
     */
    @Schema(description = "量表名称")
    private String scaleName;

    /**
     * 预警等级
     */
    @Schema(description = "预警等级")
    private Integer level;

    /**
     * 是否预警自己
     */
    @Schema(description = "是否预警自己")
    private Boolean evaluateMyself;

    /**
     * 预警范围
     */
    @Schema(description = "预警范围")
    private Integer scope;

    /**
     * 量表因子名称
     */
    @Schema(description = "量表因子名称")
    private String scaleFactorName;

}
