package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.jackson.core.TargetType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.SymbolConstant;
import com.wftk.scale.biz.event.ScaleCreateEvent;
import com.wftk.scale.biz.event.ScaleDelEvent;
import com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleQuestionRelationDTO;
import com.wftk.scale.biz.dto.scale.ScaleStrategyDTO;
import com.wftk.scale.biz.entity.ScaleQuestionOption;
import com.wftk.scale.biz.entity.ScaleQuestionRelation;
import com.wftk.scale.biz.mapper.ScaleQuestionMapper;
import com.wftk.scale.biz.mapper.ScaleQuestionRelationMapper;
import com.wftk.scale.biz.service.ScaleQuestionOptionService;
import com.wftk.scale.biz.service.ScaleQuestionRelationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 量表关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Slf4j
@Service
public class ScaleQuestionRelationServiceImpl extends ServiceImpl<ScaleQuestionRelationMapper, ScaleQuestionRelation> implements ScaleQuestionRelationService {

    @Autowired
    private ScaleQuestionRelationMapper scaleQuestionRelationMapper;

    @Autowired
    private ScaleQuestionMapper scaleQuestionMapper;
    @Resource
    private ScaleQuestionOptionService scaleQuestionOptionService;

    private final static String REPEAT_ERROR_MSG = "存在重复的逻辑跳转配置";

    @Override
    public boolean validRelationStrategyCorrect(Long id, Long scaleId, Long questionId, String strategy, Integer type) {
        if (StrUtil.isEmpty(strategy) || !this.isJsonArrFormat(strategy)) {
            return true;
        }
        List<ScaleStrategyDTO> list = JSONObject.getInstance().parseObject(strategy, new TargetType<>() {});
        if (CollUtil.isEmpty(list)) {
            return true;
        }
        checkQuestionMatchOption(list);
        boolean match = !checkSourceAndTarget(scaleId, questionId, list);
        //校验 单条件、多条件全部、多条件任一 逻辑跳转题目不能重叠 不同类型 题号与答案有重叠 同种类型 题号、答案、跳转题目有重叠
        checkFromQuestionRepeat(list, scaleId, id, type);
        return match;
    }

    private void checkFromQuestionRepeat(List<ScaleStrategyDTO> list, Long scaleId, Long id, Integer type){
        List<ScaleQuestionRelationDTO> relationList = scaleQuestionRelationMapper.getList(scaleId);
        if(CollUtil.isEmpty(relationList)){
            return;
        }
        if(id != null){
            relationList = relationList.stream().filter(it -> !it.getId().equals(id)).collect(Collectors.toList());
        }
        //1.单条件触发跳转; 2.多条件同时满足触发跳转; 3.多条件之一满足触发跳转;
        if(type == 1 && list.size() > 1){
            throw new BusinessException("单条件触发只能选择一个答案选项");
        }
        relationList.forEach(it -> {
            Integer targetType = it.getType();
            String strategy = it.getStrategy();
            if (StrUtil.isEmpty(strategy) || !this.isJsonArrFormat(strategy)) {
                return;
            }
            List<ScaleStrategyDTO> list1 = JSONObject.getInstance().parseObject(strategy, new TargetType<>() {});
            switch (targetType){
                case 1:
                    checkOneToOther(type, list1, list);
                    break;
                case 2:
                    checkSomeToOther(type, list1, list);
                    break;
                case 3:
                    checkAnyToOther(type, list1, list);
                    break;
            }
        });
    }

    private void checkOneToOther(Integer type, List<ScaleStrategyDTO> list1, List<ScaleStrategyDTO> list){
        if(type == 1){
            checkOneToOne(list1.get(0), list.get(0));
        }else if(type == 2){
            checkOneToSome(list1.get(0), list);
        }else if(type == 3){
            checkOneToAny(list1.get(0), list);
        }
    }

    private void checkAnyToOther(Integer type, List<ScaleStrategyDTO> list1, List<ScaleStrategyDTO> list){
        if(type == 1){
            checkOneToAny(list.get(0), list1);
        }else if(type == 2){
            checkAnyToAny(list1, list);
        }else if(type == 3){
            checkSomeToAny(list, list1);
        }
    }

    private void checkSomeToOther(Integer type, List<ScaleStrategyDTO> list1, List<ScaleStrategyDTO> list){
        if(type == 1){
            checkOneToSome(list.get(0), list1);
        }else if(type == 2){
            checkSomeToSome(list1, list);
        }else if(type == 3){
            checkSomeToAny(list1, list);
        }
    }

    /**
     * 单一选项 对比 单一选项 题号、选项、跳转题号一致，则认为重复
     *     1 A -> 2  1 A -> 3 错误
     *     1 A -> 2  1 A -> 2 错误
     */
    private void checkOneToOne(ScaleStrategyDTO dto1, ScaleStrategyDTO dto2){
        if(Objects.equals(dto1.getQuestionId(), dto2.getQuestionId()) && Objects.equals(dto1.getLable(), dto2.getLable())){
            throw new BusinessException(REPEAT_ERROR_MSG);
        }
    }

    /**
     * 单一选项 对比 全部 全部的题数量只有一条，且与单一的完全一致
     *    1 A -> 2      1 A -> 3 错
     *    1 A -> 2      1 A & 2 B -> 3 对
     */
    private void checkOneToSome(ScaleStrategyDTO dto, List<ScaleStrategyDTO> dtoList){
        if(CollUtil.isNotEmpty(dtoList) && dtoList.size() == 1){
            checkOneToOne(dto, dtoList.get(0));
        }
    }

    /**
     * 单一选项 对比 任意，任一中包含
     *   1 A -> 2      1 A -> 3 错
     *   1 A -> 2      1 A | 2 B -> 3 错
     */
    private void checkOneToAny(ScaleStrategyDTO dto, List<ScaleStrategyDTO> dtoList){
        if(CollUtil.isNotEmpty(dtoList)){
            boolean b = dtoList.stream().anyMatch(dto1 ->
                    dto1.getQuestionId().equals(dto.getQuestionId()) && dto1.getLable().equals(dto.getLable()));
            if(b){
                throw new BusinessException(REPEAT_ERROR_MSG);
            }
        }
    }

    /**
     * 多对多 完全一致则认为重复
     * 1 A -> 2      1 A -> 3 错
     * 1 A & 2 B -> 3      1 A & 2 C -> 3 对
     * 1 A & 2 B -> 3      1 A & 2 B -> 3 错
     */
    private void checkSomeToSome(List<ScaleStrategyDTO> dtoList1, List<ScaleStrategyDTO> dtoList2){
        if(dtoList1.size() != dtoList2.size()){
            return;
        }
        Set<String> list1 = dtoList1.stream().map(it -> it.getQuestionId() + it.getLable()).collect(Collectors.toSet());
        Set<String> list2 = dtoList2.stream().map(it -> it.getQuestionId() + it.getLable()).collect(Collectors.toSet());
        if(list1.equals(list2)){
            throw new BusinessException(REPEAT_ERROR_MSG);
        }
    }

    /**
     * 多对任一 判断重复依据: 任一中 任何一个与全部中的元素有重复
     * 1 A -> 2      1 A -> 3 错
     * 1 A & 2 B -> 3      1 A | 2 B -> 3 对
     * 1 A & 2 B -> 3      1 A | 2 C -> 3 错
     */
    private void checkSomeToAny(List<ScaleStrategyDTO> dtoSomeList, List<ScaleStrategyDTO> dtoAnyList){
        dtoAnyList.forEach(dto1 -> {
            boolean b = dtoSomeList.stream().anyMatch(dto ->
                    dto1.getQuestionId().equals(dto.getQuestionId()) && dto1.getLable().equals(dto.getLable()));
            if(b){
                throw new BusinessException(REPEAT_ERROR_MSG);
            }
        });
    }

    private void checkAnyToAny(List<ScaleStrategyDTO> dtoAnyList1, List<ScaleStrategyDTO> dtoAnyList2){
        dtoAnyList1.forEach(dtoAny1 -> {
            boolean b = dtoAnyList2.stream().anyMatch(dtoAny2 ->
                    dtoAny1.getQuestionId().equals(dtoAny2.getQuestionId()) && dtoAny1.getLable().equals(dtoAny2.getLable()));
            if(b){
                throw new BusinessException(REPEAT_ERROR_MSG);
            }
        });
    }

    private void checkQuestionMatchOption(List<ScaleStrategyDTO> list){
        list.forEach(dto -> {
            boolean exists = scaleQuestionOptionService.exists(new LambdaQueryWrapper<ScaleQuestionOption>()
                    .eq(ScaleQuestionOption::getQuestionId, dto.getQuestionId())
                    .eq(ScaleQuestionOption::getLabel, dto.getLable())
            );
            if(!exists){
                throw new BusinessException("题号与选项未对应");
            }
        });
    }

    private boolean checkSourceAndTarget(Long scaleId, Long questionId, List<ScaleStrategyDTO> list) {
        ScaleQuestionQueryDTO question = scaleQuestionMapper.findByScaleIdAndQuestionId(scaleId, questionId);
        String toQuestionNum = ObjUtil.isNotNull(question) ? question.getQuestionNumber() : "";
        String toQuestionSubNum = ObjUtil.isNotNull(question) ? question.getSubNumber() : "";
        BigDecimal toNum = new BigDecimal(formatQuestionNumber(toQuestionNum, toQuestionSubNum));
        BigDecimal maxFromQuestionNum = BigDecimal.ZERO;
        for (ScaleStrategyDTO it : list) {
            //不能出现关系策略配置题号转换至建立关系的题号相同
            if (questionId.equals(it.getQuestionId())) {
                return true;
            }
            BigDecimal fromNum = new BigDecimal(formatQuestionNumber(it.getQuestionNumber(), it.getSubNumber()));
            //不能出现关系策略配置题号小于建立关系的题号,否则会出现循环
            if(fromNum.compareTo(toNum) >= 0){
                return true;
            }
            BigDecimal fromQuestionNum = new BigDecimal(it.getQuestionNumber());
            maxFromQuestionNum = maxFromQuestionNum.compareTo(fromQuestionNum) < 0 ? fromQuestionNum : maxFromQuestionNum;
            //不允许子题跳转到其它题号
            if(StrUtil.isNotBlank(it.getSubNumber())){
                return true;
            }
        }
        return StrUtil.isNotBlank(toQuestionSubNum) && new BigDecimal(toQuestionNum).compareTo(maxFromQuestionNum) != 0;
    }

    @Override
    public boolean vaildRelationDataIntegrity(Long scaleId) {
        return scaleQuestionRelationMapper.vaildRelationDataIntegrity(scaleId);
    }

    @Override
    public boolean checkRelationQuestion(Long scaleId, Long questionId) {
        List<ScaleQuestionRelationDTO> list = scaleQuestionRelationMapper.getList(scaleId);
        if (CollUtil.isEmpty(list)) {
            return false;
        }
        ScaleQuestionRelationDTO dto = list.stream().filter(relation -> {
            //跳转的目标题号
            if (questionId.equals(relation.getQuestionId())) {
                return true;
            }
            //跳转的来源题号
            List<ScaleStrategyDTO> strategyDTOS = JSONObject.getInstance().parseObject(relation.getStrategy(), new TargetType<>() {
            });
            strategyDTOS = CollUtil.isEmpty(strategyDTOS) ? Lists.newArrayList() : strategyDTOS;
            //判断题目是否包含在跳转逻辑策略中
            ScaleStrategyDTO strategyDTO = strategyDTOS.stream().filter(strategy -> questionId.equals(strategy.getQuestionId())).findFirst().orElse(null);
            return ObjUtil.isNotNull(strategyDTO);
        }).findFirst().orElse(null);
        return ObjUtil.isNotNull(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ScaleQuestionRelation scaleQuestionRelation) {
            scaleQuestionRelationMapper.insert(scaleQuestionRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(ScaleQuestionRelation scaleQuestionRelation) {
            ScaleQuestionRelation rawData = scaleQuestionRelationMapper.selectById(scaleQuestionRelation.getId());
            BeanUtils.copyProperties(scaleQuestionRelation, rawData);
            scaleQuestionRelationMapper.updateById(rawData);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        scaleQuestionRelationMapper.deleteById(id);
    }

    @Override
    public Page<ScaleQuestionRelationDTO> selectPage(Long scaleId) {
        return Page.doSelectPage(() -> scaleQuestionRelationMapper.getList(scaleId))
                .toPage(datas -> this.buildScaleQuestionRelationDTO(datas));
    }

    private List<ScaleQuestionRelationDTO> buildScaleQuestionRelationDTO(List<ScaleQuestionRelationDTO> list) {
        list = CollUtil.isEmpty(list) ? List.of() : list;
        return list.stream().map(relation -> {
            //格式：[{"questionId":616260905161029,"lable":"A", questionNumber:"1",subNumber:""},{"questionId":616260905161029,"lable":"B",questionNumber:"2",subNumber:""}]
            String strategy = relation.getStrategy();
            relation.setQuestionNumStr(this.formatQuestionNumber(relation.getQuestionNumber(), relation.getQuestionSubNumber()));
            relation.setOptions(this.getLableText(strategy));
            relation.setQuestionNumbers(this.getQuestionNumberText(strategy));
            return relation;
        }).collect(Collectors.toList());
    }

    private String getLableText(String strategy) {
        if (StrUtil.isEmpty(strategy) || !this.isJsonArrFormat(strategy)) {
            return "";
        }
        List<ScaleStrategyDTO> list = JSONObject.getInstance().parseObject(strategy, new TargetType<>() {
        });
        if (CollUtil.isEmpty(list)) {
            return "";
        }
        String lableText = list.stream()
                .map(option -> String.valueOf(option.getLable()))
                .collect(Collectors.joining(SymbolConstant.DELIMITER));
        return lableText;
    }

    private String getQuestionNumberText(String strategy) {
        if (StrUtil.isEmpty(strategy) || !this.isJsonArrFormat(strategy)) {
            return "";
        }
        List<ScaleStrategyDTO> list = JSONObject.getInstance().parseObject(strategy, new TargetType<>() {
        });
        if (CollUtil.isEmpty(list)) {
            return "";
        }
        String questionNumber = list.stream()
                .map(option -> this.formatQuestionNumber(option.getQuestionNumber(), option.getSubNumber()))
                .collect(Collectors.joining(SymbolConstant.DELIMITER));
        return questionNumber;
    }

    private String formatQuestionNumber(String questionNumber, String subNumber) {
        StringBuilder builder = new StringBuilder();
        if (StrUtil.isNotEmpty(questionNumber)) {
            builder.append(questionNumber);
        }
        if (StrUtil.isNotEmpty(subNumber)) {
            if(builder.isEmpty()){
                builder.append(subNumber);
            }else{
                builder.append(".").append(subNumber);
            }
        }
        return builder.toString();
    }

    private boolean isJsonArrFormat(String text) {
        try {
            JSONObject.getInstance().parseObject(text, new TargetType<>() {
            });
        } catch (Exception e) {
            log.error("逻辑跳转策略格式解析发生异常,原始数据:{}", text, e);
            return false;
        }
        return true;
    }

    @Override
    public void saveScaleQuestionRelation(ScaleCreateEvent scaleEventDTO, Map<Long, Long> oldToNewIdMap) {
        Long oldScaleId = scaleEventDTO.getOldScaleId();
        Long newScaleId = scaleEventDTO.getNewScaleId();

        List<ScaleQuestionRelation> list = scaleQuestionRelationMapper.selectList(
                new LambdaQueryWrapper<ScaleQuestionRelation>().eq(ScaleQuestionRelation::getScaleId, oldScaleId));
        if (CollUtil.isEmpty(list)) {
            return;
        }
        list.forEach(scaleQuestionRelation -> {
            scaleQuestionRelation.setId(null);
            scaleQuestionRelation.setScaleId(newScaleId);
            scaleQuestionRelation.setQuestionId(oldToNewIdMap.get(scaleQuestionRelation.getQuestionId()));
            //需要重置策略里的题目ID
            scaleQuestionRelation.setStrategy(buildNewStrategy(scaleQuestionRelation.getStrategy(), oldToNewIdMap));
        });
        baseMapper.insert(list);
    }

    private String buildNewStrategy(String strategy, Map<Long, Long> oldToNewIdMap) {
        if(StrUtil.isEmpty(strategy)) {
            return null;
        }
        JSONObject instance = JSONObject.getInstance();
        List<ScaleStrategyDTO> list = instance.parseObject(strategy, new TargetType<>() {});
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        list.forEach(it -> it.setQuestionId(oldToNewIdMap.get(it.getQuestionId())));
        return instance.toJSONString(list);
    }

    @Override
    public List<ScaleQuestionRelation> findByScaleIdAndQuestionId(Long scaleId, Long questionId) {
        return scaleQuestionRelationMapper.findByScaleIdAndQuestionId(scaleId, questionId);
    }

    @EventListener
    public void onoApplication(ScaleDelEvent event) {
        scaleQuestionRelationMapper.delete(new LambdaQueryWrapper<ScaleQuestionRelation>().eq(ScaleQuestionRelation::getScaleId, event.getOldScaleId()));
    }
}
