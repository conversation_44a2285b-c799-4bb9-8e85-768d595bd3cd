package com.wftk.scale.biz.manager.report.handle;

import com.wftk.scale.biz.constant.ScaleReportConfSettingConstant;
import com.wftk.scale.biz.constant.enums.ScaleFactorTypeEnum;
import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.entity.ScaleReportConf;
import com.wftk.scale.biz.entity.ScaleUserFactorResult;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.manager.report.actuator.*;
import com.wftk.scale.biz.manager.report.dto.base.*;
import com.wftk.scale.biz.manager.report.dto.content.AvgScoreReportContentDTO;
import com.wftk.scale.biz.service.ScaleFactorService;
import com.wftk.scale.biz.service.ScaleUserFactorResultService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 平均分结果处理器
 * <AUTHOR>
 * @createDate 2025/9/9 20:07
 */
@Slf4j
@Component
public class AvgScoreResultsHandle {

    private final Integer FACTOR_TYPE = ScaleFactorTypeEnum.AVG.getType();

    @Resource
    ScaleFactorService scaleFactorService;

    @Resource
    ResultIntroActuator resultIntroActuator;

    @Resource
    SuggestionActuator suggestionActuator;

    @Resource
    AudioActuator audioActuator;

    @Resource
    VideoActuator videoActuator;

    @Resource
    ChartActuator chartActuator;

    @Resource
    FormActuator formActuator;

    @Resource
    WordageActuator wordageActuator;

    @Resource
    RemarkActuator remarkActuator;

    @Resource
    ScaleUserFactorResultService scaleUserFactorResultService;


    public AvgScoreReportContentDTO buildReportContent(ScaleUserResult scaleUserResult, ScaleReportConf scaleReportConf) {
        if (!scaleReportConf.getAvgScoreEnable()) {
            log.info("avgResults enable is close. resultId is {}",scaleUserResult.getId());
            return null;
        }
        // 查找平均分因子
        Long scaleId = scaleUserResult.getScaleId();
        ScaleFactor factor = scaleFactorService.getFactorByScaleIdAndType(scaleId, FACTOR_TYPE);
        if(factor == null){
            log.info("avgResults avg factor is null. resultId is {}, scaleId is {}"
                    ,scaleUserResult.getId(),scaleUserResult.getScaleId());
            return null;
        }

        // 查找结果解读和因子结果
        ReultIntroDTO reultIntroDTO = null;
        if(scaleReportConf.getResultIntroEnable() && scaleReportConf.getAvgScoreWordageEnable()){
            reultIntroDTO = resultIntroActuator.doResultIntro(scaleUserResult.getId(), factor.getId(), factor.getName());
        }

        // 获取建议
        SuggestionDTO suggestionDTO = null;
        if(scaleReportConf.getImprovementSuggestionEnable()){
            BigDecimal score = scaleUserFactorResultService.getScoreByResultIdAndFactorId(scaleUserResult.getId(), factor.getId());
            suggestionDTO = suggestionActuator.doSuggestion(scaleReportConf.getId(), score, factor.getName(), factor.getId());
        }

        // 获取音频
        AudioDTO audioDTO = null;
        VideoDTO videoDTO = null;
        if(scaleReportConf.getAudioAndVideoNarrateEnable()){
            BigDecimal score = scaleUserFactorResultService.getScoreByResultIdAndFactorId(scaleUserResult.getId(), factor.getId());
            audioDTO = audioActuator.doAudio(scaleReportConf.getId(), score, factor.getName(), factor.getId());
            videoDTO = videoActuator.doVideo(scaleReportConf.getId(), score, factor.getName(), factor.getId());
        }

        Integer reportType = ScaleReportConfSettingConstant.ReportType.AVG;

        // 获取图表
        ChartDTO chartDTO = null;
        if(scaleReportConf.getAvgScoreChartEnable()){
            chartDTO = chartActuator.doChart(scaleReportConf.getId(),reportType, scaleUserResult.getId(), List.of(factor.getId()));
        }

        // 获取表格
        FormDTO formDTO = null;
        if(scaleReportConf.getAvgScoreFormEnable()){
            formDTO = formActuator.doForm(scaleUserResult.getId(),List.of(factor.getId()), scaleReportConf.getId(), reportType);
        }

        // 获取文字
        String wordage = null;
        if(scaleReportConf.getAvgScoreWordageEnable()){
            List<FactorResultDTO> factorResults = new ArrayList<>();
            List<ScaleUserFactorResult> scaleUserFactorResults = scaleUserFactorResultService.getListByResultId(scaleUserResult.getId(), List.of(factor.getId()));
            for (ScaleUserFactorResult factorResult:scaleUserFactorResults) {
                FactorResultDTO factorResultDTO = new FactorResultDTO();
                factorResultDTO.setFactorName(factorResult.getFactorName());
                factorResultDTO.setScore(factorResult.getScore());
                factorResults.add(factorResultDTO);
            }
            wordage = wordageActuator.doWordage(factorResults, reportType);
        }

        String remark = null;
        if(scaleReportConf.getRemarkEnable()){
            remark = remarkActuator.doRemark(scaleReportConf.getId(),reportType);
        }

        AvgScoreReportContentDTO avgScoreReportContentDTO = new AvgScoreReportContentDTO();
        avgScoreReportContentDTO.setRemark(remark);
        avgScoreReportContentDTO.setForm(formDTO);
        avgScoreReportContentDTO.setResultIntro(reultIntroDTO);
        avgScoreReportContentDTO.setChart(chartDTO);
        avgScoreReportContentDTO.setSuggestion(suggestionDTO);
        avgScoreReportContentDTO.setWordage( wordage);
        avgScoreReportContentDTO.setAudio(audioDTO);
        avgScoreReportContentDTO.setVideo(videoDTO);

        return  avgScoreReportContentDTO;
    }

}
