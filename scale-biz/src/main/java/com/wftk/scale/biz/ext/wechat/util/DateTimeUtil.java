package com.wftk.scale.biz.ext.wechat.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @create 2023/11/2 15:37
 */
public class DateTimeUtil {

    private static final DateTimeFormatter RFC3339_FORMATTER = DateTimeFormatter
            .ofPattern("yyyy-MM-dd'T'HH:mm:ss+08:00")
            .withZone(ZoneId.of("Asia/Shanghai"));


    /**
     * 转换为rfc3339格式
     * @param localDateTime
     * @return
     */
    public static String rfc3339Format(LocalDateTime localDateTime) {
        return RFC3339_FORMATTER.format(localDateTime);
    }

}
