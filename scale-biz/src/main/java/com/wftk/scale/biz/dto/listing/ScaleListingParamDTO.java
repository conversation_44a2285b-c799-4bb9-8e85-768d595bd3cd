package com.wftk.scale.biz.dto.listing;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: ScaleListedQueryDTO
 * @Description: 量表可上架信息筛选条件传输实体
 * @Author: mq
 * @Date: 2024-10-29 16:05
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleListingParamDTO implements Serializable {

    /**
     * 量表ID
     */
    private Long targetId;

    /**
     * 量表名称
     */
    private String targetName;

    /**
     * 量表类型
     */
    private Long targetType;

    /**
     * 操作人
     */
    private String opUser;

    /**
     * 终端编号
     */
    private String terminalCode;
}
