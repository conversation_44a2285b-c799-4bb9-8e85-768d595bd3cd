package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 退款订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22 14:48:13
 */
@TableName("order_refund")
public class OrderRefund implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标识;删除逻辑标识（1：删除   0：不删除）
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 租户ID;租户ID
     */
    private String tenantId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 交易订单号
     */
    private String traderNo;

    /**
     * 退款订单号
     */
    private String refundNo;

    /**
     * 第三方退款单号
     */
    private String refundExtraNo;

    /**
     * 退款状态;1退款中；2退款成功；3退款失败
     */
    private Integer refundStatus;

    /**
     * 退款金额(分)
     */
    private Integer refundAmount;

    /**
     * 发起退款时间
     */
    private LocalDateTime refundTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 退款结果时间
     */
    private LocalDateTime refundResultTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getTraderNo() {
        return traderNo;
    }

    public void setTraderNo(String traderNo) {
        this.traderNo = traderNo;
    }

    public String getRefundNo() {
        return refundNo;
    }

    public void setRefundNo(String refundNo) {
        this.refundNo = refundNo;
    }

    public String getRefundExtraNo() {
        return refundExtraNo;
    }

    public void setRefundExtraNo(String refundExtraNo) {
        this.refundExtraNo = refundExtraNo;
    }

    public Integer getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(Integer refundStatus) {
        this.refundStatus = refundStatus;
    }

    public Integer getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Integer refundAmount) {
        this.refundAmount = refundAmount;
    }

    public LocalDateTime getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(LocalDateTime refundTime) {
        this.refundTime = refundTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getRefundResultTime() {
        return refundResultTime;
    }

    public void setRefundResultTime(LocalDateTime refundResultTime) {
        this.refundResultTime = refundResultTime;
    }

    @Override
    public String toString() {
        return "OrderRefund{" +
            "id = " + id +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
            ", deleted = " + deleted +
            ", tenantId = " + tenantId +
            ", orderNo = " + orderNo +
            ", traderNo = " + traderNo +
            ", refundNo = " + refundNo +
            ", refundExtraNo = " + refundExtraNo +
            ", refundStatus = " + refundStatus +
            ", refundAmount = " + refundAmount +
            ", refundTime = " + refundTime +
            ", remark = " + remark +
            ", refundResultTime = " + refundResultTime +
        "}";
    }
}
