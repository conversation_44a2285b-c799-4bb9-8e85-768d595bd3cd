package com.wftk.scale.biz.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ScaleEvaluationIntroEvent extends ApplicationEvent {

    /**
     * 测评记录ID
     */
    private Long resultId;

    private Long userId;

    public ScaleEvaluationIntroEvent(Long resultId){
        super(resultId);
        this.resultId = resultId;
    }

    public ScaleEvaluationIntroEvent(Long resultId, Long userId){
        super(resultId);
        this.resultId = resultId;
        this.userId = userId;
    }
}
