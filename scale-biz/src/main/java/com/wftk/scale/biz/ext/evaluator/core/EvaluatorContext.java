package com.wftk.scale.biz.ext.evaluator.core;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025-09-02
 */
public class EvaluatorContext {

    private final Map<String, Object> variables;

    private EvaluatorContext(Builder builder) {
        this.variables = builder.variables;
    }

    /**
     * 获取变量
     */
    public Object getVariable(String key) {
        return variables.get(key);
    }

    /**
     * 添加变量
     */
    public void addVariable(String key, Object value) {
        variables.put(key, value);
    }

    /**
     * 获取变量集合
     */
    public Map<String, Object> getAllVariables() {
        return Collections.unmodifiableMap(variables);
    }

    /**
     * 参数是否为空
     */
    public boolean isEmpty() {
        return variables == null || variables.isEmpty();
    }

    public static Builder builder() {
        return new Builder();
    }

    @Override
    public String toString() {
        return variables.toString();
    }



    /**
     * 构建器
     */
    public static class Builder {

        private final Map<String, Object> variables;

        public Builder() {
            this.variables = new ConcurrentHashMap<>();
        }

        public Builder addVariable(String key, Object value) {
            this.variables.put(key, value);
            return this;
        }

        public Builder addAllVariables(Map<String, Object> variables) {
            this.variables.putAll(variables);
            return this;
        }

        public EvaluatorContext build() {
            return new EvaluatorContext(this);
        }
    
    }
}
