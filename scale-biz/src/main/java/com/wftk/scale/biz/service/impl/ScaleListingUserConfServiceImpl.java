package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.dto.distribute.ScaleListingBaseDTO;
import com.wftk.scale.biz.entity.ScaleListingUserConf;
import com.wftk.scale.biz.entity.ScaleListingUserRecord;
import com.wftk.scale.biz.mapper.ScaleListingUserConfMapper;
import com.wftk.scale.biz.service.ScaleListingUserConfService;
import com.wftk.scale.biz.service.ScaleListingUserRecordService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 量表上架触达用户配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class ScaleListingUserConfServiceImpl extends ServiceImpl<ScaleListingUserConfMapper, ScaleListingUserConf> implements ScaleListingUserConfService {

    @Autowired
    private ScaleListingUserConfMapper scaleListingUserConfMapper;
    @Resource
    private ScaleListingUserRecordService scaleListingUserRecordService;

    @Override
    public boolean vaildUserConfEvaluationTimeOut(Long listingId) {
        boolean checkResult = false;
        if (ObjUtil.isNull(listingId)) {
            return checkResult;
        }
        ScaleListingUserConf userConf = scaleListingUserConfMapper.findByScaleListingId(listingId);
        if (ObjUtil.isNull(userConf)) {
            return checkResult;
        }
        LocalDateTime now = LocalDate.now().atStartOfDay();
        LocalDateTime startTime = ObjUtil.defaultIfNull(userConf.getStartTime(), now);
        LocalDateTime endTime = ObjUtil.defaultIfNull(userConf.getEndTime(), now);
        if (!(!now.isBefore(startTime) && !now.isAfter(endTime))) {
            checkResult = true;
        }
        return checkResult;
    }

    @Override
    public boolean vaildUserConfEvaluationTimeOut(ScaleListingUserConf userConf) {
        boolean checkResult = false;
        if (ObjUtil.isNull(userConf)) {
            return false;
        }
        // 目前前端传输时间不带时分秒
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = ObjUtil.defaultIfNull(userConf.getStartTime(), now);
        LocalDateTime endTime = ObjUtil.defaultIfNull(userConf.getEndTime(), now);
        if (now.isBefore(startTime) || now.isAfter(endTime)) {
            checkResult = true;
        }
        return checkResult;
    }

    @Override
    public boolean vaildUserConfTime(ScaleListingUserConf userConf) {
        // 目前前端传输时间不带时分秒
        boolean checkResult = true;
        LocalDate now = LocalDate.now();
        LocalDate startTime = userConf.getStartTime() == null ? LocalDate.now() : userConf.getStartTime().toLocalDate();
        LocalDate endTime = userConf.getEndTime() == null ? LocalDate.now() : userConf.getEndTime().toLocalDate();
        if (startTime.isAfter(endTime) || endTime.isBefore(now)) {
            checkResult = false;
            return checkResult;
        }
        return checkResult;
    }

    @Override
    public ScaleListingUserConf saveDistributeUserConf(ScaleListingBaseDTO dto, ScaleListingUserConf userConf) {
        Long scaleListingId = dto.getScaleListingId();
        userConf.setScaleListingId(scaleListingId);
        LocalDateTime endTime = userConf.getEndTime();
        if(endTime != null){
            if(endTime.getHour() == 0 && endTime.getMinute() == 0 && endTime.getSecond() == 0){
                userConf.setEndTime(endTime.toLocalDate().atTime(23, 59, 59));
            }
        }
        scaleListingUserConfMapper.insert(userConf);
        return userConf;
    }

    public boolean isAllowRepeat(Long listingId) {
        boolean checkResult = true;
        if (ObjectUtil.isNull(listingId)) {
            return checkResult;
        }
        ScaleListingUserConf userConf = scaleListingUserConfMapper.findByScaleListingId(listingId);
        if (ObjectUtil.isNull(userConf)) {
            return checkResult;
        }
        checkResult = userConf.getAllowRepeat();
        return checkResult;
    }

    @Override
    public boolean isAllowTimeLimit(Long listingId) {
        boolean checkResult = false;
        if (ObjectUtil.isNull(listingId)) {
            return checkResult;
        }
        ScaleListingUserConf userConf = scaleListingUserConfMapper.findByScaleListingId(listingId);
        if (ObjectUtil.isNull(userConf)) {
            return checkResult;
        }
        if (ObjectUtil.isNull(userConf)) {
            return checkResult;
        }
        checkResult = userConf.getAllowTimeLimit();
        return checkResult;
    }

    @Override
    public ScaleListingUserConf findByListingId(Long listingId) {
        return scaleListingUserConfMapper.findByScaleListingId(listingId);
    }

    @Override
    public ScaleListingUserConf findByListingUserIdOrOrderNo(Long listingUserId, String orderNo) {
        if(ObjectUtil.isNotNull(listingUserId)){
            ScaleListingUserRecord byId = scaleListingUserRecordService.getById(listingUserId);
            if(byId == null){
                return null;
            }
            return scaleListingUserConfMapper.selectById(byId.getUserConfId());
        }
        if(ObjectUtil.isNotNull(orderNo)){
            //通过订单号反查分发记录
            ScaleListingUserRecord one = scaleListingUserRecordService.getOne(new LambdaQueryWrapper<ScaleListingUserRecord>().eq(ScaleListingUserRecord::getOrderNo, orderNo));
            if(one == null){
                return null;
            }
            return scaleListingUserConfMapper.selectById(one.getUserConfId());
        }
        return null;
    }
}
