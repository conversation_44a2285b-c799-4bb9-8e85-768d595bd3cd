package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.scale.ScaleResultIntroDTO;
import com.wftk.scale.biz.entity.ScaleResultIntro;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 结果解读表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleResultIntroMapper extends BaseMapper<ScaleResultIntro> {

    /*
     * @Author: mq
     * @Description: 校验量表结果解读内容是否有设置
     * @Date: 2024/11/4 14:46
     * @Param: scaleId
     * @return: boolean
     **/
    boolean vaildResultIntroDataIntegrity(@Param("scaleId") Long scaleId);

    /*
     * @Author: mq
     * @Description: 校验量表结果解读名称是否存在
     * @Date: 2024/12/7 10:10
     * @Param: scaleId-量表ID
     * @Param: factorId-结果解读ID
     * @Param: resultIntroName-结果解读名称
     * @return: boolean
     **/
    boolean vaildResultIntroNameExists(@Param("scaleId") Long scaleId, @Param("factorId") Long factorId, @Param("resultIntroName") String resultIntroName);

    /*
    * @Author: mq
    * @Description: 校验因子维度关联的结果解读信息
    * @Date: 2024/12/12 15:15
    * @Param: factorId-因子维度ID
    * @return: String
    **/
    String checkFactorRelationIntro(@Param("factorId") Long factorId);

    /*
     * @Author: mq
     * @Description: 根据量表ID获取结果解读列表数据
     * @Date: 2024/11/5 17:45
     * @Param: scaleId
     * @return: java.util.List<com.wftk.scale.biz.dto.scale.ScaleResultIntroDTO>
     **/
    List<ScaleResultIntroDTO> getList(@Param("scaleId") Long scaleId);

    /*
    * @Author: mq
    * @Description: 根据结果解读ID获取结果解读详情
    * @Date: 2024/12/7 13:55
    * @Param: resultIntroId
    * @return: ScaleResultIntroDTO
    **/
    ScaleResultIntroDTO findById(@Param("resultIntroId") Long resultIntroId);

    /*
     * @Author: mq
     * @Description: 根据量表ID和因子维度ID获取结果解读信息(一个因子可能会配置多个结果解读)
     * @Date: 2024/11/26 18:42
     * @Param: scaleId
     * @Param: factorId
     * @return: ScaleResultIntro
     **/
    List<ScaleResultIntro> findByScaleIdAndFactorId(@Param("scaleId") Long scaleId, @Param("factorId") Long factorId);
}
