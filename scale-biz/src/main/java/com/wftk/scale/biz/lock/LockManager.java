package com.wftk.scale.biz.lock;

import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DReadWriteLock;

public interface LockManager {

    /**
     * 获取锁
     * @param key
     * @return
     */
    DLock getLock(String key);


    /**
     * 获取读写锁
     * @param key
     * @return
     */
    DReadWriteLock getReadWriteLock(String key);


    /**
     * 获取下单锁
     * @param scaleOrderKey
     * @return
     */
    default DReadWriteLock getScaleOrderLock(String scaleOrderKey) {
        return getReadWriteLock("LOCK:SCALE:ORDER:ID:" + scaleOrderKey);
    }

    /**
     * 获取国任下单锁
     * @param guorenOrderNo
     * @return
     */
    default DLock getCreateGuorenOrderLockWithOrderNo(String guorenOrderNo) {
        return getLock("LOCK:GUOREN:ORDER:CREATE:ORDER:NO:" + guorenOrderNo);
    }

    /**
     * 获取httpJob定时任务锁
     * @return
     */
    default DLock getHttpJobScheduleLock() {
        return getLock("LOCK:SCALE:HTTPJOB:SCHEDULE");
    }

    /**
     * 获取httpJob定时任务锁
     * @return
     */
    default DLock getHttpJobCreateLock(String bizId,String bizCode) {
        return getLock("LOCK:SCALE:HTTPJOB:CREATE:"+ bizCode + ":" + bizId);
    }

    /**
     * 获取httpJob定时任务锁
     * @return
     */
    default DLock getNoticeMessageScheduleLock() {
        return getLock("LOCK:NOTICE:MESSAGE:SCHEDULE");
    }


}
