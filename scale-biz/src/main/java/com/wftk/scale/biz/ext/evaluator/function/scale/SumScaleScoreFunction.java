package com.wftk.scale.biz.ext.evaluator.function.scale;

import java.util.List;
import java.util.Map;

import com.googlecode.aviator.runtime.type.AviatorDouble;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;
import com.wftk.scale.biz.service.ScaleUserResultRecordService;

import cn.hutool.core.collection.CollUtil;

import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.ext.evaluator.constant.EnvConstant;

/**
 * 统计量表总分
 * 示例: s_sum(), 表示统计当前量表的总分
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
public class SumScaleScoreFunction extends BaseScaleEvaluationFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Scale.S_SUM;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env) {
        Long resultId = getFromEnv(env, EnvConstant.RESULT_ID, Long.class, false);
        logger.info("s_sum: resultId: {}, scaleId: {}", resultId, env.get(EnvConstant.SCALE_ID));
        ScaleUserResultRecordService scaleUserResultRecordService = getFromEnv(env,
                EnvConstant.SCALE_USER_RESULT_RECORD_SERVICE, ScaleUserResultRecordService.class, false);
        List<ScaleUserResultRecord> scaleUserResultRecords = scaleUserResultRecordService.findByResultId(resultId);
        double score = 0;
        if (CollUtil.isEmpty(scaleUserResultRecords)) {
            throw new IllegalArgumentException("scaleUserResultRecords must not be empty");
        } else {
            score = sumScore(scaleUserResultRecords);
        }
        return new AviatorDouble(score);
    }

}
