//package com.wftk.scale.biz.excel.batch;
//
//
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.*;
//
///**
// * @ClassName: BatchHandleWrapper
// * @Description:
// * @Author: mq
// * @Date: 2024/11/27
// * @Version: 1.0
// **/
//@Slf4j
//public final class BatchHandleWrapper<E> {
//    private LinkedBlockingQueue<E> queue;
//    private boolean running = true;
//    private ExecutorService exec;
//    private int batchSize = 100;
//    private int queueSize = 1024 * 1000;
//    private int timeout = 5000;
//    private int taskCount = 5;
//    private final BatchHandler<E> batchHandler;
//    private EnqueueHandler<E> enqueueHandler;
//
//    public static <T> BatchHandleWrapper<T> batchHandler(
//            BatchHandler<T> batchHandler) {
//        return new BatchHandleWrapper<>(batchHandler);
//    }
//
//    public BatchHandleWrapper<E> batchSize(int size) {
//        if (size > 0) {
//            this.batchSize = size;
//        }
//        return this;
//    }
//
//    public BatchHandleWrapper<E> queueSize(int size) {
//        if (size > 0) {
//            this.queueSize = size;
//        }
//        return this;
//    }
//
//    public BatchHandleWrapper<E> timeout(int timeout, TimeUnit unit) {
//        if (timeout > 0) {
//            this.timeout = (int) unit.toMillis(timeout);
//        }
//        return this;
//    }
//
//    public BatchHandleWrapper<E> taskCount(int count) {
//        if (count > 0) {
//            this.taskCount = count;
//        }
//        return this;
//    }
//
//    public BatchHandleWrapper<E> enqueueHandler(EnqueueHandler<E> enqueueHandler) {
//        this.enqueueHandler = enqueueHandler;
//        return this;
//    }
//
//    public BatchHandleWrapper<E> start() {
//        check();
//        queue = new LinkedBlockingQueue<>(queueSize);
//        exec = Executors.newFixedThreadPool(taskCount);
//        BatchTask task = new BatchTask();
//        for (int i = 0; i < taskCount; i++) {
//            exec.execute(task);
//        }
//        return this;
//    }
//
//    private void check() {
//        if (batchHandler == null) {
//            throw new RuntimeException("BatchHandler不能为空");
//        }
//    }
//
//    private BatchHandleWrapper(BatchHandler<E> batchHandler) {
//        this.batchHandler = batchHandler;
//        check();
//    }
//
//    public BatchHandleWrapper(BatchHandler<E> batchHandler, int batchSize,
//                              int timeoutMs, int taskCount) {
//        this(batchHandler, Integer.MAX_VALUE, batchSize, timeoutMs, taskCount);
//    }
//
//    public BatchHandleWrapper(BatchHandler<E> batchHandler, int queueSize,
//                              int batchSize, int timeoutMs, int taskCount) {
//        this(batchHandler, queueSize, batchSize, timeoutMs, taskCount, null);
//    }
//
//    public BatchHandleWrapper(BatchHandler<E> batchHandler, int queueSize,
//                              int batchSize, int timeoutMs, int taskCount,
//                              EnqueueHandler<E> enqueueHandler) {
//        if (batchHandler == null || batchSize < 1 || timeoutMs < 0
//                || taskCount < 0) {
//            throw new RuntimeException(
//                    "参数错误：BatchHandler不能为null，batchSize必须大于0，timeout必须大于0，taskCount必须大于0");
//        }
//        this.queue = new LinkedBlockingQueue<>(queueSize);
//        this.batchSize = batchSize;
//        this.timeout = timeoutMs;
//        this.batchHandler = batchHandler;
//        this.enqueueHandler = enqueueHandler;
//        exec = Executors.newFixedThreadPool(taskCount);
//        BatchTask task = new BatchTask();
//        for (int i = 0; i < taskCount; i++) {
//            exec.execute(task);
//        }
//    }
//
//    public void add(E data) {
//        try {
//            if (enqueueHandler != null) {
//                enqueueHandler.handle(queue, data);
//            } else {
//                queue.put(data);
//            }
//        } catch (Exception e) {
//            log.error("数据入队失败：" + e.getMessage());
//        }
//    }
//
//    public void add(E data, int timeoutMs) {
//        try {
//            if (enqueueHandler != null) {
//                enqueueHandler.handle(queue, data);
//            } else {
//                if (!queue.offer(data, timeoutMs, TimeUnit.MILLISECONDS)) {
//                }
//            }
//        } catch (Exception e) {
//            log.error("数据入队失败：" + e.getMessage());
//        }
//    }
//
//    public void stop() {
//        running = false;
//        exec.shutdownNow();
//    }
//
//    /**
//     * 批量处理器
//     */
//    public interface BatchHandler<E> {
//
//        Integer handle(List<E> list, Runnable task);
//    }
//
//    /**
//     * 数据入队处理器
//     */
//    public interface EnqueueHandler<E> {
//
//        void handle(BlockingQueue<E> queue, E obj);
//    }
//
//    /**
//     * 批量处理任务
//     */
//    private class BatchTask implements Runnable {
//
//        @Override
//        public void run() {
//            ArrayList<E> list = new ArrayList<>(batchSize);
//            long l = System.currentTimeMillis();
//            while (running && !Thread.interrupted()) {
//                try {
//                    E data = queue.poll(timeout, TimeUnit.MILLISECONDS);
//                    if (data == null) {
//                        batch(list);
//                    } else {
//                        list.add(data);
//                        if (list.size() == batchSize || (System.currentTimeMillis() - l) >= timeout) {
//                            batch(list);
//                            l = System.currentTimeMillis();
//                        }
//                    }
//                } catch (Exception e) {
//                    log.error("BatchTask error:", e);
//                }
//            }
//            // 停止处理后，批量处理内存中存在的数据
//            while (queue.size() > 0) {
//                E data = queue.poll();
//                if (data != null) {
//                    list.add(data);
//                    if (list.size() == batchSize) {
//                        batch(list);
//                    }
//                }
//            }
//            batch(list);
//        }
//
//        private void batch(ArrayList<E> list) {
//            if (list.size() > 0) {
//                try {
//                    batchHandler.handle(list, this);
//                } finally {
//                    // 处理数据后，不管成功与否，均清空当前数据
//                    list.clear();
//                }
//            } else {
////				 System.out.println("当前队列无数据");
//            }
//        }
//    }
//}
//
//
//
//
