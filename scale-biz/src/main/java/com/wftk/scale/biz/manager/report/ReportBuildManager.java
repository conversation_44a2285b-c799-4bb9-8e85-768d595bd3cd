package com.wftk.scale.biz.manager.report;

import com.wftk.scale.biz.entity.Scale;
import com.wftk.scale.biz.entity.ScaleReportConf;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.manager.report.dto.ReportDTO;
import com.wftk.scale.biz.manager.report.dto.content.*;
import com.wftk.scale.biz.manager.report.handle.*;
import com.wftk.scale.biz.service.ScaleReportConfService;
import com.wftk.scale.biz.service.ScaleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createDate 2025/9/8 17:59
 */
@Slf4j
@Component
public class ReportBuildManager {

    @Resource
    ScaleReportConfService scaleReportConfService;

    @Resource
    ScaleService scaleService;

    @Resource
    UserInfoHandle userInfoHandle;

    @Resource
    ReadingInstructionHandle readingInstructionHandle;

    @Resource
    TotalScoreResultsHandle totalScoreResultsHandle;

    @Resource
    AvgScoreResultsHandle avgScoreResultsHandle;

    @Resource
    PositiveCountHandle positiveCountHandle;

    @Resource
    FactorScoreHandle factorScoreHandle;

    @Resource
    FactorAnalyseHandle factorAnalyseHandle;


    public ReportDTO buildReportResult(ScaleUserResult scaleUserResult) {
        if (scaleUserResult == null) {
            log.warn("scaleUserResult is null. ");
            return null;
        }
        // 获取报告设置
        ScaleReportConf scaleReportConf = scaleReportConfService.detail(scaleUserResult.getScaleId());
        if (scaleReportConf == null) {
            log.error("scaleReportConf is null. scaleId: {}", scaleUserResult.getScaleId());
            return null;
        }

        ReportDTO reportDTO= new ReportDTO();

        // 处理报告所需数据
        reportDTO.setScaleName(scaleService.getById(scaleUserResult.getScaleId()).getName());

        ReadingInstructionsContentDTO readingInstruction = readingInstructionHandle.buildReportContent(scaleReportConf);
        reportDTO.setReadingInstructionsContent(readingInstruction);

        UserInfoReportContentDTO userInfoReportContent = userInfoHandle.buildReportContent(scaleUserResult, scaleReportConf);
        reportDTO.setUserInfoReportContent(userInfoReportContent);

        TotalScoreReportContentDTO totalScoreReportContent = totalScoreResultsHandle.buildReportContent(scaleUserResult, scaleReportConf);
        reportDTO.setTotalScoreReportContent(totalScoreReportContent);

        AvgScoreReportContentDTO avgScoreReportContent = avgScoreResultsHandle.buildReportContent(scaleUserResult, scaleReportConf);
        reportDTO.setAvgScoreReportContent(avgScoreReportContent);

        PositiveCountReportContentDTO positiveCountReportContentDTO = positiveCountHandle.buildReportContent(scaleUserResult, scaleReportConf);
        reportDTO.setPositiveCountReportContent(positiveCountReportContentDTO);

        FactorScoreContentDTO factorScoreContentDTO = factorScoreHandle.buildReportContent(scaleUserResult, scaleReportConf);
        reportDTO.setFactorScoreContentDTO(factorScoreContentDTO);

        FactorAnalyseReportContentDTO factorAnalyseReportContentDTO = factorAnalyseHandle.buildReportContent(scaleUserResult, scaleReportConf);
        reportDTO.setFactorAnalyseReportContent(factorAnalyseReportContentDTO);

        return reportDTO;
    }

}
