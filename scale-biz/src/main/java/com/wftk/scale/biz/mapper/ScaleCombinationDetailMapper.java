package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.entity.ScaleCombinationDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 组合量表详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleCombinationDetailMapper extends BaseMapper<ScaleCombinationDetail> {

    /*
     * @Author: mq
     * @Description: 根据组合量表ID删除关联的量表信息
     * @Date: 2024/11/6 18:28 
     * @Param: combinationId-组合量表ID
     * @return: void 
     **/
    void deleteByCombinationId(@Param("combinationId") Long combinationId);

    /*
     * @Author: mq
     * @Description: 根据组合量表ID获取关联的量表数据
     * @Date: 2024/11/7 9:54
     * @Param: combinationId-组合量表ID
     * @return: java.util.List<com.wftk.scale.biz.dto.scale.ScaleQueryDTO>
     **/
    List<ScaleQueryDTO> findByCombinationId(@Param("combinationId") Long combinationId);

    /**
     * 获取量表id
     * @param combinationId
     * @return
     */
    List<Long> findScaleIdByCombinationId(@Param("combinationId") Long combinationId);

    /**
     * 获取量表id
     * @param combinationIds
     * @return
     */
    List<Long> findScaleIdByCombinationIds(@Param("combinationIds") List<Long> combinationIds);
}
