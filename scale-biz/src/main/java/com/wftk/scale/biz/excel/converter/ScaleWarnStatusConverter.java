package com.wftk.scale.biz.excel.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.wftk.common.core.enums.BaseEnum;
import com.wftk.scale.biz.constant.enums.ScaleWarnStatusEnum;

public class ScaleWarnStatusConverter implements Converter<Integer> {

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) {
        if(context.getValue() == null){
            return new WriteCellData<String>();
        }
        Integer value = context.getValue();
        ScaleWarnStatusEnum scaleWarnStatusEnum = BaseEnum.valueOf(value, ScaleWarnStatusEnum.class);
        return new WriteCellData<String>(scaleWarnStatusEnum == null ? "" : scaleWarnStatusEnum.getLabel());
    }

}
