package com.wftk.scale.biz.ext.evaluator.function.util;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorNumber;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;
import com.wftk.scale.biz.ext.evaluator.function.EvaluationFunction;

/**
 * 绝对值函数
 * 示例: u_abs(-1), 表示返回-1的绝对值
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
public class AbsFunction extends AbstractFunction implements EvaluationFunction {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public String getName() {
        return FunctionNameConstant.Util.ABS;
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        Number number = (Number) arg1.getValue(env);
        logger.info("u_abs: number: {}", number);
        if (number == null) {
            throw new IllegalArgumentException("arg1 must not be null");
        }
        return AviatorNumber.valueOf(Math.abs(number.doubleValue()));
    }

}
