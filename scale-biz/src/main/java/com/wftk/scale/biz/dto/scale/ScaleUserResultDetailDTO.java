package com.wftk.scale.biz.dto.scale;


import com.wftk.scale.biz.entity.ScaleUserResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: ScaleUserResultDetailDTO
 * @Description: 用户测评记录详情
 * @Author: mq
 * @Date: 2024/11/25
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleUserResultDetailDTO implements Serializable {

    /**
     * 测评记录基本信息
     */
    private ScaleUserResult userResult;

    /**
     * 测评因子得分详情
     */
    List<ScaleUserFactorResultDetailDTO> factorResults;
}
