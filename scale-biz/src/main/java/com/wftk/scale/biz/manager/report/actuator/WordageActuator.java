package com.wftk.scale.biz.manager.report.actuator;

import com.wftk.scale.biz.constant.ScaleReportConfSettingConstant;
import com.wftk.scale.biz.manager.report.dto.base.FactorResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2025/9/10 20:37
 */
@Slf4j
@Component
public class WordageActuator {

    public String doWordage(List<FactorResultDTO> factorResults,Integer reportType){
        // 获取文字
        if(Objects.equals(reportType, ScaleReportConfSettingConstant.ReportType.POSITIVE_COUNT)){
            return "总阳性数量："+factorResults.get(0).getScore();
        }
        return buildWordage(factorResults);
    }


    public String buildWordage(List<FactorResultDTO> factorResults){
        StringBuilder stringBuilder = new StringBuilder();
        for (FactorResultDTO factorResult : factorResults) {
            stringBuilder.append(factorResult.getFactorName()).append("得分：").append(factorResult.getScore()).append("分").append("<br/>");
        }
        return stringBuilder.toString();
    }

}
