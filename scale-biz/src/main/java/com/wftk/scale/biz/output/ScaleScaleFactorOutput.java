package com.wftk.scale.biz.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class ScaleScaleFactorOutput implements Serializable {
    @Serial
    private static final long serialVersionUID = -3276463206205629383L;

    /**
     * 量表ID
     */
    @Schema(description = "量表ID")
    private Long scaleId;

    @Schema(description = "量表编码")
    private String scaleCode;

    /**
     * 量表名称
     */
    @Schema(description = "量表名称")
    private String scaleName;

    /**
     * 量表因子维度信息
     */
    @Schema(description = "量表因子维度信息")
    private List<ScaleWarnConfFactorOutput> scaleWarnConfOutputList;


    @Data
    public static class ScaleWarnConfFactorOutput{

        /**
         * 量表因子维度ID
         */
        @Schema(description = "量表因子维度ID")
        private Long scaleFactorId;

        /**
         * 量表因子维度名称
         */
        @Schema(description = "量表因子维度名称")
        private String scaleFactorName;

        /**
         * 预警方式
         */
        @Schema(description = "预警方式")
        private String scaleWarnType;

        /**
         * 预警配置ID
         */
        @Schema(description = "预警配置ID")
        private Long scaleWarnConfId;
    }

}
