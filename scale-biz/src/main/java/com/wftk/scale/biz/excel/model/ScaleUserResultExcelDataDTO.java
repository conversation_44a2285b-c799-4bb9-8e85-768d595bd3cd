package com.wftk.scale.biz.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wftk.scale.biz.excel.converter.SexConverter;
import com.wftk.scale.biz.excel.model.base.BaseExcelDataDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ScaleUserResultExcelDataDTO extends BaseExcelDataDTO {

    @ExcelProperty("用户账号")
    private String account;
    @ExcelProperty("用户姓名")
    private String userName;
    /**
     * 性别 1男 2女
     */
    @ExcelProperty(value = "性别", converter = SexConverter.class)
    private Integer sex;
    /**
     * 所属部门名称
     */
    @ExcelProperty("归属部门")
    private String departmentName;
    /**
     * 手机号
     */
    @ExcelProperty("手机号码")
    private String phone;
    /**
     * 量表名称
     */
    @ExcelProperty("量表名称")
    private String scaleName;
    /**
     * 量表分类
     */
    @ExcelProperty("量表分类")
    private String scaleType;
    /**
     * 终端名称
     */
    @ExcelProperty("测评终端")
    private String terminalName;
}
