package com.wftk.scale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.constant.enums.OrderEnum;
import com.wftk.scale.biz.constant.enums.PayChannelEnum;
import com.wftk.scale.biz.constant.enums.PaymentEnum;
import com.wftk.scale.biz.entity.OrderPayment;
import com.wftk.scale.biz.mapper.OrderMapper;
import com.wftk.scale.biz.mapper.OrderPaymentMapper;
import com.wftk.scale.biz.service.OrderPaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
@Slf4j
public class OrderPaymentServiceImpl extends ServiceImpl<OrderPaymentMapper, OrderPayment> implements OrderPaymentService {

    @Autowired
    OrderPaymentMapper orderPaymentMapper;

    @Autowired
    OrderMapper orderMapper;

    @Override
    public Map<String, Object> doPrepay(OrderPayment orderPayment) {
        // 修改支付订单为支付中
        orderPayment.setStatus(PaymentEnum.PROCESSING.getStatus());
        updateById(orderPayment);

        // 发起支付
        if(Objects.equals(orderPayment.getPayChannel(), PayChannelEnum.OFFLINE_SETTLE.getValue())){
            // 修改支付订单为已支付
            updateStatus(orderPayment.getTradeNo(), PaymentEnum.SUCCESS.getStatus(),PaymentEnum.PROCESSING.getStatus());
            // 修改订单为待完成
            orderMapper.updateStatus(orderPayment.getOrderNo(), OrderEnum.WAIT_COMPLETE.getStatus(),OrderEnum.WAIT_PAY.getStatus());
        }

        return null;
    }

    @Override
    public Integer updateStatus(String tradeNo, Integer status, Integer oldStatus) {
        log.info("orderPayment status change. status: {} -> {}. tradeNo: {}",oldStatus,status,tradeNo);
        return orderPaymentMapper.updateStatus(tradeNo, status, oldStatus);
    }


}
