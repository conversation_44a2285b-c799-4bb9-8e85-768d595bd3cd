package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.ScaleCombinationLatest;

/**
 * <p>
 * 组合量表最新数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleCombinationLatestMapper extends BaseMapper<ScaleCombinationLatest> {

    /* 
     * @Author: mq
     * @Description: 根据量表编码获取最新版本信息
     * @Date: 2024/11/6 17:15 
     * @Param: code-量表编码
     * @return: com.wftk.scale.biz.entity.ScaleCombinationLatest 
     **/
    ScaleCombinationLatest findByCode(String code);
}
