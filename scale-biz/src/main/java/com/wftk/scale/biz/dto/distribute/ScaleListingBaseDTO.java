package com.wftk.scale.biz.dto.distribute;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: ScaleListingBaseDTO
 * @Description: 量表分发基础信息实体
 * @Author: mq
 * @Date: 2024-10-31 14:49
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleListingBaseDTO implements Serializable {

    /**
     * 上下架id
     */
    private Long scaleListingId;

    /**
     * 本次上架的量表ID或者组合ID(字段冗余,便于查询分发记录)
     */
    private Long targetId;

    /**
     * 本次上架的量表名称或者组合名称(字段冗余,便于查询分发记录)
     */
    private String targetName;

    /**
     * 本次上架的量表类型或者组合方式类型(字段冗余,便于查询分发记录)
     */
    private Long targetType;

    /**
     * 本次上架的分类(字段冗余,便于查询分发记录)
     */
    private Integer type;
}
