package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.entity.ScaleCombinationLatest;

/**
 * <p>
 * 组合量表最新数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleCombinationLatestService extends IService<ScaleCombinationLatest> {

    /*
     * @Author: mq
     * @Description: 保存量表最新版本的数据信息
     * @Date: 2024/11/6 17:05
     * @Param: scaleId-组合量表ID
     * @Param: code-组合量表编号
     * @return: void
     **/
    void saveOrUpdateScaleLatest(Long scaleId, String code);
}
