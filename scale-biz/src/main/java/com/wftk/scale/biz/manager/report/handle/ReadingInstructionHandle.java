package com.wftk.scale.biz.manager.report.handle;

import com.wftk.scale.biz.entity.ScaleReportConf;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.entity.User;
import com.wftk.scale.biz.manager.report.actuator.ReadingInstructionActuator;
import com.wftk.scale.biz.manager.report.dto.UserReportInfoDTO;
import com.wftk.scale.biz.manager.report.dto.content.ReadingInstructionsContentDTO;
import com.wftk.scale.biz.manager.report.dto.content.UserInfoReportContentDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @createDate 2025/9/16 17:41
 */
@Slf4j
@Component
public class ReadingInstructionHandle {

    @Resource
    ReadingInstructionActuator readingInstructionActuator;

    public ReadingInstructionsContentDTO buildReportContent(ScaleReportConf scaleReportConf) {
        if(!scaleReportConf.getReadingInstructionsEnable()){
            log.warn("readingInstruction enable is close. reportConfId: {}",scaleReportConf.getId());
            return null;
        }
        ReadingInstructionsContentDTO readingInstructionsContentDTO = new ReadingInstructionsContentDTO();
        String readingInstruction = readingInstructionActuator.doReadingInstruction(scaleReportConf.getId());
        readingInstructionsContentDTO.setReadingInstruction(readingInstruction);
        return readingInstructionsContentDTO;
    }

}
