package com.wftk.scale.biz.ext.evaluator.function.scale.question;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import com.wftk.scale.biz.entity.ScaleQuestion;
import com.wftk.scale.biz.ext.evaluator.function.scale.BaseScaleFunction;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 量表相关函数
 * <AUTHOR>
 * @date 2025-09-03
 */
public abstract class BaseScaleQuestionFunction extends BaseScaleFunction {

    /**
     * 根据问题序号选择问题（考虑到量表的题目一般不会太多，直接查出该量表的题目，然后通过程序处理）
     * @param env
     * @param sequences
     * @return
     */
    protected List<ScaleQuestion> selectBySequence(Map<String, Object> env, List<ScaleQuestionSequence> sequences) {
        List<ScaleQuestion> questions = getQuestions(env);
        if (questions == null || questions.isEmpty()) {
            return Collections.emptyList();
        }
        List<ScaleQuestion> resultList = new ArrayList<>(questions.size());
        for (ScaleQuestion question : questions) {
            for (ScaleQuestionSequence sequence : sequences) {
                if (!sequence.getQuestionNumber().equals(question.getQuestionNumber())) {
                    continue;
                }
                String seqInDB = StrUtil.isBlank(question.getSubNumber()) ? sequence.getQuestionNumber() : sequence.getQuestionNumber() + "." + sequence.getSubNumber();
                String seqInParam = StrUtil.isBlank(sequence.getSubNumber()) ? sequence.getQuestionNumber() : sequence.getQuestionNumber() + "." + sequence.getSubNumber();
                if (!seqInDB.equals(seqInParam)) {
                    continue;
                }
                resultList.add(question);
                break;
            }
        }
        if (resultList.size() < sequences.size()) {
            throw new IllegalArgumentException("question sequence not found");
        }
        return resultList;
    }


    /**
     * 获取问题ID
     * @param scaleQuestions
     * @return
     */
    protected String getQuestionIds(List<ScaleQuestion> scaleQuestions) {
        return scaleQuestions.stream()
            .map(ScaleQuestion::getId)
            .map(String::valueOf)
            .collect(Collectors.joining(","));
    }


    /**
     * 解析并构建问题序号
     * @param questionNumbers
     * @return
     */
    protected List<ScaleQuestionSequence> buildQuestionSequence(String... questionNumbers) {
        List<ScaleQuestionSequence> sequences = new ArrayList<>(questionNumbers.length);
        for (String seq : questionNumbers) {
            sequences.add(parseQuestionSequence(seq));
        }
        return sequences;
    }


    /**
     * 解析并构建问题序号（传入的是区间: 1-8, 2.1-2.5）
     * @param questionNumbers
     * @return
     */
    protected List<ScaleQuestionSequence> buildQuestionSequenceWithRange(String... ranges) {
        List<ScaleQuestionSequence> sequences = new ArrayList<>(ranges.length);
        for (String range : ranges) {
            if (!range.contains("-")) {
                throw new IllegalArgumentException("illegal range format: " + range);
            }
            String[] split = range.split("-");
            String start = split[0].trim();
            String end = split[1].trim();
            ScaleQuestionSequence startSequence = parseQuestionSequence(start);
            ScaleQuestionSequence endSequence = parseQuestionSequence(end);
            if (startSequence.getLevel() != endSequence.getLevel()) {
                throw new IllegalArgumentException("illegal range format: " + range);
            }
            if (startSequence.getLevel() == 1) {
                sequences.addAll(rangeSequences(start, end, null));
                continue;
            }

            if (startSequence.getLevel() == 2 && !startSequence.getQuestionNumber().equals(endSequence.getQuestionNumber())) {
                //如果是多级题目，只允许同一答题内选择区间
                throw new IllegalArgumentException("illegal range format: " + range);
            }
            sequences.addAll(rangeSequences(startSequence.getSubNumber(), endSequence.getSubNumber(), startSequence.getQuestionNumber()));
        }
        return sequences;
    }



    /**
     * 构建区间内的问题序号
     * @param start
     * @param end
     * @param questionNumber 一级题号
     */
    @SuppressWarnings("null")
    private List<ScaleQuestionSequence> rangeSequences(String start, String end, @Nullable String questionNumber) {
        if (!NumberUtil.isInteger(start) || !NumberUtil.isInteger(end)) {
            throw new IllegalArgumentException("illegal range format: " + start + "-" + end);
        }
        int startNum = NumberUtil.parseInt(start);
        int endNum = NumberUtil.parseInt(end);
        if (startNum > endNum) {
            throw new IllegalArgumentException("illegal range format: " + start + "-" + end);
        }
        List<ScaleQuestionSequence> resultList = new ArrayList<>(endNum - startNum + 1);
        for (int i = startNum; i <= endNum; i++) {
            if (StrUtil.isNotBlank(questionNumber)) {
                //多级
                resultList.add(new ScaleQuestionSequence(questionNumber, String.valueOf(i)));
            } else {
                //一级
                resultList.add(new ScaleQuestionSequence(String.valueOf(i), null));
            }
        }
        return resultList;
    }


    /**
     * 解析问题序号
     * @param seq
     * @return
     */
    private ScaleQuestionSequence parseQuestionSequence(String seq) {
        //题号通常为1或者1.1这种格式
        if (seq.contains(".")) {
            String[] split = seq.split("\\.");
            return new ScaleQuestionSequence(split[0].trim(), split[1].trim());
        } else {
            //只存在一级
            return new ScaleQuestionSequence(seq, null);
        }
    }




    /**
     * 量表问题序号(系统中最多2级，需求已确定)
     */
    public static class ScaleQuestionSequence {
        
        /**
         * 问题编号
         */
        private final String questionNumber;

        /**
         * 问题子编号
         */
        private final String subNumber;


        public ScaleQuestionSequence(@NonNull String questionNumber, String subNumber) {
            this.questionNumber = questionNumber;
            this.subNumber = subNumber;
        }

        public String getQuestionNumber() {
            return questionNumber;
        }

        public String getSubNumber() {
            return subNumber;
        }


        public int getLevel() {
            return StrUtil.isBlank(subNumber) ? 1 : 2;
        }

    }
}
