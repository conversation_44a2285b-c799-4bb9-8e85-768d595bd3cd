package com.wftk.scale.biz.dto.scale;


import com.wftk.scale.biz.dto.report.ChartSettingDTO;
import com.wftk.scale.biz.dto.report.FormSettingDTO;
import com.wftk.scale.biz.dto.report.RemarkSettingDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: ScaleReportConfSettingCreateInput
 * @Description:
 * @Author: mq
 * @Date: 2024/11/26
 * @Version: 1.0
 **/
@Data
public class ScaleReportConfSettingDetailDTO implements Serializable {

    /**
     * 阅读须知
     */
    private String readingInstruction;

    /**
     * 备注
     */
    private List<RemarkSettingDTO> remarkSettings;

    /**
     * 表格设置
     */
    private List<FormSettingDTO> formSettings;

    /**
     * 图表设置
     */
    private List<ChartSettingDTO> chartSettings;


}
