package com.wftk.scale.biz.service.impl;

import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.wftk.scale.biz.constant.enums.ScheduledEnum;
import com.wftk.scale.biz.entity.HttpJob;
import com.wftk.scale.biz.lock.LockManager;
import com.wftk.scale.biz.mapper.HttpJobMapper;
import com.wftk.scale.biz.service.HttpJobService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02 15:21:14
 */
@Service
public class HttpJobServiceImpl extends ServiceImpl<HttpJobMapper, HttpJob> implements HttpJobService {

    @Autowired
    HttpJobMapper httpJobMapper;

    @Autowired
    LockManager lockManager;

    @Override
    public HttpJob createJob(String bizId, String bizCode, Integer status, String params) {
        DLock dLock = lockManager.getHttpJobScheduleLock();
        try {
            if(dLock.tryLock()){
                HttpJob httpJobInDB = httpJobMapper.selectByBizCodeAndBizId(bizId, bizCode);
                if(httpJobInDB != null){
                    return httpJobInDB;
                }
                httpJobInDB = new HttpJob();
                httpJobInDB.setBizId(bizId);
                httpJobInDB.setBizCode(bizCode);
                httpJobInDB.setStatus(status);
                httpJobInDB.setCount(0);
                httpJobInDB.setMaxCount(ScheduledEnum.five.getCount());
                httpJobInDB.setParams(params);
                save(httpJobInDB);
                return httpJobInDB;
            }
            return null;
        }finally {
            dLock.unLock();
        }
    }

    @Override
    public HttpJob getJobByBizIdAndBizCoce(String bizId, String bizCode) {
        return httpJobMapper.selectByBizCodeAndBizId(bizId, bizCode);
    }

    @Override
    public Integer updateHttpJobResultByBizCodeAndBizId(HttpJob httpJob) {
        return httpJobMapper.updateHttpJobResultByBizCodeAndBizId(httpJob);
    }

    @Override
    public Integer updateStatusByBizCodeAndBizId(HttpJob httpJob) {
        return httpJobMapper.updateStatusByBizCodeAndBizId(httpJob);
    }

    @Override
    public Integer updateStatusByBizCodeAndBizIds(String bizCode, LocalDateTime nextTime, Integer status, List<Long> bizIds) {
        return httpJobMapper.updateStatusByBizCodeAndBizIds(bizCode,nextTime,status,bizIds);
    }

    @Override
    public List<HttpJob> selectNotEndList() {
        return httpJobMapper.selectNotEndList();
    }
}
