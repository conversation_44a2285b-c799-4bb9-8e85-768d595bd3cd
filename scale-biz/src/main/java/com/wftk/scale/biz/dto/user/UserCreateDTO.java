package com.wftk.scale.biz.dto.user;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @createDate 2024/11/26 17:48
 */
@Data
public class UserCreateDTO {

    /**
     * 用户编码
     */
    @NotBlank(message = "用户编码不能为空")
    @Length(max = 50, message = "用户编码长度不能超过50")
    private String code;

    /**
     * 用户姓名
     */
    @NotBlank(message = "用户姓名不能为空")
    @Length(max = 50, message = "用户姓名长度不能超过50")
    private String name;

    /**
     * 用户账户
     */
    @NotBlank(message = "用户账户不能为空")
    @Length(max = 50, message = "用户账号长度不能超过50")
    private String account;

    /**
     * 性别 1男 2女
     */
    private Integer sex;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 电话号码
     */
    @Length(min = 11, max = 11, message = "手机号格式有误")
    @Pattern(regexp = "^(1[3-9])\\d{9}$", message = "手机号格式有误")
    private String phone;

    /**
     * 身份证号码
     */
    @Length(min = 18, max = 18, message = "身份证号码格式错误")
    @Pattern(regexp = "^[1-9]\\d{5}(?:19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$", message = "身份证号码格式错误")
    private String idCard;

    /**
     * 邮箱
     */
    @Pattern(regexp = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@[a-zA-Z0-9-]+\\.[a-zA-Z]{2,}$", message = "邮箱格式错误")
    private String email;

    /**
     * 备注
     */
    private String remark;

    /**
     * 部门id
     */
    @NotNull(message = "所属部门不能为空")
    private Long departmentId;

    /**
     * 用户类型
     */
    private Integer userType;

    /**
     * 额外信息
     */
    private String extraInfo;

    /**
     * 入学年份
     */
    private LocalDate entryDate;

}
