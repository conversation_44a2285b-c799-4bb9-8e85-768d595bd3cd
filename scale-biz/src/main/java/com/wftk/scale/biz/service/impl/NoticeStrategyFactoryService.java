//package com.wftk.scale.biz.service.impl;
//
//import com.wftk.scale.biz.ext.notice.dto.NoticeDTO;
//import com.wftk.scale.biz.ext.notice.enums.NoticeTypeEnum;
//import com.wftk.scale.biz.service.NoticeService;
//import jakarta.annotation.PostConstruct;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.Map;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// */
//@Service
//@Slf4j
//public class NoticeStrategyFactoryService<T extends NoticeDTO> {
//
//    @Resource
//    private List<NoticeService<T>> noticeServiceList;
//
//    private Map<NoticeTypeEnum, NoticeService<T>> noticeServiceMap;
//
//    @PostConstruct
//    public void init(){
//        noticeServiceMap = noticeServiceList.stream().collect(Collectors.toMap(NoticeService::getNoticeType, Function.identity()));
//    }
//
//    public void sendNotice(T dto){
//        if(dto == null){
//            return;
//        }
//        final String tag = "sendNotice-" + dto.getNoticeType();
//        NoticeService<T> noticeService = null;
//        try {
//            log.info("{} notice dto:{}", tag, dto);
//            NoticeTypeEnum noticeType = dto.getNoticeType();
//            if(!noticeServiceMap.containsKey(noticeType)){
//                log.error("{} notice type not exist", tag);
//                return;
//            }
//            noticeService = noticeServiceMap.get(noticeType);
//            //异步通知
//            noticeService.sendNotice(dto);
//        }catch (Exception e){
//           log.error("{} notice", tag, e);
//        } finally {
//            if(noticeService != null){
//                noticeService.callBack();
//            }
//        }
//    }
//}
