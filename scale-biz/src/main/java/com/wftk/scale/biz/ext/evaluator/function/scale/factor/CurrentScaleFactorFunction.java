package com.wftk.scale.biz.ext.evaluator.function.scale.factor;

import java.util.Map;

import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.ext.evaluator.constant.EnvConstant;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;

/**
 * 获取当前量表因子函数
 * 示例: f_current(), 表示获取当前量表的因子
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
public class CurrentScaleFactorFunction extends BaseScaleFactorFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Factor.F_CURRENT;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env) {
        Long factorId = (Long) env.get(EnvConstant.CURRENT_FACTOR_ID);
        logger.info("f_current: factorId: {}", factorId);
        if (factorId == null) {
            throw new IllegalArgumentException("currentFactorId must not be null");
        }
        ScaleFactor scaleFactor = getFactorById(env, factorId);
        String questionIds = getQuestionIds(scaleFactor);
        return new AviatorString(questionIds);
    }

}
