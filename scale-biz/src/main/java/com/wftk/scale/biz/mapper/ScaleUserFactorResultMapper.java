package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.scale.ScaleUserFactorResultDetailDTO;
import com.wftk.scale.biz.entity.ScaleUserFactorResult;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 测评因子得分表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleUserFactorResultMapper extends BaseMapper<ScaleUserFactorResult> {

    /*
    * @Author: mq
    * @Description: 根据测评记录ID获取测评因子得分情况
    * @Date: 2024/11/19 17:43
    * @Param: resultId-测评记录ID
    * @return: List<ScaleUserFactorResult>
    **/
    List<ScaleUserFactorResult> getList(@Param("resultId") Long resultId);
    
    /*
    * @Author: mq
    * @Description: 根据测评记录ID获取测评因子得分详情信息
    * @Date: 2024/11/25 18:30
    * @Param: resultId-测评记录ID
    * @return: List<ScaleUserFactorResultDetailDTO>
    **/
    List<ScaleUserFactorResultDetailDTO> getDetailByResultId(Long resultId);

    /**
     * 根据因子id和结果id查询因子结果
     *
     * @param resultId
     * @param factorId
     * @param showType
     * @return
     */
    ScaleUserFactorResult getByResultIdAndFactorId(@Param("resultId") Long resultId,
                                                   @Param("factorId") Long factorId,
                                                   @Param("showType") String showType);

    List<ScaleUserFactorResult> getListByResultId(@Param("resultId") Long resultId,
                                                  @Param("factorIds") List<Long> factorIds);

    /**
     * 根据因子id和结果id查询最终分数
     *
     * @param resultId
     * @param factorId
     * @return
     */
    BigDecimal getScoreByResultIdAndFactorId(@Param("resultId") Long resultId,
                                             @Param("factorId") Long factorId);


    List<Map<String,Object>> selectReportFormData(@Param("resultId") Long resultId,
                                            @Param("clonwnStr") String clonwnStr,
                                            @Param("factorIds") List<Long> factorIds);
}
