package com.wftk.scale.biz.ext.evaluator.engine;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;

import lombok.extern.slf4j.Slf4j;
import java.util.Map;
import java.util.Set;

import com.googlecode.aviator.FunctionLoader;
import com.googlecode.aviator.runtime.type.AviatorFunction;
import com.wftk.scale.biz.ext.evaluator.exception.FunctionNotFoundException;

import cn.hutool.core.lang.ClassScanner;
import cn.hutool.core.lang.Filter;

/**
 * 根据指定的jar包扫描并实例化函数
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Slf4j
public class JavaPackageFunctionLoader implements FunctionLoader {

    private final Map<String, AviatorFunction> functions;

    public JavaPackageFunctionLoader(Collection<String> packageNames) {
        this.functions = init(packageNames);
    }

    /**
     * 初始化函数
     * 
     * @param packageNames
     * @return
     */
    protected Map<String, AviatorFunction> init(Collection<String> packageNames) {
        Set<String> packages = new HashSet<>(packageNames);
        Map<String, AviatorFunction> functions = new HashMap<>();
        packages.forEach(packageName -> {
            Set<Class<?>> classes = ClassScanner.scanPackage(packageName, new Filter<Class<?>>() {
                @Override
                public boolean accept(Class<?> clazz) {
                    return AviatorFunction.class.isAssignableFrom(clazz) && !clazz.isInterface()
                            && !java.lang.reflect.Modifier.isAbstract(clazz.getModifiers());
                }
            });

            for (Class<?> clazz : classes) {
                try {
                    AviatorFunction function = (AviatorFunction) clazz.getDeclaredConstructor().newInstance();
                    functions.put(function.getName(), function);
                } catch (Exception e) {
                    log.error("Failed to instantiate function: " + clazz.getName(), e);
                    throw new RuntimeException("Failed to instantiate function: " + clazz.getName(), e);
                }
            }
        });
        return functions;
    }

    /**
     * 获取函数集合
     * 
     * @return
     */
    public Map<String, AviatorFunction> getFunctions() {
        return Collections.unmodifiableMap(functions);
    }

    @Override
    public AviatorFunction onFunctionNotFound(String name) {
        AviatorFunction function = this.functions.get(name);
        if (function == null) {
            throw new FunctionNotFoundException(name);
        }
        return function;
    }

}
