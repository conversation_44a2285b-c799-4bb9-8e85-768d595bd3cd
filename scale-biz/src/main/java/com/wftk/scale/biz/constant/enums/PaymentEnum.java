package com.wftk.scale.biz.constant.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @createDate 2024/11/19 17:36
 */
@Getter
public enum PaymentEnum {

    WAIT(1,"待支付"),
    PROCESSING(2,"支付中"),
    SUCCESS(3,"支付成功"),
    FAIL(4,"支付失败"),
    CANCLE(5,"取消支付");

    private Integer status;

    private String desc;

    PaymentEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
