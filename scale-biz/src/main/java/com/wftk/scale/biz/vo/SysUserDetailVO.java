package com.wftk.scale.biz.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class SysUserDetailVO {

    private Long id;

    private String account;

    private String name;

    private Long departmentId;

    private String departmentName;

    private String tel;

    private String roleIds;

    private String roleNames;

    private Boolean enable;

    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
