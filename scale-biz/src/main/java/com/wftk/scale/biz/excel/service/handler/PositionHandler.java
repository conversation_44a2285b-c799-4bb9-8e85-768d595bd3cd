package com.wftk.scale.biz.excel.service.handler;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.entity.Position;
import com.wftk.scale.biz.excel.model.PositionExcelDataDTO;
import com.wftk.scale.biz.excel.utils.ExcelUtil;
import com.wftk.scale.biz.mapper.PositionMapper;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PositionHandler extends ServiceImpl<PositionMapper, Position> {

    public void exportExcel(HttpServletResponse response, String fileName, List<PositionExcelDataDTO> list) {
        ExcelUtil.write(response, PositionExcelDataDTO.class, fileName, list);
    }
}
