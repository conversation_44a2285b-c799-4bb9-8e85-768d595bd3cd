package com.wftk.scale.biz.util;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
public class ParentPathUtils {

    public static final String SEPARATOR = "|";

    public static String buildParentPath(String parentPath, String path) {
        if (StrUtil.isEmpty(path)) {
            return parentPath.endsWith(SEPARATOR) ? parentPath : parentPath + SEPARATOR;
        }
        return parentPath.trim() + path.trim() + SEPARATOR;
    }
}
