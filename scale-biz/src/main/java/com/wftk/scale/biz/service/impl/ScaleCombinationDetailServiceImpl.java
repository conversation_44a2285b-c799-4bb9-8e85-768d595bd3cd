package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.entity.ScaleCombinationDetail;
import com.wftk.scale.biz.mapper.ScaleCombinationDetailMapper;
import com.wftk.scale.biz.service.ScaleCombinationDetailService;
import com.wftk.scale.biz.service.ScaleService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * <p>
 * 组合量表详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class ScaleCombinationDetailServiceImpl extends ServiceImpl<ScaleCombinationDetailMapper, ScaleCombinationDetail> implements ScaleCombinationDetailService {

    @Autowired
    private ScaleCombinationDetailMapper scaleCombinationDetailMapper;

    @Autowired
    private ScaleService scaleService;

    @Override
    public void copy(Long oldScaleId, Long newScaleId, String opUser) {
        List<ScaleQueryDTO> scaleIds = scaleCombinationDetailMapper.findByCombinationId(oldScaleId);
        if (CollUtil.isEmpty(scaleIds)) {
            return;
        }
        List<ScaleCombinationDetail> details = scaleIds.stream().map(item -> {
            ScaleCombinationDetail detail = new ScaleCombinationDetail();
            BeanUtils.copyProperties(item, detail);
            detail.setId(null);
            detail.setCombinationId(newScaleId);
            detail.setScaleId(item.getId());
            return detail;
        }).collect(Collectors.toList());
        this.saveBatch(details);
    }

    @Override
    public List<Long> findScaleIdByCombinationId(Long combinationId) {
        return scaleCombinationDetailMapper.findScaleIdByCombinationId(combinationId);
    }

    @Override
    public List<Long> findScaleIdByCombinationIds(List<Long> combinationIds) {
        return scaleCombinationDetailMapper.findScaleIdByCombinationIds(combinationIds);
    }

    @Override
    public void create(Long combinationId, List<ScaleCombinationDetail> scaleIds) {
        if (CollUtil.isEmpty(scaleIds)) {
            return;
        }
        List<ScaleCombinationDetail> unique = scaleIds.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                        new TreeSet<>(Comparator.comparing(ScaleCombinationDetail::getScaleId))), ArrayList::new));
        unique.forEach(detail -> {
            detail.setId(null);
            detail.setCombinationId(combinationId);
        });
        baseMapper.insert(scaleIds);
    }

    @Override
    public void modify(Long combinationId, List<ScaleCombinationDetail> scaleIds) {
        scaleCombinationDetailMapper.deleteByCombinationId(combinationId);
        this.create(combinationId, scaleIds);
    }

    @Override
    public void deleteByCombinationId(Long combinationId) {
        scaleCombinationDetailMapper.deleteByCombinationId(combinationId);
    }

    @Override
    public List<ScaleQueryDTO> findByCombinationId(Long combinationId) {
        List<ScaleQueryDTO> details = scaleCombinationDetailMapper.findByCombinationId(combinationId);
        return scaleService.buildScaleData(details);
    }
}
