package com.wftk.scale.biz.valid.date;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.scale.biz.valid.date.constant.DateValidatorConstant;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/8/25 18:49
 */
@Slf4j
public class DateUniformValidator {

    // 预定义支持的格式列表
    private static final String[] SUPPORTED_DATE_FORMATS = {
            "yyyy-MM-dd", "yyyy/MM/dd", "yyyy.MM.dd", "yyyyMMdd",
            "MM-dd-yyyy", "MM/dd/yyyy", "dd-MM-yyyy", "dd/MM/yyyy"
    };

    private static final String[] SUPPORTED_TIME_FORMATS = {
            "HH:mm", "HH:mm:ss", "HHmmss", "hh:mm a", "hh:mm:ss a"
    };


    public static String identifyDateFormat(String detectedFormat) {
        if(Arrays.asList(SUPPORTED_DATE_FORMATS).contains(detectedFormat)){
            return DateValidatorConstant.FORMART_TYPE_DATE;
        }else if(Arrays.asList(SUPPORTED_TIME_FORMATS).contains(detectedFormat)){
            return DateValidatorConstant.FORMART_TYPE_TIME;
        }
        return null;
    }

    /**
     * 校验日期列表格式统一性（增强版）
     */
    public static UniformValidationResult validateUniformDates(List<String> dateStrings) {
        if (dateStrings == null || dateStrings.isEmpty()) {
            return new UniformValidationResult(false, null, "输入列表为空");
        }

        String firstValidFormat = null;

        for (String dateStr : dateStrings) {
            if (StrUtil.isBlank(dateStr)) {
                return new UniformValidationResult(false, null, "存在空字符串");
            }

            String detectedFormat = detectFormat(dateStr, SUPPORTED_DATE_FORMATS);
            if (detectedFormat == null) {
                return new UniformValidationResult(false, null,
                        String.format("字符串 '%s' 不是有效的日期格式",dateStr));
            }

            if (firstValidFormat == null) {
                firstValidFormat = detectedFormat;
            } else if (!firstValidFormat.equals(detectedFormat)) {
                return new UniformValidationResult(false, null,
                        String.format("格式不统一: 期望 %s, 但发现 %s",firstValidFormat,detectedFormat) );
            }
        }

        return new UniformValidationResult(true, firstValidFormat, "所有日期格式统一");
    }

    /**
     * 校验时间列表格式统一性（增强版）
     */
    public static UniformValidationResult validateUniformTimes(List<String> timeStrings) {
        if (timeStrings == null || timeStrings.isEmpty()) {
            return new UniformValidationResult(false, null, "输入列表为空");
        }

        String firstValidFormat = null;

        for (String timeStr : timeStrings) {
            if (StrUtil.isBlank(timeStr)) {
                return new UniformValidationResult(false, null, "存在空字符串");
            }

            String detectedFormat = detectFormat(timeStr, SUPPORTED_TIME_FORMATS);
            if (detectedFormat == null) {
                return new UniformValidationResult(false, null,
                        String.format("字符串 '%s' 不是有效的时间格式",timeStr));
            }

            if (firstValidFormat == null) {
                firstValidFormat = detectedFormat;
            } else if (!firstValidFormat.equals(detectedFormat)) {
                return new UniformValidationResult(false, null,
                        String.format("格式不统一: 期望 %s, 但发现 %s",firstValidFormat,detectedFormat));
            }
        }

        return new UniformValidationResult(true, firstValidFormat, "所有时间格式统一");
    }

    /**
     * 检测字符串匹配的格式
     */
    private static String detectFormat(String value, String[] supportedFormats) {
        for (String format : supportedFormats) {
            try {
                DateUtil.parse(value, format);
                return format; // 解析成功，返回该格式
            } catch (Exception e) {
                // 解析失败，继续尝试下一个格式
                log.debug("date str convert fail. str: {}.",value,e);
            }
        }
        return null; // 没有匹配的格式
    }



}
