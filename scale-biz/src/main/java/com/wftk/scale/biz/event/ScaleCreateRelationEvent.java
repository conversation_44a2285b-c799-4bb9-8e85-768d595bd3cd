package com.wftk.scale.biz.event;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class ScaleCreateRelationEvent extends ApplicationEvent {

    private Long newScaleId;

    private Long oldScaleId;

    private String opUser;

    public ScaleCreateRelationEvent(Long newScaleId, Long oldScaleId, String opUser) {
        super(newScaleId);
        this.newScaleId = newScaleId;
        this.oldScaleId = oldScaleId;
        this.opUser = opUser;
    }
}
