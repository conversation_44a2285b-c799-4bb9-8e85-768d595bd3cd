package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.OrderPayment;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface OrderPaymentMapper extends BaseMapper<OrderPayment> {

    Integer updateStatus(@Param("tradeNo")String tradeNo, @Param("status")Integer status,@Param("oldStatus") Integer oldStatus);

}
