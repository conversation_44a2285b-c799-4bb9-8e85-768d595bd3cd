package com.wftk.scale.biz.ext.evaluator.function.scale.question;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;
import com.wftk.scale.biz.ext.evaluator.function.scale.BaseScaleEvaluationFunction;

import cn.hutool.core.util.StrUtil;

/**
 * 过滤掉量中不能参与计算的题目
 * 示例: q_computable("1,2,3"), 表示过滤掉量表题目中分数为null的题目,返回的量表题目均分数不为null
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
public class QuestionScoreNoneNullFilterFunction extends BaseScaleEvaluationFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Question.Q_SCORE_NONE_NULL;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env, final AviatorObject arg1) {
        String questionIds = (String) arg1.getValue(env);
        logger.info("q_computable: questionIds: {}", questionIds);
        if (StrUtil.isBlank(questionIds)) {
            throw new IllegalArgumentException("questionIds must not be blank");
        }
        List<ScaleUserResultRecord> resultItemDetailByQuestionIds = getUserResultOptions(env, questionIds, false);

        return new AviatorString(resultItemDetailByQuestionIds.stream()
                .map(ScaleUserResultRecord::getId)
                .map(String::valueOf)
                .collect(Collectors.joining(",")));
    }

}
