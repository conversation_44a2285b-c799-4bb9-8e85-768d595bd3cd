package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.dto.user.menu.SysCreateMenuDTO;
import com.wftk.scale.biz.dto.user.menu.SysUpdateMenuDTO;
import com.wftk.scale.biz.entity.SysMenu;
import com.wftk.scale.biz.vo.SysMenuVO;
import com.wftk.scale.biz.vo.SysRoleMenuVO;

import java.util.List;

/**
 * <p>
 * 菜单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface SysMenuService extends IService<SysMenu> {

    List<SysMenuVO> getList();

    List<SysMenuVO> getEditSelectParentList(Long id);

    List<SysMenuVO> getOwnerMenus();

    void createMenu(SysCreateMenuDTO dto);

    void updateMenu(SysUpdateMenuDTO dto);

    void deleteMenu(Long menuId);

    List<SysRoleMenuVO> selectMenusByRoleId(Long roleId);
}