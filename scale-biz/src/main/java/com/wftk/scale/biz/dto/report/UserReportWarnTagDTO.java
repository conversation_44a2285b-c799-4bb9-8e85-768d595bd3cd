package com.wftk.scale.biz.dto.report;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/12/5 19:37
 */
@Data
public class UserReportWarnTagDTO {

    /**
     * 分数
     */
    private BigDecimal score;

    /**
     * 预警标签
     */
    private String warnTag;

    /**
     * 参考范围
     */
    private String range;

    /**
     * 总分范围
     */
    private String totalRange;

    /**
     * 总分图形统计
     */
    private List<UserReportWarnTagStatisticalDTO> warnTagStatistical;

    /**
     * 首部标签
     */
    private String startWarn;

    /**
     * 尾部标签
     */
    private String endWarn;
}
