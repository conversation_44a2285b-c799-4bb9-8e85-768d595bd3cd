//package com.wftk.scale.biz.service.impl;
//
//import cn.hutool.core.collection.CollUtil;
//import com.wftk.scale.biz.ext.notice.dto.SmsDTO;
//import com.wftk.scale.biz.ext.notice.enums.NoticeTypeEnum;
//import com.wftk.scale.biz.service.NoticeService;
//import com.wftk.scale.biz.service.UserService;
//import com.wftk.sms.client.spring.boot.autoconfigure.core.client.SmsClient;
//import com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.request.AliyunSendSmsRequest;
//import com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.response.AliyunSendSmsResponse;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.util.Assert;
//
//import java.util.Arrays;
//import java.util.Set;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// */
//@Slf4j
//@Service
//public class SmsNoticeServiceImpl implements NoticeService<SmsDTO> {
//
//    @Resource
//    private SmsClient smsClient;
//    @Resource
//    private UserService userService;
//
//    @Override
//    public NoticeTypeEnum getNoticeType() {
//        return NoticeTypeEnum.SMS;
//    }
//
//    @Override
//    public void sendNotice(SmsDTO sms) {
//        Assert.hasLength(sms.getScene(), "场景不能为空");
//        Assert.hasLength(sms.getUserIds(), "接收用户ID不能为空");
//        Assert.notNull(sms.getParams(), "场景不能为空");
//        Set<Long> ids = Arrays.stream(sms.getUserIds().split(",")).map(Long::parseLong).collect(Collectors.toSet());
//        Set<String> phoneList = userService.getPhoneListByUserIds(ids);
//        if(CollUtil.isNotEmpty(phoneList)){
//            AliyunSendSmsRequest request = new AliyunSendSmsRequest();
//            request.setTels(phoneList);
//            request.setTemplateParams(sms.getParams());
//            request.setScene(sms.getScene());
//            AliyunSendSmsResponse smsResponse = smsClient.sendSms(request);
//            if(smsResponse.isFail()){
//                log.error("发送失败:{}", smsResponse.getMessage());
//            }
//        }
//
//    }
//}
