//package com.wftk.scale.biz.ext.notice.sms;
//
//import com.wftk.scale.biz.ext.notice.enums.NoticeEventEnum;
//import lombok.Getter;
//
//import java.util.Arrays;
//import java.util.Objects;
//
///**
// * 短信通知场景
// */
//@Getter
//public enum SmsNoticeEventSceneEnum {
//
//    SCALE_WARNING_SCENE(NoticeEventEnum.SCALE_WARNING, "scale_warning");// 考虑变量Map也放这儿
//
//    private final NoticeEventEnum noticeEventEnum;
//    private final String smsScene;// 短息模板
//
//    SmsNoticeEventSceneEnum(NoticeEventEnum noticeEventEnum, String smsScene) {
//        this.noticeEventEnum = noticeEventEnum;
//        this.smsScene = smsScene;
//    }
//
//    public static String getSmsSceneByNoticeEventEnum(NoticeEventEnum noticeEventEnum){
//        return Objects.requireNonNull(Arrays.stream(SmsNoticeEventSceneEnum.values()).filter(item -> item.getNoticeEventEnum().equals(noticeEventEnum)).findFirst().orElse(null)).getSmsScene();
//    }
//
//
//}
