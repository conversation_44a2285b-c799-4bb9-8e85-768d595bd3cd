package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleWarnDetailDTO;
import com.wftk.scale.biz.entity.*;
import com.wftk.scale.biz.vo.ScaleWarnVO;
import com.wftk.scale.biz.vo.UserVO;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
public interface ScaleWarnDetailService extends IService<ScaleWarn> {

    Page<ScaleWarnVO> queryList(String account, String userName, Long departmentId, String phone, String scaleName, String terminalCode);

    ScaleWarnVO detail(Long id);

    void deleteById(Long id);

    ScaleWarn saveByWarnNotice(ScaleListing scaleListing, UserVO userVO, Scale scale, ScaleUserResult scaleUserResult,
                          String tagName, int noticeType, ScaleFactor scaleFactor, String terminalName, ScaleWarnSetting scaleWarnSetting, Long receivingWarnUserId);

    void export(HttpServletResponse response, ScaleWarnDetailDTO dto);

}
