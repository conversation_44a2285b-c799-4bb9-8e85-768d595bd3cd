package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.entity.SystemTag;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface SystemTagService extends IService<SystemTag> {

    /*
     * @Author: mq
     * @Description: 校验预警标签编号是否已经存在
     * @Date: 2024/10/25 14:27
     * @Param: tagId-标签ID
     * @Param: tagName-标签名称
     * @return: boolean
     **/
    boolean validSystemTagCode(Long tagId, String tagName);

    /*
     * @Author: mq
     * @Description: 创建预警标签信息
     * @Date: 2024/10/25 14:28
     * @Param: systemTag
     * @return: void
     **/
    void create(SystemTag systemTag);

    /* 
     * @Author: mq
     * @Description: 根据ID删除预警标签信息 
     * @Date: 2024/10/25 14:29 
     * @Param: tagId-标签ID
     * @return: void 
     **/
    void delete(Long tagId);

    /* 
     * @Author: mq
     * @Description: 创建修改签信息 
     * @Date: 2024/10/25 14:29 
     * @Param: systemTag  
     * @return: void 
     **/
    void modify(SystemTag systemTag);

    /*
     * @Author: mq
     * @Description: 根据ID更新预警设置数据的状态
     * @Date: 2024/10/25 14:53
     * @Param: tagId-主键ID
     * @Param: enable-启用状态(false 未开启 true 已开启)
     * @return: void
     **/
    void updateEnable(Long tagId, Boolean enable);

    /* 
     * @Author: mq
     * @Description: 根据条件检索预警标签信息
     * @Date: 2024/10/25 14:30 
     * @Param: tagName-标签名称
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.entity.SystemTag> 
     **/
    Page<SystemTag> selectPage(String tagName);

    /*
    * @Author: mq
    * @Description: 获取可用的预警标签列表
    * @Date: 2024/11/26 13:52
    * @Param: tagName-标签名称
    * @return: List<SystemTag>
    **/
    List<SystemTag> getListOfEnabled(String tagName);

    void export(HttpServletResponse response, String tagName) throws Exception;
}
