package com.wftk.scale.biz.mapper;

import com.wftk.scale.biz.entity.ScaleReportConfItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 报告设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04 14:29:16
 */
public interface ScaleReportConfItemMapper extends BaseMapper<ScaleReportConfItem> {

    List<ScaleReportConfItem> findByReportConfId(@Param("reportConfId") Long reportConfId,@Param("itemCodes")List<String> itemCodes);

    List<ScaleReportConfItem> findByReportConfIdAndFactorId(@Param("reportConfId") Long reportConfId,
                                                            @Param("itemCodes")List<String> itemCodes,
                                                            @Param("factorId")Long factorId);

    Integer updateDeletedById(@Param("id")Long id);

    ScaleReportConfItem getById(@Param("id")Long id);

}
