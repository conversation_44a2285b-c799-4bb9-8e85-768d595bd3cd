package com.wftk.scale.biz.service;

import com.wftk.scale.biz.constant.enums.OrderNotifyTypeEnum;
import com.wftk.scale.biz.dto.httpjob.HttpResult;
import com.wftk.scale.biz.dto.notify.EvaluationRecordNotifyInput;
import com.wftk.scale.biz.dto.notify.OrderStautsNotifyInput;
import com.wftk.scale.biz.dto.notify.UserReportNotifyInput;

import java.util.Map;


/**
 * <AUTHOR>
 * @create 2023/10/18 14:36
 */
public interface NotifyService {

    /**
     * @param userReportNotifyInput
     * @param terminalCode
     * @return
     */
    HttpResult userReportNotify(UserReportNotifyInput userReportNotifyInput,String terminalCode);

    /**
     * @param evaluationRecordNotifyInput
     * @return
     */
    HttpResult evaluationRecordNotify(EvaluationRecordNotifyInput evaluationRecordNotifyInput,String terminalCode);

    /**
     * @param orderStautsNotifyInput
     * @param terminalCode
     * @return
     */
    HttpResult orderStautsNotify(OrderStautsNotifyInput orderStautsNotifyInput,String terminalCode);

    /**
     * 通知
     *
     * @param terminalCode
     * @param notifyType
     * @param dataMap
     * @return
     */
    HttpResult notify(String terminalCode, OrderNotifyTypeEnum notifyType, Map<String,Object> dataMap);

}
