package com.wftk.scale.biz.ext.wechat.core;

import cn.hutool.core.util.StrUtil;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.core.util.GsonUtil;
import com.wechat.pay.java.service.payments.app.AppServiceExtension;
import com.wechat.pay.java.service.payments.h5.H5Service;
import com.wechat.pay.java.service.payments.h5.model.PrepayResponse;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.model.QueryOrderByOutTradeNoRequest;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.*;
import com.wftk.scale.biz.ext.wechat.exception.WechatOrderNotExistException;
import com.wftk.scale.biz.ext.wechat.exception.WechatQueryException;
import com.wftk.scale.biz.ext.wechat.input.WechatPrepayInput;
import com.wftk.scale.biz.ext.wechat.input.WechatRefundInput;
import com.wftk.scale.biz.ext.wechat.output.WechatPaymentNotifyOutput;
import com.wftk.scale.biz.ext.wechat.output.WechatRefundOutput;
import com.wftk.scale.biz.ext.wechat.util.DateTimeUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @create 2023/10/12 21:10
 */
public class DefaultWechatClient implements WechatClient {

    private final H5Service h5Service;
    private final JsapiServiceExtension jsapiService;
    private final NativePayService nativePayService;
    private final RefundService refundService;
    private final NotificationParser notificationParser;


    public DefaultWechatClient(Config config, NotificationConfig notificationConfig) {
        this.h5Service = new H5Service.Builder().config(config).build();
        this.jsapiService = new JsapiServiceExtension.Builder().config(config).build();
        this.nativePayService = new NativePayService.Builder().config(config).build();
        this.refundService = new RefundService.Builder().config(config).build();
        this.notificationParser = new NotificationParser(notificationConfig);
    }


    @Override
    public String weappPrepay(WechatPrepayInput prepayInput) {
        com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest prepayRequest = new com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest();
        prepayRequest.setAppid(prepayInput.getAppId());
        prepayRequest.setMchid(prepayInput.getMchId());
        prepayRequest.setDescription(prepayInput.getSubject());
        prepayRequest.setOutTradeNo(prepayInput.getOrderNo());
        prepayRequest.setNotifyUrl(prepayInput.getNotifyUrl());
        com.wechat.pay.java.service.payments.jsapi.model.Amount amount = new com.wechat.pay.java.service.payments.jsapi.model.Amount();
        amount.setTotal(prepayInput.getAmount());
        prepayRequest.setAmount(amount);
        com.wechat.pay.java.service.payments.jsapi.model.Payer payer = new com.wechat.pay.java.service.payments.jsapi.model.Payer();
        payer.setOpenid(prepayInput.getOpenId());
        prepayRequest.setPayer(payer);
        if (prepayInput.getExpireAt() != null) {
            prepayRequest.setTimeExpire(DateTimeUtil.rfc3339Format(prepayInput.getExpireAt()));
        }
        com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse prepayWithRequestPaymentResponse =
                jsapiService.prepayWithRequestPayment(prepayRequest);
        return GsonUtil.toJson(prepayWithRequestPaymentResponse);
    }

    @Override
    public String h5Prepay(WechatPrepayInput prepayInput) {
        // 实现H5支付
        com.wechat.pay.java.service.payments.h5.model.PrepayRequest prepayRequest = new com.wechat.pay.java.service.payments.h5.model.PrepayRequest();
        prepayRequest.setAppid(prepayInput.getAppId());
        prepayRequest.setMchid(prepayInput.getMchId());
        prepayRequest.setDescription(prepayInput.getSubject());
        prepayRequest.setOutTradeNo(prepayInput.getOrderNo());
        prepayRequest.setNotifyUrl(prepayInput.getNotifyUrl());
        com.wechat.pay.java.service.payments.h5.model.Amount amount = new com.wechat.pay.java.service.payments.h5.model.Amount();
        amount.setTotal(prepayInput.getAmount());
        prepayRequest.setAmount(amount);
        if (prepayInput.getExpireAt() != null) {
            prepayRequest.setTimeExpire(DateTimeUtil.rfc3339Format(prepayInput.getExpireAt()));
        }
        com.wechat.pay.java.service.payments.h5.model.SceneInfo sceneInfo = new com.wechat.pay.java.service.payments.h5.model.SceneInfo();
        sceneInfo.setPayerClientIp(prepayInput.getIp());
        com.wechat.pay.java.service.payments.h5.model.H5Info h5Info = new com.wechat.pay.java.service.payments.h5.model.H5Info();
        h5Info.setType(prepayInput.getType());
        sceneInfo.setH5Info(h5Info);
        prepayRequest.setSceneInfo(sceneInfo);
        com.wechat.pay.java.service.payments.h5.model.PrepayResponse prepay = h5Service.prepay(prepayRequest);
        return  GsonUtil.toJson(prepay);
    }

    @Override
    public String nativePrepay(WechatPrepayInput prepayInput) {
        com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest prepayRequest = new com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest();
        prepayRequest.setAppid(prepayInput.getAppId());
        prepayRequest.setMchid(prepayInput.getMchId());
        prepayRequest.setDescription(prepayInput.getSubject());
        prepayRequest.setOutTradeNo(prepayInput.getOrderNo());
        prepayRequest.setNotifyUrl(prepayInput.getNotifyUrl());
        com.wechat.pay.java.service.payments.nativepay.model.Amount amount = new com.wechat.pay.java.service.payments.nativepay.model.Amount();
        amount.setTotal(prepayInput.getAmount());
        prepayRequest.setAmount(amount);
        if (prepayInput.getExpireAt() != null) {
            prepayRequest.setTimeExpire(DateTimeUtil.rfc3339Format(prepayInput.getExpireAt()));
        }
        com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse prepayResponse = nativePayService.prepay(prepayRequest);
        return GsonUtil.toJson(prepayResponse);
    }


    @Override
    public WechatPaymentNotifyOutput h5PaymentNotify(HttpServletRequest httpServletRequest) {
        return paymentNotify(httpServletRequest);
    }

    @Override
    public WechatRefundOutput refund(WechatRefundInput wechatRefundInput) {
        CreateRequest createRequest = new CreateRequest();
        createRequest.setTransactionId(wechatRefundInput.getTransactionId());
        createRequest.setOutTradeNo(wechatRefundInput.getOrderNo());
        createRequest.setOutRefundNo(wechatRefundInput.getOutRefundNo());
        createRequest.setReason(wechatRefundInput.getReason());
        createRequest.setNotifyUrl(wechatRefundInput.getNotifyUrl());
        AmountReq amountReq = new AmountReq();
        amountReq.setTotal(Long.valueOf(wechatRefundInput.getTotal()));
        amountReq.setRefund(Long.valueOf(wechatRefundInput.getAmount()));
        amountReq.setCurrency(wechatRefundInput.getCurrency());
        createRequest.setAmount(amountReq);
        Refund refund = refundService.create(createRequest);

        WechatRefundOutput refundOutput = new WechatRefundOutput();
        refundOutput.setRefundId(refund.getRefundId());
        if (refund.getStatus() != null) {
            refundOutput.setStatus(refund.getStatus().name());
        }
        if (refund.getChannel() != null) {
            refundOutput.setChannel(refund.getChannel().name());
        }
        refundOutput.setOutRefundNo(refund.getOutRefundNo());
        refundOutput.setOrderNo(refund.getOutTradeNo());
        if (StrUtil.isNotBlank(refund.getSuccessTime())) {
            refundOutput.setSuccessTime(LocalDateTime.parse(refund.getSuccessTime(), DateTimeFormatter.ISO_DATE_TIME));
        }
        refundOutput.setUserReceivedAccount(refund.getUserReceivedAccount());
        refundOutput.setAmount(refund.getAmount().getRefund().intValue());
        refundOutput.setTransactionId(refund.getTransactionId());
        return refundOutput;
    }

    @Override
    public WechatRefundOutput refundNotify(HttpServletRequest httpServletRequest) {
        RequestParam requestParam = getRequestParam(httpServletRequest);
        RefundNotification refund = notificationParser.parse(requestParam, RefundNotification.class);
        WechatRefundOutput refundOutput = new WechatRefundOutput();
        refundOutput.setRefundId(refund.getRefundId());
        refundOutput.setStatus(refund.getRefundStatus().name());
        refundOutput.setOutRefundNo(refund.getOutRefundNo());
        refundOutput.setOrderNo(refund.getOutTradeNo());
        refundOutput.setSuccessTime(LocalDateTime.parse(refund.getSuccessTime(), DateTimeFormatter.ISO_DATE_TIME));
        refundOutput.setUserReceivedAccount(refund.getUserReceivedAccount());
        refundOutput.setAmount(refund.getAmount().getRefund().intValue());
        refundOutput.setTransactionId(refund.getTransactionId());
        return refundOutput;
    }

    @Override
    public WechatPaymentNotifyOutput queryPaymentInfo(String orderNo, String mchId) {
        //微信订单查询接口是一个，此处不做区分
        QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
        request.setMchid(mchId);
        request.setOutTradeNo(orderNo);
        try {
            Transaction transaction = jsapiService.queryOrderByOutTradeNo(request);
            return parseTransaction(transaction);
        } catch (ServiceException e) {
            if (e.getHttpStatusCode() == 404) {
                throw new WechatOrderNotExistException(e);
            }
            throw new WechatQueryException(e);
        }

    }

    @Override
    public WechatRefundOutput queryRefundInfo(String refundNo) {
        QueryByOutRefundNoRequest queryByOutRefundNoRequest = new QueryByOutRefundNoRequest();
        queryByOutRefundNoRequest.setOutRefundNo(refundNo);
        Refund refund = refundService.queryByOutRefundNo(queryByOutRefundNoRequest);
        WechatRefundOutput refundOutput = new WechatRefundOutput();
        refundOutput.setRefundId(refund.getRefundId());
        refundOutput.setStatus(refund.getStatus().name());
        refundOutput.setOutRefundNo(refund.getOutRefundNo());
        refundOutput.setOrderNo(refund.getOutTradeNo());
        refundOutput.setSuccessTime(LocalDateTime.parse(refund.getSuccessTime(), DateTimeFormatter.ISO_DATE_TIME));
        refundOutput.setUserReceivedAccount(refund.getUserReceivedAccount());
        refundOutput.setAmount(refund.getAmount().getRefund().intValue());
        refundOutput.setTransactionId(refund.getTransactionId());
        return refundOutput;
    }


    /**
     *
     * @param httpServletRequest
     * @return
     */
    private RequestParam getRequestParam(HttpServletRequest httpServletRequest) {
        String jsonBody;
        try (InputStream in = httpServletRequest.getInputStream()) {
            jsonBody = StreamUtils.copyToString(in, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        //随机串
        String nonceStr = httpServletRequest.getHeader("Wechatpay-Nonce");
        //微信传递过来的签名
        String signature = httpServletRequest.getHeader("Wechatpay-Signature");
        //证书序列号（微信平台）
        String serialNo = httpServletRequest.getHeader("Wechatpay-Serial");
        //时间戳
        String timestamp = httpServletRequest.getHeader("Wechatpay-Timestamp");
        return new RequestParam.Builder()
                .serialNumber(serialNo)
                .nonce(nonceStr)
                .signature(signature)
                .timestamp(timestamp)
                .body(jsonBody)
                .build();
    }


    /**
     *
     * @param httpServletRequest
     * @return
     */
    private WechatPaymentNotifyOutput paymentNotify(HttpServletRequest httpServletRequest) {
        RequestParam requestParam = getRequestParam(httpServletRequest);
        Transaction transaction = notificationParser.parse(requestParam, Transaction.class);
        return parseTransaction(transaction);
    }


    /**
     *
     * @param transaction
     * @return
     */
    private WechatPaymentNotifyOutput parseTransaction(Transaction transaction) {
        WechatPaymentNotifyOutput output = new WechatPaymentNotifyOutput();
        if (transaction.getSuccessTime() != null) {
            output.setSuccessTime(LocalDateTime.parse(transaction.getSuccessTime(), DateTimeFormatter.ISO_DATE_TIME));
        }
        output.setTradeState(transaction.getTradeState().name());
        output.setOutTradeNo(transaction.getOutTradeNo());
        output.setTransactionId(transaction.getTransactionId());
        return output;
    }


}
