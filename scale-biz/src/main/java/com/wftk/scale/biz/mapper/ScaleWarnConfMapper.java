package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.scale.ScaleWarnConfQueryDTO;
import com.wftk.scale.biz.entity.ScaleWarnConf;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 预警阈值表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleWarnConfMapper extends BaseMapper<ScaleWarnConf> {


    /*
     * @Author: mq
     * @Description:校验量表预警因子阈值信息是否已经存在
     * @Date: 2024/11/5 16:54
     * @Param: id
     * @Param: scaleId
     * @Param: factorId
     * @return: boolean
     **/
    boolean validWarnConFactorExists(@Param("id") Long id, @Param("scaleId") Long scaleId, @Param("factorId") Long factorId);

    /*
     * @Author: mq
     * @Description:校验量表预警因子阈值信息是否已经存在
     * @Date: 2024/11/5 16:54
     * @Param: id
     * @Param: scaleId
     * @Param: factorId
     * @return: boolean
     **/
    boolean validWarnConfFactorTagExists(@Param("scaleId") Long scaleId, @Param("factorId") Long factorId,@Param("tagId") Long tagId,@Param("batchNo")String batchNo);

    /*
     * @Author: mq
     * @Description: 根据条件检索量表预警阈值信息
     * @Date: 2024/11/5 16:48
     * @Param: scaleId-量表ID
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleWarnConf>
     **/
    List<ScaleWarnConfQueryDTO> getList(@Param("scaleId") Long scaleId);

    /*
    * @Author: mq
    * @Description: 校验因子维度信息关联的预警阈值信息
    * @Date: 2024/12/12 15:22
    * @Param: factorId-因子维度ID
    * @return: String
    **/
    String checkFactorRelationWarnConf(@Param("factorId") Long factorId);

    List<ScaleWarnConfQueryDTO> getFactorList(@Param("scaleId") Long scaleId,@Param("factorId")Long factorId);

    Integer deleteByBatchNo(@Param("batchNo") String batchNo);

    List<ScaleWarnConfQueryDTO> getListByBatchNo(@Param("batchNo") String batchNo);

    List<ScaleWarnConfQueryDTO> getByIds(@Param("scaleId")Long scaleId,@Param("ids") List<String> ids);
}
