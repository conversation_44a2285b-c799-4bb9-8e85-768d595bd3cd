package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22 14:48:13
 */
@TableName("`order`")
@Getter
@Setter
public class Order implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 终端业务流水号
     */
    private String terminalSerialNo;

    /**
     * 类型: 1.量表; 2.组合量表;
     */
    private Integer type;

    /**
     * 可能是量表ID，也可能是组合量表ID
     */
    private Long targetId;

    /**
     * 量表名称 冗余字段，避免大表关联查询
     */
    private String targetName;

    /**
     * 上架id
     */
    private Long scaleListingId;

    /**
     * 用户id。来自user的主键
     */
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 终端ID
     */
    private String terminalCode;

    /**
     * 机构ID
     */
    private Long departmentId;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 订单金额,单位是分
     */
    private Integer amount;

    /**
     * 来源，1自己购买2他人推荐3批量分发
     */
    private Integer source;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 1支付宝2微信3现金4他人代付5.终端线下结算
     */
    private Integer payChannel;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 原价金额（分）
     */
    private Integer originalAmount;

    /**
     * 过期时间
     */
    private LocalDateTime expirTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    @Override
    public String toString() {
        return "Order{" +
                "id=" + id +
                ", orderNo='" + orderNo + '\'' +
                ", terminalSerialNo='" + terminalSerialNo + '\'' +
                ", type=" + type +
                ", targetId=" + targetId +
                ", targetName='" + targetName + '\'' +
                ", scaleListingId=" + scaleListingId +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", userAccount='" + userAccount + '\'' +
                ", terminalCode='" + terminalCode + '\'' +
                ", departmentId=" + departmentId +
                ", phone='" + phone + '\'' +
                ", amount=" + amount +
                ", source=" + source +
                ", status=" + status +
                ", payChannel=" + payChannel +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleted=" + deleted +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", originalAmount=" + originalAmount +
                ", expirTime=" + expirTime +
                ", completeTime=" + completeTime +
                '}';
    }
}
