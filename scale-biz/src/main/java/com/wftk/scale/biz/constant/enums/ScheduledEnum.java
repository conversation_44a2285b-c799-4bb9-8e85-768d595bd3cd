package com.wftk.scale.biz.constant.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/3/10 21:58
 */
@Getter
public enum ScheduledEnum {


    /**
     * 梯度配置
     */
    one(1,30),
    two(2,60),
    three(3,5*60),
    four(4,10*60),
    five(5,30*60);


    private final int count;
    private final int second;

    ScheduledEnum(int count, int second) {
        this.count = count;
        this.second = second;
    }

    public static int getSecond(int count) {
        for (ScheduledEnum value : values()) {
            if(value.count == count){
                return value.second;
            }
        }
        return 30;
    }
}
