package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.SysMenu;
import com.wftk.scale.biz.vo.SysRoleMenuVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 菜单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface SysMenuMapper extends BaseMapper<SysMenu> {

    List<SysMenu> selectChildrenByMenuId(@Param("menuId") Long menuId);

    List<SysRoleMenuVO> selectRoleMenuByRoleId(@Param("roleId") Long roleId);

    List<SysMenu> selectMenuByUserId(@Param("userId") Long userId);
}
