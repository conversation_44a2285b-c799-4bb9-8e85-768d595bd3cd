package com.wftk.scale.biz.event.publisher;

import com.wftk.scale.biz.event.SysMenuDeleteEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createDate 2024/12/4 17:27
 */
@Component
public class SysMenuDeletePublisher implements BaseEventPublisher<SysMenuDeleteEvent> {

    private final ApplicationEventPublisher applicationEventPublisher;

    public SysMenuDeletePublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Override
    public void publishEvent(SysMenuDeleteEvent sysMenuDeleteEvent) {
        applicationEventPublisher.publishEvent(sysMenuDeleteEvent);
    }
}
