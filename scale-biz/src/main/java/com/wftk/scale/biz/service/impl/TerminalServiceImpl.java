package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.DictCodeEnum;
import com.wftk.scale.biz.dto.dict.DictDTO;
import com.wftk.scale.biz.dto.scale.TerminalDTO;
import com.wftk.scale.biz.entity.DepartmentTerminal;
import com.wftk.scale.biz.entity.Terminal;
import com.wftk.scale.biz.mapper.TerminalMapper;
import com.wftk.scale.biz.service.DepartmentTerminalService;
import com.wftk.scale.biz.service.DictService;
import com.wftk.scale.biz.service.TerminalService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 终端信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class TerminalServiceImpl extends ServiceImpl<TerminalMapper, Terminal> implements TerminalService {

    @Autowired
    private TerminalMapper terminalMapper;
    @Resource
    private DepartmentTerminalService departmentTerminalService;

    @Autowired
    private DictService dictService;

    @Override
    public boolean vaildTerminalCode(Long terminalId, String terminalCode) {
        boolean checkResult = false;
        if (StrUtil.isEmpty(terminalCode)) {
            return checkResult;
        }
        checkResult = terminalMapper.vaildTerminalCodeExists(terminalId, terminalCode);
        return checkResult;
    }

    @Override
    public Terminal getByCode(String terminalCode) {
        return terminalMapper.getByCode(terminalCode);
    }

    @Override
    public void create(Terminal terminal) {
        String code = IdUtil.simpleUUID();
        boolean result = vaildTerminalCode(null, code);
        if (result) {
            throw new BusinessException("创建失败,终端信息已存在!,请重试");
        }
        terminal.setCode(code);
        terminalMapper.insert(terminal);
    }

    @Override
    public void modify(Terminal terminal) {
        Terminal rawData = terminalMapper.selectById(terminal.getId());
        BeanUtils.copyProperties(terminal, rawData);
        terminalMapper.updateById(rawData);
    }

    @Override
    public void delete(Long terminalId) {
        //校验终端是否被机构绑定
        Terminal terminal = terminalMapper.selectById(terminalId);
        if(terminal==null){
            throw new BusinessException("删除机构不存在!");
        }
        boolean exists = departmentTerminalService.exists(new LambdaQueryWrapper<DepartmentTerminal>()
                .eq(DepartmentTerminal::getTerminalCode, terminal.getCode()));
        if(exists){
            throw new BusinessException("该终端已绑定机构，不允许删除!");
        }
        terminalMapper.deleteById(terminalId);
    }

    @Override
    public void updateEnable(Long terminalId, Boolean enable) {
        String opUser = AuthenticationHolder.getAuthentication().getAuthUser().getAccount();
        terminalMapper.updateEnable(terminalId, enable, opUser);
    }


    @Override
    public Page<TerminalDTO> selectPage(String terminalName) {
        Page<TerminalDTO> page = Page.doSelectPage(() -> terminalMapper.getList(terminalName));
        Map<String, String> labelToValueMap = getLabelToValueMap();
        page.setList(converTypeToTypeName(page.getList(),labelToValueMap));
        return page;
    }

    @Override
    public List<TerminalDTO> getListOfEnabled(String terminalName) {
        List<TerminalDTO> dtoList =terminalMapper.getList(terminalName);
        Map<String, String> labelToValueMap = getLabelToValueMap();
        return converTypeToTypeName(dtoList,labelToValueMap);
    }

    @Override
    public List<TerminalDTO> getNotBindingDepartmentList() {
        List<TerminalDTO> dtoList =terminalMapper.getNotBindingDepartmentList();
        Map<String, String> labelToValueMap = getLabelToValueMap();
        return converTypeToTypeName(dtoList,labelToValueMap);
    }

    public Map<String, String> getLabelToValueMap() {
        List<DictDTO> dictDTOList = dictService.findByCode(DictCodeEnum.PATHTYPE.getCode());
        return dictDTOList.stream().collect(Collectors.toMap(
                DictDTO::getDictValue,
                DictDTO::getDictLabel,
                (oldValue, newValue) -> newValue
        ));
    }

    public List<TerminalDTO> converTypeToTypeName(List<TerminalDTO> list, Map<String, String> labelToValueMap){
        list.forEach(terminal -> {
            String typeName = "未知类型";
            if (ObjectUtil.isNotEmpty(terminal.getType())) {
                String key = terminal.getType().toString();
                typeName = labelToValueMap.getOrDefault(key, "未知类型");
            }
            terminal.setTypeName(typeName);
        });
        return list;
    }
}
