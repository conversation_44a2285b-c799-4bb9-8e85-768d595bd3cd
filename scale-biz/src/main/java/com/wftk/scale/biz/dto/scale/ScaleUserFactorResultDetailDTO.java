package com.wftk.scale.biz.dto.scale;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName: ScaleUserFactorResultDetailDTO
 * @Description: 用户测评因子得分详情
 * @Author: mq
 * @Date: 2024/11/25
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleUserFactorResultDetailDTO implements Serializable {

    /**
     * 因子id，来自scale_factor的主键
     */
    private Long factorId;

    /**
     * 因子名称
     */
    private String factorName;

    /**
     * 因子得分
     */
    private BigDecimal score;

    /**
     * 结果解读
     */
    private String intro;
}
