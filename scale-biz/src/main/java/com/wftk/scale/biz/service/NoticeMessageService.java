package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.entity.NoticeMessage;
import com.wftk.scale.biz.entity.ScaleListingUserRecord;
import com.wftk.scale.biz.entity.ScaleWarn;
import com.wftk.scale.biz.ext.notice.NoticeResult;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03 14:10:07
 */
public interface NoticeMessageService extends IService<NoticeMessage> {

    void updateNextCountTime(NoticeMessage noticeMessage);

    List<NoticeMessage> getNeedSendNoticeMessage(Integer maxNoticeCount);

    /**
     * 发送消息预警
     * @param noticeMessage
     * @return
     */
    NoticeResult sendNoticeByMessage(NoticeMessage noticeMessage);

    /**
     * 发送量表预警信息
     * @param scaleWarn
     * @return
     */
    NoticeResult sendScaleWarnNoticeByMessage(ScaleWarn scaleWarn);

    /**
     * 发送量表分发短信
     * @param scaleListingUserRecord
     * @return
     */
    NoticeResult sendScaleListingUserRecordSMSMessage(ScaleListingUserRecord scaleListingUserRecord);

}
