package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 租户下的client信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27 15:56:59
 */
@Data
@TableName("tenant_client_info")
public class TenantClientInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    private String tenantId;

    private String clientId;

    private String clientSecret;

    private Boolean enable;

    /**
     * 简介
     */
    private String description;

    /**
     * 预授权类型(多个用逗号分隔)
     */
    private String preGrantType;

    /**
     * 授权类型(多个用逗号分隔)
     */
    private String grantType;

    /**
     * 预授权有效时间
     */
    private Long preAuthExpireInSeconds;

    /**
     * 令牌有效期
     */
    private Long accessTokenExpireInSeconds;

    /**
     * 令牌过渡期（新申请令牌时，老令牌过渡期）
     */
    private Long accessTokenTransitionInSeconds;

    /**
     * 加密方式
     */
    private String algorithm;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 合作方公钥
     */
    private String targetPublicKey;

    /**
     * 合作方IP白名单
     */
    private String serverIp;


    @TableLogic
    private Boolean deleted;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;
}
