package com.wftk.scale.biz.ext.evaluator.function.scale.factor;

import java.util.List;
import java.util.Map;

import com.googlecode.aviator.runtime.type.AviatorDouble;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;

/**
 * 计算量表因子平均数函数(只允许选择单个因子)
 * 示例: f_avg(1), 表示计算当前量表中ID为1的因子的平均分
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
public class AvgScaleFactorFunction extends BaseScaleFactorFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Factor.F_AVG;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env, final AviatorObject arg1) {
        Long factorId = (Long) arg1.getValue(env);
        logger.info("f_avg: factorId: {}", factorId);
        if (factorId == null) {
            throw new IllegalArgumentException("factorId must not be null");
        }
        ScaleFactor scaleFactor = getFactorById(env, factorId);
        String questionIds = getQuestionIds(scaleFactor);
        List<ScaleUserResultRecord> userResultOptions = getUserResultOptions(env, questionIds, false);
        double score = sumScore(userResultOptions);

        double avg = (int) (score / userResultOptions.size() * 100) / 100;
        return AviatorDouble.valueOf(avg);
    }

}
