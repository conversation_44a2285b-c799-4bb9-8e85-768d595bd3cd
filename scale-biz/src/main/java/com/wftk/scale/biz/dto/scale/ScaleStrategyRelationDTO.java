package com.wftk.scale.biz.dto.scale;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: ScaleStrategyDTO
 * @Description:
 * @Author: mq
 * @Date: 2024/11/26
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleStrategyRelationDTO {

    private Integer type;

    private List<ScaleStrategyDTO> condition;

    private Long toQuestionId;

    private String reminder;
}
