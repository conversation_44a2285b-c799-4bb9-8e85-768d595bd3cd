package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.ScaleEnum;
import com.wftk.scale.biz.dto.distribute.ScaleListingBaseDTO;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordParamDTO;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordQueryDTO;
import com.wftk.scale.biz.entity.*;
import com.wftk.scale.biz.mapper.ScaleListingUserRecordMapper;
import com.wftk.scale.biz.service.*;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 量表分发用户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class ScaleListingUserRecordServiceImpl extends ServiceImpl<ScaleListingUserRecordMapper, ScaleListingUserRecord> implements ScaleListingUserRecordService {

    private static final int LIST_PARTITION_SIZE = 100;

    private static final int TASK_TIMEOUT_VAL = 10;

    @Autowired
    private ThreadPoolTaskExecutor taskModuleExecutor;

    @Autowired
    private ScaleListingUserRecordMapper scaleListingUserRecordMapper;

    @Autowired
    private ScaleListingService scaleListingService;

    @Resource
    private UserService userService;
    @Resource
    private ScaleCombinationService scaleCombinationService;

    @Autowired
    private NoticeMessageService noticeMessageService;

    private List<ScaleListingUserRecord> handler(Long userConfId, ScaleListingBaseDTO scaleListingBase, List<ScaleListingUserRecord> userRecords) {
        //防止分发用户数据重复
        List<ScaleListingUserRecord> unique = userRecords.stream()
                .filter(ObjUtil::isNotNull)
                .collect(
                        Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(
                                    Comparator.comparing(ScaleListingUserRecord::getUserId))), ArrayList::new));
        //校验用户状态、用户所属机构状态、终端编码
        List<Long> userIds = unique.stream().map(ScaleListingUserRecord::getUserId).collect(Collectors.toList());
        if(CollUtil.isEmpty(userIds)) {
            throw new BusinessException("未选择分发用户");
        }
        checkUserStatusAndDepartStatusAndTerminalCodeSame(scaleListingBase, userIds);
        //列表数据属性值处理
        return unique.stream()
                .peek(record -> {
                    record.setScaleListingId(scaleListingBase.getScaleListingId());
                    record.setTargetId(scaleListingBase.getTargetId());
                    record.setTargetName(scaleListingBase.getTargetName());
                    record.setTargetType(scaleListingBase.getTargetType());
                    record.setType(scaleListingBase.getType());
                    record.setUserConfId(userConfId);
                }).collect(Collectors.toList());
    }

    /**
     * 校验用户状态、用户所属机构状态、终端编码
     * @param scaleListingBase 所选量表
     * @param userIds          分发用户ID集合
     */
    private void checkUserStatusAndDepartStatusAndTerminalCodeSame(ScaleListingBaseDTO scaleListingBase, List<Long> userIds){
        List<User> users = userService.listByIds(userIds);
        if(users.isEmpty() || users.size() != userIds.size()) {
            throw new BusinessException("用户不存在");
        }
        boolean bool = users.stream().anyMatch(user -> !user.getEnable());
        if(bool){
            throw new BusinessException("存在禁用状态的分发用户");
        }
        List<Long> enableDeptIdsByUserIds = userService.getEnableDeptIdsByUserIds(userIds);
        if(enableDeptIdsByUserIds == null || enableDeptIdsByUserIds.size() != userIds.size()){
            throw new BusinessException("存在分发用户所属机构为禁用状态");
        }
        ScaleListing scaleListing = scaleListingService.getById(scaleListingBase.getScaleListingId());
        if(!scaleListing.getEnable()){
            throw new BusinessException("所选量表未启用");
        }
        if(scaleListing.getStatus() != 1){
            throw new BusinessException("所选量表未上架");
        }
        String terminalCode = scaleListing.getTerminalCode();
        boolean anyMatch = userService.queryTerminalCodeByUserDeptIds(enableDeptIdsByUserIds).stream()
                .filter(StrUtil::isNotBlank)
                .anyMatch(code -> code.equals(terminalCode));
        if(!anyMatch){
            throw new BusinessException("分发量表所属终端与分发用户所属终端不匹配");
        }
    }

    @Override
    public boolean vaildListingUserRecord(List<ScaleListingUserRecord> userRecords) {
        return !CollUtil.isEmpty(userRecords);
    }

    @Override
    public void saveDistributeUser(ScaleListingBaseDTO scaleListingBase, ScaleListingUserConf scaleListingUserConf, List<ScaleListingUserRecord> userRecords) throws Exception {
        if (ObjectUtil.isNull(scaleListingUserConf) || CollUtil.isEmpty(userRecords)) {
            return;
        }
        userRecords = this.handler(scaleListingUserConf.getId(), scaleListingBase, userRecords);
        Boolean allowNotify = scaleListingUserConf.getAllowNotify();// 是否允许发送短信通知
        //量表分发用户数据分片
        List<List<ScaleListingUserRecord>> partitionList = ListUtil.partition(userRecords, LIST_PARTITION_SIZE);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (List<ScaleListingUserRecord> itemList : partitionList) {
            CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                this.saveBatch(itemList);
                if (allowNotify != null && allowNotify) {
                    itemList.forEach(record -> {
                        try {
                            noticeMessageService.sendScaleListingUserRecordSMSMessage(record);
                        } catch (Exception e) {
                            log.error("发送量表分发用户短信失败。", e);
                        }
                    });
                }
            }, taskModuleExecutor);
            futures.add(completableFuture);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(TASK_TIMEOUT_VAL, TimeUnit.SECONDS);
    }

    @Override
    public void delete(Long userRecordId) {
        scaleListingUserRecordMapper.deleteById(userRecordId);
    }

    @Override
    public Page<ScaleListingUserRecordQueryDTO> getScaleDistributeRecord(ScaleListingUserRecordParamDTO scaleListingUserParamDTO) {
        scaleListingUserParamDTO.setType(ScaleEnum.SINGLE_SCALE_LISTING_TYPE.getCode());
        return Page.doSelectPage(() -> scaleListingUserRecordMapper.getScaleDistributeRecord(scaleListingUserParamDTO));
    }

    @Override
    public Page<ScaleListingUserRecordQueryDTO> getScaleCombinationDistributeRecord(ScaleListingUserRecordParamDTO scaleListingUserParamDTO) {
        scaleListingUserParamDTO.setType(ScaleEnum.COMBINATION_SCALE_LISTING_TYPE.getCode());
        Page<ScaleListingUserRecordQueryDTO> page = Page.doSelectPage(() -> scaleListingUserRecordMapper.getScaleDistributeRecord(scaleListingUserParamDTO));
        if(page.getTotal() > 0){
            List<ScaleListingUserRecordQueryDTO> list = page.getList();
            Set<Long> collect = list.stream().map(ScaleListingUserRecordQueryDTO::getTargetId).collect(Collectors.toSet());
            Map<Long, Integer> map = scaleCombinationService.listByIds(collect).stream()
                    .collect(Collectors.toMap(ScaleCombination::getId, ScaleCombination::getType));
            list.forEach(item -> item.setEvaluationType(map.get(item.getTargetId())));
        }
        return page;
    }

    @Override
    public Page<ScaleListingUserRecordQueryDTO> getAllScaleDistributeRecord(ScaleListingUserRecordParamDTO scaleListingUserParamDTO) {
        return Page.doSelectPage(() -> scaleListingUserRecordMapper.getScaleDistributeRecord(scaleListingUserParamDTO))
                .toPage(this::buildScaleDistributeRecordDTO);
    }

    private List<ScaleListingUserRecordQueryDTO> buildScaleDistributeRecordDTO(List<ScaleListingUserRecordQueryDTO> list) {
        list = CollUtil.isEmpty(list) ? Collections.emptyList() : list;
        return list.stream().peek(dto -> dto.setCover(scaleListingService.getCover(dto.getScaleListingId()))).collect(Collectors.toList());
    }

    @Override
    public ScaleListingUserRecord getScaleDistributeRecordByUserId(Long userId, Long scaleListingId) {
        return scaleListingUserRecordMapper.getScaleDistributeRecordByUserId(userId, scaleListingId);
    }
}
