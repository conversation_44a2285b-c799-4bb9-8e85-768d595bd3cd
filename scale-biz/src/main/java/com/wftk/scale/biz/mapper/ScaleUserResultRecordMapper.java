package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 测评回答答案表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleUserResultRecordMapper extends BaseMapper<ScaleUserResultRecord> {

    /*
     * @Author: mq
     * 
     * @Description: 根据测评记录ID获取测评答案信息
     * 
     * @Date: 2024/11/7 14:41
     * 
     * @Param: resultId-测评记录ID
     * 
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleUserResultRecord>
     **/
    List<ScaleUserResultRecord> findByResultId(@Param("resultId") Long resultId);

    /**
     * 根据测评记录ID和题目ID获取测评答案信息
     * 
     * @param resultId
     * @param questionId
     * @return
     */
    ScaleUserResultRecord findOneByResultIdAndQuestionId(@Param("resultId") Long resultId,
            @Param("questionId") Long questionId);

    int deletedByIds(@Param("ids") List<Long> ids);
}
