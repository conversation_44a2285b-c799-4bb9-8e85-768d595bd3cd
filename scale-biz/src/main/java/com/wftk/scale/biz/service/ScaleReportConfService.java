package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.entity.ScaleReportConf;

/**
 * <p>
 * 报告设置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleReportConfService extends IService<ScaleReportConf> {

    /*
    * @Author: mq
    * @Description: 创建量表报告设置信息
    * @Date: 2024/11/26 11:13
    * @Param: reportConf-报告设置信息
    * @return: void
    **/
    Long create(ScaleReportConf reportConf);
    
    /*
    * @Author: mq
    * @Description: 修改量表报告设置信息
    * @Date: 2024/11/26 11:14
    * @Param: reportConf-报告设置信息
    * @return: void
    **/
    void modify(ScaleReportConf reportConf);

    /*
    * @Author: mq
    * @Description: 根据量表ID获取报告设置详情
    * @Date: 2024/11/27 14:04
    * @Param: scaleId
    * @return: ScaleReportConf
    **/
    ScaleReportConf detail(Long scaleId);

    boolean validReportConfDataIntegrity( Long scaleId);
}
