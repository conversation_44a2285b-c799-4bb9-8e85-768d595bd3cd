package com.wftk.scale.biz.manager.report.handle;

import com.wftk.scale.biz.constant.enums.UserSexEnum;
import com.wftk.scale.biz.entity.ScaleReportConf;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.entity.User;
import com.wftk.scale.biz.manager.report.dto.UserReportInfoDTO;
import com.wftk.scale.biz.manager.report.dto.content.UserInfoReportContentDTO;
import com.wftk.scale.biz.service.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 用户信息
 * <AUTHOR>
 * @createDate 2025/9/8 16:02
 */
@Slf4j
@Component
public class UserInfoHandle {

    @Resource
    UserService userService;

    public UserInfoReportContentDTO buildReportContent(ScaleUserResult scaleUserResult, ScaleReportConf scaleReportConf) {
        User user = userService.getById(scaleUserResult.getUserId());
        UserReportInfoDTO map = this.map(user, scaleReportConf);
        UserInfoReportContentDTO userInfoReportContentDTO = new UserInfoReportContentDTO();
        userInfoReportContentDTO.setUserReportInfo(map);
        if(scaleReportConf.getEvaluationTimeEnable()){
            long second = ChronoUnit.SECONDS.between(scaleUserResult.getStartTime(), scaleUserResult.getEndTime());
            userInfoReportContentDTO.setEvaluationTime(String.valueOf(second));
            userInfoReportContentDTO.setStartTime(scaleUserResult.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        return userInfoReportContentDTO;
    }

    public UserReportInfoDTO map(User user, ScaleReportConf conf) {
        UserReportInfoDTO dto = new UserReportInfoDTO();
        setIfEnabled(dto::setCode, user::getCode, conf.getUserCodeEnable());
        setIfEnabled(dto::setName, user::getName, conf.getUserNameEnable());
        setIfEnabled(dto::setPhone, user::getPhone, conf.getUserPhoneEnable());
        setIfEnabled(dto::setSex, () -> UserSexEnum.valueOf(user.getSex()), conf.getUserSexEnable());
        setIfEnabled(dto::setIdCard, user::getIdCard, conf.getUserIdCardEnable());
        setBirthdayIfEnabled(dto, user, conf);
        return dto;
    }

    private <T> void setIfEnabled(Consumer<T> setter, Supplier<T> getter, boolean enabled) {
        if (enabled) {
            setter.accept(getter.get());
        }
    }

    private void setBirthdayIfEnabled(UserReportInfoDTO dto, User user, ScaleReportConf conf) {
        if (conf.getUserBirthdayEnable() && user.getBirthday() != null) {
            dto.setBirthday(user.getBirthday().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
    }
}
