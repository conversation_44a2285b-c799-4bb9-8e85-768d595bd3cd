package com.wftk.scale.biz.dto.distribute;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: ScaleListingUserParamDTO
 * @Description: 量表分发记录查询参数传输实体
 * @Author: mq
 * @Date: 2024-10-31 13:35
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleListingUserRecordParamDTO implements Serializable {

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 量表名称
     */
    private String targetName;

    /**
     * 终端编号
     */
    private String terminalCode;

    /**
     * 类型
     */
    private Integer type;
}
