package com.wftk.scale.biz.dto.scale;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import com.wftk.scale.biz.entity.ScaleQuestionOption;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ScaleQuestionQueryDTO
 * @Description: 量表题目信息
 * @Author: mq
 * @Date: 2024-11-05 13:55
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleQuestionTreeDTO implements Serializable {

    /**
     * 量表ID
     */
    private Long scaleId;

    /**
     * 题目ID
     */
    private Long id;

    /**
     * 题号
     */
    private String questionNumber;

    /**
     * 子题号
     */
    private String subNumber;

    /**
     * 是否必填
     */
    private Boolean requireAnswer;

    /**
     * 问题,富文本
     */
    private String question;

    /**
     * 题干数据类型: TEXT.普通文本;
     */
    private String dataType;

    /**
     * 题型，1单选，2单选其他，3多选，4单行输入
     */
    private Integer type;

    /**
     * 答案
     */
    private String answer;

    /**
     * 答案得分
     */
    private String answerScore;

    /**
     * 性质
     */
    private String property;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 跳题逻辑: 1.单条件触发跳转; 2.多条件同时满足触发跳转; 3.多条件之一满足触发跳转;
     */
    private Integer relationType;

    /**
     * 关系策略
     */
    private String strategy;

    /**
     * 关系策略-解析后的结果
     */
    private List<ScaleStrategyRelationDTO> strategyResult;

    /**
     * 提示
     */
    private String reminder;

    /**
     * 目标题目ID
     */
    private Long toQuestionId;

    /**
     * 选项值
     */
    private List<ScaleQuestionOption> options;

    /**
     * 子题
     */
    private List<ScaleQuestionTreeDTO> children;

    /**
     * 位置
     */
    private Integer offset;

    /**
     * 附件地址
     */
    @RFile(role = FileConstant.FILE_SCALE_SIGN_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    private String filePath;
}
