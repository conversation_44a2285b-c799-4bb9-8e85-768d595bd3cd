package com.wftk.scale.biz.dto.order;

import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2024/11/15 17:47
 */
@Data
public class PlaceOrderDTO {

    /**
     * 终端流水号
     */
    private String terminalSerialNo;

    /**
     * 量表类型
     */
    private Integer type;

    /**
     * 目标id
     */
    private Long targetId;

    /**
     * 量表名称
     */
    private String targetName;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 上架id
     */
    private Long scaleListingId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 终端id
     */
    private String terminalCode;

    /**
     * 电话
     */
    private String phone;

    /**
     * 金额
     */
    private Integer amount;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 原价
     */
    private Integer originalAmount;

    /**
     * 支付渠道
     */
    private Integer payChannel;

}
