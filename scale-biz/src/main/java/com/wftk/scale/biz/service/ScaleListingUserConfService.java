package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.dto.distribute.ScaleListingBaseDTO;
import com.wftk.scale.biz.entity.ScaleListingUserConf;

/**
 * <p>
 * 量表上架触达用户配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleListingUserConfService extends IService<ScaleListingUserConf> {

    /*
    * @Author: mq
    * @Description: 校验量表是否超过分发配置的截止日期
    * @Date: 2024/12/21 16:41
    * @Param: dto
    * @return: boolean
    **/
    boolean vaildUserConfEvaluationTimeOut(Long listingId);

    /*
     * @Author: mq
     * @Description: 校验量表是否超过分发配置的截止日期
     * @Date: 2024/12/24 11:52
     * @Param: dto
     * @return: boolean
     **/
    boolean vaildUserConfEvaluationTimeOut(ScaleListingUserConf listingUserConf);

    /*
    * @Author: mq
    * @Description: 校验分发的截至日期是否正确
    * @Date: 2024/12/12 15:31
    * @Param: userConf
    * @return: boolean
    **/
    boolean vaildUserConfTime(ScaleListingUserConf userConf);

    /*
    * @Author: mq
    * @Description: 保存分发配置信息信息
    * @Date: 2024/11/19 16:56
    * @Param: dto-上架量表基础信息
    * @Param userConf-上架触达用户配置信息
    * @return: Long
    **/
    ScaleListingUserConf saveDistributeUserConf(ScaleListingBaseDTO dto, ScaleListingUserConf userConf);

    /*
    * @Author: mq
    * @Description: 判断量表是否可复测
    * @Date: 2024/11/19 17:00
    * @Param: listingId-上架配置ID
    * @return: boolean
    **/
    boolean isAllowRepeat(Long listingId);
    
    /*
    * @Author: mq
    * @Description: 判断量表是否限制作答时间
    * @Date: 2024/11/20 17:27
    * @Param: listingId-上架配置ID
    * @return: boolean
    **/
    boolean isAllowTimeLimit(Long listingId);

    /*
    * @Author: mq
    * @Description: 根据上架配置ID获取用户配置信息
    * @Date: 2024/12/14 14:02
    * @Param: listingId
    * @return: ScaleListingUserConf
    **/
    ScaleListingUserConf findByListingId(Long listingId);

    ScaleListingUserConf findByListingUserIdOrOrderNo(Long listingUserId, String orderNo);
}
