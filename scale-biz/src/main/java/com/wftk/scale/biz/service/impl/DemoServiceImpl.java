package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.wftk.scale.biz.entity.Demo;
import com.wftk.scale.biz.service.DemoService;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @create 2024/10/23 18:15
 */
@Service
public class DemoServiceImpl implements DemoService {
    @Override
    public Demo findById(Long id) {
        Demo demo = new Demo();
        demo.setName("n" + id);
        demo.setAge(RandomUtil.randomInt(10, 100));
        return demo;
    }

    @Override
    public void save(Demo demo) {

    }
}
