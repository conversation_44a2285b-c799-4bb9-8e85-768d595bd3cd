package com.wftk.scale.biz.event.publisher;

import com.wftk.scale.biz.event.ScaleDelEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createDate 2024/12/4 17:27
 */
@Component
public class ScaleDelPublisher implements BaseEventPublisher<ScaleDelEvent> {

    private final ApplicationEventPublisher applicationEventPublisher;

    public ScaleDelPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Override
    public void publishEvent(ScaleDelEvent scaleDelEvent) {
        applicationEventPublisher.publishEvent(scaleDelEvent);
    }
}
