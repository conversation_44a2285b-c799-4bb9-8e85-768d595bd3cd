package com.wftk.scale.biz.constant.enums;


import lombok.Getter;

import java.util.Arrays;

/**
 * @EnumName: QuestionTypeEnum
 * @Description:
 * @Author: mq
 * @Date: 2024/11/28
 * @Version: 1.0
 **/
@Getter
public enum QuestionTypeEnum {

    SELECT_SINGLE(1, "单选"),

    SELECT_OTHER(2, "单选其他"),

    SELECT_MULTIPLE(3, "多选"),

    LINE_INPUT(4, "单行输入");
    private Integer type;

    private String value;

    QuestionTypeEnum(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public static Integer getType(String value) {
        return Arrays.stream(QuestionTypeEnum.values())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .map(QuestionTypeEnum::getType)
                .orElse(null);
    }

    public static String getValue(Integer type) {
        return Arrays.stream(QuestionTypeEnum.values())
                .filter(e -> e.getType().equals(type))
                .findFirst()
                .map(QuestionTypeEnum::getValue)
                .orElse(null);
    }
}
