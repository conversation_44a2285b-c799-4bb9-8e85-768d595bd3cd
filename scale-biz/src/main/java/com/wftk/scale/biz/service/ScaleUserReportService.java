package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.report.UserReportDTO;
import com.wftk.scale.biz.dto.report.UserReportQueryDTO;
import com.wftk.scale.biz.entity.ScaleUserReport;
import com.wftk.scale.biz.vo.ScaleUserReportVO;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05 11:14:30
 */
public interface ScaleUserReportService extends IService<ScaleUserReport> {

    ScaleUserReport getByResultId(Long resultId);

    UserReportDTO getUserReportByResultId(Long resultId);

    String generateReportPdf(Long resultId);

    Page<ScaleUserReportVO> queryPage(UserReportQueryDTO dto);
}
