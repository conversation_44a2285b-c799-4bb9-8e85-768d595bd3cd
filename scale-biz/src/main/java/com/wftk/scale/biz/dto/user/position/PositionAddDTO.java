package com.wftk.scale.biz.dto.user.position;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PositionAddDTO implements Serializable {

    @NotBlank(message = "岗位编码不能为空")
    @Length(max = 50, message = "岗位编码最大输入50个字符")
    private String code;

    @NotBlank(message = "岗位编码不能为空")
    @Length(max = 50, message = "岗位名称最大输入50个字符")
    private String name;

    @NotNull(message = "岗位顺序不能为空")
    private Integer sort;

    private Boolean enable;

    @Length(max = 255, message = "岗位备注最大输入255个字符")
    private String remark;
}
