package com.wftk.scale.biz.excel.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
public class CompletableFutureUtils {

    private final static DataSourceTransactionManager TRANSACTION_MANAGER = SpringContextUtil.getBean(DataSourceTransactionManager.class);

    private final static ThreadPoolTaskExecutor EXECUTOR = SpringContextUtil.getBean(ThreadPoolTaskExecutor.class);

    private final AtomicBoolean errorOccurred;

    private final CountDownLatch latch;

    /**
     * CompletableFuture与外部上下文使用的不是一个事务，手动控制
     * 异步的任务组，任一一个失败，全部回滚，异常抛到外部
     * 如果需要外部异常，异步的任务组全回滚，latch计数器+1，然后设置errorOccurred为true即可
     */
    public CompletableFuture<Void> runAsync(CompletableTask task){
        return CompletableFuture.runAsync(() -> {
            //手动开启事务，获取事务状态
            DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
            //开启一个新事务
            definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            TransactionStatus status = TRANSACTION_MANAGER.getTransaction(definition);
            try {
                task.run();
            }catch (Exception e){
                log.error("批量异步处理失败", e);
                errorOccurred.set(true);
                //往外抛异常，外部事务自行控制
                throw e;
            }finally {
                latch.countDown();
                try {
                    //等待其他任务执行完成，任何一个任务失败，全部回滚
                    latch.await();
                } catch (InterruptedException e) {
                    log.error("批量异步处理失败，回滚失败", e);
                }
                if(errorOccurred.get()){
                    log.info("transaction rollback");
                    TRANSACTION_MANAGER.rollback(status);
                }else{
                    log.info("transaction commit");
                    TRANSACTION_MANAGER.commit(status);
                }
            }
        }, EXECUTOR);
    }
}
