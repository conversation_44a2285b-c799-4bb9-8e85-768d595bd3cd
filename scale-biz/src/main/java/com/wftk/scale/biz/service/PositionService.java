package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.user.position.PositionAddDTO;
import com.wftk.scale.biz.dto.user.position.PositionQueryDTO;
import com.wftk.scale.biz.dto.user.position.PositionUpdateDTO;
import com.wftk.scale.biz.entity.Position;
import com.wftk.scale.biz.vo.PositionVO;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <p>
 * 岗位表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface PositionService extends IService<Position> {

    Page<PositionVO> getList(PositionQueryDTO dto);

    PositionVO detailById(Long id);

    void add(PositionAddDTO dto);

    void update(PositionUpdateDTO dto);

    void delete(Long id);

    void export(PositionQueryDTO dto, HttpServletResponse response);
}
