package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleConvertResultIntro;
import com.wftk.scale.biz.dto.scale.ScaleUserFactorResultDetailDTO;
import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.entity.ScaleUserFactorResult;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.ext.evaluator.constant.EnvConstant;
import com.wftk.scale.biz.ext.evaluator.core.EvaluatorContext;
import com.wftk.scale.biz.ext.evaluator.core.EvaluatorEngine;
import com.wftk.scale.biz.ext.evaluator.core.EvaluatorContext.Builder;
import com.wftk.scale.biz.mapper.ScaleUserFactorResultMapper;
import com.wftk.scale.biz.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 测评因子得分表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Slf4j
@Service
public class ScaleUserFactorResultServiceImpl extends ServiceImpl<ScaleUserFactorResultMapper, ScaleUserFactorResult>
        implements ScaleUserFactorResultService {

    @Autowired
    private ScaleUserFactorResultMapper scaleUserFactorResultMapper;

    @Autowired
    private ScaleUserResultRecordService scaleUserResultRecordService;

    @Autowired
    private ScaleFactorService scaleFactorService;

    @Autowired
    private ScaleResultIntroService scaleResultIntroService;

    @Autowired
    private ThreadPoolTaskExecutor taskModuleExecutor;

    @Autowired
    private EvaluatorEngine evaluatorEngine;

    private static final int TASK_TIMEOUT_VAL = 30;

    @Override
    public void saveFactorScore(Long resultId) {
        // 根据测评记录获取测评答案信息
        List<ScaleUserResultRecord> list = scaleUserResultRecordService.findByResultId(resultId);
        if (CollUtil.isEmpty(list)) {
            throw new BusinessException("测评答案不能为空");
        }
        try {
            Long scaleId = list.stream().mapToLong(ScaleUserResultRecord::getScaleId).findFirst().getAsLong();
            // 获取测评记录量表设置的因子维度信息
            List<ScaleFactor> factors = scaleFactorService.findByScaleId(scaleId);
            if (CollUtil.isEmpty(factors)) {
                throw new BusinessException("量表因子维度不能为空");
            }
            // 计算所有因子维度的得分结果
            List<ScaleUserFactorResult> factorResults = this.calculateFactorResults(resultId, factors, list);
            this.saveBatch(factorResults);
        } catch (Exception e) {
            throw new BusinessException("保存因子得分发生异常: resultId: " + resultId, e);
        }
    }

    @Override
    public Page<ScaleUserFactorResult> selectPage(Long resultId) {
        return Page.doSelectPage(() -> scaleUserFactorResultMapper.getList(resultId));
    }

    @Override
    public List<ScaleUserFactorResult> selectListByResultId(Long resultId) {
        return scaleUserFactorResultMapper.getList(resultId);
    }

    @Override
    public List<ScaleUserFactorResultDetailDTO> getDetailByResultId(Long resultId) {
        return scaleUserFactorResultMapper.getDetailByResultId(resultId);
    }

    @Override
    public ScaleUserFactorResult getByResultIdAndFactorId(Long resultId, Long factorId, String showType) {
        return scaleUserFactorResultMapper.getByResultIdAndFactorId(resultId, factorId, showType);
    }

    @Override
    public List<ScaleUserFactorResult> getListByResultId(Long resultId, List<Long> factorIds) {
        return scaleUserFactorResultMapper.getListByResultId(resultId, factorIds);
    }

    @Override
    public BigDecimal getScoreByResultIdAndFactorId(Long resultId, Long factorId) {
        return scaleUserFactorResultMapper.getScoreByResultIdAndFactorId(resultId, factorId);
    }

    @Override
    public List<Map<String, Object>> selectReportFormData(Long resultId, String clonwnStr, List<Long> factorIds) {
        return scaleUserFactorResultMapper.selectReportFormData(resultId, clonwnStr, factorIds);
    }

    private List<ScaleUserFactorResult> calculateFactorResults(Long resultId, List<ScaleFactor> factors,
            List<ScaleUserResultRecord> records) throws Exception {
        List<ScaleUserFactorResult> factorResults = new ArrayList<>();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        // 遍历每个因子维度的得分情况
        factors.forEach(scaleFactor -> {
            // 异步计算每条因子维度的得分情况
            CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                // 计算因子分
                Object rawScoreObject = this.calculateRawScore(scaleFactor, resultId);
                BigDecimal rawScore = null;
                // 预期是数值类型或者Bigdecimal类型
                if (rawScoreObject instanceof BigDecimal) {
                    rawScore = (BigDecimal) rawScoreObject;
                } else if (rawScoreObject instanceof Number) {
                    rawScore = new BigDecimal(rawScoreObject.toString());
                } else {
                    throw new IllegalArgumentException("因子维度计算公式计算发生异常");
                }

                // 分值转换
                ScaleConvertResultIntro intro = this.calculateConvertScore(scaleFactor, rawScore);
                // 结果性质 false.阴; ture.阳;
                Boolean positive = this.calculatePositive(records);
                // 得分结果
                ScaleUserFactorResult factorResult = this.generateFactorResult(resultId, scaleFactor, rawScore,
                        intro, positive);
                factorResults.add(factorResult);
                // 默认线程池,存在OOM的问题,后期调整
            }, taskModuleExecutor);
            futures.add(completableFuture);
        });
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(TASK_TIMEOUT_VAL, TimeUnit.SECONDS);
        return factorResults;
    }

    private ScaleUserFactorResult generateFactorResult(Long resultId, ScaleFactor scaleFactor, BigDecimal rawScore,
            ScaleConvertResultIntro intro, Boolean positive) {
        ScaleUserFactorResult factorResult = new ScaleUserFactorResult();
        factorResult.setResultId(resultId);
        factorResult.setFactorId(scaleFactor.getId());
        factorResult.setScore(ObjUtil.defaultIfNull(intro.getConvertScore(), BigDecimal.ZERO));
        factorResult.setRawScore(ObjUtil.defaultIfNull(rawScore, BigDecimal.ZERO));
        factorResult.setResultIntro(intro.getResultIntroName());
        factorResult.setScoreSection(intro.getScoreRange());
        factorResult.setFactorName(scaleFactor.getName());
        factorResult.setResult(positive);
        return factorResult;
    }

    /**
     * 计算因子维度的原始得分
     *
     * @param factor
     * @param resultId
     * @return
     */
    private Object calculateRawScore(ScaleFactor factor, Long resultId) {
        String expression = factor.getFormulaCompiled();
        if (StrUtil.isBlank(expression)) {
            throw new IllegalArgumentException("因子维度计算公式不能为空");
        }
        // 添加环境变量
        Builder builder = EvaluatorContext.builder()
                .addVariable(EnvConstant.SCALE_ID, factor.getScaleId())
                .addVariable(EnvConstant.RESULT_ID, resultId)
                .addVariable(EnvConstant.CURRENT_FACTOR_ID, factor.getId());

        // 添加参数
        String parameterNameString = factor.getFormulaParamName();
        if (StrUtil.isNotBlank(parameterNameString)) {
            if (StrUtil.isBlank(factor.getFormulaParam())) {
                throw new IllegalArgumentException("因子维度计算公式参数不能为空");
            }
            String splitSymbol = "\\|";
            String[] paramNames = parameterNameString.split(splitSymbol);
            String[] paramValues = factor.getFormulaParam().split(splitSymbol);
            if (paramNames.length != paramValues.length) {
                throw new IllegalArgumentException("因子维度计算公式参数名称和参数值不匹配");
            }
            for (int i = 0; i < paramNames.length; i++) {
                builder.addVariable(paramNames[i].trim(), paramValues[i].trim());
            }
        }
        EvaluatorContext context = builder.build();
        try {
            return evaluatorEngine.evaluateTemplate(expression, context);
        } catch (Exception e) {
            throw new BusinessException("因子维度计算公式计算发生异常", e);
        }
    }

    private Boolean calculatePositive(List<ScaleUserResultRecord> records) {
        // 有一个阳性则认为阳
        return records.stream().anyMatch(record -> Objects.equals(record.getResult(), true));
    }

    private ScaleConvertResultIntro calculateConvertScore(ScaleFactor factor, BigDecimal result) {
        // 分值转换
        ScaleConvertResultIntro intro = scaleResultIntroService.getConvertScore(factor.getScaleId(), factor.getId(),
                result);
        return intro;
    }

}
