package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.TenantClientInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 租户下的client信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27 15:56:59
 */
public interface TenantClientInfoMapper extends BaseMapper<TenantClientInfo> {

    /**
     * 根据clientId查询tenant信息
     * @param clientId
     * @param enable
     * @return
     */
    TenantClientInfo selectByClintId(@Param("clientId") String clientId, @Param("enable") Boolean enable);
}
