//package com.wftk.scale.biz.ext.notice.dto;
//
//import com.wftk.scale.biz.ext.notice.enums.NoticeTypeEnum;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.ToString;
//
//import java.util.LinkedHashMap;
//
///**
// * <AUTHOR>
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//@ToString(callSuper = true)
//public class SmsDTO extends NoticeDTO{
//
//    /**
//     * 动态参数，保证顺序
//     */
//    private LinkedHashMap<String, Object> params;
//    /**
//     * 场景
//     */
//    private String scene;
//
//    public SmsDTO(LinkedHashMap<String, Object> params, String scene, String userIds) {
//        this.scene = scene;
//        this.params = params;
//        super.setUserIds(userIds);
//    }
//
//    @Override
//    public NoticeTypeEnum getNoticeType() {
//        return NoticeTypeEnum.SMS;
//    }
//}
