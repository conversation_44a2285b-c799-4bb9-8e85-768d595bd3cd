package com.wftk.scale.biz.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class SysRoleVO {

    private Long id;

    private String name;

    private String code;

    private Integer sort;

    private Boolean enable;

    private String remark;

    private Integer permissionRange;

    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
