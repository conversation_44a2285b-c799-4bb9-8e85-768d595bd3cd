package com.wftk.scale.biz.converter;

import com.wftk.scale.biz.dto.user.DepartmentCreateDTO;
import com.wftk.scale.biz.dto.user.DepartmentQueryDTO;
import com.wftk.scale.biz.dto.user.DepartmentUpdateDTO;
import com.wftk.scale.biz.entity.Department;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/11/28 11:28
 */
@Mapper(componentModel = "spring")
public interface DepartmentConverter {

    DepartmentQueryDTO entityToDepartmentQueryDTO(Department department);

    Department departmentCreateDTOToEntity(DepartmentCreateDTO departmentCreateDTO);

    Department departmentUpdateDTOToEntity(DepartmentUpdateDTO departmentUpdateDTO);

    List<DepartmentQueryDTO> entityToDepartmentQueryDTO(List<Department> department);

}
