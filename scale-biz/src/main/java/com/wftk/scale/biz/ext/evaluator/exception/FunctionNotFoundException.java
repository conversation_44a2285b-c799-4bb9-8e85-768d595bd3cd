package com.wftk.scale.biz.ext.evaluator.exception;

/**
 * 
 * 表达式不存在异常
 * 
 * <AUTHOR>
 * @date 2025-09-10
 */
public class FunctionNotFoundException extends ExpressionException {

    private final String function;

    public FunctionNotFoundException(String function) {
        super("Expression not found: " + function);
        this.function = function;
    }

    public FunctionNotFoundException(String function, Throwable cause) {
        super("Expression not found: " + function, cause);
        this.function = function;
    }

    public String getFunction() {
        return function;
    }

}
