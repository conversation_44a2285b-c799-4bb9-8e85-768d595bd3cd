package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.core.password.PasswordEncoder;
import com.wftk.auth.spring.boot.autoconfigure.ext.password.BcryptPasswordEncoder;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.converter.UserConverter;
import com.wftk.scale.biz.dto.user.*;
import com.wftk.scale.biz.entity.Department;
import com.wftk.scale.biz.entity.User;
import com.wftk.scale.biz.entity.UserDepartment;
import com.wftk.scale.biz.entity.UserExtraInfo;
import com.wftk.scale.biz.excel.service.handler.UserHandler;
import com.wftk.scale.biz.mapper.DepartmentMapper;
import com.wftk.scale.biz.mapper.UserDepartmentMapper;
import com.wftk.scale.biz.mapper.UserExtraInfoMapper;
import com.wftk.scale.biz.mapper.UserMapper;
import com.wftk.scale.biz.service.UserService;
import com.wftk.scale.biz.vo.UserVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    UserMapper userMapper;

    @Autowired
    private UserExtraInfoMapper userExtraInfoMapper;

    @Autowired
    UserConverter userConverter;

    @Autowired
    UserDepartmentMapper userDepartmentMapper;
    @Resource
    private DepartmentMapper departmentMapper;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Resource
    private UserHandler userHandler;

    @Override
    public User selectByCodeAndDepartmentId(String code, Long departmentId) {
        return baseMapper.selectByCodeAndDepartmentId(code, departmentId);
    }

    @Override
    public User selectByAccount(String account, Boolean enable) {
        return baseMapper.selectByAccount(account, enable);
    }

    @Override
    public Page<UserQueryDTO> selectUserQueryList(UserSearchDTO dto) {
        return Page.doSelectPage(()->userMapper.selectUserQueryList(dto));
    }

    @Override
    public void changePassword(Long userId, String password) {
        User user = new User();
        PasswordEncoder passwordEncoder = new BcryptPasswordEncoder();
        user.setPassword(passwordEncoder.encode(password));
        user.setId(userId);
        updateById(user);
    }

    @Override
    public void changeEnable(UserChangeEnableDTO userChangeEnableDTO) {
        //校验下状态是否正确
        User user = baseMapper.selectById(userChangeEnableDTO.getId());
        if(user == null){
            throw new BusinessException("用户不存在！");
        }
        if(user.getEnable() != null && user.getEnable().equals(userChangeEnableDTO.getEnable())){
            throw new BusinessException(String.format("该用户状态已%s,无需重复操作!", user.getEnable() ? "启用" : "禁用"));
        }
        //判断下用户所属机构的状态，如果机构为禁用，则不允许启用
        if(userChangeEnableDTO.getEnable()){
            UserDepartment userDepartment = userDepartmentMapper.selectOne(new LambdaQueryWrapper<UserDepartment>().eq(UserDepartment::getUserId, user.getId()));
            if(userDepartment != null){
                Department department = departmentMapper.selectById(userDepartment.getDepartmentId());
                if(department != null && !department.getEnable()){
                    throw new BusinessException("该用户所属机构为禁用状态，请先启用后再操作");
                }
            }
        }
        baseMapper.update(new LambdaUpdateWrapper<User>()
                .eq(User::getId, userChangeEnableDTO.getId())
                .set(User::getEnable, userChangeEnableDTO.getEnable()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeUser(UserChangeDTO userChangeDTO) {
        User user = userMapper.selectById(userChangeDTO.getId());
        if(user == null){
            throw new BusinessException("用户不存在");
        }
        Long userId = user.getId();
        UserDepartment userDepartment = userDepartmentMapper.selectOne(
                new LambdaQueryWrapper<UserDepartment>().eq(UserDepartment::getUserId, userId));
        if(userDepartment == null){
            throw new BusinessException("用户无所属机构");
        }
        //如果机构没有发生变更，则不进行编码、账号的校验
        Long departmentId = userChangeDTO.getDepartmentId();
        if(userDepartment.getDepartmentId().equals(departmentId)){
            validParam(departmentId, null, null);
        }else{
            validParam(departmentId, user.getCode(), user.getAccount());
            userDepartmentMapper.update(new LambdaUpdateWrapper<UserDepartment>()
                    .eq(UserDepartment::getUserId, userId)
                    .set(UserDepartment::getDepartmentId, departmentId));
        }
        // 保存身份类型数据
        if(userChangeDTO.getUserType() != null && StrUtil.isNotBlank(userChangeDTO.getExtraInfo())){
            UserExtraInfo userExtraInfo = userExtraInfoMapper.selectOne(
                    new LambdaQueryWrapper<UserExtraInfo>().eq(UserExtraInfo::getUserId, userId));
            if(userExtraInfo == null){
                userExtraInfo = new UserExtraInfo();
                userExtraInfo.setUserType(userChangeDTO.getUserType());
                userExtraInfo.setExtraInfo(userChangeDTO.getExtraInfo());
                userExtraInfo.setUserId(userId);
                userExtraInfoMapper.insert(userExtraInfo);
            }else{
                userExtraInfo.setExtraInfo(userChangeDTO.getExtraInfo());
                userExtraInfo.setUserType(userChangeDTO.getUserType());
                userExtraInfoMapper.updateById(userExtraInfo);
            }
        }
        baseMapper.updateById(userConverter.userChangeDTOToEntity(userChangeDTO));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(Long userId) {
        User user = userMapper.selectById(userId);
        if(user == null){
            throw new BusinessException("用户不存在");
        }
        baseMapper.deleteById(userId);
        userDepartmentMapper.delete(new LambdaQueryWrapper<UserDepartment>().eq(UserDepartment::getUserId, userId));
        userExtraInfoMapper.delete(new LambdaQueryWrapper<UserExtraInfo>().eq(UserExtraInfo::getUserId, userId));
    }

    @Override
    public List<String> queryTerminalCodeByUserDeptIds(List<Long> userDeptIds){
        return userMapper.queryTerminalCodeByUserDeptIds(userDeptIds);
    }

    @Override
    public Set<String> getPhoneListByUserIds(Set<Long> userIds) {
        List<User> users = this.listByIds(userIds);
        return users.stream().map(User::getPhone).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
    }

    @Override
    public Set<String> getEmailListByUserIds(Set<Long> userIds) {
        List<User> users = this.listByIds(userIds);
        return users.stream().map(User::getEmail).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
    }

    @Override
    public boolean checkDepartmentUserStatus(List<Long> orgIds, Boolean enable) {
        return userMapper.countStatusUser(orgIds, enable) > 0;
    }

    @Override
    public void exportUser(HttpServletResponse response, UserSearchDTO dto) {
        List<UserExcelConvertDTO> list = userMapper.queryList(dto);
        userHandler.exportExcel(response, "用户数据导出", list);
    }

    @Override
    public void downTemplate(HttpServletResponse response) {
        //构造机构下拉选项 code-名称
        userHandler.downloadTemplate(response, "用户导入模板");
    }

    @Override
    public void importData(MultipartFile file){
        try {
            userHandler.importExcel(file);
        }catch (Exception e){
            log.error("导入失败", e);
            throw new BusinessException("导入失败:" + e.getMessage());
        }
    }

    @Override
    public UserVO selectByUserId(Long userId) {
        if(Objects.isNull(userId)){
            throw new BusinessException("用户ID不能为空!");
        }
        return userMapper.selectByUserId(userId);
    }

    @Override
    public List<Long> getEnableDeptIdsByUserIds(List<Long> userIds) {
        return userMapper.getEnableDeptIdsByUserIds(userIds);
    }

    @Override
    public List<DistributableUserDTO> findDistributionUser(String departmentCode, String terminalCode) {
        return userMapper.findDistributionUser(departmentCode,terminalCode);
    }

    private void validParam(Long departmentId, String userCode, String account){
        // 查询该用户是否被创建,编码和登录账号都不重复，根据机构区分
        Department department = departmentMapper.selectById(departmentId);
        if(department == null || !department.getEnable()){
            throw new BusinessException("机构状态无效!");
        }
        if(StrUtil.isNotBlank(userCode)){
            User user = selectByCodeAndDepartmentId(userCode, departmentId);
            if(user != null && !user.getDeleted()){
                throw new BusinessException("该用户编码对应的用户已存在!");
            }
        }
        if(StrUtil.isNotBlank(account)){
            User user = userMapper.selectByAccountAndDepartmentId(account, departmentId);
            if(user != null && !user.getDeleted()){
                throw new BusinessException("该登录账号对应的用户已存在!");
            }
        }
    }

    @Transactional
    @Override
    public User createUser(UserCreateDTO userCreateDTO) {
        //校验参数
        validParam(userCreateDTO.getDepartmentId(), userCreateDTO.getCode(), userCreateDTO.getAccount());
        // 保存用户
        User saveUser = userConverter.userCreateDTOToEntity(userCreateDTO);
        saveUser.setTerminalCode(null);
        saveUser.setPassword(getPassword(saveUser.getAccount()));
        save(saveUser);
        log.info("save user succeed. [{}]", saveUser);

        // 保存身份类型数据
        if(userCreateDTO.getUserType() != null && StrUtil.isNotBlank(userCreateDTO.getExtraInfo())){
            UserExtraInfo userExtraInfo = new UserExtraInfo();
            userExtraInfo.setUserType(userCreateDTO.getUserType());
            userExtraInfo.setExtraInfo(userCreateDTO.getExtraInfo());
            userExtraInfo.setUserId(saveUser.getId());
            userExtraInfoMapper.insert(userExtraInfo);
        }

        // 绑定部门与用户关系
        UserDepartment userDepartment = new UserDepartment();
        userDepartment.setUserId(saveUser.getId());
        userDepartment.setDepartmentId(userCreateDTO.getDepartmentId());
        userDepartmentMapper.insert(userDepartment);
        return saveUser;
    }

    @Override
    public String getPassword(String account) {
        // 默认密码为 zxlb + 账户
        String pwdStr = "zxlb" + account;
        return passwordEncoder.encode(pwdStr);
    }
}