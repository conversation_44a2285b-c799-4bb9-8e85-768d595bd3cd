package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.jackson.core.TargetType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.TerminalConfConstant;
import com.wftk.scale.biz.constant.enums.HttpJobStatusEnum;
import com.wftk.scale.biz.constant.enums.OrderEnum;
import com.wftk.scale.biz.converter.NotifyConvert;
import com.wftk.scale.biz.dto.notify.EvaluationRecordNotifyInput;
import com.wftk.scale.biz.dto.notify.OrderStautsNotifyInput;
import com.wftk.scale.biz.dto.scale.ScaleCombinationQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleUserResultParamDTO;
import com.wftk.scale.biz.entity.*;
import com.wftk.scale.biz.event.ScaleEvaluationIntroEvent;
import com.wftk.scale.biz.event.publisher.ScaleEvaluationIntroPublisher;
import com.wftk.scale.biz.excel.model.ScaleUserResultExcelDataDTO;
import com.wftk.scale.biz.excel.service.handler.ScaleUserResultHandler;
import com.wftk.scale.biz.manager.job.executor.RequestExecutor;
import com.wftk.scale.biz.mapper.ScaleUserResultMapper;
import com.wftk.scale.biz.service.*;
import com.wftk.scale.biz.vo.ScaleUserResultVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户测评记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class ScaleUserResultServiceImpl extends ServiceImpl<ScaleUserResultMapper, ScaleUserResult> implements ScaleUserResultService {

    @Autowired
    private ScaleUserResultMapper scaleUserResultMapper;

    @Autowired
    private ScaleListingUserConfService userConfService;

    @Autowired
    private ScaleUserResultRecordService scaleUserResultRecordService;

    @Autowired
    private ScaleService scaleService;

    @Autowired
    private ScaleListingService scaleListingService;

    @Autowired
    private ScaleListingUserRecordService scaleListingUserRecordService;

    @Autowired
    private ScaleEvaluationIntroPublisher scaleEvaluationIntroPublisher;

    @Autowired
    NotifyService notifyService;

    @Autowired
    HttpJobService httpJobService;

    @Autowired
    private RequestExecutor requestExecutor;

    @Autowired
    NotifyConvert notifyConvert;

    @Autowired
    OrderService orderService;
    @Resource
    private ScaleCombinationService scaleCombinationService;
    @Resource
    private ScaleUserResultHandler scaleUserResultHandler;

    @Override
    public boolean vaildScaleUserResultRecord(Long userId, Long scaleId) {
        return scaleUserResultMapper.vaildScaleUserResultRecord(userId, scaleId);
    }

    @Override
    public boolean validScaleUserResultComplete(String orderNo, Long scaleId) {
        return scaleUserResultMapper.exists(new LambdaQueryWrapper<ScaleUserResult>()
                .eq(ScaleUserResult::getOrderNo, orderNo)
                .eq(ScaleUserResult::getScaleId,scaleId)
        );
    }

    @Override
    public boolean vaildScaleUserResultRecordEmpty(List<ScaleUserResultRecord> recordList) {
        return CollUtil.isEmpty(recordList) ? true : false;
    }

    @Override
    public boolean vaildScaleEvaluationAgain(ScaleUserResult scaleUserResult, Long userId, String orderNo, ScaleListingUserConf userConf) {
        boolean checkResult = true;
        Long listingId = scaleUserResult.getScaleListingId();
        Long scaleId = scaleUserResult.getScaleId();
        if (ObjUtil.isNotNull(userConf) && userConf.getAllowRepeat()) {
            return checkResult;
        }
        //页面展示,则不允许重复测评
        ScaleUserResult userResult = scaleUserResultMapper.findLastUserResult(listingId, scaleId, userId, orderNo);
        if (ObjectUtil.isNotNull(userResult)) {
            checkResult = false;
        }
        return checkResult;
    }

    @Override
    public ScaleListing findByListingId(Long listingId) {
        return scaleListingService.getById(listingId);
    }

    @Override
    public boolean vaildScaleListingEnabled(ScaleListing scaleListing) {
        if (ObjUtil.isNull(scaleListing)) {
            return true;
        }
        return !scaleListing.getEnable();
    }

    @Override
    public void validAnswerSort(Long scaleId, Long scaleListingId, String orderNo, Long userId) {
        if(scaleListingId == null){
            return;
        }
        //获取记录
        ScaleListing scaleListing = scaleListingService.getById(scaleListingId);
        Long targetId = scaleListing.getTargetId();
        Integer type = scaleListing.getType();
        //只校验组合量表答题顺序
        if(type == 2){
            ScaleCombinationQueryDTO scaleCombinationQuery = scaleCombinationService.findByScaleId(targetId);
            Integer answerSortType = scaleCombinationQuery.getType();
            //如果是依次作答，则获取状态为未完成的第一条
            List<ScaleQueryDTO> details = scaleCombinationQuery.getDetails();
            boolean check = true;
            if(CollUtil.isNotEmpty(details) && answerSortType == 1){
                if(StrUtil.isBlank(orderNo)){
                    check = details.get(0).getId().equals(scaleId);
                }else{
                    ScaleQueryDTO dto = details.stream().filter(detail ->
                            !scaleUserResultMapper.exists(new LambdaQueryWrapper<ScaleUserResult>()
                                    .eq(ScaleUserResult::getUserId, userId)
                                    .eq(ScaleUserResult::getScaleId, detail.getId())
                                    .eq(ScaleUserResult::getOrderNo, orderNo))).findFirst().orElse(null);
                    if(dto == null){
                        throw new BusinessException("已完成答题，请勿重复作答");
                    }else{
                        check = dto.getId().equals(scaleId);
                    }
                }
            }
            if(!check){
                throw new BusinessException("请依次作答");
            }
        }
    }

    public ScaleListingUserConf findByListingIdAndlistingUserId(Long listingId, Long listingUserId) {
        if (ObjUtil.isNull(listingId)) {
            return null;
        }
        //如果是页面分发则无配置信息
        if (!scaleListingService.vaildShowTypeOfDistribution(listingId)) {
            return null;
        }
        ScaleListingUserRecord userRecord = scaleListingUserRecordService.getById(listingUserId);
        if (ObjUtil.isNull(userRecord)) {
            return null;
        }
        Long userConfId = userRecord.getUserConfId();
        return userConfService.getById(userConfId);
    }

    @Override
    public boolean vaildUserConfEvaluationTimeOut(ScaleListingUserConf userConf) {
        return userConfService.vaildUserConfEvaluationTimeOut(userConf);
    }

    @Override
    public boolean vaildScaleTimeLimit(ScaleUserResult scaleUserResult, ScaleListingUserConf userConf) {
        Long scaleId = scaleUserResult.getScaleId();
        LocalDateTime startTime = scaleUserResult.getStartTime();
        LocalDateTime endTime = scaleUserResult.getEndTime();
        //不限制作答时间
        if (ObjUtil.isNotNull(userConf) && !userConf.getAllowTimeLimit()) {
            return false;
        }
        return this.checkTimeOutOfRange(scaleId, startTime, endTime);
    }

    private boolean checkTimeOutOfRange(Long scaleId, LocalDateTime startTime, LocalDateTime endTime) {
        //默认超出时限
        boolean checkResult = true;
        //获取用户量表答题测评时间(秒)
        Long diff = this.getTimeDiff(startTime, endTime);
        Scale scale = scaleService.getById(scaleId);
        Integer minTimeLimit = ObjUtil.isNotNull(scale) ? ObjectUtil.defaultIfNull(scale.getMinTimeLimit(), 0) : 0;
        Integer maxTimeLimit = ObjUtil.isNotNull(scale) ? ObjectUtil.defaultIfNull(scale.getMaxTimeLimit(), 0) : 0;
        // todo 判断量表设置的时限是否超出限制,false-未超出限时要求,true-超出限时要求
        if ((diff >= minTimeLimit && diff <= maxTimeLimit) || diff > minTimeLimit) {
            checkResult = false;
        }
        return checkResult;
    }

    private Long getTimeDiff(LocalDateTime startTime, LocalDateTime endTime) {
        startTime = ObjectUtil.defaultIfNull(startTime, LocalDateTime.now());
        endTime = ObjectUtil.defaultIfNull(endTime, LocalDateTime.now());
        Long diff = DateUtil.between(new DateTime(startTime), new DateTime(endTime), DateUnit.SECOND);
        return diff;
    }

    @Override
    public ScaleUserResult create(ScaleUserResult scaleUserResult, List<ScaleUserResultRecord> resultRecordList) {
        Long userId = scaleUserResult.getUserId();
        scaleUserResult.setStartTime(ObjUtil.defaultIfNull(scaleUserResult.getStartTime(), LocalDateTime.now()));
        scaleUserResult.setEndTime(ObjUtil.defaultIfNull(scaleUserResult.getEndTime(), LocalDateTime.now()));
        scaleUserResult.setUserId(ObjUtil.defaultIfNull(scaleUserResult.getUserId(), userId));
        scaleUserResultMapper.insert(scaleUserResult);
        scaleUserResultRecordService.create(scaleUserResult.getId(), resultRecordList);

        // 生成报告
        scaleEvaluationIntroPublisher.publishEvent(new ScaleEvaluationIntroEvent(scaleUserResult.getId(), userId));
        return scaleUserResult;
    }

    @Override
    public void notifyUserResult(Order order, ScaleUserResult scaleUserResult) {
        // 推送测评结果
        try {
            if (StrUtil.isNotBlank(order.getTerminalSerialNo())) {
                EvaluationRecordNotifyInput evaluationRecordNotifyInput = notifyConvert.scaleResultToEvaluationRecordNotifyInput(scaleUserResult);
                evaluationRecordNotifyInput.setEvaluationRecordNo(scaleUserResult.getId().toString());
                evaluationRecordNotifyInput.setOrderNo(order.getOrderNo());
                evaluationRecordNotifyInput.setTerminalSerialNo(order.getTerminalSerialNo());
                evaluationRecordNotifyInput.setScaleCode(scaleService.getById(scaleUserResult.getScaleId()).getCode());
                HttpJob httpJob = httpJobService.createJob(evaluationRecordNotifyInput.getEvaluationRecordNo(),
                        TerminalConfConstant.ORDER_EVALUATION_RECORD_NOTIFY,
                        HttpJobStatusEnum.WAIT_SCHEDULE.getStatus(),
                        JSONObject.getInstance().toJSONString(evaluationRecordNotifyInput));
                requestExecutor.execute(httpJob);
            }
        } catch (Exception e) {
            log.error("推送测评结果异常.", e);
        }
    }


    @Override
    public void delete(Long resultId) {
        scaleUserResultMapper.deleteById(resultId);
    }

    @Override
    public Page<ScaleUserResultVO> selectPage(ScaleUserResultParamDTO paramDTO) {
        return Page.doSelectPage(() -> scaleUserResultMapper.getList(paramDTO));
    }

    @Override
    public ScaleUserResultVO detail(Long resultId) {
        List<ScaleUserResultVO> list = scaleUserResultMapper.getList(ScaleUserResultParamDTO.builder().id(resultId).build());
        if(CollUtil.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<ScaleUserResult> selectByOrderNo(String orderNo) {
        return scaleUserResultMapper.selectByOrderNo(orderNo);
    }

    @Override
    public void export(HttpServletResponse response, ScaleUserResultParamDTO searchParam) {
        List<ScaleUserResultVO> list = scaleUserResultMapper.getList(searchParam);
        JSONObject instance = JSONObject.getInstance();
        List<ScaleUserResultExcelDataDTO> excelList = instance.parseObject(instance.toJSONString(list), new TargetType<>() {});
        scaleUserResultHandler.exportExcel(response, "测评记录导出", excelList);
    }
}
