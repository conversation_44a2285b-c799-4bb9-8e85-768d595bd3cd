package com.wftk.scale.biz.dto.listing;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ScaleListedQueryDTO
 * @Description: 量表已上架信息传输实体
 * @Author: mq
 * @Date: 2024-10-29 16:05
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleListedQueryDTO implements Serializable {

    /**
     * 上架ID
     */
    private Long id;

    /**
     *  用户ID
     */
    private Long userId;

    /**
     *  上下架ID
     */
    private Long scaleListingId;

    /**
     *  分发
     */
    private Long listingUserId;

    /**
     * 封面图url
     */
    @RFile(role = FileConstant.FILE_SCALE_PUB_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    private String cover;

    /**
     * 可能是量表ID，也可能是组合量表ID
     */
    private Long targetId;

    /**
     * 可能是量表名称，也可能是组合量表名称
     */
    private String targetName;

    /**
     * 量表类型
     */
    private Long targetType;

    /**
     * 量表类型名称
     */
    private String targetTypeName;

    /**
     * 量表问题数量或者关联的量表数量
     */
    private Integer numOfQuestionOrScale;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 详情
     */
    private List<ScaleQueryDTO> details;

    /**
     * 类型: 1.量表; 2.组合量表;
     */
    private Integer type;

    /**
     * 测评方式 1依次测评 2选择测评
     */
    private Integer evaluationType;

    /**
     * 终端Code
     */
    private String terminalCode;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 呈现方式: 1.页面展示; 2.用户分发;
     */
    private Integer showType;

    /**
     * 报告地址，多个用逗号分隔
     */
    private String reportUrl;

    /**
     * 量表API路径
     */
    private String api;

    /**
     * 量表服务费原价, 单位:分
     */
    private Integer originalPrice;

    /**
     * 量表服务费优惠后价格，单位: 分
     */
    private Integer price;

    /**
     * 上架状态: 0.未上架; 1.已上架;
     */
    private Integer status;

    /**
     * 是否启用: 0.未启用; 1.已开启; 注意：此字段控制当前上架的量表是否可做业务（当值为0时用户即使买了也不能做，量表本身的enable字段不能限制用户做业务）
     */
    private Boolean enable;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 删除标记
     */
    private Boolean deleted;
}
