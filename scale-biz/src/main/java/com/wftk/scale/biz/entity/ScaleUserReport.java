package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05 11:14:30
 */
@Data
@TableName("scale_user_report")
public class ScaleUserReport implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 测评记录ID
     */
    private Long resultId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 量表名称
     */
    private String scaleName;

    /**
     * 用户信息
     */
    private String userInfo;

    /**
     * 总分
     */
    private String totalScore;

    /**
     * 平均分
     */
    private String avgScore;

    /**
     * 阳性数量
     */
    private String positiveCount;

    /**
     * 因子分析
     */
    private String factorAnalysis;

    /**
     * 因子得分
     */
    private String factorScore;

    /**
     * 阅读须知
     */
    private String readingInstructions;


}
