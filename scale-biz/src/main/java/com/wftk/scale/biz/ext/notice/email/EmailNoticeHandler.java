package com.wftk.scale.biz.ext.notice.email;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.wftk.jackson.core.JSONObject;
import com.wftk.message.spring.boot.autoconfigure.ext.properties.MailProperties;
import com.wftk.scale.biz.entity.NoticeMessage;
import com.wftk.scale.biz.ext.notice.NoticeHandler;
import com.wftk.scale.biz.ext.notice.NoticeRequest;
import com.wftk.scale.biz.ext.notice.NoticeResult;
import com.wftk.scale.biz.ext.notice.enums.NoticeTypeEnum;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;

import java.nio.charset.StandardCharsets;

@Slf4j
public class EmailNoticeHandler extends NoticeHandler<EmailNoticeRequest> {

    private final JavaMailSender javaMailSender;
    private final MailProperties mailProperties;

    public EmailNoticeHandler(JavaMailSender javaMailSender,MailProperties mailProperties) {
        super(NoticeTypeEnum.EMAIL);
        this.javaMailSender = javaMailSender;
        this.mailProperties = mailProperties;
    }

    @Override
    public NoticeResult sendNotice(EmailNoticeRequest emailRequest) {
        try {
            Assert.notBlank(emailRequest.getContent(), "邮件内容不能为空");
            Assert.notBlank(emailRequest.getSubject(), "邮件标题不能为空");
            Assert.notBlank(emailRequest.getAddress(), "邮件地址不能为空");
            MimeMessage mimeMessage = javaMailSender.createMimeMessage();
            MimeMessageHelper message = new MimeMessageHelper(mimeMessage, StandardCharsets.UTF_8.name());
            message.setText(emailRequest.getContent());
            message.setSubject(emailRequest.getSubject());
            message.setTo(emailRequest.getAddress());
            message.setFrom(mailProperties.getUsername());
            javaMailSender.send(mimeMessage);
            return NoticeResult.builder().ok(true).build();
        } catch (Exception e) {
            log.error("send e-mail is error. ", e);
            return NoticeResult.builder().ok(false).message(e.getMessage()).build();
        }
    }

    @Override
    public NoticeRequest buildNoticeRequest(NoticeMessage noticeMessage) {
        String content = noticeMessage.getContent();
        if (StrUtil.isBlank( content)) {
            return null;
        }
        return JSONObject.getInstance().parseObject(content, EmailNoticeRequest.class);
    }
}
