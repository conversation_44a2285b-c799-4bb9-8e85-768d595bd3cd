package com.wftk.scale.biz.manager.job.registry;


import com.wftk.scale.biz.manager.job.action.RequestAction;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/3/7 10:34
 */
public class DefaultActionRegistry implements ActionRegistry{

    private final Map<String,RequestAction> requestActionMap;

    public DefaultActionRegistry(Map<String,RequestAction> requestActionMap) {
        this.requestActionMap = requestActionMap;
    }


    @Override
    public RequestAction get(String var1) {
        return requestActionMap.get(var1);
    }

    @Override
    public Map<String,RequestAction> getAll() {
        return this.requestActionMap;
    }
}
