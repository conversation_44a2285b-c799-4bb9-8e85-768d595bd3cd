package com.wftk.scale.biz.constant.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @createDate 2025/1/3 14:54
 */
@Getter
public enum HttpJobStatusEnum {

    INIT(0, "初始化"),
    WAIT_SCHEDULE(1,"待调度"),
    SUCCESS(2,"成功"),
    FAIL(10,"失败")
    ;
    private Integer status;

    private String desc;

    HttpJobStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
