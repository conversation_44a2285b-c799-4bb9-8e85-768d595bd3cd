package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.user.sysuser.SysUserCreateDTO;
import com.wftk.scale.biz.dto.user.sysuser.SysUserQueryDTO;
import com.wftk.scale.biz.dto.user.sysuser.SysUserUpdateDTO;
import com.wftk.scale.biz.entity.SysUser;
import com.wftk.scale.biz.vo.SysUserDetailVO;
import com.wftk.scale.biz.vo.SysUserVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.lang.Nullable;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 系统管理员 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface SysUserService extends IService<SysUser> {

    /**
     * 根据账号查询
     * @param account
     * @param enable
     * @return
     */
    SysUser findByAccount(String account, @Nullable Boolean enable);

    void create(SysUserCreateDTO dto);

    void update(SysUserUpdateDTO dto);

    void delete(Long id);

    SysUserDetailVO detailById(Long id);

    Page<SysUserVO> getList(SysUserQueryDTO dto);

    void export(SysUserQueryDTO dto, HttpServletResponse response);

    void downTemplate(HttpServletResponse response);

    void importExcelData(MultipartFile file);
}
