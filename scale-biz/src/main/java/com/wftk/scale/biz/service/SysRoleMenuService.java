package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.dto.user.rolemenu.SysRoleMenuCreateDTO;
import com.wftk.scale.biz.entity.SysRoleMenu;
import com.wftk.scale.biz.vo.SysRoleMenuVO;

import java.util.List;

/**
 * <p>
 * 菜单权限表（角色+租户套餐） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface SysRoleMenuService extends IService<SysRoleMenu> {

    List<SysRoleMenuVO> getList(Long menuId);

    void create(SysRoleMenuCreateDTO sysRoleMenuCreateDTO);
}
