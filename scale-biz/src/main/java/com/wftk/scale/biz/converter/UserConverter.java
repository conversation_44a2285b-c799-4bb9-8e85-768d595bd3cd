package com.wftk.scale.biz.converter;

import com.wftk.scale.biz.dto.user.UserChangeDTO;
import com.wftk.scale.biz.dto.user.UserCreateDTO;
import com.wftk.scale.biz.entity.User;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @createDate 2024/11/26 19:41
 */
@Mapper(componentModel = "spring")
public interface UserConverter {

    User userCreateDTOToEntity(UserCreateDTO userCreateDTO);

    User userChangeDTOToEntity(UserChangeDTO userChangeDTO);
}
