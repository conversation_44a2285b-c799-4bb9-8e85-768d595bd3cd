package com.wftk.scale.biz.converter;

import com.wftk.scale.biz.dto.notify.EvaluationRecordNotifyInput;
import com.wftk.scale.biz.dto.notify.OrderStautsNotifyInput;
import com.wftk.scale.biz.dto.notify.UserReportNotifyInput;
import com.wftk.scale.biz.entity.Order;
import com.wftk.scale.biz.entity.ScaleUserResult;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @createDate 2024/12/18 20:30
 */
@Mapper(componentModel = "spring")
public interface NotifyConvert {

    UserReportNotifyInput orderToUserReportNotifyInput(Order order);

    EvaluationRecordNotifyInput scaleResultToEvaluationRecordNotifyInput(ScaleUserResult scaleUserResult);

    OrderStautsNotifyInput orderToOrderStautsNotifyInput(Order order);
}
