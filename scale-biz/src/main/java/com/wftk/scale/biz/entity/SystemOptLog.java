package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@TableName("system_opt_log")
@Data
@Accessors(chain = true)
public class SystemOptLog {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户类型 1业务用户 2管理用户
     */
    private Integer userType;
    /**
     * ip
     */
    private String ip;
    /**
     * 模块
     */
    private String module;
    /**
     * 操作类型
     */
    private OptType optType;
    /**
     * 请求方式
     */
    private String requestMethod;
    /**
     * 请求参数
     */
    private String params;
    /**
     * 请求接口
     */
    private String uri;
    /**
     * 描述
     */
    private String description;
    /**
     * 操作时间
     */
    private LocalDateTime optTime;
    /**
     * 删除标识
     */
    private Boolean deleted;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 浏览器
     */
    private String userAgent;
    /**
     * 租户
     */
    private String tenantId;
}