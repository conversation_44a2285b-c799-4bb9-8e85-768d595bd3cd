package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 量表上架触达用户配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22 14:48:13
 */
@TableName("scale_listing_user_conf")
public class ScaleListingUserConf implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 上下架id
     */
    private Long scaleListingId;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 截止开始时间
     */
    private LocalDateTime startTime;

    /**
     * 截止结束时间
     */
    private LocalDateTime endTime;

    /**
     * 测评类型: 1.普通测��; 2.团体测评;
     */
    private Integer type;

    /**
     * 是否允许重复测试: 0.否; 1.是;
     */
    private Boolean allowRepeat;

    /**
     * 是否允许获取报告: 0.不允许; 1.允许;
     */
    private Boolean allowGetReport;

    /**
     * 是否允许通知: 0.不允许; 1.允许;
     */
    private Boolean allowNotify;

    /**
     * 订单是否免费: 0.否; 1.是;
     */
    private Boolean orderFree;

    /**
     * 是否允许限制作答时间: 0.否; 1.是;
     */
    private Boolean allowTimeLimit;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getScaleListingId() {
        return scaleListingId;
    }

    public void setScaleListingId(Long scaleListingId) {
        this.scaleListingId = scaleListingId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Boolean getAllowRepeat() {
        return allowRepeat;
    }

    public void setAllowRepeat(Boolean allowRepeat) {
        this.allowRepeat = allowRepeat;
    }

    public Boolean getAllowGetReport() {
        return allowGetReport;
    }

    public void setAllowGetReport(Boolean allowGetReport) {
        this.allowGetReport = allowGetReport;
    }

    public Boolean getAllowNotify() {
        return allowNotify;
    }

    public void setAllowNotify(Boolean allowNotify) {
        this.allowNotify = allowNotify;
    }

    public Boolean getOrderFree() {
        return orderFree;
    }

    public void setOrderFree(Boolean orderFree) {
        this.orderFree = orderFree;
    }

    public Boolean getAllowTimeLimit() {
        return allowTimeLimit;
    }

    public void setAllowTimeLimit(Boolean allowTimeLimit) {
        this.allowTimeLimit = allowTimeLimit;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "ScaleListingUserConf{" +
            "id = " + id +
            ", scaleListingId = " + scaleListingId +
            ", name = " + name +
            ", startTime = " + startTime +
            ", endTime = " + endTime +
            ", type = " + type +
            ", allowRepeat = " + allowRepeat +
            ", allowGetReport = " + allowGetReport +
            ", allowNotify = " + allowNotify +
            ", orderFree = " + orderFree +
            ", allowTimeLimit = " + allowTimeLimit +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
            ", deleted = " + deleted +
            ", createBy = " + createBy +
            ", updateBy = " + updateBy +
            ", tenantId = " + tenantId +
        "}";
    }
}
