package com.wftk.scale.biz.dto.scale;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <p>
 * 协议内容表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02 17:20:42
 */
@Data
public class AgreementQueryDTO{

    private Long id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 协议代码
     */
    private String code;

    /**
     * 终端
     */
    private String terminal;
}
