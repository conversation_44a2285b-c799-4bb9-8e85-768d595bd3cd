package com.wftk.scale.biz.event.publisher;

import com.wftk.scale.biz.event.SysUserDeleteEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createDate 2024/12/4 17:27
 */
@Component
public class SysUserDeletePublisher implements BaseEventPublisher<SysUserDeleteEvent> {

    private final ApplicationEventPublisher applicationEventPublisher;

    public SysUserDeletePublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Override
    public void publishEvent(SysUserDeleteEvent sysUserDeleteEvent) {
        applicationEventPublisher.publishEvent(sysUserDeleteEvent);
    }
}
