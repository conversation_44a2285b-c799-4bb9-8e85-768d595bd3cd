package com.wftk.scale.biz.manager.report.actuator;

import com.wftk.scale.biz.constant.ScaleReportConfItemConstant;
import com.wftk.scale.biz.entity.ScaleReportConfItem;
import com.wftk.scale.biz.manager.report.dto.base.SuggestionDTO;
import com.wftk.scale.biz.service.ScaleReportConfItemService;
import com.wftk.scale.biz.util.ScopeUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/9/10 20:38
 */
@Slf4j
@Component
public class SuggestionActuator {
    private final String SUGGESTION_CODE = ScaleReportConfItemConstant.SUGGESTION_CODE;

    @Resource
    ScaleReportConfItemService scaleReportConfItemService;

    public SuggestionDTO doSuggestion(Long scaleReportConfId,BigDecimal score,String factorName,Long factorId) {
        SuggestionDTO suggestionDTO = new SuggestionDTO();
        // 查找建议
        // 根据因子和全部查询
        List<ScaleReportConfItem> scaleReportConfItems = scaleReportConfItemService.findByReportConfIdAndFactorId(scaleReportConfId, List.of(SUGGESTION_CODE),factorId);
        for (ScaleReportConfItem scaleReportConfItem : scaleReportConfItems) {
            String itemDescribe = scaleReportConfItem.getItemDescribe();
            Boolean inScope = ScopeUtil.inScope(ScopeUtil.parseSectionStr(itemDescribe), score);
            if (inScope) {
                suggestionDTO.setFactorName(factorName);
                suggestionDTO.setSuggestion(scaleReportConfItem.getItemValue());
                suggestionDTO.setFilePath(scaleReportConfItem.getItemFilePath());
                break;
            }
        }
        return suggestionDTO;
    }





}
