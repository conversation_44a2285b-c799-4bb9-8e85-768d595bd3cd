package com.wftk.scale.biz.config;

import cn.hutool.core.util.StrUtil;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.ext.dto.AuthenticationInfoDTO;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.jackson.core.JSONObject;
import com.wftk.opt.log.spring.boot.autoconfigure.builder.MethodOptLogBuilder;
import com.wftk.opt.log.spring.boot.autoconfigure.log.DefaultSysOptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.writer.OptLogWriter;
import com.wftk.scale.biz.constant.enums.UserTypeEnum;
import com.wftk.scale.biz.entity.SysUser;
import com.wftk.scale.biz.entity.SystemOptLog;
import com.wftk.scale.biz.entity.User;
import com.wftk.scale.biz.mapper.SystemOptLogMapper;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
public class SystemLogConfig {

    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private SystemOptLogMapper systemOptLogMapper;



    private static final String UNKNOWN_STR = "unknown";

    @Bean
    public MethodOptLogBuilder.UserInfoHolder userInfoHolder() {
        return new MethodOptLogBuilder.UserInfoHolder() {
            @Override
            public String getUserId() {
                //登录认证接口,authentication值为null,userId、userName、userType不能为空，BaseOptLogBuilder有校验
                Authentication authentication = AuthenticationHolder.getAuthentication();
                if(authentication == null) {
                    return UNKNOWN_STR;
                }
                Long id = authentication.getAuthUser().getId();
                return id == null ? UNKNOWN_STR : id.toString();
            }
            @Override
            public String getUserName() {
                Authentication authentication = AuthenticationHolder.getAuthentication();
                if(authentication == null) {
                    return UNKNOWN_STR;
                }
                Object obj = authentication.getAuthUser().getUser();
                if(obj == null){
                    return UNKNOWN_STR;
                }
                if(obj instanceof SysUser sysUser){
                    return sysUser.getName();
                }
                if(obj instanceof User user){
                    return user.getName();
                }
                return UNKNOWN_STR;
            }
            @Override
            public Integer getUserType() {
                Authentication authentication = AuthenticationHolder.getAuthentication();
                if(authentication == null) {
                    return UserTypeEnum.UNKNOWN_USER.getValue();
                }
                Object obj = authentication.getAuthUser().getUser();
                if(obj == null){
                    return UserTypeEnum.UNKNOWN_USER.getValue();
                }
                if(obj instanceof SysUser){
                    //管理用户
                    return UserTypeEnum.MANAGE_USER.getValue();
                }
                if(obj instanceof User){
                    //业务用户
                    return UserTypeEnum.BIZ_UER.getValue();
                }
                return UserTypeEnum.UNKNOWN_USER.getValue();
            }
        };
    }

    @Bean
    public OptLogWriter<DefaultSysOptLog> optLogWriter(){
        return log -> threadPoolTaskExecutor.execute(() -> {
            SystemOptLog systemOptLog = new SystemOptLog();
            BeanUtils.copyProperties(log,systemOptLog);
            systemOptLog.setRequestMethod(log.getRequestMethod().getLabel());
            Map<String, String> headers = log.getHeaders();
            if(headers != null){
                systemOptLog.setUserAgent(headers.get("user-agent"));
            }
            //脱敏密码
            String params = log.getParams();
            if(StrUtil.isNotBlank(params) && log.getUri().endsWith("/auth/token")){
                JSONObject instance = JSONObject.getInstance();
                Map<String, Object> paramsMap = instance.parseObject(params, Map.class);
                AuthenticationInfoDTO authenticationInfo = instance.parseObject(
                        instance.toJSONString(paramsMap.get("authenticationInfoDTO")), AuthenticationInfoDTO.class);
                authenticationInfo.setPassword(null);
                paramsMap.put("authenticationInfoDTO", authenticationInfo);
                systemOptLog.setParams(instance.toJSONString(paramsMap));
            }
            systemOptLogMapper.insert(systemOptLog);
        });
    }
}
