package com.wftk.scale.biz.dto.scale;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: ScaleUserResultParamDTO
 * @Description: 测评记录筛选参数传输实体
 * @Author: mq
 * @Date: 2024-11-07 17:48
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleUserResultParamDTO implements Serializable {

    private Long id;
    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 量表名称
     */
    private String scaleName;

    /**
     * 终端ID
     */
    private Long terminalId;

    /**
     * 终端编码
     */
    private String terminalCode;
}
