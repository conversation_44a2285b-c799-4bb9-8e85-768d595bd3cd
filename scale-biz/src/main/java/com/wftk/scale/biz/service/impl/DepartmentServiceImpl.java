package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.scale.biz.converter.DepartmentConverter;
import com.wftk.scale.biz.dto.user.DepartmentCreateDTO;
import com.wftk.scale.biz.dto.user.DepartmentQueryDTO;
import com.wftk.scale.biz.dto.user.DepartmentUpdateDTO;
import com.wftk.scale.biz.entity.Department;
import com.wftk.scale.biz.entity.DepartmentTerminal;
import com.wftk.scale.biz.mapper.DepartmentMapper;
import com.wftk.scale.biz.service.DepartmentService;
import com.wftk.scale.biz.service.DepartmentTerminalService;
import com.wftk.scale.biz.service.UserService;
import com.wftk.scale.biz.util.ParentPathUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 部门表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
@Slf4j
public class DepartmentServiceImpl extends ServiceImpl<DepartmentMapper, Department> implements DepartmentService {

    @Autowired
    DepartmentMapper departmentMapper;

    @Autowired
    DepartmentTerminalService departmentTerminalService;

    @Autowired
    DepartmentConverter departmentConverter;
    @Resource
    private UserService userService;

    /**
     * 机构路径上存的是code，code不能重复
     */
    private void checkCodeRepeat(String code){
        if(StrUtil.isBlank(code)){
            return;
        }
        if(departmentMapper.exists(new LambdaQueryWrapper<Department>().eq(Department::getCode, code))){
            throw new BusinessException("机构编码已存在");
        }
    }

    @Transactional
    @Override
    public void createDepartment(DepartmentCreateDTO departmentCreateDTO) {
        Department department = departmentConverter.departmentCreateDTOToEntity(departmentCreateDTO);
        String parentPath;
        checkCodeRepeat(department.getCode());
        // 查询父级部门
        if(department.getParentId() != null && department.getParentId() != 0){
            Department parentDepartment = departmentMapper.selectById(department.getParentId());
            if(parentDepartment == null){
                throw new BusinessException("父级机构不存在");
            }
            // 判断父级机构是否已绑定终端 已绑定不能创建
            Boolean bindTerminal = departmentMapper.validDepartmentBindTerminal(parentDepartment.getId());
            if(bindTerminal){
                log.error("parentDepartment[{}] already binding terminal, not allow create",parentDepartment.getId());
                throw new BusinessException("父级机构已绑定终端，不允许创建");
            }
            parentPath = ParentPathUtils.buildParentPath(parentDepartment.getParentPath(), department.getCode());
        }else {
            // 默认为0
            department.setParentId(0L);
            parentPath = ParentPathUtils.buildParentPath(department.getCode(),"");
        }
        // 设置父级路径
        department.setParentPath(parentPath);
        save(department);

        // 判断终端编码是否不为空 不为空为机构绑定终端
        if(departmentCreateDTO.getTerminalCode() != null && !departmentCreateDTO.getTerminalCode().isEmpty()){
            for (String terminalCode:departmentCreateDTO.getTerminalCode()) {
                // 校验终端是否已绑定机构
                Boolean validTerminalBindDepartment = departmentMapper.validTerminalBindDepartment(terminalCode);
                if(validTerminalBindDepartment){
                    log.error("terminalCode[{}] already binding department, not allow binding",terminalCode);
                    throw new BusinessException("终端已绑定机构，不允许创建");
                }
                DepartmentTerminal departmentTerminal = new DepartmentTerminal();
                departmentTerminal.setDepartmentId(department.getId());
                departmentTerminal.setTerminalCode(terminalCode);
                departmentTerminalService.save(departmentTerminal);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDepartment(DepartmentUpdateDTO departmentUpdateDTO) {
        Department department = departmentConverter.departmentUpdateDTOToEntity(departmentUpdateDTO);
        //级联查询机构下是否有用户，如果有用用户，则不允许被禁用
        Department oldDepartment = departmentMapper.selectById(department.getId());
        if(oldDepartment == null){
            throw new BusinessException("修改机构不存在");
        }
        if(!Objects.equals(oldDepartment.getCode(), department.getCode())){
            checkCodeRepeat(department.getCode());
        }
        List<Department> childrenByParentIdList = null;
        boolean isUpdateChildren = false;
        //判断状态是否修改
        if(!oldDepartment.getEnable().equals(department.getEnable())){
            //查询当前机构下的所有子机构，包含当前机构
            childrenByParentIdList = new ArrayList<>(departmentMapper.findChildrenByParentId(oldDepartment.getId()));
            List<Long> orgIds = childrenByParentIdList.stream().map(Department::getId).toList();
            if(!department.getEnable() && !orgIds.isEmpty()){
                if(userService.checkDepartmentUserStatus(orgIds, true)){
                    throw new BusinessException("该机构下存在启用状态的用户，不允许禁用");
                }
            }
            childrenByParentIdList = childrenByParentIdList.stream().peek(it -> it.setEnable(department.getEnable())).collect(Collectors.toList());
            isUpdateChildren = true;
        }
        //code修改，则需要重新生成parentPath
        String code = department.getCode();
        if(!code.equals(oldDepartment.getCode())){
            if(CollUtil.isEmpty(childrenByParentIdList)){
                childrenByParentIdList = new ArrayList<>(departmentMapper.findChildrenByParentId(oldDepartment.getId()));
            }
            //修改下parentPath
            String newPath = replaceLastCode(oldDepartment.getParentPath(), department.getCode());
            department.setParentPath(newPath);
            Map<Long, String> idToPathMap = new HashMap<>(16);
            idToPathMap.put(department.getId(), newPath);
            for (int i = 0 ; i < childrenByParentIdList.size() ; i++){
                Department childDepartment = childrenByParentIdList.get(i);
                if(Objects.equals(childDepartment.getId(), department.getId())){
                    continue;
                }
                Long parentId = childDepartment.getParentId();
                if(idToPathMap.containsKey(parentId)){
                    String path = idToPathMap.get(parentId) + childDepartment.getCode() + ParentPathUtils.SEPARATOR;
                    childDepartment.setParentPath(path);
                    idToPathMap.put(childDepartment.getId(), path);
                }else{
                    childrenByParentIdList.add(childDepartment);
                }
            }
            isUpdateChildren = true;
        }
        departmentMapper.updateById(department);
        if(isUpdateChildren){
            childrenByParentIdList = childrenByParentIdList.stream()
                    .filter(childDepartment -> !Objects.equals(childDepartment.getId(), department.getId())).collect(Collectors.toList());
            departmentMapper.updateById(childrenByParentIdList);
        }
        //如果有下级机构，不允许绑定终端
        boolean exists = departmentMapper.exists(new LambdaQueryWrapper<Department>().eq(Department::getParentId, department.getId()));
        List<String> updateTerminalCodeList = departmentUpdateDTO.getTerminalCode();
        if(CollUtil.isNotEmpty(updateTerminalCodeList)){
            if(exists){
                throw new BusinessException("该机构有子机构，不允许绑定终端");
            }
            Long id = department.getId();
            departmentTerminalService.remove(new LambdaQueryWrapper<DepartmentTerminal>().eq(DepartmentTerminal::getDepartmentId, id));
            for (String terminalCode : updateTerminalCodeList) {
                // 校验终端是否已绑定机构
                Boolean validTerminalBindDepartment = departmentMapper.validTerminalBindDepartment(terminalCode);
                if(validTerminalBindDepartment){
                    log.error("terminalCode:{} already binding department, not allow binding", terminalCode);
                    throw new BusinessException("终端已绑定机构，不允许修改！");
                }
                DepartmentTerminal departmentTerminal = new DepartmentTerminal();
                departmentTerminal.setDepartmentId(department.getId());
                departmentTerminal.setTerminalCode(terminalCode);
                departmentTerminalService.save(departmentTerminal);
            }
        }
    }

    private String replaceLastCode(String oldPath, String code){
        String[] split = oldPath.split("\\|");
        StringBuilder sb = new StringBuilder();
        if(split.length == 0){
            return null;
        }
        if(split.length == 1){
           return code + ParentPathUtils.SEPARATOR;
        }else {
            for (int i = 0 ; i < split.length; i++){
                if(i == split.length - 1){
                    sb.append(code).append(ParentPathUtils.SEPARATOR);
                }else{
                    sb.append(split[i]).append(ParentPathUtils.SEPARATOR);
                }
            }
        }
        return sb.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDepartment(Long id) {
        Department oldDepartment = departmentMapper.selectById(id);
        if(oldDepartment == null){
            throw new BusinessException("删除机构不存在");
        }
        //机构下是否存在用户
        List<Long> orgIds = departmentMapper.findChildrenByParentId(oldDepartment.getId()).stream().map(Department::getId).collect(Collectors.toList());
        orgIds.add(id);
        if(userService.checkDepartmentUserStatus(orgIds, null)){
            throw new BusinessException("该机构下存在用户，不允许删除");
        }
        departmentMapper.deleteByIds(orgIds);
        departmentTerminalService.remove(new LambdaQueryWrapper<DepartmentTerminal>().in(DepartmentTerminal::getDepartmentId, orgIds));
    }

    @Override
    public List<DepartmentQueryDTO> selectTreeList(String name, Integer enable, String terminalCode, String parentName) {
        List<Department> list = departmentMapper.selectTreeList(name, enable, terminalCode);
        if(list.isEmpty()){
            return new ArrayList<>();
        }

        //上级机构
        Set<String> codeSet = new HashSet<>();
        list.forEach(department -> {
            String parentPath = department.getParentPath();
            if(StrUtil.isNotBlank(parentPath)){
                codeSet.addAll(List.of(parentPath.split("\\|")));
            }
        });
        Map<String, String> codeToNameMap = departmentMapper.selectList(new LambdaQueryWrapper<Department>().in(Department::getCode, codeSet)).stream()
                .collect(Collectors.toMap(Department::getCode, Department::getName));
        Map<Long, String> departIdToNameMap = new HashMap<>();
        list.forEach(department -> {
            String parentPath = department.getParentPath();
            if(StrUtil.isNotBlank(parentPath)){
                String str = departIdToNameMap.get(department.getId());
                StringBuilder parentNameStr = new StringBuilder(str == null ? "" : str);
                String[] split = parentPath.split("\\|");
                for (int i = 0 ; i < split.length - 1; i++){
                    parentNameStr.append(",").append(codeToNameMap.get(split[i]));
                }
                departIdToNameMap.put(department.getId(), parentNameStr.toString());
            }
        });
        // 封装树形结构
        return buildDepartmentQueryDTO(departmentConverter.entityToDepartmentQueryDTO(list), departIdToNameMap, parentName);
    }

    @Override
    public Department selectByTerminalCode(String terminalCode) {
        return departmentMapper.selectByTerminalCode(terminalCode);
    }

    /**
     * 转换为树形结构数据
     * @param departments 部门
     * @return 部门
     */
    public List<DepartmentQueryDTO> buildDepartmentQueryDTO(List<DepartmentQueryDTO> departments, Map<Long, String> departIdToNameMap, String parentName) {
        List<DepartmentQueryDTO> returnList = new ArrayList<>();
        if(CollUtil.isEmpty(departments)){
            return returnList;
        }
        //过滤parentName
        if(StrUtil.isNotBlank(parentName)){
            departments = departments.stream().filter(dto -> {
                String parentNameStr = departIdToNameMap.get(dto.getId());
                return !StrUtil.isBlank(parentNameStr) && parentNameStr.contains(parentName);
            }).collect(Collectors.toList());
        }

        Map<Long, DepartmentQueryDTO> map = new HashMap<>(16);
        //将菜单放入map,菜单ID作为key,对象作为value
        departments.forEach(department -> map.put(department.getId(), department));
        //查询机构id 与 终端编码（逗号隔开）的关联关系
        Map<Long, String> collect = departmentTerminalService.getConcatTerminalCodeByDepartmentIds(map.keySet())
                .stream().collect(Collectors.toMap(DepartmentTerminal::getDepartmentId, DepartmentTerminal::getTerminalCode));
        for (DepartmentQueryDTO menu : departments) {
            String terminalCode = collect.get(menu.getId());
            menu.setTerminalCode(StrUtil.isNotBlank(terminalCode) ? List.of(terminalCode.split(",")) : List.of());
            DepartmentQueryDTO department = map.get(menu.getParentId());
            if (department != null) {
                menu.setParentName(department.getName());
                if (department.getChildren() == null) {
                    department.setChildren(new ArrayList<>());
                }
                department.getChildren().add(menu);
            } else {
                returnList.add(menu);
            }
        }
        return returnList;
    }
}
