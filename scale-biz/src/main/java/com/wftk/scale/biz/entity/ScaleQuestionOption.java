package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 问题选项表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Data
@TableName("scale_question_option")
public class ScaleQuestionOption implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private Long questionId;

    private Long scaleId;

    /**
     * 问题选项，就是答案
     */
    private String value;

    /**
     * 标签: 例如（1,2,3,4; A,B,C,D）
     */
    private String label;

    /**
     * 分数
     */
    private Integer score;

    /**
     * 0.阴; 1.阳;
     */
    private Boolean result;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 开启其它（0.未开启，1.未开启）
     */
    private Boolean enableInput;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件地址
     */
    @RFile(role = FileConstant.FILE_SCALE_PUB_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String filePath;

    /**
     * 偏移位置(1.上 2.下 3.左 4.右)
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer offset;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 操作值(转换结果)
     */
    private Integer operateValue;

}
