package com.wftk.scale.biz.valid;

import com.wftk.scale.biz.valid.annoation.RangeConstraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.List;

/**
 * <AUTHOR>
 */
public class RangeValidator implements ConstraintValidator<RangeConstraint, Integer> {

    private List<Integer> validValues;

    @Override
    public boolean isValid(Integer integer, ConstraintValidatorContext constraintValidatorContext) {
        return validValues.contains(integer);
    }

    @Override
    public void initialize(RangeConstraint constraintAnnotation) {
        validValues = List.of(1,2,3);
    }
}
