package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.converter.PositionConverter;
import com.wftk.scale.biz.dto.user.position.PositionAddDTO;
import com.wftk.scale.biz.dto.user.position.PositionQueryDTO;
import com.wftk.scale.biz.dto.user.position.PositionUpdateDTO;
import com.wftk.scale.biz.entity.Position;
import com.wftk.scale.biz.excel.model.PositionExcelDataDTO;
import com.wftk.scale.biz.excel.service.handler.PositionHandler;
import com.wftk.scale.biz.mapper.PositionMapper;
import com.wftk.scale.biz.service.PositionService;
import com.wftk.scale.biz.vo.PositionVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 岗位表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class PositionServiceImpl extends ServiceImpl<PositionMapper, Position> implements PositionService {

    @Resource
    private PositionConverter positionConverter;
    @Resource
    private PositionHandler positionHandler;

    @Override
    public Page<PositionVO> getList(PositionQueryDTO dto) {
        return Page.doSelectPage(() -> queryList(dto))
                .toPage(list -> list.stream()
                        .map(it -> positionConverter.positionToPositionVO(it))
                        .collect(Collectors.toList()));
    }

    private List<Position> queryList(PositionQueryDTO dto) {
        LambdaQueryWrapper<Position> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(dto.getCode())) {
            queryWrapper.eq(Position::getCode, dto.getCode());
        }
        if (StrUtil.isNotBlank(dto.getName())) {
            queryWrapper.like(Position::getName, dto.getName());
        }
        if (Objects.nonNull(dto.getEnable())) {
            queryWrapper.eq(Position::getEnable, dto.getEnable());
        }
        queryWrapper.orderByDesc(Position::getSort);
        queryWrapper.orderByDesc(Position::getCreateTime);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public PositionVO detailById(Long id) {
        if(Objects.isNull(id)){
            throw new NullPointerException("参数id不能为空");
        }
        Position position = baseMapper.selectById(id);
        return position == null ? null : positionConverter.positionToPositionVO(position);
    }

    @Override
    public void add(PositionAddDTO dto) {
        validCodeUnique(dto.getCode());
        baseMapper.insert(positionConverter.positionAddDtoToPosition(dto));
    }

    @Override
    public void update(PositionUpdateDTO dto) {
        Position position = baseMapper.selectById(dto.getId());
        if(position == null){
            throw new BusinessException("岗位不存在");
        }
        if(!Objects.equals(position.getCode(), dto.getCode())){
            validCodeUnique(position.getCode());
        }
        if(!dto.getEnable()){
            validPositionIsUse();
        }
        baseMapper.updateById(positionConverter.positionUpdateDtoToPosition(dto));
    }

    @Override
    public void delete(Long id) {
        validPositionIsUse();
        baseMapper.deleteById(id);
    }

    @Override
    public void export(PositionQueryDTO dto, HttpServletResponse response) {
        List<PositionExcelDataDTO> list = this.list().stream().map(vo -> positionConverter.positionToPositionExcelDataDTO(vo)).collect(Collectors.toList());
        positionHandler.exportExcel(response, "岗位导出", list);
    }

    private void validPositionIsUse(){

    }

    private void validCodeUnique(String code){
        boolean exists = baseMapper.exists(new LambdaQueryWrapper<Position>().eq(Position::getCode, code));
        if(exists){
            throw new BusinessException("岗位编码已存在");
        }
    }
}
