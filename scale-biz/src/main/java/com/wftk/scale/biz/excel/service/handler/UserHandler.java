package com.wftk.scale.biz.excel.service.handler;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.IdGenerator;
import com.wftk.scale.biz.dto.user.UserExcelConvertDTO;
import com.wftk.scale.biz.entity.Department;
import com.wftk.scale.biz.entity.User;
import com.wftk.scale.biz.entity.UserDepartment;
import com.wftk.scale.biz.excel.model.SelectDataListBO;
import com.wftk.scale.biz.excel.model.UserExcelDataDTO;
import com.wftk.scale.biz.excel.utils.AsyncTaskUtils;
import com.wftk.scale.biz.excel.utils.CompletableTask;
import com.wftk.scale.biz.excel.utils.ExcelUtil;
import com.wftk.scale.biz.mapper.DepartmentMapper;
import com.wftk.scale.biz.mapper.UserDepartmentMapper;
import com.wftk.scale.biz.mapper.UserMapper;
import com.wftk.scale.biz.service.UserService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class UserHandler extends ServiceImpl<UserMapper, User> {

    @Resource
    private DepartmentMapper departmentMapper;
    @Resource
    private UserDepartmentMapper userDepartmentMapper;
    @Resource
    private UserService userService;
    @Resource
    private IdGenerator idGenerator;

    public void exportExcel(HttpServletResponse response, String fileName, List<UserExcelConvertDTO> list) {
        List<UserExcelDataDTO> dataList = this.convertToExcelData(list);
        ExcelUtil.write(response, UserExcelDataDTO.class, fileName, dataList);
    }

    public void downloadTemplate(HttpServletResponse response, String fileName){
        List<String> selectDataList = departmentMapper.concatCodeAndName();
        ExcelUtil.write(
                response,
                UserExcelDataDTO.class,
                fileName,
                List.of(UserExcelDataDTO.builder().build()),
                List.of(
                        SelectDataListBO.builder().selectDataIndex(4).selectDataList(List.of("男", "女")).sheetName("dicSexSheet").build(),
                        SelectDataListBO.builder().selectDataIndex(7).selectDataList(selectDataList).sheetName("dicDepartmentSheet").build()
                ));
    }

    public void importExcel(MultipartFile file) throws Exception {
        List<UserExcelDataDTO> list = ExcelUtil.readContainsHeader(file, UserExcelDataDTO.class, this::checkUserExist);
        if(CollUtil.isEmpty(list)){
            return;
        }
        List<UserDepartment> userDepartments = new ArrayList<>(10);
        List<User> userList = list.stream().map(vo -> {
            User user = new User();
            BeanUtils.copyProperties(vo, user);
            long id = idGenerator.nextId();
            user.setId(id);
            user.setPassword(userService.getPassword(user.getAccount()));
            UserDepartment userDepartment = new UserDepartment();
            userDepartment.setDepartmentId(vo.getDepartmentId());
            userDepartment.setUserId(id);
            userDepartments.add(userDepartment);
            return user;
        }).toList();

        Authentication authentication = AuthenticationHolder.getAuthentication();
        AsyncTaskUtils.execute(
                List.of(
                        new CompletableTask(authentication, () -> baseMapper.insert(userList)),
                        new CompletableTask(authentication, () -> userDepartmentMapper.insert(userDepartments))
                )
        );
    }

    private void checkUserExist(List<UserExcelDataDTO> dataList) {
        if(CollUtil.isEmpty(dataList)){
            return;
        }
        //用户的逻辑，同一个机构下 account跟code不能重复,用户所属机构需存在
        Map<String, Long> departmentNameToIdMap = departmentMapper.selectList(new LambdaQueryWrapper<Department>().eq(Department::getEnable, 1)).stream()
                .collect(Collectors.toMap(department -> department.getCode() + "-" + department.getName(), Department::getId));

        Map<String, CodeAndAccountList> map = new HashMap<>(8);
        baseMapper.selectUserToDepartment().forEach(vo -> {
            CodeAndAccountList codeAndAccountList = map.get(vo.getConcatName());
            if(codeAndAccountList == null){
                codeAndAccountList = new CodeAndAccountList();
                map.put(vo.getConcatName(), codeAndAccountList);
            }
            codeAndAccountList.accountList.add(vo.getAccount());
            codeAndAccountList.codeList.add(vo.getCode());
        });
        int i = 2;
        for (UserExcelDataDTO dto : dataList){
            String departmentName = dto.getDepartmentName();
            if(!departmentNameToIdMap.containsKey(departmentName)){
                throw new BusinessException("第" + i + "行数据有误:归属部门不存在");
            }
            dto.setDepartmentId(departmentNameToIdMap.get(departmentName));
            CodeAndAccountList codeAndAccountList = map.get(departmentName);
            if(codeAndAccountList.codeList.contains(dto.getCode())){
                throw new BusinessException("第" + i + "行数据有误:该部门下存在相同的用户编码");
            }
            codeAndAccountList.codeList.add(dto.getCode());
            if(codeAndAccountList.accountList.contains(dto.getAccount())){
                throw new BusinessException("第" + i + "行数据有误:该部门下存在相同的登录账号");
            }
            codeAndAccountList.accountList.add(dto.getAccount());
            i++;
        }
    }

    private List<UserExcelDataDTO> convertToExcelData(List<UserExcelConvertDTO> list) {
        return list.stream().map(user -> {
            UserExcelDataDTO data = UserExcelDataDTO.builder().build();
            BeanUtils.copyProperties(user, data);
            return data;
        }).collect(Collectors.toList());
    }

    private static class CodeAndAccountList{

        private final Set<String> codeList = new HashSet<>(10);

        private final Set<String> accountList = new HashSet<>(10);
    }
}
