package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.distribute.ScaleListingBaseDTO;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordParamDTO;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordQueryDTO;
import com.wftk.scale.biz.entity.ScaleListingUserConf;
import com.wftk.scale.biz.entity.ScaleListingUserRecord;

import java.util.List;

/**
 * <p>
 * 量表分发用户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleListingUserRecordService extends IService<ScaleListingUserRecord> {

    /*
    * @Author: mq
    * @Description: 校验分发用户是否为空
    * @Date: 2024/12/16 17:17
    * @Param: userRecords
    * @return: boolean
    **/
    boolean vaildListingUserRecord(List<ScaleListingUserRecord> userRecords);


    /*
     * @Author: mq
     * @Description: 保存量表分发用户信息
     * @Date: 2024/10/31 14:54
     * @Param: scaleListingBase-量表基本信息
     * @Param: userConfId-分发用户配置ID
     * @Param: userRecords-分发用户
     * @return: void
     **/
    void saveDistributeUser(ScaleListingBaseDTO scaleListingBase, ScaleListingUserConf scaleListingUserConf, List<ScaleListingUserRecord> userRecords) throws Exception;

    /*
     * @Author: mq
     * @Description: 根据ID删除分发用户记录信息
     * @Date: 2024/10/31 10:47
     * @Param: userRecordId-分发用户记录ID
     * @return: void
     **/
    void delete(Long userRecordId);

    /*
     * @Author: mq
     * @Description: 根据条件检索单个量表分发用户记录
     * @Date: 2024/10/31 13:50
     * @Param: scaleListingUserParamDTO-检索条件
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordQueryDTO>
     **/
    Page<ScaleListingUserRecordQueryDTO> getScaleDistributeRecord(ScaleListingUserRecordParamDTO scaleListingUserParamDTO);

    /* 
     * @Author: mq
     * @Description: 根据条件检索组合量表分发用户记录
     * @Date: 2024/10/31 16:00 
     * @Param: scaleListingUserParamDTO  
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordQueryDTO> 
     **/
    Page<ScaleListingUserRecordQueryDTO> getScaleCombinationDistributeRecord(ScaleListingUserRecordParamDTO scaleListingUserParamDTO);

    /**
    * @Author: mq
    * @Description: 根据条件检索量表分发用户记录
    * @Date: 2024/12/6 16:41
    * @Param: scaleListingUserParamDTO
    * @return: Page<ScaleListingUserRecordQueryDTO>
    **/
    Page<ScaleListingUserRecordQueryDTO> getAllScaleDistributeRecord(ScaleListingUserRecordParamDTO scaleListingUserParamDTO);

    ScaleListingUserRecord getScaleDistributeRecordByUserId(Long userId, Long scaleListingId);

}
