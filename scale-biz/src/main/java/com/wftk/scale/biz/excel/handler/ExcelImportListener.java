package com.wftk.scale.biz.excel.handler;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.scale.biz.excel.service.CheckDataIntegrityFunctionService;
import com.wftk.scale.biz.excel.utils.SpringContextUtil;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;

import java.util.Set;
import java.util.List;

/**
 * @ClassName: ExcelImportListener
 * @Description:
 * @Author: mq
 * @Date: 2024/11/29
 * @Version: 1.0
 **/
@Slf4j
public class ExcelImportListener<T> implements ReadListener<T> {

    @Getter
    private final List<T> excelLineResultList = Lists.newArrayList();

    private final CheckDataIntegrityFunctionService<T> checkDataIntegrityFunctionService;

    private final Validator validator;

    public ExcelImportListener(CheckDataIntegrityFunctionService<T> checkDataIntegrityFunctionService) {
        this.checkDataIntegrityFunctionService = checkDataIntegrityFunctionService;
        this.validator = SpringContextUtil.getBean(Validator.class);
    }

    @Override
    public void invoke(T data, AnalysisContext analysisContext) {
        if (log.isDebugEnabled()) {
            log.debug("读取到数据: {}", data);
        }
        //读取的时候就开始校验必要参数，避免内存占用
        Set<ConstraintViolation<T>> validate = validator.validate(data);
        Integer rowIndex = getRowIndex(analysisContext);
        //校验不通过, 不必执行业务逻辑
        if (!validate.isEmpty()) {
            //这里直接抛异常，提示前端，修改文件后重新导入
            throw new BusinessException("文件导入失败,第" + rowIndex + "行数据错误:" + validate.iterator().next().getMessage());
        }
        //转换数据
        if(ObjUtil.isNotNull(data)){
            if(!excelLineResultList.contains(data)){
                excelLineResultList.add(data);
            }
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (CollUtil.isEmpty(excelLineResultList)) {
            return;
        }
        checkDataIntegrityFunctionService.checkDataIntegrity(excelLineResultList);
    }

    private Integer getRowIndex(AnalysisContext analysisContext) {
        return analysisContext.readRowHolder().getRowIndex() + 1;
    }
}
