package com.wftk.scale.biz.excel.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.wftk.scale.biz.excel.converter.SexConverter;
import com.wftk.scale.biz.excel.model.base.BaseExcelDataDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysUserExcelDataDTO extends BaseExcelDataDTO {

    @ExcelProperty("登录账号")
    @NotBlank(message = "登录账号不能为空")
    @Length(max = 50, message = "用户账号长度不能超过50")
    private String account;

    @ExcelProperty("用户姓名")
    @NotNull(message = "用户姓名不能为空")
    @Length(max = 50, message = "用户姓名长度不能超过50")
    private String name;

    @ExcelProperty(value = "出生日期")
    private LocalDate birthday;

    @ExcelProperty(value = "性别", converter = SexConverter.class)
    private Integer sex;

    @ExcelProperty("手机号码")
    @Length(min = 11, max = 11, message = "手机号格式有误")
    @Pattern(regexp = "^(1[3-9])\\d{9}$", message = "手机号格式有误")
    private String tel;

    @ExcelProperty("邮箱")
    @Pattern(regexp = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@[a-zA-Z0-9-]+\\.[a-zA-Z]{2,}$", message = "邮箱格式错误")
    private String email;

    @ExcelProperty("入学年份")
    private LocalDate entryDate;

    @ExcelProperty("归属部门")
    @NotBlank(message = "归属部门不能为空")
    private String departmentName;

    @ExcelProperty("角色名称")
    @NotBlank(message = "角色不能为空")
    private String roleNames;

    @ExcelProperty("身份证号")
    @Length(min = 18, max = 18, message = "身份证号码格式错误")
    @Pattern(regexp = "^[1-9]\\d{5}(?:19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$", message = "身份证号码格式错误")
    private String idCard;

    @ExcelIgnore
    private Long departmentId;
    @ExcelIgnore
    private Long roleId;
}
