package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleTypeDetailDTO;
import com.wftk.scale.biz.entity.Scale;
import com.wftk.scale.biz.entity.ScaleType;
import com.wftk.scale.biz.mapper.ScaleTypeMapper;
import com.wftk.scale.biz.service.ScaleService;
import com.wftk.scale.biz.service.ScaleTypeService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 量表类型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class ScaleTypeServiceImpl extends ServiceImpl<ScaleTypeMapper, ScaleType> implements ScaleTypeService {

    @Autowired
    private ScaleTypeMapper scaleTypeMapper;
    @Resource
    private ScaleService scaleService;

    @Override
    public boolean validScaleTypeName(Long scaleTypeId, String scaleTypeName) {
        boolean checkResult = false;
        if (StrUtil.isEmpty(scaleTypeName)) {
            return checkResult;
        }
        checkResult = scaleTypeMapper.validScaleTypeNameExist(scaleTypeId, scaleTypeName);
        return checkResult;
    }

    @Override
    public boolean validScaleTypeCode(Long scaleTypeId, String scaleTypeCode) {
        boolean checkResult = false;
        if (StrUtil.isEmpty(scaleTypeCode)) {
            return checkResult;
        }
        checkResult = scaleTypeMapper.validScaleTypeCodeExist(scaleTypeId, scaleTypeCode);
        return checkResult;
    }

    @Override
    public void create(ScaleType scaleType) {
        scaleTypeMapper.insert(scaleType);
    }

    @Override
    public void delete(Long scaleTypeId) {
        //校验下分类是否被量表所引用
        boolean exists = scaleService.exists(new LambdaQueryWrapper<Scale>().eq(Scale::getType, scaleTypeId));
        if(exists){
            throw new BusinessException("量表分类正在使用，请勿删除!");
        }
        scaleTypeMapper.deleteById(scaleTypeId);
    }

    @Override
    public void modify(ScaleType scaleType) {
        ScaleType rawData = scaleTypeMapper.selectById(scaleType.getId());
        BeanUtils.copyProperties(scaleType, rawData);
        scaleTypeMapper.updateById(rawData);
    }

    @Override
    public Page<ScaleTypeDetailDTO> selectPage(String typeName) {
        return Page.doSelectPage(() -> scaleTypeMapper.getList(typeName))
                .toPage(datas -> this.buildScaleTypeDetailDTO(datas));
    }

    @Override
    public List<ScaleTypeDetailDTO> getListOfEnabled(String scaleTypeName) {
        List<ScaleType> list = scaleTypeMapper.getList(scaleTypeName);
        return this.buildScaleTypeDetailDTO(list);
    }

    @Override
    public List<ScaleType> getListByTerminalCode(String terminalCode) {
        return scaleTypeMapper.getListByTerminalCode(terminalCode);
    }

    @Override
    public List<ScaleTypeDetailDTO> ownerScaleTypes(String terminalCode) {
        return scaleTypeMapper.ownerScaleTypes(terminalCode).stream().map(vo -> {
            ScaleTypeDetailDTO dto = new ScaleTypeDetailDTO();
            BeanUtils.copyProperties(vo, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    private List<ScaleTypeDetailDTO> buildScaleTypeDetailDTO(List<ScaleType> list) {
        list = CollUtil.isEmpty(list) ? List.of() : list;
        return list.stream().map(item -> {
            Integer numOfScale = scaleTypeMapper.getNumOfScale(item.getId());
            ScaleTypeDetailDTO dto = new ScaleTypeDetailDTO();
            BeanUtils.copyProperties(item, dto);
            dto.setNumOfScales(ObjUtil.defaultIfNull(numOfScale, 0));
            return dto;
        }).collect(Collectors.toList());
    }
}
