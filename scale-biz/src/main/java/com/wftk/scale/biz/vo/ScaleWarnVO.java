package com.wftk.scale.biz.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ScaleWarnVO {

    @Schema(description = "id")
    private Long id;
    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String account;
    /**
     * 用户姓名
     */
    @Schema(description = "用户姓名")
    private String userName;
    /**
     * 性别 1男 2女
     */
    @Schema(description = "性别 1男 2女")
    private Integer sex;
    /**
     * 所属部门名称
     */
    @Schema(description = "所属部门名称")
    private String departmentName;
    /**
     * 所属部门ID
     */
    @Schema(description = "所属部门ID")
    private Long departmentId;
    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;
    /**
     * 量表ID
     */
    @Schema(description = "量表ID")
    private Long scaleId;
    /**
     * 量表名称
     */
    @Schema(description = "量表名称")
    private String scaleName;
    /**
     * 量表分类
     */
    @Schema(description = "量表分类")
    private String scaleType;
    /**
     * 量表分类ID
     */
    @Schema(description = "量表分类ID")
    private String scaleTypeId;
    /**
     * 量表类型 单量表/组合量表
     */
    @Schema(description = "量表类型 单量表/组合量表")
    private Integer type;
    /**
     * 终端编码
     */
    @Schema(description = "终端编码")
    private String terminalCode;
    /**
     * 终端名称
     */
    @Schema(description = "终端名称")
    private String terminalName;
    /**
     * 测评时间
     */
    @Schema(description = "测评时间")
    private LocalDateTime assessmentTime;
    /**
     * 测评耗时，单位:秒
     */
    @Schema(description = "测评耗时，单位:秒")
    private Integer cost;
    /**
     * 触达方式，页面展示/分发
     */
    @Schema(description = "触达方式，页面展示/分发")
    private Integer contactMode;
    /**
     * 测评结果
     */
    @Schema(description = "测评结果")
    private String tagName;
    /**
     * 风险等级
     */
    @Schema(description = "风险等级")
    private Integer riskLevel;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 接收预警用户ID
     */
    @Schema(description = "接收预警用户ID")
    private Long receivingWarnUserId;

    /**
     * 接收预警用户名称
     */
    @Schema(description = "接收预警用户名称")
    private String receivingWarnUserName;

    /**
     * 预警方式
     */
    @Schema(description = "预警方式 (1：短信；2：邮件；3：加红标注)")
    private Integer noticeType;
}
