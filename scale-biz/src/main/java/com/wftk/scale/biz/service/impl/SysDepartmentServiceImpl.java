package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.scale.biz.converter.SysDepartmentConverter;
import com.wftk.scale.biz.dto.user.sysdepartment.SysDepartmentCreateDTO;
import com.wftk.scale.biz.dto.user.sysdepartment.SysDepartmentQueryDTO;
import com.wftk.scale.biz.dto.user.sysdepartment.SysDepartmentUpdateDTO;
import com.wftk.scale.biz.entity.SysDepartment;
import com.wftk.scale.biz.mapper.SysDepartmentMapper;
import com.wftk.scale.biz.service.SysDepartmentService;
import com.wftk.scale.biz.util.ParentPathUtils;
import com.wftk.scale.biz.vo.SysDepartmentVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
public class SysDepartmentServiceImpl extends ServiceImpl<SysDepartmentMapper, SysDepartment> implements SysDepartmentService {

    @Resource
    private SysDepartmentConverter sysDepartmentConverter;

    @Override
    public void createSysDepartment(SysDepartmentCreateDTO dto) {
        validCodeUnique(dto.getCode());
        SysDepartment sysDepartment = sysDepartmentConverter.sysDepartmentCreateDtoToSysDepartment(dto);
        //构建parentPath
        if(sysDepartment.getParentId() == null || sysDepartment.getParentId() == 0L) {
            sysDepartment.setParentId(0L);
            sysDepartment.setParentPath(ParentPathUtils.buildParentPath(sysDepartment.getCode(), ""));
        }else{
            SysDepartment parentSysDepartment = baseMapper.selectById(sysDepartment.getParentId());
            if(parentSysDepartment == null){
                throw new BusinessException("上级部门不存在");
            }
            sysDepartment.setParentPath(ParentPathUtils.buildParentPath(parentSysDepartment.getParentPath(), sysDepartment.getCode()));
        }
        baseMapper.insert(sysDepartment);
    }

    @Override
    public void updateSysDepartment(SysDepartmentUpdateDTO dto) {
        SysDepartment sysDepartment = sysDepartmentConverter.sysDepartmentUpdateDtoToSysDepartment(dto);
        SysDepartment dbSysDepartment = baseMapper.selectById(dto.getId());
        if(dbSysDepartment == null){
            throw new BusinessException("部门不存在");
        }
        Long id = sysDepartment.getId();
        //如果部门的状态修改为禁用，判断下机构下是否有启用用户
        if(!Objects.equals(sysDepartment.getEnable(), dbSysDepartment.getEnable()) && !sysDepartment.getEnable()){
            boolean exists = baseMapper.countSysUserByIdAndEnable(id, true);
            if(exists){
                throw new BusinessException("该部门下存在启用用户，请先禁用用户后再操作");
            }
            List<Long> childIdList = baseMapper.selectChildIdList(id);
            //修改子机构的状态
            baseMapper.update(new LambdaUpdateWrapper<SysDepartment>()
                    .in(SysDepartment::getId, childIdList).set(SysDepartment::getEnable, sysDepartment.getEnable()));
        }
        baseMapper.updateById(sysDepartment);
    }

    @Override
    public void deleteById(Long id) {
        //判断部门下是否存在用户
        boolean exists = baseMapper.countSysUserByIdAndEnable(id, null);
        if(exists){
            throw new BusinessException("该部门下存在用户，请先删除用户后再操作");
        }
        baseMapper.deleteByIds(baseMapper.selectChildIdList(id));
    }

    private void validCodeUnique(String code) {
        boolean exists = baseMapper.exists(new LambdaQueryWrapper<SysDepartment>().eq(SysDepartment::getCode, code));
        if(exists){
            throw new BusinessException("部门编码已存在");
        }
    }

    @Override
    public List<SysDepartmentVO> selectSysDepartmentList(SysDepartmentQueryDTO dto) {
        List<SysDepartment> sysDepartments = baseMapper.selectList(new LambdaQueryWrapper<SysDepartment>().orderByDesc(SysDepartment::getSort));
        // code -> vo
        Map<String, SysDepartment> map = sysDepartments.stream().collect(Collectors.toMap(SysDepartment::getCode, vo -> vo));
        Stream<SysDepartment> stream = sysDepartments.stream();
        //直接通过内存过滤
        String name = dto.getName();
        if(StrUtil.isNotBlank(dto.getParentName())){
            stream = stream.filter(vo -> {
                String parentPath = vo.getParentPath();
                String[] codeArray = parentPath.split("\\|");
                //自身不判断
                for (int i = 0; i < codeArray.length - 1; i++) {
                    SysDepartment sysDepartment = map.get(codeArray[i]);
                    if(sysDepartment != null && sysDepartment.getName().contains(dto.getParentName())){
                        return true;
                    }
                }
                return false;
            });
        }
        if(StrUtil.isNotBlank(name)){
            stream = stream.filter(vo -> vo.getName().contains(name));
        }
        if(dto.getEnable() != null){
            stream = stream.filter(vo -> vo.getEnable().equals(dto.getEnable()));
        }
        List<SysDepartmentVO> collect = stream.map(entity -> sysDepartmentConverter.sysDepartmentToSysDepartmentVO(entity)).collect(Collectors.toList());
        return buildReturnList(collect);
    }

    private List<SysDepartmentVO> buildReturnList(List<SysDepartmentVO> collect){
        List<SysDepartmentVO> returnList = new ArrayList<>(10);
        Map<Long, SysDepartmentVO> map = collect.stream().collect(Collectors.toMap(SysDepartmentVO::getId, vo -> vo));
        for (SysDepartmentVO sysDepartment : collect) {
            if(sysDepartment.getParentId() == null || sysDepartment.getParentId() == 0L || !map.containsKey(sysDepartment.getParentId())){
                returnList.add(sysDepartment);
            }else{
                SysDepartmentVO parentSysDepartment = map.get(sysDepartment.getParentId());
                sysDepartment.setParentName(parentSysDepartment.getName());
                parentSysDepartment.getChildren().add(sysDepartment);
            }
        }
        return returnList;
    }
}
