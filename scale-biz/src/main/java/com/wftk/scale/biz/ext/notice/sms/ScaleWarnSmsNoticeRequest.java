package com.wftk.scale.biz.ext.notice.sms;

import com.wftk.scale.biz.ext.sms.scene.SmsSceneEnum;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


public class ScaleWarnSmsNoticeRequest extends SmsNoticeRequest<ScaleWarnSmsNoticeRequest.ScaleWarnSmsParams>{
    @Serial
    private static final long serialVersionUID = 6391902828911553660L;

    public ScaleWarnSmsNoticeRequest(String tel, ScaleWarnSmsParams scaleWarnSmsParams) {
        super(tel, SmsSceneEnum.SCALE_WARN.name(), scaleWarnSmsParams);
    }

    @Data
    @Builder
    public static class ScaleWarnSmsParams implements Serializable {

        @Serial
        private static final long serialVersionUID = 5978324295957301871L;
    }

}
