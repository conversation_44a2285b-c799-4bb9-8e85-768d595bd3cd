package com.wftk.scale.biz.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ScaleUserReportUrlVO {

    /**
     * 用户报告id
     */
    private Long id;

    /**
     * 测评报告链接
     */
    @RFile(role = FileConstant.FILE_SCALE_SIGN_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    private String reportUrl;
}
