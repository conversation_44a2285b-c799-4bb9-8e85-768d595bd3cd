package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordParamDTO;
import com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordQueryDTO;
import com.wftk.scale.biz.entity.ScaleListingUserRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 量表分发用户 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleListingUserRecordMapper extends BaseMapper<ScaleListingUserRecord> {

    /*
     * @Author: mq
     * @Description: 根据条件检索量表分发记录信息
     * @Date: 2024/10/31 14:02
     * @Param: scaleListingUserParamDTO-检索条件
     * @return: java.util.List<com.wftk.scale.biz.dto.distribute.ScaleListingUserRecordQueryDTO>
     **/
    List<ScaleListingUserRecordQueryDTO> getScaleDistributeRecord(@Param("param") ScaleListingUserRecordParamDTO scaleListingUserParamDTO);

    /*
    * @Author: mq
    * @Description: 根据条件检索量表分发记录信息
    * @Date: 2024/12/7 18:04
    * @Param: listingId
     * @Param: targetId
    * @return: List<ScaleListingUserRecord>
    **/
    List<ScaleListingUserRecord> findByListingIdAndTargetId(@Param("listingId") Long listingId, @Param("targetId") Long targetId);

    /**
     *
     * @return
     */
    ScaleListingUserRecord getScaleDistributeRecordByUserId(@Param("userId")Long userId,@Param("scaleListingId")Long scaleListingId);


}
