package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11 15:20:06
 */
@Data
@TableName("scale_report_conf_setting")
public class ScaleReportConfSetting implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 报告配置id。来自scale_report_conf的主键
     */
    private Long reportConfId;

    /**
     * 类型 1.阅读须知 2.备注 3.表格设置 4.图表设置
     */
    private Integer type;

    /**
     * 报告类型 1.总分  2.总均分 3.阳性数量 4.因子分析
     */
    private Integer reportType;

    /**
     * 图表类型
     */
    private Integer chartType;

    /**
     * 存储值或者json配置
     */
    private String value;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;


}
