package com.wftk.scale.biz.dto.listing;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: ScaleListedQueryDTO
 * @Description: 组合量表可上架信息传输实体
 * @Author: mq
 * @Date: 2024-10-29 16:05
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScaleCombinationListingParamDTO implements Serializable {

    /**
     * 量表ID
     */
    private Long targetId;

    /**
     * 量表名称
     */
    private String targetName;

    /**
     *  测评方式
     */
    private Long targetType;

    /**
     * 终端ID
     */
    private Long terminalId;

    /**
     * 操作人
     */
    private String opUser;
}
