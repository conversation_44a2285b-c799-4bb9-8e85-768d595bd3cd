package com.wftk.scale.biz.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class ScaleWarnSettingPageQueryOutput implements Serializable {
    @Serial
    private static final long serialVersionUID = 3242162453890564707L;

    /**
     * 量表设置ID
     */
    @Schema(description = "量表设置ID")
    private Long id;

    /**
     * 量表ID
     */
    @Schema(description = "量表ID")
    private Long scaleId;

    @Schema(description = "量表编码")
    private String scaleCode;

    /**
     * 量表名称
     */
    @Schema(description = "量表名称")
    private String scaleName;

    /**
     * 预警等级
     */
    @Schema(description = "预警等级")
    private Integer level;

    /**
     * 是否预警自己
     */
    @Schema(description = "是否预警自己")
    private Boolean evaluateMyself;

    /**
     * 预警范围
     */
    @Schema(description = "预警范围")
    private Integer scope;

    /**
     * 预警因子
     */
    @Schema(description = "预警因子")
    private String scaleFactorNames;

    /**
     * 接收预警用户
     */
    @Schema(description = "接收预警用户")
    private String receivingScaleWarnUserNames;

    /**
     * 预警部门或人员
     */
    @Schema(description = "预警部门或人员")
    private String scaleWarnScopeNames;
}
