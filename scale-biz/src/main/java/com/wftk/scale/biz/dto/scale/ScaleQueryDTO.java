package com.wftk.scale.biz.dto.scale;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/10/24 18:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleQueryDTO {

    /**
     * 量表id
     */
    private Long id;

    /**
     * 量表编码
     */
    private String code;

    /**
     * 量表名称
     */
    private String name;

    /**
     * 量表介绍
     */
    private String intro;

    /**
     * 量表类型
     */
    private Long type;

    /**
     * 量表类型名称
     */
    private String typeName;

    /**
     * 量表指导语
     */
    private String guideline;

    /**
     * 量表问题数量
     */
    private Integer numOfQuestion;

    /**
     * 封面图url
     */
    @RFile(role = FileConstant.FILE_SCALE_PUB_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(using = FileSerializer.class)
    private String cover;

    /**
     * 量表排序
     */
    private Integer sort;

    /**
     * 0未完成，1已完成，问题，因子，解读等没完成之前是未完成
     */
    private Integer status;

    /**
     * 量表更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 介绍
     */
    private String description;

    /**
     * 限时秒数最小值
     */
    private Integer minTimeLimit;

    /**
     * 限时秒数最大值
     */
    private Integer maxTimeLimit;

    /**
     * 是否完成
     */
    private boolean isCompleted;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 允许复测
     */
    private Boolean allowRepeat;

    /**
     * 量表上架项目配置开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime listingStartTime;

    /**
     * 量表上架项目配置结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime listingEndTime;
}
