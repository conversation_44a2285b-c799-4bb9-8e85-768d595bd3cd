package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.ScaleType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 量表类型表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleTypeMapper extends BaseMapper<ScaleType> {

    /*
     * @Author: mq
     * @Description: 校验量表分类名称是否已经存在
     * @Date: 2024/10/25 11:08
     * @Param: id-量表分类主键
     * @Param: scaleTypeName-量表分类名称
     * @return: boolean
     **/
    boolean validScaleTypeNameExist(@Param("id") Long id, @Param("scaleTypeName") String scaleTypeName);

    /*
     * @Author: mq
     * @Description: 校验量表分类编号是否已经存在
     * @Date: 2024/10/25 11:08
     * @Param: id-量表分类主键
     * @Param: scaleTypeCode-量表分类编号
     * @return: boolean
     **/
    boolean validScaleTypeCodeExist(@Param("id") Long id, @Param("scaleTypeCode") String scaleTypeCode);

    /*
     * @Author: mq
     * @Description: 根据条件查询量表分类数据
     * @Date: 2024/10/25 10:55
     * @Param: scaleTypeName-量表分类名称
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleType>
     **/
    List<ScaleType> getList(@Param("scaleTypeName") String scaleTypeName);

    /*
    * @Author: mq
    * @Description: 根据量表分类ID获取分类量表的数量
    * @Date: 2024/11/26 11:47
    * @Param: scaleTypeId-量表分类ID
    * @return: Integer(量表数量)
    **/
    Integer getNumOfScale(@Param("scaleTypeId") Long scaleTypeId);

    List<ScaleType> getListByTerminalCode(@Param("terminalCode") String terminalCode);

    List<ScaleType> ownerScaleTypes(@Param("terminalCode") String terminalCode);
}
