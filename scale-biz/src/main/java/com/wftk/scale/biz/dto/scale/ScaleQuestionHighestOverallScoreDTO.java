package com.wftk.scale.biz.dto.scale;

import com.wftk.scale.biz.excel.model.base.BaseConvertDataDTO;
import lombok.*;
import java.io.Serializable;

/**
 * @Description: 量表下的所有题目最高总分
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleQuestionHighestOverallScoreDTO extends BaseConvertDataDTO implements Serializable {

    /**
     * 总分
     */
    private Integer totalScore;
}
