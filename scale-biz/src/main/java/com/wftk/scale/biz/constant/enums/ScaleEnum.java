package com.wftk.scale.biz.constant.enums;

import lombok.Getter;

/**
 * @EnumName: ScaleEnum
 * @Description: 量表常量信息
 * @Author: mq
 * @Date: 2024-10-31 17:09
 * @Version: 1.0
 **/
@Getter
public enum ScaleEnum {

    SCALE_UN_COMPLETED(0, "量表设置未完成"),

    SCALE_COMPLETED(1, "量表设置已完成"),

    SCALE_LISTING_ON_SHELL_STATUS(1, "量表上架"),

    SCALE_LISTING_OFF_SHELL_STATUS(0, "量表下架"),

    SINGLE_SCALE_LISTING_TYPE(1, "单个量表"),

    COMBINATION_SCALE_LISTING_TYPE(2, "组合量表"),

    COMBINATION_SCALE_TYPE_ORDER_BY(1,"依次测评"),

    COMBINATION_SCALE_TYPE_CHOOSE(2,"选择测评"),

    ;
    private Integer code;

    private String desc;

    ScaleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
