package com.wftk.scale.biz.constant.enums;


import lombok.Getter;

/**
 * @EnumName: ConvertTypeEnum
 * @Description: 因子分值转换方式
 * @Author: mq
 * @Date: 2024/11/26
 * @Version: 1.0
 **/
@Getter
public enum ConvertTypeEnum {

    RATIO(1, "等比转换"),
    FIEXD(2, "固定值转换"),
    ;
    private Integer code;

    private String desc;

    ConvertTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
