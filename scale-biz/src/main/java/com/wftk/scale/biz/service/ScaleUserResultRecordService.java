package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;

import java.util.List;

/**
 * <p>
 * 测评回答答案表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleUserResultRecordService extends IService<ScaleUserResultRecord> {

    /*
     * @Author: mq
     * 
     * @Description: 保存测评回答答案信息
     * 
     * @Date: 2024/11/7 14:23
     * 
     * @Param: resultId-测评记录ID
     * 
     * @Param: list-测评答案集合
     * 
     * @return: void
     **/
    void create(Long resultId, List<ScaleUserResultRecord> list);

    /*
     * @Author: mq
     * 
     * @Description: 根据测评记录ID获取测评答案信息
     * 
     * @Date: 2024/11/7 14:40
     * 
     * @Param: resultId-测评记录ID
     * 
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleUserResultRecord>
     **/
    List<ScaleUserResultRecord> findByResultId(Long resultId);

    /**
     * 根据测评记录ID和题目ID获取测评答案信息
     * 
     * @param resultId
     * @param questionId
     * @return
     */
    ScaleUserResultRecord findByResultIdAndQuestionId(Long resultId, Long questionId);
}
