package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.SymbolConstant;
import com.wftk.scale.biz.constant.enums.EnableEnum;
import com.wftk.scale.biz.dto.scale.ScaleFactorDTO;
import com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO;
import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.entity.ScaleQuestion;
import com.wftk.scale.biz.event.ScaleCreateRelationEvent;
import com.wftk.scale.biz.event.ScaleDelEvent;
import com.wftk.scale.biz.ext.evaluator.core.EvaluatorEngine;
import com.wftk.scale.biz.ext.evaluator.core.EvaluatorEngine.ExpressionValidateResult;
import com.wftk.scale.biz.ext.evaluator.core.ExpressionTemplate;
import com.wftk.scale.biz.mapper.ScaleFactorMapper;
import com.wftk.scale.biz.mapper.ScaleQuestionMapper;
import com.wftk.scale.biz.service.ScaleFactorService;
import com.wftk.scale.biz.util.ParentPathUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 因子维度 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Slf4j
@Service
public class ScaleFactorServiceImpl extends ServiceImpl<ScaleFactorMapper, ScaleFactor> implements ScaleFactorService {

    @Autowired
    private ScaleFactorMapper scaleFactorMapper;

    @Autowired
    private ScaleQuestionMapper scaleQuestionMapper;

    @Autowired
    private EvaluatorEngine evaluatorEngine;

    @Override
    public boolean vaildFactorDataIntegrity(Long scaleId) {
        return scaleFactorMapper.vaildFactorDataIntegrity(scaleId);
    }

    @Override
    public String checkFactorFormulaEnable(Long scaleId) {
        return scaleFactorMapper.checkFactorFormulaEnable(scaleId);
    }

    @Override
    public boolean vaildFactorFormulaEnable(Long scaleId) {
        Integer factorFormulaNotEnableCount = scaleFactorMapper.getFactorFormulaNotEnableCount(scaleId);
        return !(factorFormulaNotEnableCount > 0);
    }

    @Override
    public boolean vaildFactorFormulaEnable(List<Long> scaleIds) {
        Integer notEnableCountByScaleIds = scaleFactorMapper.getFactorFormulaNotEnableCountByScaleIds(scaleIds);
        return !(notEnableCountByScaleIds > 0);
    }

    @Override
    public boolean vaildFactorName(Long factorId, Long scaleId, String factorName) {
        boolean checkResult = false;
        if (ObjectUtil.isNull(scaleId) || StrUtil.isEmpty(factorName)) {
            return checkResult;
        }
        checkResult = scaleFactorMapper.vaildFactorNameExists(factorId, scaleId, factorName);
        return checkResult;
    }

    @Override
    public boolean checkFactorType(Long scaleId, Long id, Integer type) {
        return scaleFactorMapper.checkFactorType(scaleId, id, type);
    }

    @Override
    public ScaleFactor getFactorByScaleIdAndType(Long scaleId, Integer type) {
        return scaleFactorMapper.getFactorByScaleIdAndType(scaleId, type);
    }

    @Override
    public List<ScaleFactor> getFactorListByScaleIdAndType(Long scaleId, Integer type) {
        return scaleFactorMapper.getFactorListByScaleIdAndType(scaleId, type);
    }

    @Override
    public String checkFactorRelationQuestion(Long scaleId, Long questionId) {
        List<ScaleFactor> list = scaleFactorMapper.getList(scaleId, null);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().filter(factor -> {
            String relationIds = factor.getQuestionId();
            return StrUtil.isNotEmpty(relationIds) && relationIds.indexOf(String.valueOf(questionId)) >= 0;
        }).map(factor -> factor.getName()).findFirst().orElse(null);
    }

    @Override
    public ScaleFactor getByScaleIdAndName(Long scaleId, String factorName) {
        return scaleFactorMapper.getByScaleIdAndName(scaleId, factorName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ScaleFactor scaleFactor) {
        // 处理因子维度公式
        handleExpression(scaleFactor, scaleFactor.getFormula());
        scaleFactor.setSort(ObjUtil.defaultIfNull(scaleFactor.getSort(), 0));
        scaleFactor.setEnable(ObjUtil.defaultIfNull(scaleFactor.getEnable(), EnableEnum.ENABLE.getEnable()));
        scaleFactorMapper.insert(scaleFactor);
    }

    @Override
    public void modify(ScaleFactor scaleFactor) {
        ScaleFactor rawData = scaleFactorMapper.selectById(scaleFactor.getId());
        String formula = scaleFactor.getFormula();
        // 公式或者参数值发生变化时，需要重新处理因子维度公式（因为涉及到校验）
        if (StrUtil.isNotBlank(formula) && !formula.equalsIgnoreCase(rawData.getFormula())) {
            // 处理因子维度公式
            handleExpression(scaleFactor, scaleFactor.getFormula());
        } else if (StrUtil.isBlank(scaleFactor.getFormulaParam()) || !StrUtil.equalsIgnoreCase(scaleFactor.getFormulaParam(), rawData.getFormulaParam())) {
            // 处理因子维度公式
            handleExpression(scaleFactor, scaleFactor.getFormula());
        }
        BeanUtils.copyProperties(scaleFactor, rawData);
        scaleFactorMapper.updateById(rawData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long factorId) {
        scaleFactorMapper.deleteById(factorId);
    }

    @Override
    public Page<ScaleFactorDTO> selectPage(Long scaleId, String factorName) {
        return Page.doSelectPage(() -> scaleFactorMapper.getList(scaleId, factorName))
                .toPage(this::buildScaleFactorDTO);
    }

    private List<ScaleFactorDTO> buildScaleFactorDTO(List<ScaleFactor> list) {
        list = CollUtil.isEmpty(list) ? List.of() : list;

        return list.stream().map(factor -> {
            ScaleFactorDTO dto = new ScaleFactorDTO();
            BeanUtils.copyProperties(factor, dto);
            // 处理题号
            List<String> questionIds = StrUtil.split(factor.getQuestionId(), ParentPathUtils.SEPARATOR);
            if (CollUtil.isNotEmpty(questionIds)) {
                List<Long> longQuestionIds = questionIds.stream().map(Long::valueOf).toList();
                List<ScaleQuestion> scaleQuestions = scaleQuestionMapper.getQuestionByIds(longQuestionIds);
                if (CollUtil.isNotEmpty(scaleQuestions)) {
                    dto.setQuestionNumber(StrUtil.join(ParentPathUtils.SEPARATOR,
                            scaleQuestions.stream().map(e ->
                                    e.getQuestionNumber() + (StrUtil.isBlank(e.getSubNumber()) ?"" : "." + e.getSubNumber()))
                                    .toList()));
                }
            }

            return dto;
        }).toList();

    }

    @Override
    public List<ScaleFactor> findByScaleId(Long scaleId) {
        return scaleFactorMapper.getList(scaleId, null);
    }

    @Override
    public String getNameByScaleId(Long factorId) {
        ScaleFactor scaleFactor = scaleFactorMapper.selectById(factorId);
        if (ObjectUtil.isNotNull(scaleFactor)) {
            return null;
        }
        return scaleFactor.getName();
    }

    private String generateNumberNo(ScaleQuestionQueryDTO question) {
        return question.getQuestionNumber() + "." + question.getSubNumber();
    }

    private String generateNewNumberId(String questionId, Map<Long, Long> questionNumberMap) {
        StringBuilder sb = new StringBuilder();
        if (StrUtil.isNotBlank(questionId)) {
            for (String s : questionId.split("\\|")) {
                sb.append(questionNumberMap.get(Long.parseLong(s))).append("|");
            }
        }
        return sb.isEmpty() ? sb.toString() : sb.substring(0, sb.length() - 1);
    }

    @Override
    public Map<Long, Long> saveBatchByNewQuestion(ScaleCreateRelationEvent event) {
        Long oldScaleId = event.getOldScaleId();
        Long newScaleId = event.getNewScaleId();
        List<ScaleFactor> list = scaleFactorMapper.getList(oldScaleId, null);
        // 获取所有题号，与反选题号
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>(1);
        }
        // 维护新老题号ID对应关系
        Map<Long, Long> questionNumberMap = new HashMap<>(16);
        List<ScaleQuestionQueryDTO> oldQuestions = scaleQuestionMapper.findByScaleId(oldScaleId);
        List<ScaleQuestionQueryDTO> newQuestions = scaleQuestionMapper.findByScaleId(newScaleId);
        if (CollUtil.isNotEmpty(oldQuestions) && CollUtil.isNotEmpty(newQuestions)) {
            Map<String, Long> collect = newQuestions.stream()
                    .collect(Collectors.toMap(this::generateNumberNo, ScaleQuestionQueryDTO::getId));
            oldQuestions.forEach(oldQuestion -> questionNumberMap.put(oldQuestion.getId(),
                    collect.get(generateNumberNo(oldQuestion))));
        }
        Map<Long, Long> map = new HashMap<>(16);
        list.forEach(factor -> {
            Long oldFactorId = factor.getId();
            factor.setScaleId(newScaleId);
            factor.setId(null);
            factor.setQuestionId(generateNewNumberId(factor.getQuestionId(), questionNumberMap));
            this.save(factor);
            map.put(oldFactorId, factor.getId());
        });
        return map;
    }

    @Override
    public ScaleFactor getById(Long id) {
        return baseMapper.getById(id);
    }

    @EventListener
    public void onoApplication(ScaleDelEvent event) {
        scaleFactorMapper
                .delete(new LambdaQueryWrapper<ScaleFactor>().eq(ScaleFactor::getScaleId, event.getOldScaleId()));
    }

    /**
     * 处理因子维度公式
     *
     * @param scaleFactor
     * @param formula
     */
    private void handleExpression(ScaleFactor scaleFactor, String formula) {
        ExpressionValidateResult validateResult = evaluatorEngine.validate(formula);
        if (!validateResult.valid()) {
            throw new BusinessException(validateResult.message());
        }
        scaleFactor.setFormula(formula);
        ExpressionTemplate expressionToTemplate = evaluatorEngine.expressionToTemplate(formula);
        scaleFactor.setFormulaCompiled(expressionToTemplate.getTemplate());
        if (CollUtil.isNotEmpty(expressionToTemplate.getParameters())) {
            String paramNames = StrUtil.join(SymbolConstant.DELIMITER, expressionToTemplate.getParameters());
            String formulaParam = scaleFactor.getFormulaParam();
            if (StrUtil.isBlank(formulaParam)) {
                throw new BusinessException("因子维度计算公式参数不能为空!");
            }
            // 前端传的值可能"|"之间包含空格，也可能"|"之间没有空格，因为是用户输入的，所以需要注意空格问题
            String splitSymbol = "\\|";
            String[] params = formulaParam.split(splitSymbol);
            if (params.length != expressionToTemplate.getParameters().size()) {
                throw new BusinessException("因子维度计算公式参数名称和参数值不匹配!");
            }
            // 去除params中的空值
            String paramResult = Arrays.stream(params)
                    .filter(StrUtil::isNotBlank)
                    .map(String::trim)
                    .collect(Collectors.joining(SymbolConstant.DELIMITER));
            scaleFactor.setFormulaParamName(paramNames);
            scaleFactor.setFormulaParam(paramResult);
        }
    }
}
