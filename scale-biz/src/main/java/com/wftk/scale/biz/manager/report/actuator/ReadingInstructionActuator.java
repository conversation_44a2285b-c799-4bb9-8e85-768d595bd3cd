package com.wftk.scale.biz.manager.report.actuator;

import com.wftk.scale.biz.constant.ScaleReportConfSettingConstant;
import com.wftk.scale.biz.entity.ScaleReportConfSetting;
import com.wftk.scale.biz.service.ScaleReportConfSettingService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/9/16 17:13
 */
@Slf4j
@Component
public class ReadingInstructionActuator {

    private final Integer READING_INSTRUCT = ScaleReportConfSettingConstant.Type.READING_INSTRUCT;

    @Resource
    ScaleReportConfSettingService scaleReportConfSettingService;

    public String doReadingInstruction(Long reportConfId) {
        List<ScaleReportConfSetting> settingList = scaleReportConfSettingService.selectScaleReportConfSettingByReportConfId(reportConfId, READING_INSTRUCT, null);
        if(settingList.isEmpty()){
            log.warn("scaleReportConfSetting is null. reportConfId: {} type: {}",reportConfId,READING_INSTRUCT);
            return null;
        }
        if (settingList.size() > 1) {
            log.warn("scaleReportConfSetting data error. reportConfId: {} type: {} data: {}", reportConfId, READING_INSTRUCT, settingList);
            return null;
        }

        return settingList.get(0).getValue();
    }

}
