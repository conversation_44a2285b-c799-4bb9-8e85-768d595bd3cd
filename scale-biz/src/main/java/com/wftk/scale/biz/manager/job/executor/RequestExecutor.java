package com.wftk.scale.biz.manager.job.executor;

import cn.hutool.core.date.DateUtil;
import com.wftk.scale.biz.constant.enums.HttpJobEndEnum;
import com.wftk.scale.biz.constant.enums.HttpJobStatusEnum;
import com.wftk.scale.biz.constant.enums.ScheduledEnum;
import com.wftk.scale.biz.entity.HttpJob;
import com.wftk.scale.biz.manager.job.action.RequestAction;
import com.wftk.scale.biz.manager.job.registry.ActionRegistry;
import com.wftk.scale.biz.service.HttpJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * <AUTHOR>
 * @create 2024/3/7 09:51
 */
@Slf4j
public class RequestExecutor {

    private final ActionRegistry registry;

    private final HttpJobService httpJobService;

    public RequestExecutor(ActionRegistry registry,HttpJobService httpJobService) {
        this.registry = registry;
        this.httpJobService = httpJobService;
    }

    @Async
    public void execute(HttpJob httpJob){
        RequestAction action = registry.get(httpJob.getBizCode());
        if(action != null){
            try {
                action.doRequestAction(httpJob);
                // 修改状态为通知成功
                httpJob.setStatus(HttpJobStatusEnum.SUCCESS.getStatus());
                httpJob.setRemark(HttpJobStatusEnum.SUCCESS.getDesc());
                httpJob.setEnded(HttpJobEndEnum.YES.getValue());
                httpJob.setEndTime(LocalDateTime.now());
                log.info("requestExecutor execute success. {}",httpJob);
            }catch (Exception e){
                httpJob.setStatus(HttpJobStatusEnum.FAIL.getStatus());
                httpJob.setRemark(e.getMessage());
                if(httpJob.getCount() + 1 >= httpJob.getMaxCount()){
                    httpJob.setEnded(HttpJobEndEnum.YES.getValue());
                    httpJob.setEndTime(LocalDateTime.now());
                }
                int second = ScheduledEnum.getSecond(httpJob.getCount() + 1);
                httpJob.setNextTime(DateUtil.offsetSecond(new Date(),second).toLocalDateTime());
                log.error("requestExecutor execute fail. {}",httpJob,e);
            }
            httpJobService.updateHttpJobResultByBizCodeAndBizId(httpJob);
        }
    }

}
