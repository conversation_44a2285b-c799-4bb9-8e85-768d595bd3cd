package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.scale.biz.dto.user.rolemenu.SysRoleMenuCreateDTO;
import com.wftk.scale.biz.entity.SysRoleMenu;
import com.wftk.scale.biz.event.SysMenuDeleteEvent;
import com.wftk.scale.biz.event.SysRoleDeleteEvent;
import com.wftk.scale.biz.mapper.SysRoleMenuMapper;
import com.wftk.scale.biz.service.SysMenuService;
import com.wftk.scale.biz.service.SysRoleMenuService;
import com.wftk.scale.biz.vo.SysRoleMenuVO;
import jakarta.annotation.Resource;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单权限表（角色+租户套餐） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class SysRoleMenuServiceImpl extends ServiceImpl<SysRoleMenuMapper, SysRoleMenu> implements SysRoleMenuService {

    @Resource
    private SysMenuService sysMenuService;

    @Override
    public List<SysRoleMenuVO> getList(Long roleId) {
        if(roleId == null){
            throw new BusinessException("角色id不能为空");
        }
        List<SysRoleMenuVO> sysRoleMenus = sysMenuService.selectMenusByRoleId(roleId);
        return buildResult(sysRoleMenus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(SysRoleMenuCreateDTO sysRoleMenuCreateDTO) {
        Long roleId = sysRoleMenuCreateDTO.getRoleId();
        baseMapper.delete(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getRoleId, roleId));
        List<Long> menuIdList = sysRoleMenuCreateDTO.getMenuIdList();
        if(CollUtil.isNotEmpty(menuIdList)){
            baseMapper.insert(menuIdList.stream().map(menuId -> {
                SysRoleMenu sysRoleMenu = new SysRoleMenu();
                sysRoleMenu.setRoleId(roleId);
                sysRoleMenu.setMenuId(menuId);
                return sysRoleMenu;
            }).collect(Collectors.toList()));
        }
    }

    private List<SysRoleMenuVO> buildResult(List<SysRoleMenuVO> sysRoleMenus){
        List<SysRoleMenuVO> result = new ArrayList<>(10);
        Map<Long, SysRoleMenuVO> map = sysRoleMenus.stream().collect(Collectors.toMap(SysRoleMenuVO::getId, vo -> vo));
        for (SysRoleMenuVO vo : sysRoleMenus) {
            if(vo.getParentId() == null || vo.getParentId() == 0){
                result.add(vo);
            }else{
                map.get(vo.getParentId()).getChildren().add(vo);
            }
        }
        return result;
    }

    @EventListener(SysMenuDeleteEvent.class)
    public void onApplication(SysMenuDeleteEvent event) {
        List<Long> menuId = event.getMenuId();
        if(CollUtil.isEmpty(menuId)){
            return;
        }
        baseMapper.delete(new LambdaQueryWrapper<SysRoleMenu>().in(SysRoleMenu::getMenuId, event.getMenuId()));
    }

    @EventListener(SysRoleDeleteEvent.class)
    public void onApplication(SysRoleDeleteEvent event) {
        Long roleId = event.getRoleId();
        if(roleId == null){
            return;
        }
        baseMapper.delete(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getRoleId, roleId));
    }
}
