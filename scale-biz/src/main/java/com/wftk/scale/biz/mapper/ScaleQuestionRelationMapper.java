package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.scale.ScaleQuestionRelationDTO;
import com.wftk.scale.biz.entity.ScaleQuestionRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 量表关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleQuestionRelationMapper extends BaseMapper<ScaleQuestionRelation> {

    /* 
     * @Author: mq
     * @Description: 校验量表题目跳转逻辑是否有设置
     * @Date: 2024/11/6 16:52
     * @Param: scaleId  
     * @return: boolean 
     **/
    boolean vaildRelationDataIntegrity(@Param("scaleId") Long scaleId);

    /* 
     * @Author: mq
     * @Description: 根据条件检索量表跳转逻辑列表数据
     * @Date: 2024/11/5 17:28 
     * @Param: scaleId-量表ID
     * @return: java.util.List<com.wftk.scale.biz.dto.scale.ScaleQuestionRelationDTO>
     **/
    List<ScaleQuestionRelationDTO> getList(@Param("scaleId") Long scaleId);

    /*
     * @Author: mq
     * @Description: 根据量表ID和题目ID获取逻辑跳转信息
     * @Date: 2024/11/4 17:17
     * @Param: scaleId-量表ID
     * @Param: questionId-问题ID
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleQuestionRelation>
     **/
    List<ScaleQuestionRelation> findByScaleIdAndQuestionId(@Param("scaleId") Long scaleId, @Param("questionId") Long questionId);
}
