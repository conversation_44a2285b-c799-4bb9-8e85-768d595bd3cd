package com.wftk.scale.biz.manager.report.actuator;

import com.wftk.scale.biz.constant.ScaleReportConfSettingConstant;
import com.wftk.scale.biz.dto.dict.DictDTO;
import com.wftk.scale.biz.entity.ScaleReportConfSetting;
import com.wftk.scale.biz.manager.report.dto.base.FormDTO;
import com.wftk.scale.biz.service.DictService;
import com.wftk.scale.biz.service.ScaleReportConfSettingService;
import com.wftk.scale.biz.service.ScaleUserFactorResultService;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createDate 2025/9/10 20:37
 */
@Slf4j
@Component
public class FormActuator {

    private final Integer FORM_SETTING = ScaleReportConfSettingConstant.Type.FORM_SETTING;

    private final String DICT_REPORT_FORM_COLUMN = "REPORT_FORM_COLUMN";

    @Resource
    ScaleReportConfSettingService scaleReportConfSettingService;

    @Resource
    ScaleUserFactorResultService scaleUserFactorResultService;

    @Resource
    DictService dictService;

    public FormDTO doForm(Long resultId,List<Long> factorIds,Long reportConfId,Integer reportType){
        // 获取表格展示的列
        List<ScaleReportConfSetting> settingList = scaleReportConfSettingService.selectScaleReportConfSettingByReportConfId(reportConfId, FORM_SETTING, reportType);
        if(settingList.isEmpty()){
            log.warn("scaleReportConfSetting is null. reportConfId: {} type: {}",reportConfId,FORM_SETTING);
            return null;
        }

        if(settingList.size() > 1){
            log.warn("scaleReportConfSetting data error. reportConfId: {} type: {} data: {}",reportConfId,FORM_SETTING,settingList);
            return null;
        }

        ScaleReportConfSetting scaleReportConfSetting = settingList.get(0);
        String clomwnStr = scaleReportConfSetting.getValue();
        // 获取表头
        List<String> clomwnList = List.of(clomwnStr.split(","));
        List<DictDTO> dicts = dictService.findByCode(DICT_REPORT_FORM_COLUMN);
        String titleTd = buildTitleTd(buildHeader(dataConvert(dicts), clomwnList));

        FormDTO formDTO = new FormDTO();
        formDTO.setFormTitle(titleTd);

        // 获取表数据
        List<Map<String, Object>> maps = scaleUserFactorResultService.selectReportFormData(resultId, clomwnStr, factorIds);
        formDTO.setFormData(buildDataTd(maps,clomwnList));

        return formDTO;
    }

    private String buildTitleTd(List<String> headers) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("<tr style=\"text-align:center;\">\n");
        headers.forEach(header -> stringBuilder.append("<td>").append(header).append("</td>"));
        stringBuilder.append("</tr>");
        return stringBuilder.toString();
    }

    private String buildDataTd(List<Map<String, Object>> datas, List<String> headers) {
        StringBuilder stringBuilder = new StringBuilder();
        for (Map<String, Object> row : datas) {
            stringBuilder.append("<tr style=\"text-align:center;\">");
            for (String field : headers) {
                Object value = row.get(field);
                stringBuilder.append("<td>").append(value != null ? value : "").append("</td>");
            }
            stringBuilder.append("</tr>");
        }
        return stringBuilder.toString();
    }

    private List<String> buildHeader(Map<String ,Clomwn> clomwnMap,List<String> clomwnList){
        List<String> headers = new ArrayList<>();
        clomwnList.forEach(clomwnName->{
            Clomwn clomwn = clomwnMap.get(clomwnName);
            headers.add(clomwn.getDesc());
        });
        return headers;
    }


    private Map<String ,Clomwn> dataConvert(List<DictDTO> dicts){
        Map<String ,Clomwn> map = new HashMap<>();
        dicts.forEach(dictDTO -> {
            Clomwn clomwn = new Clomwn();
            clomwn.setName(dictDTO.getDictValue());
            clomwn.setDesc(dictDTO.getDictLabel());
            map.put(dictDTO.getDictValue(),clomwn);
        });
        return map;
    }

    /**
     * 列名称以及描述
     */
    @Data
    class Clomwn{

        private String name;

        private String desc;

    }

}
