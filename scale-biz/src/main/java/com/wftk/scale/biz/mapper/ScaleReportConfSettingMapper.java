package com.wftk.scale.biz.mapper;

import com.wftk.scale.biz.entity.ScaleReportConfSetting;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11 15:20:06
 */
public interface ScaleReportConfSettingMapper extends BaseMapper<ScaleReportConfSetting> {

    Integer delByReportConfId(@Param("reportConfId")Long reportConfId);

    List<ScaleReportConfSetting> selectByReportConfId(@Param("reportConfId")Long reportConfId);

    List<ScaleReportConfSetting> selectScaleReportConfSettingByReportConfId(@Param("reportConfId")Long reportConfId,
                                                                      @Param("type") Integer type,
                                                                      @Param("reportType")Integer reportType);

}
