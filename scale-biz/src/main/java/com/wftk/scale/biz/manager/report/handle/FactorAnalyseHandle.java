package com.wftk.scale.biz.manager.report.handle;

import com.wftk.scale.biz.constant.enums.ScaleFactorTypeEnum;
import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.entity.ScaleReportConf;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.manager.report.actuator.*;
import com.wftk.scale.biz.manager.report.dto.FactorAnalyseReportDTO;
import com.wftk.scale.biz.manager.report.dto.base.AudioDTO;
import com.wftk.scale.biz.manager.report.dto.base.ReultIntroDTO;
import com.wftk.scale.biz.manager.report.dto.base.SuggestionDTO;
import com.wftk.scale.biz.manager.report.dto.base.VideoDTO;
import com.wftk.scale.biz.manager.report.dto.content.FactorAnalyseReportContentDTO;
import com.wftk.scale.biz.service.ScaleFactorService;
import com.wftk.scale.biz.service.ScaleUserFactorResultService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 其它因子分析处理器
 *
 * <AUTHOR>
 * @createDate 2025/9/8 17:35
 */
@Slf4j
@Component
public class FactorAnalyseHandle {

    @Resource
    ScaleFactorService scaleFactorService;

    @Resource
    ResultIntroActuator resultIntroActuator;

    @Resource
    ScaleUserFactorResultService scaleUserFactorResultService;

    @Resource
    SuggestionActuator suggestionActuator;

    @Resource
    AudioActuator audioActuator;

    @Resource
    VideoActuator videoActuator;


    public FactorAnalyseReportContentDTO buildReportContent(ScaleUserResult scaleUserResult, ScaleReportConf scaleReportConf) {
        if (!scaleReportConf.getFactorResultIntroEnable() &&
                !scaleReportConf.getFactorImprovementSuggestionEnable() &&
                !scaleReportConf.getFactorAudioAndVideoNarrateEnable()) {
            log.info("factorAnalyse enable is close. resultId is {}", scaleUserResult.getId());
            return null;
        }

        // 查找其它因子
        List<ScaleFactor> scaleFactors = scaleFactorService.getFactorListByScaleIdAndType(scaleUserResult.getScaleId(), ScaleFactorTypeEnum.OTHER.getType());
        if (scaleFactors.isEmpty()) {
            log.info("factorAnalyse factor is empty. resultId is {}", scaleUserResult.getId());
            return null;
        }
        List<FactorAnalyseReportDTO> factorAnalyseReports = new ArrayList<>();
        for (ScaleFactor factor : scaleFactors) {
            FactorAnalyseReportDTO factorAnalyseReport = new FactorAnalyseReportDTO();
            // 查找结果解读和因子结果
            ReultIntroDTO reultIntroDTO = null;
            if (scaleReportConf.getFactorResultIntroEnable()) {
                reultIntroDTO = resultIntroActuator.doResultIntro(scaleUserResult.getId(), factor.getId(), factor.getName());
            }

            // 获取建议
            SuggestionDTO suggestionDTO = null;
            if (scaleReportConf.getFactorImprovementSuggestionEnable()) {
                BigDecimal score = scaleUserFactorResultService.getScoreByResultIdAndFactorId(scaleUserResult.getId(), factor.getId());
                suggestionDTO = suggestionActuator.doSuggestion(scaleReportConf.getId(), score, factor.getName(), factor.getId());
            }

            // 获取音频
            AudioDTO audioDTO = null;
            VideoDTO videoDTO = null;
            if (scaleReportConf.getFactorAudioAndVideoNarrateEnable()) {
                BigDecimal score = scaleUserFactorResultService.getScoreByResultIdAndFactorId(scaleUserResult.getId(), factor.getId());
                audioDTO = audioActuator.doAudio(scaleReportConf.getId(), score, factor.getName(), factor.getId());
                videoDTO = videoActuator.doVideo(scaleReportConf.getId(), score, factor.getName(), factor.getId());
            }

            factorAnalyseReport.setAudio(audioDTO);
            factorAnalyseReport.setSuggestion(suggestionDTO);
            factorAnalyseReport.setVideo(videoDTO);
            factorAnalyseReport.setResultIntro(reultIntroDTO);
            factorAnalyseReports.add(factorAnalyseReport);

        }

        FactorAnalyseReportContentDTO factorAnalyseReportContent = new FactorAnalyseReportContentDTO();
        factorAnalyseReportContent.setFactorAnalyseReports(factorAnalyseReports);

        return factorAnalyseReportContent;
    }


}
