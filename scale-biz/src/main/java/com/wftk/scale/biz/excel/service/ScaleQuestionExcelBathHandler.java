//package com.wftk.scale.biz.excel.service;
//
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.IdUtil;
//import cn.hutool.core.util.ObjUtil;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.core.util.ReflectUtil;
//import cn.hutool.core.util.StrUtil;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
//import com.wftk.exception.core.exception.BusinessException;
//import com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO;
//import com.wftk.scale.biz.entity.ScaleQuestion;
//import com.wftk.scale.biz.entity.ScaleQuestionOption;
//import com.wftk.scale.biz.excel.batch.BatchHandleWrapper;
//import com.wftk.scale.biz.excel.handler.ExcelLineResult;
//import com.wftk.scale.biz.excel.model.ScaleQuestionExcelDataDTO;
//import com.wftk.scale.biz.excel.utils.ExcelContextUtil;
//import com.wftk.scale.biz.excel.utils.ExcelUtil;
//import com.wftk.scale.biz.mapper.ScaleQuestionMapper;
//import com.wftk.scale.biz.service.ScaleQuestionOptionService;
//import jakarta.annotation.PostConstruct;
//import jakarta.servlet.http.HttpServletResponse;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.compress.utils.Lists;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.io.ClassPathResource;
//import org.springframework.stereotype.Service;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.InputStream;
//import java.util.List;
//import java.util.Set;
//import java.util.concurrent.CompletableFuture;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//
///**
// * @ClassName: ScaleQuestionExcelBathHandler
// * @Description: 量表题目导入导出
// * @Author: mq
// * @Date: 2024/11/28
// * @Version: 1.0
// **/
//@Service
//@Slf4j
//public class ScaleQuestionExcelBathHandler extends ServiceImpl<ScaleQuestionMapper, ScaleQuestion> {
//
//    /**
//     * 默认EXCEL模板答案的最大数量，每个题目最大允许10个答案
//     */
//    private static final Integer IMPORT_MAX_QUESTION_NUM = 10;
//
//    /**
//     * 默认EXCEL模板答案得分的标签值，题目答案对应的标签值
//     */
//    private static final String[] IMPORT_QUESTION_LABLE = new String[]{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J"};
//
//    /**
//     * 默认提交题目数量
//     */
//    private static final Integer DEFAULT_BATCH_SIZE = 10;
//
//    /**
//     * 默认提交线程数
//     */
//    private static final Integer DEFAULT_TASK_COUNT = 1;
//
//    /**
//     * 定时提交间隔
//     */
//    private static final Integer DEFAULT_TIMEOUT = 1;
//
//    @Autowired
//    private ScaleQuestionOptionService scaleQuestionOptionService;
//
//    @PostConstruct
//    public void init() {
//        batch.start();
//    }
//
//    public BatchHandleWrapper.BatchHandler<ScaleQuestionQueryDTO> batchHandler = (list, task) -> {
//        this.saveBatchQuestion(list);
//        return 0;
//    };
//
//    public BatchHandleWrapper.EnqueueHandler<ScaleQuestionQueryDTO> enqueueHandler = (queue, obj) -> queue.offer(obj);
//
//    /**
//     * 通过批处理器进行数据库的批量写入，由于使用队列缓存,如果服务宕机会存在指定时间间隔数据的丢失。
//     */
//    public final BatchHandleWrapper<ScaleQuestionQueryDTO> batch = BatchHandleWrapper
//            //批处理器
//            .batchHandler(batchHandler)
//            //按照指定的时间间隔处理队列中的数据
//            .timeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
//            //当队列的数据达到设定的值时，进行提交处理
//            .batchSize(DEFAULT_BATCH_SIZE)
//            //处理线程的数量
//            .taskCount(DEFAULT_TASK_COUNT)
//            .enqueueHandler(enqueueHandler);
//
//    public void saveBatchQuestion(List<ScaleQuestionQueryDTO> questions) {
//        if (CollUtil.isEmpty(questions)) {
//            return;
//        }
//        List<ScaleQuestionOption> optionList = Lists.newArrayList();
//        List<ScaleQuestion> questionList = questions.stream().map(item -> {
//            ScaleQuestion question = new ScaleQuestion();
//            BeanUtils.copyProperties(item, question);
//            optionList.addAll(item.getOptions());
//            return question;
//        }).collect(Collectors.toList());
//        baseMapper.insert(questionList);
//        this.saveBatchQuestionOption(optionList);
//    }
//
//    public void saveBatchQuestionOption(List<ScaleQuestionOption> options) {
//        if (CollUtil.isEmpty(options)) {
//            return;
//        }
//        scaleQuestionOptionService.saveBatch(options);
//    }
//
//    public void downloadTemplate(HttpServletResponse response) {
//        try {
//            String templatePath = "/templates/量表题目模板.xlsx";
//            ClassPathResource classPathResource = new ClassPathResource(templatePath);
//            InputStream inputStream = classPathResource.getInputStream();
//            ExcelContextUtil.setExportHeader(response, classPathResource.getFilename());
//            byte[] buffer = new byte[1024];
//            int bytesRead;
//            while ((bytesRead = inputStream.read(buffer)) != -1) {
//                response.getOutputStream().write(buffer, 0, bytesRead);
//            }
//            inputStream.close();
//            response.flushBuffer();
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//        }
//    }
//
//    public void importExcel(Long scaleId, MultipartFile file) throws Exception {
////        ExcelUtil.read(file, ScaleQuestionExcelDataDTO.class, (rowData) -> this.convertToJavaData(scaleId, rowData), 1, response);
//        //增加下校验，对某一行某列数据错误做出提示
//        String tenantId = AuthenticationHolder.getAuthentication().getAuthUser().getTenantId();
//        List<ExcelLineResult<ScaleQuestionExcelDataDTO>> results = ExcelUtil.readContainsHeader(
//                file,
//                ScaleQuestionExcelDataDTO.class,
//                (rowData) -> this.convertToJavaData(scaleId, rowData, tenantId),
//                this::validImportList
//        );
//        CompletableFuture.runAsync(() -> {
//
//        })
//    }
//
//    private void validImportList(List<ExcelLineResult<ScaleQuestionExcelDataDTO>> read){
//        Set<String> questionNumSet = read.stream().filter(it -> {
//            ScaleQuestionExcelDataDTO data = it.getData();
//            return StrUtil.isBlank(data.getSubNumber()) && StrUtil.isNotBlank(data.getQuestionNumber());
//        }).map(dto -> dto.getData().getQuestionNumber()).collect(Collectors.toSet());
//        read.forEach(it -> {
//            ScaleQuestionExcelDataDTO data = it.getData();
//            //增加标题行
//            int rowIndex = it.getRowIndex() + 1;
//            if(checkInputNumberError(data.getQuestionNumber())){
//                throw new BusinessException("第" + rowIndex + "行数据有误，题号需为数字");
//            }
//            if(checkInputNumberError(data.getSubNumber())){
//                throw new BusinessException("第" + rowIndex + "行数据有误，子题号需为数字");
//            }
//            if(StrUtil.isNotBlank(data.getSubNumber()) && !questionNumSet.contains(data.getQuestionNumber())){
//                throw new BusinessException("第" + rowIndex + "行数据有误，子题号对应的题号不存在");
//            }
//        });
//    }
//
//    private boolean checkInputNumberError(String val){
//        try {
//           if(StrUtil.isNotBlank(val)){
//               Integer.parseInt(val);
//           }
//           return false;
//        }catch (Exception ignore){
//            return true;
//        }
//    }
//
//    protected void convertToJavaData(Long scaleId, ScaleQuestionExcelDataDTO rowData, String tenantId) {
//        ScaleQuestionQueryDTO question = ScaleQuestionQueryDTO.builder().build();
//        Long questionId = IdUtil.getSnowflakeNextId();
//        BeanUtils.copyProperties(rowData, question);
//        question.setId(questionId);
//        question.setScaleId(scaleId);
//        question.setOptions(this.convertQuestionOption(scaleId, questionId, rowData, tenantId));
//        question.setTenantId(tenantId);
//        //将数据添加至批处理器中进行批量提交处理
//        batch.add(question);
//    }
//
//    protected List<ScaleQuestionOption> convertQuestionOption(Long scaleId, Long questionId, ScaleQuestionExcelDataDTO rowData, String tenantId) {
//        List<ScaleQuestionOption> optionList = Lists.newArrayList();
//        for (int index = 1; index <= IMPORT_MAX_QUESTION_NUM; index++) {
//            Object answerObj = ReflectUtil.getFieldValue(rowData, "answer" + index);
//            Object answerScoreObj = ReflectUtil.getFieldValue(rowData, "answerScore" + index);
//            Object property = ReflectUtil.getFieldValue(rowData, "property" + index);
//            if (ObjUtil.isNotNull(answerObj) && ObjUtil.isNotNull(answerScoreObj)) {
//                ScaleQuestionOption option = new ScaleQuestionOption();
//                option.setScaleId(scaleId);
//                option.setQuestionId(questionId);
//                option.setValue(String.valueOf(answerObj));
//                option.setScore(Integer.valueOf(String.valueOf(answerScoreObj)));
//                option.setLabel(IMPORT_QUESTION_LABLE[index - 1]);
//                option.setTenantId(tenantId);
//                option.setResult(ObjectUtil.isEmpty(property) ? Boolean.FALSE : Boolean.valueOf(String.valueOf(property)));
//                optionList.add(option);
//            }
//        }
//        return optionList;
//    }
//
//    public void exportExcel(HttpServletResponse response, String fileName, List<ScaleQuestionQueryDTO> questionList) {
//        List<ScaleQuestionExcelDataDTO> dataList = this.convertToExcelData(questionList);
//        ExcelUtil.write(response, ScaleQuestionExcelDataDTO.class, fileName, dataList);
//    }
//
//    private List<ScaleQuestionExcelDataDTO> convertToExcelData(List<ScaleQuestionQueryDTO> questionList) {
//        return questionList.stream().map(question -> {
//            ScaleQuestionExcelDataDTO data = ScaleQuestionExcelDataDTO.builder().build();
//            BeanUtils.copyProperties(question, data);
//            List<ScaleQuestionOption> optionList = question.getOptions();
//            if (CollUtil.isNotEmpty(optionList)) {
//                for (int index = 1; index <= optionList.size(); index++) {
//                    ReflectUtil.setFieldValue(data, "answer" + index, optionList.get(index - 1).getValue());
//                    ReflectUtil.setFieldValue(data, "answerScore" + index, optionList.get(index - 1).getScore());
//                    ReflectUtil.setFieldValue(data, "property" + index, optionList.get(index - 1).getResult());
//                }
//            }
//            return data;
//        }).collect(Collectors.toList());
//    }
//}
