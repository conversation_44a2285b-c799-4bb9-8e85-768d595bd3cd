package com.wftk.scale.biz.ext.evaluator.function.scale.question;

import java.util.Map;

import com.github.xiaoymin.knife4j.core.util.StrUtil;
import com.googlecode.aviator.runtime.type.AviatorBigInt;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.wftk.scale.biz.entity.ScaleQuestionOption;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.ext.evaluator.constant.EnvConstant;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;
import com.wftk.scale.biz.ext.evaluator.function.scale.BaseScaleEvaluationFunction;
import com.wftk.scale.biz.service.ScaleQuestionOptionService;

import cn.hutool.core.util.NumberUtil;

/**
 * 根据量表题目ID获取所选答案的运算值(对应数据库scale_question_option表的operate_value字段)
 * 示例: q_opt_value("1"), 代表获取ID为1的选项的运算值（注意：参数是ID，所以可能需要结合选择题目的函数配合使用）
 * 
 * <AUTHOR>
 * @date 2025-09-10
 */
public class QueryAnswerOperationValueFunction extends BaseScaleEvaluationFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Question.Q_OPT_VALUE;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env, final AviatorObject arg1) {
        String questionId = (String) arg1.getValue(env);
        logger.info("q_opt_value: questionId: {}", questionId);
        if (StrUtil.isBlank(questionId)) {
            throw new IllegalArgumentException("questionId must not be blank");
        }
        if (!NumberUtil.isNumber(questionId)) {
            throw new IllegalArgumentException("questionId must be a number");
        }
        ScaleUserResultRecord record = getUserResultOption(env, Long.valueOf(questionId));
        if (record == null) {
            throw new IllegalArgumentException("questionId not found");
        }
        ScaleQuestionOptionService scaleQuestionOptionService = getFromEnv(env,
                EnvConstant.SCALE_QUESTION_OPTION_SERVICE, ScaleQuestionOptionService.class, false);
        ScaleQuestionOption scaleQuestionOption = scaleQuestionOptionService.getById(record.getOptionId());
        if (scaleQuestionOption == null) {
            throw new IllegalArgumentException("optionId not found");
        }

        return new AviatorBigInt(scaleQuestionOption.getOperateValue());
    }
}
