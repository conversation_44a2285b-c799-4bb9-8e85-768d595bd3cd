package com.wftk.scale.biz.event.publisher;

import com.wftk.scale.biz.event.SysRoleDeleteEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createDate 2024/12/4 17:27
 */
@Component
public class SysRoleDeletePublisher implements BaseEventPublisher<SysRoleDeleteEvent> {

    private final ApplicationEventPublisher applicationEventPublisher;

    public SysRoleDeletePublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Override
    public void publishEvent(SysRoleDeleteEvent sysRoleDeleteEvent) {
        applicationEventPublisher.publishEvent(sysRoleDeleteEvent);
    }
}
