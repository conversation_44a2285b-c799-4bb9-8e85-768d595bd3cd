package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.scale.ScaleQuestionHighestOverallScoreDTO;
import com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO;
import com.wftk.scale.biz.entity.ScaleQuestion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 题目答案表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleQuestionMapper extends BaseMapper<ScaleQuestion> {

    /*
     * @Author: mq
     * @Description: 校验量表题目内容是否有设置
     * @Date: 2024/11/4 14:36
     * @Param: scaleId
     * @return: boolean
     **/
    boolean vaildQuestionDataIntegrity(@Param("scaleId") Long scaleId);

    /*
     * @Author: mq
     * @Description: 校验量表题目编号是否已经存在
     * @Date: 2024/11/5 11:50
     * @Param: question
     * @return: boolean
     **/
    boolean vaildQuestionNumberExists(@Param("param") ScaleQuestion question);

    /*
     * @Author: mq
     * @Description: 校验量表题目是否存在子题号
     * @Date: 2024/12/16 9:58
     * @Param: scaleId-量表ID
     * @Param: questionNum-题号
     * @return: boolean
     **/
    boolean vaildQuestionChildrenExists(@Param("scaleId") Long scaleId, @Param("questionId") Long questionId, @Param("questionNum") String questionNum);

    /*
     * @Author: mq
     * @Description: 校验量表上层题目编号是否已经存在
     * @Date: 2024/12/6 13:48
     * @Param: question
     * @return: boolean
     **/
    boolean vaildQuestionUpperNumberExists(@Param("param") ScaleQuestion question);

    /*
     * @Author: mq
     * @Description: 根据量表ID获取关联的题目信息
     * @Date: 2024/11/4 17:07
     * @Param: scaleId
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleQuestion>
     **/
    List<ScaleQuestionQueryDTO> findByScaleId(@Param("scaleId") Long scaleId);

    /*
     * @Author: mq
     * @Description: 根据量表ID和题目ID获取题目详情信息
     * @Date: 2024/11/5 15:19
     * @Param: scaleId-量表ID
     * @Param: questionId-题目ID
     * @return: com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO
     **/
    ScaleQuestionQueryDTO findByScaleIdAndQuestionId(@Param("scaleId") Long scaleId, @Param("questionId") Long questionId);

    /*
     * @Author: mq
     * @Description: 根据量表ID删除关联的问题数据
     * @Date: 2024/11/4 11:02
     * @Param: scaleId-量表ID
     * @return: void
     **/
    void deleteByScaleId(@Param("scaleId") Long scaleId);

    /*
     * @Author: mq
     * @Description: 根据量表ID统计题目数量
     * @Date: 2024/11/7 9:38
     * @Param: scaleId-量表ID
     * @return: java.lang.Integer
     **/
    Integer getNumOfQuestion(@Param("scaleId") Long scaleId);

    ScaleQuestion findByQuestionId(@Param("questionId") Long questionId);

    List<ScaleQuestion> findListByScaleId(@Param("scaleId") Long scaleId);

    List<ScaleQuestion> getQuestionByIds(@Param("ids")List<Long> ids);
}
