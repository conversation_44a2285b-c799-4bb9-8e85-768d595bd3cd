package com.wftk.scale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.entity.TenantClientInfo;
import com.wftk.scale.biz.mapper.TenantClientInfoMapper;
import com.wftk.scale.biz.service.TenantClientInfoService;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 租户下的client信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27 15:50:09
 */
@Service
public class TenantClientInfoServiceImpl extends ServiceImpl<TenantClientInfoMapper, TenantClientInfo> implements TenantClientInfoService {

    @Override
    public TenantClientInfo findOneByClientId(@NonNull String clientId, @Nullable Boolean enable) {
        return baseMapper.selectByClintId(clientId, enable);
    }
}
