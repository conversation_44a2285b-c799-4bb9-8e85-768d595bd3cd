package com.wftk.scale.biz.dto.scale;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: ScaleWarnConfCreateInput
 * @Description: 创建量表预警阈值信息
 * @Author: mq
 * @Date: 2024-11-05 14:25
 * @Version: 1.0
 **/
@Data
@Builder
public class ScaleWarnConfCreateDTO implements Serializable {

    /**
     * 量表ID
     */
    private Long scaleId;

    /**
     * 因子ID
     */
    private Long factorId;

    /**
     * 预警逻辑，1大于，2等于，3小于, 4区间
     */
    private Integer type;

    /**
     * 预警阈值
     */
    private Integer threshold;

    /**
     * 区间起始值
     */
    private Integer start;

    /**
     * 区间结束值
     */
    private Integer end;

    /**
     * 标识标签
     */
    private String tag;

    /**
     * 标识标签
     */
    private Long tagId;

    /**
     * 预警方式，1短信，2邮件,可多个，逗号分隔。
     */
    private String warnType;

}
