package com.wftk.scale.biz.service.impl;

import com.wftk.scale.biz.event.ScaleCreateRelationEvent;
import com.wftk.scale.biz.service.ScaleFactorService;
import com.wftk.scale.biz.service.ScaleRelationService;
import com.wftk.scale.biz.service.ScaleResultIntroService;
import com.wftk.scale.biz.service.ScaleWarnConfService;
import jakarta.annotation.Resource;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class ScaleRelationServiceImpl implements ScaleRelationService {

    @Resource
    private ScaleFactorService scaleFactorService;
    @Resource
    private ScaleResultIntroService scaleResultIntroService;
    @Resource
    private ScaleWarnConfService scaleWarnConfService;

    @EventListener(ScaleCreateRelationEvent.class)
    public void onApplicationEvent(ScaleCreateRelationEvent event){
        //设置因子维度关联的题号
        Map<Long, Long> map = scaleFactorService.saveBatchByNewQuestion(event);
        //设置结果解读关联的所属因子
        scaleResultIntroService.saveBatchByNewFactor(event, map);
        //关联预警阈值的因子
        scaleWarnConfService.saveBatchByFactorId(event, map);
    }

}
