package com.wftk.scale.biz.converter;

import com.wftk.scale.biz.dto.user.role.SysRoleCreateDTO;
import com.wftk.scale.biz.dto.user.role.SysRoleUpdateDTO;
import com.wftk.scale.biz.entity.SysRole;
import com.wftk.scale.biz.excel.model.SysRoleExcelDataDTO;
import com.wftk.scale.biz.vo.SysRoleVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface SysRoleConverter {

    List<SysRoleVO> sysRoleListToSysRoleVOList(List<SysRole> sysRoleList);

    SysRoleVO sysRoleToSysRoleVO(SysRole sysRole);

    SysRole sysRoleCreateDtoToSysRole(SysRoleCreateDTO dto);

    SysRole sysRoleUpdateDtoToSysRole(SysRoleUpdateDTO dto);

    List<SysRoleExcelDataDTO> sysRoleListToSysRoleExcelDataDTOList(List<SysRole> sysRoleList);
}
