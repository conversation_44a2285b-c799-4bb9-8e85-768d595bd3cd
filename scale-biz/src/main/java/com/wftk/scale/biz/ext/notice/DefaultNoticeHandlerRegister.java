package com.wftk.scale.biz.ext.notice;

import cn.hutool.core.collection.CollUtil;
import com.wftk.scale.biz.ext.notice.enums.NoticeTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class DefaultNoticeHandlerRegister implements NoticeHandlerRegister{

    private final Map<NoticeTypeEnum, NoticeHandler<? extends NoticeRequest>> noticeHandlerMap;

    public DefaultNoticeHandlerRegister(List<NoticeHandler<? extends NoticeRequest>> noticeHandlerList){
        if (CollUtil.isEmpty(noticeHandlerList)) {
            throw new IllegalArgumentException("NoticeHandler list cannot be empty");
        }
        noticeHandlerMap = noticeHandlerList.stream().collect(Collectors.toMap(NoticeHandler::getNoticeType, Function.identity()));
        log.info("NoticeHandlerRegister init success {}", noticeHandlerMap);

    }

    @Override
    public NoticeHandler<? extends NoticeRequest> getHandler(NoticeTypeEnum noticeTypeEnum) {
        NoticeHandler<? extends NoticeRequest> noticeHandler = noticeHandlerMap.get(noticeTypeEnum);
        if (noticeHandler == null) {
            log.error("NoticeHandler not found for noticeType: {}", noticeTypeEnum);
            throw new IllegalArgumentException("NoticeHandler not found for noticeType: " + noticeTypeEnum);
        }
        return noticeHandler;
    }

}
