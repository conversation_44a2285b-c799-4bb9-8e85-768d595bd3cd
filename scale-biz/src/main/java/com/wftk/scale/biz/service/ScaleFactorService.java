package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.event.ScaleCreateRelationEvent;
import com.wftk.scale.biz.dto.scale.ScaleFactorDTO;
import com.wftk.scale.biz.entity.ScaleFactor;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 因子维度 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleFactorService extends IService<ScaleFactor> {

    /*
     * @Author: mq
     * @Description: 校验量表因子维度容是否有设置
     * @Date: 2024/11/4 14:39
     * @Param: scaleId-量表ID
     * @return: boolean
     **/
    boolean vaildFactorDataIntegrity(Long scaleId);

    /*
     * @Author: mq
     * @Description: 校验因子公式是否被禁用
     * @Date: 2024/11/25 16:47
     * @Param: scaleId-量表ID
     * @return: String
     **/
    String checkFactorFormulaEnable(Long scaleId);

    /**
     * 校验因子公式是否可用
     *
     * @Description: 校验量表因子维度容是否有设置
     * @Date: 2024/11/4 14:39
     * @Param: scaleId-量表ID
     * @return: boolean
     */
    boolean vaildFactorFormulaEnable(Long scaleId);

    /**
     * 校验因子公式是否可用
     *
     * @Description: 校验量表因子维度容是否有设置
     * @Date: 2024/11/4 14:39
     * @Param: scaleId-量表ID
     * @return: boolean
     */
    boolean vaildFactorFormulaEnable(List<Long> scaleIds);

    /*
     * @Author: mq
     * @Description: 校验量表因子维度名称是否存在
     * @Date: 2024/11/5 9:54
     * @Param: factorId-因子维度主键ID
     * @Param: scaleId-量表ID
     * @Param: factorName-因子维度名称
     * @return: boolean
     **/
    boolean vaildFactorName(Long factorId, Long scaleId, String factorName);


    /**
     * 判断该量表下是否存在该类型因子
     *
     * @param scaleId 量表id
     * @param id
     * @param type
     */
    boolean checkFactorType(Long scaleId, Long id,Integer type);

    ScaleFactor getFactorByScaleIdAndType(Long scaleId, Integer type);

    /**
     *
     * @param scaleId
     * @param type
     * @return
     */
    List<ScaleFactor> getFactorListByScaleIdAndType(Long scaleId, Integer type);

    /*
     * @Author: mq
     * @Description: 校验题目关联的因子维度信息
     * @Date: 2024/12/11 16:20
     * @Param: scaleId-量表ID
     * @Param: questionId-题目ID
     * @return: ScaleFactor
     **/
    String checkFactorRelationQuestion(Long scaleId, Long questionId);

    ScaleFactor getByScaleIdAndName( Long scaleId, String factorName);

    /*
     * @Author: mq
     * @Description: 创建量表因子维度信息
     * @Date: 2024/11/5 10:02
     * @Param: scaleFactor-因子维度信息
     * @return: void
     **/
    void create(ScaleFactor scaleFactor);

    /*
     * @Author: mq
     * @Description: 修改量表因子维度信息
     * @Date: 2024/11/5 10:06
     * @Param: scaleFactor-因子维度信息
     * @return: void
     **/
    void modify(ScaleFactor scaleFactor);

    /*
     * @Author: mq
     * @Description: 根据因子维度ID删除因子维度信息
     * @Date: 2024/11/5 10:08
     * @Param: factorId-因子维度ID
     * @return: void
     **/
    void delete(Long factorId);

    /*
     * @Author: mq
     * @Description: 根据条件检索因子维度信息
     * @Date: 2024/11/5 10:10
     * @Param: factorName-因子维度名称
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.scale.ScaleFactorDTO>
     **/
    Page<ScaleFactorDTO> selectPage(Long scaleId, String factorName);

    /*
     * @Author: mq
     * @Description: 根据量表ID获取因子维度信息
     * @Date: 2024/11/7 14:50
     * @Param: scaleId-量表ID
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleFactor>
     **/
    List<ScaleFactor> findByScaleId(Long scaleId);

    /**
     * @Author: mq
     * @Description: 根据因子维度ID获取因子名称
     * @Date: 2024/11/25 18:28
     * @Param: factorId-因子维度ID
     * @return: String
     **/
    String getNameByScaleId(Long factorId);

    /**
     * 获取新老因子的对应关系，结果解读，预警阈值都有用的
     */
    Map<Long, Long> saveBatchByNewQuestion(ScaleCreateRelationEvent event);

    ScaleFactor getById(Long id);
}
