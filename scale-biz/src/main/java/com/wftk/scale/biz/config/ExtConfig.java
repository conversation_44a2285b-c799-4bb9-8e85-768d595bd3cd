package com.wftk.scale.biz.config;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.message.spring.boot.autoconfigure.ext.properties.MessageProperties;
import com.wftk.scale.biz.ext.evaluator.core.EvaluatorEngine;
import com.wftk.scale.biz.ext.evaluator.engine.AviatorEvaluatorEngine;
import com.wftk.scale.biz.ext.evaluator.engine.JavaPackageFunctionLoader;
import com.wftk.scale.biz.ext.notice.DefaultNoticeHandlerRegister;
import com.wftk.scale.biz.ext.notice.NoticeHandler;
import com.wftk.scale.biz.ext.notice.NoticeHandlerRegister;
import com.wftk.scale.biz.ext.notice.NoticeRequest;
import com.wftk.scale.biz.ext.notice.email.EmailNoticeHandler;
import com.wftk.scale.biz.ext.notice.sms.SmsNoticeHandler;
import com.wftk.scale.biz.ext.notifier.DefaultNotificationClient;
import com.wftk.scale.biz.ext.notifier.NotificationClient;
import com.wftk.scale.biz.ext.sms.DefaultSmsApi;
import com.wftk.scale.biz.ext.sms.SmsApi;
import com.wftk.scale.biz.ext.wechat.core.DefaultWechatClientFactory;
import com.wftk.scale.biz.ext.wechat.core.WechatClientFactory;
import com.wftk.scale.biz.manager.job.action.RequestAction;
import com.wftk.scale.biz.manager.job.executor.RequestExecutor;
import com.wftk.scale.biz.manager.job.registry.DefaultActionRegistry;
import com.wftk.scale.biz.property.ThreadPoolProperty;
import com.wftk.scale.biz.service.HttpJobService;
import com.wftk.scale.biz.service.OrderService;
import com.wftk.scale.biz.service.ScaleListingService;
import com.wftk.scale.biz.service.TerminalConfService;
import com.wftk.scale.biz.thread.BizThreadPoolTaskExecutor;
import com.wftk.sms.client.spring.boot.autoconfigure.core.client.SmsClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 扩展相关(不扫包，通过@Bean实例化)
 *
 * <AUTHOR>
 * @create 2024/10/24 10:01
 */
@Configuration
@EnableConfigurationProperties(ThreadPoolProperty.class)
class ExtConfig {

    @Bean
    WechatClientFactory wechatClientFactory() {
        return new DefaultWechatClientFactory();
    }

    @Bean
    NotificationClient notificationClient(HttpRequestExecutor httpRequestExecutor,
            TerminalConfService terminalConfService, OrderService orderService,
            ScaleListingService scalelistingService) {
        return new DefaultNotificationClient(httpRequestExecutor, terminalConfService, orderService,
                scalelistingService);
    }

    @Bean
    RequestExecutor requestExecutor(Map<String, RequestAction> actions, HttpJobService httpJobService) {
        DefaultActionRegistry defaultActionRegistry = new DefaultActionRegistry(actions);
        return new RequestExecutor(defaultActionRegistry, httpJobService);
    }

    @Bean
    SmsApi smsApi(SmsClient smsClient) {
        return new DefaultSmsApi(smsClient, "prod");
    }

    @Bean
    SmsNoticeHandler smsNoticeHandler(SmsApi smsApi) {
        return new SmsNoticeHandler(smsApi);
    }

    @Bean
    EmailNoticeHandler emailNoticeHandler(JavaMailSender javaMailSender, MessageProperties messageProperties) {
        return new EmailNoticeHandler(javaMailSender, messageProperties.getMail());
    }

    @Bean
    NoticeHandlerRegister defaultNoticeHandlerRegister(List<NoticeHandler<? extends NoticeRequest>> list) {
        return new DefaultNoticeHandlerRegister(list);
    }

    @Bean
    EvaluatorEngine aviatorEvaluatorEngine() {
        AviatorEvaluatorInstance evaluatorInstance = AviatorEvaluator.getInstance();
        JavaPackageFunctionLoader javaPackageFunctionLoader = new JavaPackageFunctionLoader(
                List.of("com.wftk.scale.biz.ext.evaluator.function"));
        evaluatorInstance.addFunctionLoader(javaPackageFunctionLoader);
        return new AviatorEvaluatorEngine(evaluatorInstance);
    }
    @Bean
    BizThreadPoolTaskExecutor bizThreadPoolTask(ThreadPoolProperty threadPoolProperty, TaskDecorator taskDecorator) {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setTaskDecorator(taskDecorator);
        threadPoolTaskExecutor.setMaxPoolSize(threadPoolProperty.maxPoolSize());
        threadPoolTaskExecutor.setKeepAliveSeconds(threadPoolProperty.keepAliveSeconds());
        threadPoolTaskExecutor.setCorePoolSize(threadPoolProperty.corePoolSize());
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.setQueueCapacity(threadPoolProperty.queueCapacity());
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        threadPoolTaskExecutor.setThreadNamePrefix(threadPoolProperty.namePrefix());
        threadPoolTaskExecutor.initialize();
        return new BizThreadPoolTaskExecutor(threadPoolTaskExecutor);
    }


}
