package com.wftk.scale.biz.manager.job.action;

import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.jackson.exception.JSONException;
import com.wftk.scale.biz.constant.TerminalConfConstant;
import com.wftk.scale.biz.constant.enums.HttpResultStatusEnum;
import com.wftk.scale.biz.dto.httpjob.HttpResult;
import com.wftk.scale.biz.dto.notify.EvaluationRecordNotifyInput;
import com.wftk.scale.biz.dto.notify.OrderStautsNotifyInput;
import com.wftk.scale.biz.entity.HttpJob;
import com.wftk.scale.biz.entity.Order;
import com.wftk.scale.biz.entity.ScaleUserResult;
import com.wftk.scale.biz.service.NotifyService;
import com.wftk.scale.biz.service.OrderService;
import com.wftk.scale.biz.service.ScaleUserResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2025/1/6 11:23
 */
@Component(TerminalConfConstant.ORDER_EVALUATION_RECORD_NOTIFY)
@Slf4j
public class EvaluationRecordAction implements RequestAction{

    @Autowired
    NotifyService notifyService;

    @Autowired
    OrderService orderService;

    @Autowired
    ScaleUserResultService scaleUserResultService;

    @Override
    public void doRequestAction(HttpJob httpJob) {
        JSONObject instance = JSONObject.getInstance();
        try{
            EvaluationRecordNotifyInput evaluationRecordNotifyInput = instance.parseObject(httpJob.getParams(), EvaluationRecordNotifyInput.class);
            ScaleUserResult scaleUserResult = scaleUserResultService.getById(Long.parseLong(evaluationRecordNotifyInput.getEvaluationRecordNo()));
            Order order = orderService.getByOrderNo(scaleUserResult.getOrderNo());
            HttpResult httpResult = notifyService.evaluationRecordNotify(evaluationRecordNotifyInput, order.getTerminalCode());

            if(!Objects.equals(httpResult.getStatus(), HttpResultStatusEnum.SUCCESS.getStatus())) {
                throw new BusinessException(httpResult.getMsg());
            }
        }catch (BusinessException e){
            throw e;
        }catch (JSONException e){
            log.error("evaluation record notify job json convert error.",e);
            throw new BusinessException("params format error .");
        }catch (Exception e){
            log.error("evaluation record notify job execute error",e);
            throw new BusinessException("http request error .");
        }
    }
}
