package com.wftk.scale.biz.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SysUserChangeEvent extends ApplicationEvent {

    private Long sysUserId;

    private List<Long> roleId;

    private Long sysDepartmentId;

    public SysUserChangeEvent(Long sysUserId, List<Long> roleId, Long sysDepartmentId) {
        super(sysUserId);
        this.sysUserId = sysUserId;
        this.roleId = roleId;
        this.sysDepartmentId = sysDepartmentId;
    }
}
