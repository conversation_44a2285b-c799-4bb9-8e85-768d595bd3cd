package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.scale.ScaleDTO;
import com.wftk.scale.biz.dto.scale.ScaleListingDetailDTO;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleSerialDTO;
import com.wftk.scale.biz.entity.Scale;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 量表主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleMapper extends BaseMapper<Scale> {

    /*
     * @Author: mq
     * @Description: 校验量表编码是否存在
     * @Date: 2024/11/1 14:49
     * @Param: scaleId - 主键ID
     * @Param: scaleName - 量表名称
     * @return: boolean (true-已存在 false-未存在)
     **/
    boolean validScaleNameExists(@Param("scaleId") Long scaleId, @Param("scaleCode") String scaleCode, @Param("scaleName") String scaleName);

    /*
     * @Author: mq
     * @Description: 获取单个量表最新版本数据
     * @Date: 2024/11/4 13:53
     * @Param: scaleName-量表名称
     * @Param: complateStatus-量表状态
     * @return: java.util.List<com.wftk.scale.biz.dto.scale.ScaleQueryDTO>
     **/
    List<ScaleQueryDTO> selectListByScaleNameAndCompleteStatus(@Param("scaleName") String scaleName, @Param("complateStatus") Integer complateStatus);

    /*
     * @Author: mq
     * @Description: 根据ID更新量表完成状态
     * @Date: 2024/11/4 14:16
     * @Param: scaleId
     * @Param: status
     * @Param: opUser
     * @return: void
     **/
    void updateComplateStatus(@Param("scaleId") Long scaleId, @Param("status") Integer status, @Param("opUser") String opUser);

    /**
     * 获取最新版本的量表
     * @param scaleCode
     * @return
     */
    Scale getLatestScaleByCode(@Param("scaleCode")String scaleCode);

    /**
     * 获取最新版本的量表
     * @param scaleCodes
     * @return
     */
    List<Scale> getLatestScaleListByCodes(@Param("scaleCodes")List<String> scaleCodes);



    /**
     * 获取已上架量表
     * @return
     */
    List<ScaleDTO> getListedScale(@Param("scaleId")Long scaleId,
                                  @Param("scaleCode")String scaleCode,
                                  @Param("scaleName")String scaleName,
                                  @Param("scaleType")Long scaleType,
                                  @Param("listingShowType")Integer listingShowType,
                                  @Param("terminalCode")String terminalCode);

    /**
     * 获取已上架量表详情
     * @return
     */
    ScaleListingDetailDTO getListedScaleDetail(@Param("scaleCode")String scaleCode, @Param("terminalCode")String terminalCode,@Param("scaleListingId")Long scaleListingId);

    List<ScaleListingDetailDTO> getScaleListingDetailDTO(@Param("scaleSerials")List<ScaleSerialDTO> scaleSerials, @Param("terminalCode")String terminalCode);

    Scale getById(@Param("id")Long id);
}
