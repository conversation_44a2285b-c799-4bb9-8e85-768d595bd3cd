package com.wftk.scale.biz.ext.evaluator.function.scale.question;

import java.util.Map;

import com.googlecode.aviator.runtime.type.AviatorLong;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;

import cn.hutool.core.util.StrUtil;

/**
 * 获取传入题目的数量（通常为逗号分隔的字符串）
 * 示例: q_size("1,2,3")
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
public class QuestionSizeFunction extends BaseScaleQuestionFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Question.Q_SIZE;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env, final AviatorObject arg1) {
        String questionIds = (String) arg1.getValue(env);
        logger.info("q_size: questionIds: {}", questionIds);
        if (StrUtil.isBlank(questionIds)) {
            return AviatorLong.valueOf(0L);
        }
        return AviatorLong.valueOf(questionIds.split(",").length);
    }

}
