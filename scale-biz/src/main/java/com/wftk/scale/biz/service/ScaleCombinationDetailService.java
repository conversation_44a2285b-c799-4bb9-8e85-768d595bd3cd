package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.entity.ScaleCombinationDetail;

import java.util.List;

/**
 * <p>
 * 组合量表详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleCombinationDetailService extends IService<ScaleCombinationDetail> {

    /*
     * @Author: mq
     * @Description: 复制组合量表详情信息
     * @Date: 2024/11/7 11:19
     * @Param: oldScaleId
     * @Param: newScaleId
     * @Param: opUser
     * @return: void
     **/
    void copy(Long oldScaleId, Long newScaleId, String opUser);

    /**
     * 获取量表id
     * @param combinationId
     * @return
     */
    List<Long> findScaleIdByCombinationId( Long combinationId);

    /**
     * 获取量表id
     * @param combinationIds
     * @return
     */
    List<Long> findScaleIdByCombinationIds(List<Long> combinationIds);

    /* 
     * @Author: mq
     * @Description: 创建组合量表选项信息
     * @Date: 2024/11/6 18:26 
     * @Param: combinationId 
     * @Param: scaleIds
     * @return: void 
     **/
    void create(Long combinationId, List<ScaleCombinationDetail> scaleIds);

    /* 
     * @Author: mq
     * @Description: 修改组合量表选项信息
     * @Date: 2024/11/6 18:27 
     * @Param: combinationId 
     * @Param: scaleIds
     * @return: void 
     **/
    void modify(Long combinationId, List<ScaleCombinationDetail> scaleIds);
    
    /* 
     * @Author: mq
     * @Description: 根据组合ID删除详情信息
     * @Date: 2024/11/7 13:33
     * @Param: combinationId  
     * @return: void 
     **/
    void deleteByCombinationId(Long combinationId);

    /* 
     * @Author: mq
     * @Description: 根据组合ID获取关联的量表信息
     * @Date: 2024/11/7 9:52 
     * @Param: combinationId-组合量表ID
     * @return: java.util.List<com.wftk.scale.biz.dto.scale.ScaleQueryDTO> 
     **/
    List<ScaleQueryDTO> findByCombinationId(Long combinationId);
    
}
