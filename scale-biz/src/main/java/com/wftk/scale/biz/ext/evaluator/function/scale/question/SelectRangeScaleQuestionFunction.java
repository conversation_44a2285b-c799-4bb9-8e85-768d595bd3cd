package com.wftk.scale.biz.ext.evaluator.function.scale.question;

import java.util.List;
import java.util.Map;

import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.wftk.scale.biz.entity.ScaleQuestion;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;
import cn.hutool.core.util.StrUtil;

/**
 * 范围选择量表题目函数(多个题目编号范围用逗号分隔)
 * 示例：q_select_range("1-8,2.1-2.5"), 表示选择当前量表中编号为1-8题和2.1-2.5题
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public class SelectRangeScaleQuestionFunction extends BaseScaleQuestionFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Question.Q_SELECT_RANGE;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env, final AviatorObject arg1) {
        String ranges = (String) arg1.getValue(env);
        logger.info("q_select_range: ranges: {}", ranges);
        if (StrUtil.isBlank(ranges)) {
            throw new IllegalArgumentException("ranges must not be blank");
        }
        List<ScaleQuestionSequence> questionSequence = null;
        if (ranges.contains(",")) {
            String[] rangesArray = ranges.split(",");
            questionSequence = buildQuestionSequenceWithRange(rangesArray);
        } else {
            questionSequence = buildQuestionSequenceWithRange(ranges);
        }
        List<ScaleQuestion> scaleQuestions = selectBySequence(env, questionSequence);
        String questionIds = getQuestionIds(scaleQuestions);

        return new AviatorString(questionIds);
    }

}
