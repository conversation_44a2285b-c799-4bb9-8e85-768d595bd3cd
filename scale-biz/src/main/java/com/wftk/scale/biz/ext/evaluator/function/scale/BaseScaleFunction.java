package com.wftk.scale.biz.ext.evaluator.function.scale;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.wftk.scale.biz.entity.ScaleQuestion;
import com.wftk.scale.biz.service.ScaleQuestionService;
import com.wftk.scale.biz.ext.evaluator.constant.EnvConstant;

/**
 * 量表相关函数
 * <AUTHOR>
 * @date 2025-09-03
 */
public abstract class BaseScaleFunction extends AbstractFunction {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 获取量表题目
     * @param env
     * @return
     */
    protected List<ScaleQuestion> getQuestions(Map<String, Object> env) {
        ScaleQuestionService scaleQuestionService = getFromEnv(env, EnvConstant.SCALE_QUESTION_SERVICE, ScaleQuestionService.class, false);
        if (scaleQuestionService == null) {
            throw new IllegalArgumentException("scaleQuestionService is not found in env");
        }
        Long scaleId = getFromEnv(env, EnvConstant.SCALE_ID, Long.class, false);
        if (scaleId == null) {
            throw new IllegalArgumentException("scaleId is not found in env");
        }
        return scaleQuestionService.findEntitysByScaleId(scaleId);
    }


    /**
     * 从环境变量中获取值
     * @param env
     * @param key
     * @param clazz
     * @param allowNull 是否允许为空
     * @return
     */
    protected <T> T getFromEnv(Map<String, Object> env, String key, Class<T> clazz, boolean allowNull) {
        Object value = env.get(key);
        if (value == null) {
            if (allowNull) {
                return null;
            }
            throw new IllegalArgumentException("environment variable " + key + " is not found");
        }
        return clazz.cast(value);
    }
}
