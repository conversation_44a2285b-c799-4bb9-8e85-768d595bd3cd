package com.wftk.scale.biz.excel.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.wftk.scale.biz.excel.handler.ExcelImportListener;
import com.wftk.scale.biz.excel.model.SelectDataListBO;
import com.wftk.scale.biz.excel.service.CheckDataIntegrityFunctionService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * <AUTHOR>
 */
@Slf4j
public class ExcelUtil {

    /**
     * 每个Sheet数据切片大小
     */
    private static final Integer PARTITION_LIMIT_SIZE = 1000;

    /**
     * Sheet名称
     */
    private static final String EXCEL_SHEET_NAME = "%s-第%s页";

    public static <T> void write(HttpServletResponse response, Class<T> clazz, String fileName, List<T> dataList) {
        write(response, clazz, fileName, dataList, null);
    }

    public static <T> void write(HttpServletResponse response, Class<T> clazz, String fileName, List<T> dataList,
                                 List<SelectDataListBO> list) {
        ExcelContextUtil.setExportHeader(response, fileName);
        //如果dataList数据过大,写入一个sheet,会导致excel打开编辑操作响应比较慢,或者是旧版excel会有条数写入限制，所以将dataList进行数据切片
        List<List<T>> partitionList = ListUtil.partition(dataList, PARTITION_LIMIT_SIZE);
        Integer[] sheet = new Integer[]{0};
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), clazz).build()) {
            partitionList.forEach(partition -> {
                //创建sheet页
                String sheetName = String.format(EXCEL_SHEET_NAME, fileName, sheet[0]);
                ExcelWriterSheetBuilder builder = EasyExcel.writerSheet(sheetName).registerWriteHandler(new CustomCellWriteWidthConfig());
                if(CollUtil.isNotEmpty(list)){
                    for (SelectDataListBO bo : list) {
                        builder.registerWriteHandler(new CustomSheetWriteHandler(bo.getSelectDataList(), bo.getSelectDataIndex(), bo.getSheetName()));
                    }
                }
                WriteSheet writeSheet = builder.build();
                //写入数据
                excelWriter.write(partition, writeSheet);
            });
            excelWriter.finish();
        } catch (Exception e) {
            log.error("文件写入失败", e);
        }
    }

    public static <T> List<T> readContainsHeader(MultipartFile file,
                                                 Class<T> clazz,
                                                 CheckDataIntegrityFunctionService<T> checkDataIntegrityFunctionService) throws Exception {
        ExcelImportListener<T> listener = new ExcelImportListener<>(checkDataIntegrityFunctionService);
        @Cleanup InputStream inputStream = file.getInputStream();
        EasyExcel.read(inputStream, clazz, listener)
                .headRowNumber(1)
                .sheet()
                .doRead();
        return listener.getExcelLineResultList();
    }

    private record CustomSheetWriteHandler(List<String> selectDataList, Integer selectDataListIndex, String hiddenSheetName) implements SheetWriteHandler {
        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            /*
             * 构造下拉选项单元格列的位置，以及下拉选项可选参数值的map集合
             * key：下拉选项要放到哪个单元格，比如A列的单元格那就是0，C列的单元格，那就是2
             * value：key对应的那个单元格下拉列表里的数据项，比如这里就是下拉选项1..100
             */
            if (CollUtil.isEmpty(selectDataList) || selectDataListIndex == null || selectDataListIndex < 0) {
                return;
            }
            // 获取当前sheet页
            Sheet sheet = writeSheetHolder.getCachedSheet();
            // 获取sheet页的数据校验对象
            DataValidationHelper helper = sheet.getDataValidationHelper();
            // 获取工作簿对象，用于创建存放下拉数据的字典sheet数据页
            Workbook workbook = writeWorkbookHolder.getWorkbook();
            // 设置存放下拉数据的字典sheet，并把这些sheet隐藏掉，这样用户交互更友好
            Sheet dictSheet = workbook.getSheet(hiddenSheetName);
            if(dictSheet == null){
                dictSheet = workbook.createSheet(hiddenSheetName);
                // 隐藏字典sheet页
                int sheetIndex = workbook.getSheetIndex(dictSheet);
                workbook.setSheetHidden(sheetIndex, true);
                for (int i = 0; i < selectDataList.size(); i++) {
                    // 向字典sheet写数据，从第一行开始写，此处可根据自己业务需要，自定义从第几行还是写，写的时候注意一下行索引是从0开始的即可
                    dictSheet.createRow(i).createCell(0).setCellValue(selectDataList.get(i));
                }
            }
            /*
             * 设置下拉列表覆盖的行数，从第一行开始到最后一行，这里注意，Excel行的索引是从0开始的，
             * 最后一行的行索引是1048575，千万别写成1048576，不然会导致下拉列表失效，出不来
             */
            CellRangeAddressList infoList = new CellRangeAddressList(1, 1048575, selectDataListIndex, selectDataListIndex);
            // 设置关联数据公式，这个格式跟Excel设置有效性数据的表达式是一样的
            String refers = hiddenSheetName + "!$A$1:$A$" + selectDataList.size();
            Name name = workbook.createName();
            name.setNameName(hiddenSheetName);
            // 将关联公式和sheet页做关联
            name.setRefersToFormula(refers);
            // 将上面设置好的下拉列表字典sheet页和目标sheet关联起来
            DataValidationConstraint constraint = helper.createFormulaListConstraint(hiddenSheetName);
            DataValidation dataValidation = helper.createValidation(constraint, infoList);
            sheet.addValidationData(dataValidation);
        }
    }

    private static class CustomCellWriteWidthConfig extends AbstractColumnWidthStyleStrategy {

        private final Map<Integer, Map<Integer, Integer>> CACHE = MapUtils.newHashMapWithExpectedSize(8);

        @Override
        protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer integer, Boolean isHead) {
            boolean needSetWidth = isHead || CollUtil.isNotEmpty(cellDataList);
            if (needSetWidth) {
                Map<Integer, Integer> maxColumnWidthMap = CACHE.computeIfAbsent(writeSheetHolder.getSheetNo(), k -> new HashMap<>(8));
                Integer columnWidth = this.dataLength(cellDataList, cell, isHead);
                if (columnWidth > 0) {
                    if (columnWidth < 10) {
                        columnWidth = 10;
                    } else if (columnWidth >= 255) {
                        columnWidth = 255;
                    }
                    Integer maxColumnWidth = maxColumnWidthMap.get(cell.getColumnIndex());
                    if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
                        maxColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
                        Sheet sheet = writeSheetHolder.getSheet();
                        sheet.setColumnWidth(cell.getColumnIndex(), 256 * columnWidth + 184);
                    }
                }
            }
        }

        private Integer dataLength(List<WriteCellData<?>> cellDataList, Cell cell, Boolean isHead) {
            if (isHead) {
                return cell.getStringCellValue().getBytes().length;
            } else {
                WriteCellData<?> cellData = cellDataList.get(0);
                CellDataTypeEnum type = cellData.getType();
                if (type == null) {
                    return -1;
                } else {
                    return switch (type) {
                        case STRING -> {
                            int index = cellData.getStringValue().indexOf("\n");
                            yield index != -1 ?
                                    cellData.getStringValue().substring(0, index).getBytes().length + 1 :
                                    cellData.getStringValue().getBytes().length + 1;
                        }
                        case BOOLEAN -> cellData.getBooleanValue().toString().getBytes().length;
                        case NUMBER -> cellData.getNumberValue().toString().getBytes().length;
                        case DATE -> cell.getDateCellValue().toString().getBytes().length;
                        default -> -1;
                    };
                }
            }
        }
    }
}
