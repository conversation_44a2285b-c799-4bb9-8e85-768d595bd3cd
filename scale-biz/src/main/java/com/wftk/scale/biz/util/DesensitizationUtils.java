package com.wftk.scale.biz.util;

/**
 * <AUTHOR>
 */
public class DesensitizationUtils {

    /**
     * 脱敏身份证号（保留前六位和后四位，其余用*号替换）
     * @param idCard 身份证号
     * @return 脱敏后的身份证号
     */
    public static String desensitizeIdCard(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            // 如果身份证号无效或为空，直接返回
            return idCard;
        }
        return idCard.substring(0, 6) + "********" + idCard.substring(14);
    }

    /**
     * 脱敏手机号（保留前三位和后四位，其余用*号替换）
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    public static String desensitizePhone(String phone) {
        if (phone == null || phone.length() != 11) {
            // 如果手机号无效或为空，直接返回
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 脱敏姓名（只保留姓，名用*号替换）
     * @param name 姓名
     * @return 脱敏后的姓名
     */
    public static String desensitizeName(String name) {
        if (name == null || name.isEmpty()) {
            // 如果姓名为空，直接返回
            return name;
        }
        return name.charAt(0) + "*" + name.substring(1);
    }

    /**
     * 脱敏银行卡号（保留前四位和后四位，其余用*号替换）
     * @param bankCard 银行卡号
     * @return 脱敏后的银行卡号
     */
    public static String desensitizeBankCard(String bankCard) {
        if (bankCard == null || bankCard.length() < 16) {
            // 如果银行卡号无效或为空，直接返回
            return bankCard;
        }
        return bankCard.substring(0, 4) + " **** **** " + bankCard.substring(bankCard.length() - 4);
    }

    /**
     * 脱敏地址（保留前面一部分和最后部分，其余用*号替换）
     * @param address 地址
     * @return 脱敏后的地址
     */
    public static String desensitizeAddress(String address) {
        // 如果地址为空，直接返回
        if (address == null || address.isEmpty()) {
            return address;
        }
        // 保留前五个字符和最后五个字符
        if (address.length() <= 10) {
            // 地址长度过短，直接返回
            return address;
        }
        return address.substring(0, 5) + "*****" + address.substring(address.length() - 5);
    }
}
