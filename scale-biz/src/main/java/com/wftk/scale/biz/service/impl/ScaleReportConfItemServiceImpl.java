package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.scale.biz.constant.ScaleReportConfItemConstant;
import com.wftk.scale.biz.event.ScaleDelEvent;
import com.wftk.scale.biz.entity.ScaleReportConfItem;
import com.wftk.scale.biz.mapper.ScaleReportConfItemMapper;
import com.wftk.scale.biz.service.ScaleReportConfItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 报告设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04 14:29:16
 */
@Service
public class ScaleReportConfItemServiceImpl extends ServiceImpl<ScaleReportConfItemMapper, ScaleReportConfItem> implements ScaleReportConfItemService {

    @Autowired
    ScaleReportConfItemMapper scaleReportConfItemMapper;

    @Override
    public List<ScaleReportConfItem> findByReportConfId(Long reportConfId, List<String> itemCodes) {
        return scaleReportConfItemMapper.findByReportConfId(reportConfId, itemCodes);
    }

    @Override
    public List<ScaleReportConfItem> findByReportConfIdAndFactorId(Long reportConfId, List<String> itemCodes, Long factorId) {
        return scaleReportConfItemMapper.findByReportConfIdAndFactorId(reportConfId, itemCodes, factorId);
    }

    @Override
    public void create(ScaleReportConfItem scaleReportConfItem) {
        checkScoreRangeRepeat(scaleReportConfItem);
        scaleReportConfItemMapper.insert(scaleReportConfItem);
    }

    @Override
    public void modify(ScaleReportConfItem scaleReportConfItem) {
        checkScoreRangeRepeat(scaleReportConfItem);
        scaleReportConfItemMapper.updateById(scaleReportConfItem);
    }

    private Integer[] getIntegerArrayByItemDescribe(String itemDescribe){
        if(StrUtil.isBlank(itemDescribe) || itemDescribe.length() <= 2){
            return null;
        }
        String[] split = itemDescribe.substring(1, itemDescribe.length() - 1).split(",");
        return new Integer[]{Integer.parseInt(split[0]), Integer.parseInt(split[1])};
    }

    private void checkScoreRangeRepeat(ScaleReportConfItem scaleReportConfItem){
        //查询已配置的数据，得分区间不能重叠
        Long scaleId = scaleReportConfItem.getScaleId();
        Long reportConfId = scaleReportConfItem.getReportConfId();
        String itemCode = scaleReportConfItem.getItemCode();
        String itemDescribe = scaleReportConfItem.getItemDescribe();
        Integer[] array = getIntegerArrayByItemDescribe(itemDescribe);
        if(array == null){
            return;
        }
        //音视频分为一类 建议为单独的一类
        LambdaQueryWrapper<ScaleReportConfItem> queryWrapper = new LambdaQueryWrapper<ScaleReportConfItem>()
                .eq(ScaleReportConfItem::getScaleId, scaleId)
                .eq(ScaleReportConfItem::getReportConfId, reportConfId);
        if(ScaleReportConfItemConstant.VIDEO_CODE.equalsIgnoreCase(itemCode) || ScaleReportConfItemConstant.AUDIO_CODE.equalsIgnoreCase(itemCode)){
            queryWrapper.in(ScaleReportConfItem::getItemCode, List.of(ScaleReportConfItemConstant.VIDEO_CODE, ScaleReportConfItemConstant.AUDIO_CODE));
        }else{
            queryWrapper.eq(ScaleReportConfItem::getItemCode, itemCode);
        }
        List<ScaleReportConfItem> list = scaleReportConfItemMapper.selectList(queryWrapper);
        if(CollUtil.isEmpty(list)){
            return;
        }
        if(scaleReportConfItem.getId() != null){
            list = list.stream().filter(it -> !it.getId().equals(scaleReportConfItem.getId())).collect(Collectors.toList());
        }
        list.forEach(it -> {
            Integer[] dbArray = getIntegerArrayByItemDescribe(it.getItemDescribe());
            if(dbArray == null){
                return;
            }
            checkArrayRepeat(array, dbArray);
        });
    }

    private void checkArrayRepeat(Integer[] array1, Integer[] array2){
        //前后都包含
        if (array1[0] <= array2[1] && array2[0] <= array1[1]) {
            throw new BusinessException("存在重叠的得分区间配置");
        }
    }

    @Override
    public void delete(Long scaleReportConfItemId) {
        scaleReportConfItemMapper.updateDeletedById(scaleReportConfItemId);
    }

    @Override
    public void saveCopyReportItem(Long oldReportConfId, Long newReportConfId, Long newScaleId, String opUser) {
        List<ScaleReportConfItem> scaleReportConfItems = scaleReportConfItemMapper.selectList(new LambdaQueryWrapper<ScaleReportConfItem>().eq(ScaleReportConfItem::getReportConfId, oldReportConfId));
        if(CollUtil.isEmpty(scaleReportConfItems)){
            return;
        }
        scaleReportConfItems.forEach(scaleReportConfItem -> {
            scaleReportConfItem.setId(null);
            scaleReportConfItem.setReportConfId(newReportConfId);
            scaleReportConfItem.setScaleId(newScaleId);
        });
        scaleReportConfItemMapper.insert(scaleReportConfItems);
    }

    @Override
    public ScaleReportConfItem getById(Long id) {
        return baseMapper.getById(id);
    }

    @EventListener
    public void onoApplication(ScaleDelEvent event) {
        scaleReportConfItemMapper.delete(new LambdaQueryWrapper<ScaleReportConfItem>().eq(ScaleReportConfItem::getScaleId, event.getOldScaleId()));
    }
}
