package com.wftk.scale.biz.manager.job.action;

import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.jackson.exception.JSONException;
import com.wftk.scale.biz.constant.TerminalConfConstant;
import com.wftk.scale.biz.constant.enums.HttpResultStatusEnum;
import com.wftk.scale.biz.dto.httpjob.HttpResult;
import com.wftk.scale.biz.dto.notify.OrderStautsNotifyInput;
import com.wftk.scale.biz.entity.HttpJob;
import com.wftk.scale.biz.entity.Order;
import com.wftk.scale.biz.service.NotifyService;
import com.wftk.scale.biz.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2025/1/3 18:33
 */
@Component(TerminalConfConstant.ORDER_STATUS_NOTIFY)
@Slf4j
public class OrderStatusAction implements RequestAction{

    @Autowired
    NotifyService notifyService;

    @Autowired
    OrderService orderService;

    @Override
    public void doRequestAction(HttpJob httpJob) {
        JSONObject instance = JSONObject.getInstance();
        try{
            OrderStautsNotifyInput orderStautsNotifyInput = instance.parseObject(httpJob.getParams(), OrderStautsNotifyInput.class);
            Order order = orderService.getByOrderNo(orderStautsNotifyInput.getOrderNo());
            HttpResult httpResult = notifyService.orderStautsNotify(orderStautsNotifyInput, order.getTerminalCode());

            if(!Objects.equals(httpResult.getStatus(), HttpResultStatusEnum.SUCCESS.getStatus())) {
                throw new BusinessException(httpResult.getMsg());
            }
        }catch (BusinessException e){
            throw e;
        }catch (JSONException e){
            log.error("order status notify job json convert error.",e);
            throw new BusinessException("params format error .");
        }catch (Exception e){
            log.error("order status notify job execute error",e);
            throw new BusinessException("http request error .");
        }
    }

}
