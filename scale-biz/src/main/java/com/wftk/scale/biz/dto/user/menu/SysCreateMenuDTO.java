package com.wftk.scale.biz.dto.user.menu;

import com.wftk.scale.biz.valid.annoation.RangeConstraint;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SysCreateMenuDTO implements Serializable {

    /**
     * 上级菜单，最上级为null或者0
     */
    private Long parentId;
    /**
     * 菜单类型 1 目录 2 菜单 3 按钮
     */
    @NotNull(message = "菜单类型不能为空")
    @RangeConstraint(message = "菜单类型错误，1目录 2菜单 3按钮")
    private Integer type;
    /**
     * 菜单图标
     */
    private String icon;
    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    @Length(max = 50, message = "菜单名称最大输入50个字符")
    private String name;
    /**
     * 编码
     */
    @NotBlank(message = "菜单编码不能为空")
    @Length(max = 50, message = "菜单编码最大输入50个字符")
    private String code;
    /**
     * 排序
     */
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;
    /**
     * 路由名称
     */
    @NotBlank(message = "路由名称不能为空")
    @Length(max = 50, message = "路由名称最大输入255个字符")
    private String routeName;
    /**
     * 路由地址
     */
    @NotBlank(message = "路由地址不能为空")
    @Length(max = 255, message = "路由地址最大输入255个字符")
    private String routePath;
    /**
     * 组件名称
     */
    @Length(max = 50, message = "组件名称最大输入50个字符")
    private String componentName;
    /**
     * 组件地址
     */
    @Length(max = 255, message = "组件地址最大输入255个字符")
    private String componentPath;
    /**
     * 权限字符
     */
    @Length(max = 50, message = "权限字符最大输入50个字符")
    private String permissionCode;
    /**
     * 菜单描述
     */
    @Length(max = 255, message = "菜单描述最大输入255个字符")
    private String description;
    /**
     * 1为隐藏，0不隐藏
     */
    private Boolean hidden;
    /**
     * 菜单状态
     */
    @NotNull(message = "菜单状态不能为空")
    private Boolean enable;
    /**
     * 是否缓存
     */
    private Boolean cache;
}
