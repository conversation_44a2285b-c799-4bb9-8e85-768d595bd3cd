package com.wftk.scale.biz.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wftk.scale.biz.constant.enums.OrderEnum;
import com.wftk.scale.biz.constant.enums.PayChannelEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/11/21 11:21
 */
@Data
public class OrderQueryDTO {

    private Long id;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 归属机构
     */
    private Long departmentId;
    /**
     * 归属机构中文名
     */
    private String departmentName;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 量表名称
     */
    private String scaleName;
    /**
     * 量表类型 1单量表 2组合量表
     */
    private Integer scaleType;
    /**
     * 测评终端
     */
    private String terminalCode;
    /**
     * 测评终端
     */
    private String terminalName;
    /**
     * 订单来源 1自己购买 2他人推荐 3批量分发
     */
    private Integer source;
    /**
     * 订单金额
     */
    private Integer amount;
    /**
     * 费用类型 0免费 1收费
     */
    private Integer amountType;
    /**
     * 支付方式
     */
    private Integer payMethod;
    /**
     * 支付方式
     */
    private String payMethodDesc;
    /**
     * 订单状态
     */
    private Integer status;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    public void setStatus(Integer status) {
        this.status = status;
        if(status != null){
            this.orderStatus = OrderEnum.getDesc(status);
        }
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
        if(payMethod != null){
            this.payMethodDesc = PayChannelEnum.getDesc(payMethod);
        }
    }
}
