package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.dto.scale.ScaleConvertResultIntro;
import com.wftk.scale.biz.event.ScaleCreateRelationEvent;
import com.wftk.scale.biz.dto.scale.ScaleResultIntroDTO;
import com.wftk.scale.biz.entity.ScaleResultIntro;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 结果解读表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleResultIntroService extends IService<ScaleResultIntro> {

    /* 
     * @Author: mq
     * @Description: 校验量表结果解读是否有设置 
     * @Date: 2024/11/4 14:46 
     * @Param: scaleId  
     * @return: boolean 
     **/
    boolean vaildResultIntroDataIntegrity(Long scaleId);

    /*
    * @Author: mq
    * @Description: 校验量表结果解读名称是否存在
    * @Date: 2024/12/7 10:09
    * @Param: scaleId
     * @Param: resultIntroId
     * @Param: resultIntroName
    * @return: boolean
    **/
    boolean vaildResultIntroName(Long scaleId, Long factorId, String resultIntroName);

    /*
     * @Author: mq
     * @Description: 校验量表结果解读设置的得分区间是否正确（同一个因子维度的得分解读区间不允许重叠）
     * @Date: 2024/12/7 10:06
     * @Param: scaleResultIntro
     * @return: boolean
     **/
    boolean vaildFactorScoreRangeOverlap(ScaleResultIntro scaleResultIntro);

    /*
    * @Author: mq
    * @Description: 校验因子维度关联的结果解读信息
    * @Date: 2024/12/12 15:14
    * @Param: factorId
    * @return: String
    **/
    String checkFactorRelationIntro(Long factorId);

    /* 
     * @Author: mq
     * @Description: 创建量表结果解读信息
     * @Date: 2024/11/5 17:41 
     * @Param: scaleResultIntro
     * @return: void 
     **/
    void create(ScaleResultIntro scaleResultIntro);
    
    /* 
     * @Author: mq
     * @Description: 修改量表结果解读信息
     * @Date: 2024/11/5 17:41 
     * @Param: scaleResultIntro  
     * @return: void 
     **/
    void modify(ScaleResultIntro scaleResultIntro);

    /*
     * @Author: mq
     * @Description: 根据ID删除量表结果解读信息
     * @Date: 2024/11/5 17:42
     * @Param: id
     * @return: void
     **/
    void delete(Long id);
    
    /* 
     * @Author: mq
     * @Description: 根据量表ID获取结果解读列表数据
     * @Date: 2024/11/5 17:42 
     * @Param: scaleId  
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.scale.ScaleResultIntroDTO>
     **/
    Page<ScaleResultIntroDTO> seletePage(Long scaleId);

    /*
    * @Author: mq
    * @Description: 根据结果解读ID获取结果解读详情
    * @Date: 2024/12/7 13:54
    * @Param: resultIntroId
    * @return: ScaleResultIntroDTO
    **/
    ScaleResultIntroDTO findById(Long resultIntroId);

    /*
     * @Author: mq
     * @Description: 根据量表ID获取结果解读列表数据
     * @Date: 2024/11/5 17:42
     * @Param: scaleId
     **/
    List<ScaleResultIntroDTO> seleteList(Long scaleId);
    
    /*
    * @Author: mq
    * @Description: 根据量表ID和因子维度ID获取因子转换后的分值
    * @Date: 2024/11/26 18:41
    * @Param: scaleId-量表ID
    * @Param: factorId-因子维度ID
    * @Param: inversionScore-转换前计算结果
    * @return: com.wftk.scale.biz.dto.scale.ScaleConvertResultIntro
    **/
    ScaleConvertResultIntro getConvertScore(Long scaleId, Long factorId, BigDecimal inversionScore);

    void saveBatchByNewFactor(ScaleCreateRelationEvent event, Map<Long, Long> map);
}
