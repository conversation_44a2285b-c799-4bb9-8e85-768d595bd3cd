package com.wftk.scale.biz.ext.evaluator.constant;

/**
 * 函数名称常量
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
public interface FunctionNameConstant {

    /**
     * 因子类函数
     */
    interface Factor {

        /**
         * 计算量表因子平均数函数(只允许选择单个因子)
         * 示例: f_avg(1), 表示计算当前量表中ID为1的因子的平均分
         */
        String F_AVG = "f_avg";

        /**
         * 计算量表因子总分函数(只允许选择单个因子)
         * 示例: f_sum(1), 表示计算当前量表中ID为1的因子的总分
         * 
         */
        String F_SUM = "f_sum";

        /**
         * 选择量表因子函数(只允许选择单个因子)
         * 示例: f_select(1), 表示选择当前量表中ID为1的因子
         */
        String F_SELECT = "f_select";

        /**
         * 获取当前量表因子函数
         * 示例: f_current(), 表示获取当前量表的因子, 返回因子中的所有题目ID
         */
        String F_CURRENT = "f_current";
    }

    /**
     * 题目类函数
     */
    interface Question {

        /**
         * 范围选择量表题目函数(多个题目范围用逗号分隔)
         * 示例：q_select_range("1-8,2.1-2.5"), 表示选择当前量表中1-8题和2.1-2.5题
         */
        String Q_SELECT_RANGE = "q_select_range";

        /**
         * 选择量表题目函数(多个题目用逗号分隔)
         * 示例: q_select("1,2,3"), 表示选择当前量表中1,2,3题
         */
        String Q_SELECT = "q_select";

        /**
         * 根据量表题目统计匹配指定答案的数量
         * 示例: q_count("1,2,3", "A"), 代表统计1,2,3题中回答为A的数量
         */
        String Q_COUNT = "q_count";

        /**
         * 根据量表题目算分
         * 示例: q_sum("1,2,3"), 表示计算当前量表中1,2,3题的总分
         */
        String Q_SUM = "q_sum";

        /**
         * 根据量表题目获取所选答案的运算值(对应数据库scale_question_option表的operate_value字段)
         * 示例: q_opt_value("1"), 代表获取第1题所选答案的运算值
         */
        String Q_OPT_VALUE = "q_opt_value";

        /**
         * 获取传入题目的数量（通常为逗号分隔的字符串）
         * 示例: q_size("1,2,3")
         */
        String Q_SIZE = "q_size";

        /**
         * 过滤掉量表题目中不能参与计算的题目
         * 示例: q_computable("1,2,3"), 表示过滤掉量表题目中分数为null的题目,返回的量表题目均分数不为null
         */
        String Q_SCORE_NONE_NULL = "q_computable";

        /**
         * 根据量表题目ID获取所选答案的分数
         * 示例: q_score("1"), 代表获取ID为1的选项的分数（注意：参数是ID，所以可能需要结合选择题目的函数配合使用）
         */
        String Q_SCORE = "q_score";
    }

    /**
     * 量表整体函数
     */
    interface Scale {

        /**
         * 计算量表总分函数
         * 示例: s_sum(), 表示计算当前量表的总分
         */
        String S_SUM = "s_sum";

        /**
         * 获取当前量表
         * 示例: s_current(), 表示获取当前量表, 返回问题id逗号分隔的字符串
         */
        String S_CURRENT = "s_current";

        /**
         * 计算量表平均分函数
         * 示例: s_avg(), 表示计算当前量表的平均分
         */
        String S_AVG = "s_avg";
    }

    /**
     * 工具函数
     */
    interface Util {

        /**
         * 绝对值函数
         * 示例: u_abs(-1), 表示返回-1的绝对值
         */
        String ABS = "u_abs";
    }

}
