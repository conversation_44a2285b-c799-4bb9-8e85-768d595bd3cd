package com.wftk.scale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.entity.DepartmentTerminal;
import com.wftk.scale.biz.mapper.DepartmentTerminalMapper;
import com.wftk.scale.biz.service.DepartmentTerminalService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13 14:47:49
 */
@Service
public class DepartmentTerminalServiceImpl extends ServiceImpl<DepartmentTerminalMapper, DepartmentTerminal> implements DepartmentTerminalService {

    @Resource
    private DepartmentTerminalMapper departmentTerminalMapper;

    @Override
    public List<DepartmentTerminal> getConcatTerminalCodeByDepartmentIds(Set<Long> idSet) {
        return departmentTerminalMapper.getConcatTerminalCodeByDepartmentIds(idSet);
    }
}
