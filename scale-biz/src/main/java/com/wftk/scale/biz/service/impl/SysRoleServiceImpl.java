package com.wftk.scale.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.converter.SysRoleConverter;
import com.wftk.scale.biz.dto.user.role.SysRoleCreateDTO;
import com.wftk.scale.biz.dto.user.role.SysRoleQueryDTO;
import com.wftk.scale.biz.dto.user.role.SysRoleUpdateDTO;
import com.wftk.scale.biz.dto.user.role.SysRoleUpdateEnableDTO;
import com.wftk.scale.biz.entity.SysRole;
import com.wftk.scale.biz.entity.SysUserRole;
import com.wftk.scale.biz.event.SysRoleDeleteEvent;
import com.wftk.scale.biz.event.publisher.SysRoleDeletePublisher;
import com.wftk.scale.biz.excel.service.handler.SysRoleHandler;
import com.wftk.scale.biz.mapper.SysRoleMapper;
import com.wftk.scale.biz.service.SysRoleService;
import com.wftk.scale.biz.service.SysUserRoleService;
import com.wftk.scale.biz.vo.SysRoleVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    @Resource
    private SysRoleConverter sysRoleConverter;
    @Resource
    private SysRoleHandler sysRoleHandler;
    @Resource
    private SysUserRoleService sysUserRoleService;
    @Resource
    private SysRoleDeletePublisher publisher;

    @Override
    public Page<SysRoleVO> getList(SysRoleQueryDTO dto) {
        return Page.doSelectPage(() -> listAll(dto)).toPage(list -> sysRoleConverter.sysRoleListToSysRoleVOList(list));
    }

    @Override
    public SysRoleVO detailById(Long id) {
        if(Objects.isNull(id)){
            throw new BusinessException("参数id不能为空");
        }
        SysRole sysRole = baseMapper.selectById(id);
        return sysRole == null ? null : sysRoleConverter.sysRoleToSysRoleVO(sysRole);
    }

    @Override
    public void create(SysRoleCreateDTO dto) {
        validCodeUnique(dto.getCode());
        validNameUnique(dto.getName());
        baseMapper.insert(sysRoleConverter.sysRoleCreateDtoToSysRole(dto));
    }

    @Override
    public void update(SysRoleUpdateDTO dto) {
        SysRole sysRole = baseMapper.selectById(dto.getId());
        if(sysRole == null){
            throw new BusinessException("角色数据不存在");
        }
        if(!Objects.equals(dto.getCode(), sysRole.getCode())){
            validCodeUnique(dto.getCode());
        }
        if(!Objects.equals(dto.getName(), sysRole.getName())){
            validNameUnique(dto.getName());
        }
        baseMapper.updateById(sysRoleConverter.sysRoleUpdateDtoToSysRole(dto));
    }

    @Override
    public void updateStatus(SysRoleUpdateEnableDTO dto) {
        SysRole sysRole = baseMapper.selectById(dto.getId());
        if(sysRole == null){
            throw new BusinessException("角色数据不存在");
        }
        baseMapper.update(new LambdaUpdateWrapper<SysRole>().eq(SysRole::getId, dto.getId()).set(SysRole::getEnable, dto.getEnable()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        validSysRoleIsUse(id);
        baseMapper.deleteById(id);
        publisher.publishEvent(new SysRoleDeleteEvent(id));
    }

    @Override
    public void export(SysRoleQueryDTO dto, HttpServletResponse response) {
        sysRoleHandler.exportExcel(response, "角色信息导出", sysRoleConverter.sysRoleListToSysRoleExcelDataDTOList(this.listAll(dto)));
    }

    private void validSysRoleIsUse(Long sysRoleId){
        boolean exists = sysUserRoleService.exists(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getRoleId, sysRoleId));
        if(exists){
            throw new BusinessException("该角色已被用户绑定");
        }
    }

    private void validNameUnique(String name){
        boolean exists = baseMapper.exists(new LambdaQueryWrapper<SysRole>().eq(SysRole::getName, name));
        if(exists){
            throw new BusinessException("角色名称已存在");
        }
    }

    private void validCodeUnique(String code){
        boolean exists = baseMapper.exists(new LambdaQueryWrapper<SysRole>().eq(SysRole::getCode, code));
        if(exists){
            throw new BusinessException("角色编码已存在");
        }
    }

    private List<SysRole> listAll(SysRoleQueryDTO dto){
        LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
        if(StrUtil.isNotBlank(dto.getCode())){
            queryWrapper.eq(SysRole::getCode, dto.getCode());
        }
        if(StrUtil.isNotBlank(dto.getName())){
            queryWrapper.like(SysRole::getName, dto.getName());
        }
        if(Objects.nonNull(dto.getEnable())){
            queryWrapper.eq(SysRole::getEnable, dto.getEnable());
        }
        if(StrUtil.isNotBlank(dto.getStartDate())){
            queryWrapper.ge(SysRole::getCreateTime, dto.getStartDateTime());
        }
        if(StrUtil.isNotBlank(dto.getEndDate())){
            queryWrapper.le(SysRole::getCreateTime, dto.getEndDateTime());
        }
        return this.list(queryWrapper);
    }
}
