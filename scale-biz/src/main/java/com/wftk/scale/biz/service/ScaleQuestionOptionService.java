package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.dto.scale.ScaleQuestionHighestOverallScoreDTO;
import com.wftk.scale.biz.event.ScaleCreateEvent;
import com.wftk.scale.biz.entity.ScaleQuestionOption;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 问题选项表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleQuestionOptionService extends IService<ScaleQuestionOption> {

    /*
     * @Author: mq
     * 
     * @Description: 校验题目选项标签是否有重复
     * 
     * @Date: 2024/12/3 18:12
     * 
     * @Param: options
     * 
     * @return: boolean
     **/
    boolean vaildQuestionOptionLabel(List<ScaleQuestionOption> options);

    /*
     * @Author: mq
     * 
     * @Description: 创建题目选项信息
     * 
     * @Date: 2024/11/5 13:32
     * 
     * @Param: scaleId-量表ID
     * 
     * @Param: questionId-题目ID
     * 
     * @Param: options-题目选项
     * 
     * @return: void
     **/
    void create(Long scaleId, Long questionId, List<ScaleQuestionOption> options);

    /*
     * @Author: mq
     * 
     * @Description: 修改题目选项信息
     * 
     * @Date: 2024/11/5 15:30
     * 
     * @Param: scaleId
     * 
     * @Param: questionId
     * 
     * @Param: options
     * 
     * @return: void
     **/
    void modify(Long scaleId, Long questionId, List<ScaleQuestionOption> options);

    /*
     * @Author: mq
     * 
     * @Description:
     * 
     * @Date: 2024/11/4 17:05
     * 
     * @Param: scaleEventDTO
     * 
     * @Param: newQuestionId
     * 
     * @Param: oldQuestionId
     * 
     * @return: void
     **/
    void saveScaleQuestionOption(ScaleCreateEvent scaleEventDTO, Map<Long, Long> oldToNewIdMap);

    /*
     * @Author: mq
     * 
     * @Description: 根据量表ID和题目ID获取答案选项信息
     * 
     * @Date: 2024/11/5 14:14
     * 
     * @Param: scaleId-量表ID
     * 
     * @Param: questionId-题目ID
     * 
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleQuestionOption>
     **/
    List<ScaleQuestionOption> findByScaleIdAndQuestionId(Long scaleId, Long questionId);

    /*
     * @Author: mq
     * 
     * @Description: 根据量表ID获取答案选项信息
     * 
     * @Date: 2024/11/5 14:14
     * 
     * @Param: scaleId-量表ID
     * 
     * @return: java.util.List<com.wftk.scale.biz.entity.ScaleQuestionOption>
     **/
    List<ScaleQuestionOption> findByScaleId(Long scaleId);

    /*
     * @Author: mq
     * 
     * @Description: 根据量表ID和题目ID删除选项数据
     * 
     * @Date: 2024/11/5 15:27
     * 
     * @Param: scaleId
     * 
     * @Param: questionId
     * 
     * @return: void
     **/
    void deleteByScaleIdAndQuestionId(Long scaleId, Long questionId);

    /**
     * 获取总分
     * 
     * @param scaleId
     * @return
     */
    BigDecimal getQuestionTotalScore(Long scaleId);

    /*
     * @Author: funian
     * @Description: 量表下每题的最高分总分
     * @Date: 2025/08/20 16:56
     * @Param: scaleId-量表ID
     * @Param: response-响应
     * @return: ScaleQuestionHighestOverallScoreDTO
     **/
    ScaleQuestionHighestOverallScoreDTO findOverallScoreByScaleId(Long scaleId,List<Long> questionIds);
}
