package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.ScaleWarnUser;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04 16:56:01
 */
public interface ScaleWarnUserMapper extends BaseMapper<ScaleWarnUser> {

    Set<Long> getUserIdByScaleWarnSettingId(@Param("scaleWarnSettingId") Long scaleWarnSettingId, @Param("scaleId")Long scaleId);

    int deleteByScaleWarnSettingId(@Param("scaleWarnSettingId") Long scaleWarnSettingId, @Param("scaleId")Long scaleId);

}
