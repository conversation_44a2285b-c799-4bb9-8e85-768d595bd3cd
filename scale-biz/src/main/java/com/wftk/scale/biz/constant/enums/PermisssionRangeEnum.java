package com.wftk.scale.biz.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum PermisssionRangeEnum {

    ALL(1, "全部数据权限"),
    DESIGN_DEPART(2, "指定部门数据权限"),
    CURRENT_DEPART(3, "本部门数据权限"),
    CURRENT_DEPART_UNDER(4, "本部门及以下数据权限"),
    ONLY_CURRENT_USER(5, "仅本人数据权限"),
    ;

    private final Integer value;

    private final String desc;
}
