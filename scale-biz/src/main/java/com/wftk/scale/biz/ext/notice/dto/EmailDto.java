//package com.wftk.scale.biz.ext.notice.dto;
//
//import com.wftk.scale.biz.ext.notice.enums.NoticeTypeEnum;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.ToString;
//
///**
// * <AUTHOR>
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//@ToString(callSuper = true)
//public class EmailDto extends NoticeDTO{
//
//    /**
//     * 邮件内容
//     */
//    private String content;
//    /**
//     * 邮件主题
//     */
//    private String subject;
//
//    public EmailDto(String content, String subject, String userIds) {
//        this.content = content;
//        this.subject = subject;
//        super.setUserIds(userIds);
//    }
//
//    @Override
//    public NoticeTypeEnum getNoticeType() {
//        return NoticeTypeEnum.EMAIL;
//    }
//}
