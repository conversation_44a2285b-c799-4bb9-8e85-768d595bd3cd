package com.wftk.scale.biz.ext.evaluator.function.scale.factor;

import java.util.Map;

import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;

/**
 * 选择量表因子函数(只允许选择单个因子)
 * 示例: f_select(1), 表示选择当前量表中ID为1的因子,返回因子中的所有题目ID
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
public class SelectScaleFactorFunction extends BaseScaleFactorFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Factor.F_SELECT;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env, final AviatorObject arg1) {
        Long factorId = (Long) arg1.getValue(env);
        logger.info("f_select: factorId: {}", factorId);
        if (factorId == null) {
            throw new IllegalArgumentException("factorId must not be null");
        }
        ScaleFactor scaleFactor = getFactorById(env, factorId);
        String questionIds = getQuestionIds(scaleFactor);
        return new AviatorString(questionIds);
    }

}
