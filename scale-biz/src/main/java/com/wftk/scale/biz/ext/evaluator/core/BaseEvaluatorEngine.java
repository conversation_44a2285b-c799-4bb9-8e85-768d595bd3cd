package com.wftk.scale.biz.ext.evaluator.core;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;

/**
 * <AUTHOR>
 * @date 2025-09-02
 */
public abstract class BaseEvaluatorEngine implements EvaluatorEngine {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public Object evaluate(String expression, @Nullable EvaluatorContext context) {
        validate(expression, context);
        return doEvaluate(expression, context);
    }


    /**
     * 校验表达式
     * 
     * @param expression
     * @param context
     */
    protected void validate(String expression, @Nullable EvaluatorContext context) {

    }

    /**
     * 执行计算
     * 
     * @param expression
     * @param context
     * @return
     */
    protected abstract Object doEvaluate(String expression, @Nullable EvaluatorContext context);

}
