package com.wftk.scale.biz.dto.scale;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/12/16 11:38
 */
@Data
public class ScaleCombinationDTO {

    private Long id;

    /**
     * 上架Id
     */
    private Long listingId;

    /**
     * 上架呈现方式: 1.页面展示; 2.用户分发;
     */
    private Integer listingShowType;

    /**
     * 组合名称
     */
    private String name;

    /**
     * 量表编码
     */
    private String code;

    /**
     * 测评方式，1依次测评，2选择测评
     */
    private Integer type;

    /**
     * 0未完成，1已完成
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;



}
