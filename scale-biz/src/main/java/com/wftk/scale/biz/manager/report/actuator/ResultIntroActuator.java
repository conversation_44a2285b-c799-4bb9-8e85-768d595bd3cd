package com.wftk.scale.biz.manager.report.actuator;

import com.wftk.scale.biz.entity.ScaleUserFactorResult;
import com.wftk.scale.biz.manager.report.dto.base.ReultIntroDTO;
import com.wftk.scale.biz.service.ScaleUserFactorResultService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createDate 2025/9/10 20:37
 */
@Slf4j
@Component
public class ResultIntroActuator {

    @Resource
    ScaleUserFactorResultService scaleUserFactorResultService;


    /**
     * 获取单个因子的结果解读
     * @param resultId
     * @param factorId
     * @return
     */
    public ReultIntroDTO doResultIntro(Long resultId, Long factorId,String factorName ) {
        // 查找结果解读和因子结果
        ScaleUserFactorResult scaleUserFactorResult = scaleUserFactorResultService.getByResultIdAndFactorId(resultId, factorId, null);
        if(scaleUserFactorResult == null){
            return null;
        }
        ReultIntroDTO reultIntroDTO = new ReultIntroDTO();
        reultIntroDTO.setResultRead(scaleUserFactorResult.getResultIntro());
        reultIntroDTO.setFactorName(factorName);
        return reultIntroDTO;
    }


}
