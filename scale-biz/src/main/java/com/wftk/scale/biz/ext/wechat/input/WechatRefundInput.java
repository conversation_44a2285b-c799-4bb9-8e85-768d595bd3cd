package com.wftk.scale.biz.ext.wechat.input;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/10/17 11:34
 */
@Data
public class WechatRefundInput {

    private String transactionId;

    /** 商户订单号 说明：原支付交易对应的商户订单号 */
    private String orderNo;
    /** 商户退款单号 说明：商户系统内部的退款单号，商户系统内部唯一，只能是数字、大小写字母_-|*@ ，同一退款单号多次请求只退一笔。 */
    private String outRefundNo;

    /**
     * 退款金额
     */
    private Integer amount;

    /**
     * 订单总金额
     */
    private Integer total;

    /**
     * 退款币种
     */
    private String currency = "CNY";

    /** 退款原因 说明：若商户传入，会在下发给用户的退款消息中体现退款原因 */
    private String reason;
    /**
     * 退款结果回调url 说明：异步接收微信支付退款结果通知的回调地址，通知url必须为外网可访问的url，不能携带参数。
     * 如果参数中传了notify_url，则商户平台上配置的回调地址将不会生效，优先回调当前传的这个地址。
     */
    private String notifyUrl;
}
