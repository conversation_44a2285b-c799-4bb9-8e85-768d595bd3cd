package com.wftk.scale.biz.ext.evaluator.core;

import org.springframework.lang.Nullable;

import com.wftk.scale.biz.ext.evaluator.exception.IllegalExpressionException;

/**
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface EvaluatorEngine {

    /**
     * 校验表达式
     * 
     * @param expression
     * @return
     */
    default ExpressionValidateResult validate(String expression) {
        return validate(expression, false);
    }

    /**
     * 校验表达式
     * 
     * @param expression
     * @param allowSimpleExpression 是否允许简单表达式(如：单个字符串("simple"))
     * @return
     */
    ExpressionValidateResult validate(String expression, boolean allowSimpleExpression);

    /**
     * 计算表达式
     * 
     * @param expression
     * @return
     */
    default Object evaluate(String expression) {
        return evaluate(expression, null);
    }

    /**
     * 计算表达式
     * 
     * @param expression
     * @param context
     * @return
     */
    Object evaluate(String expression, @Nullable EvaluatorContext context);

    /**
     * 计算表达式模板
     * 
     * @param template
     * @param context
     * @return
     */
    default Object evaluateTemplate(String template, @Nullable EvaluatorContext context) {
        String bindingExpression = null;
        if (context == null) {
            bindingExpression = template;
        } else {
            bindingExpression = bindingExpressionTemplate(template, context);
        }
        if (bindingExpression.contains("${")) {
            // 说明还存在尚未被绑定的变量
            throw new IllegalExpressionException(template);
        }
        return evaluate(bindingExpression, context);
    }

    /**
     * 将表达式转换为模板
     * 
     * @param expression
     * @return
     */
    ExpressionTemplate expressionToTemplate(String expression);

    /**
     * 绑定表达式模板
     * 
     * @param expression
     * @return
     */
    String bindingExpressionTemplate(String expression, EvaluatorContext context);


    /**
     * 表达式校验结果
     */
    record ExpressionValidateResult(boolean valid, String message) {

        public static ExpressionValidateResult success() {
            return new ExpressionValidateResult(true, null);
        }

        public static ExpressionValidateResult fail(String message) {
            return new ExpressionValidateResult(false, message);
        }
    }

}
