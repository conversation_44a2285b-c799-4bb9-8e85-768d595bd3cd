package com.wftk.scale.biz.constant.enums;

import com.wftk.common.core.enums.BaseEnum;

public enum ScaleWarnStatusEnum implements BaseEnum {

    INVALID(0, "无效"),
    VALID(1, "有效");

    private Integer value;

    private String label;

    ScaleWarnStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
