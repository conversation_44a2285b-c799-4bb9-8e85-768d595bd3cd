package com.wftk.scale.biz.event.publisher;

import com.wftk.scale.biz.event.ScaleEvaluationIntroEvent;
import org.springframework.context.ApplicationEventPublisher;

/**
 * <AUTHOR>
 * @createDate 2024/12/4 17:27
 */
public class ScaleEvaluationIntroPublisher implements BaseEventPublisher<ScaleEvaluationIntroEvent> {

    private final ApplicationEventPublisher applicationEventPublisher;

    public ScaleEvaluationIntroPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Override
    public void publishEvent(ScaleEvaluationIntroEvent scaleEvaluationIntroEvent) {
        applicationEventPublisher.publishEvent(scaleEvaluationIntroEvent);
    }
}
