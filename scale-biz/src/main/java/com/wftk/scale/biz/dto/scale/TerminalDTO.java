package com.wftk.scale.biz.dto.scale;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 终端信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Data
public class TerminalDTO{

    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 终端编码
     */
    private String code;

    /**
     * 0.未开启; 1.已开启;
     */
    private Boolean enable;

    /**
     * 路由类型;
     */
    private String type;

    /**
     * 路由类型名称;
     */
    private String typeName;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;
}
