package com.wftk.scale.biz.excel.converter;


import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.wftk.scale.biz.constant.enums.QuestionScoringTypeEnum;

/**
 * @ClassName: QuestionTypeConverter
 * @Description: 将题型数据对象转换为Excel单元格的值
 * @Author: mq
 * @Date: 2024/11/28
 * @Version: 1.0
 **/
public class QuestionScoringTypeConverter implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadConverterContext<?> context) {
        return QuestionScoringTypeEnum.getType(context.getReadCellData().getStringValue());
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) {
        return new WriteCellData<String>(QuestionScoringTypeEnum.getValue(context.getValue()));
    }
}
