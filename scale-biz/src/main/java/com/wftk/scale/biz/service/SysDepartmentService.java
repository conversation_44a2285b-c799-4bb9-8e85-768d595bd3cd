package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.dto.user.sysdepartment.SysDepartmentCreateDTO;
import com.wftk.scale.biz.dto.user.sysdepartment.SysDepartmentQueryDTO;
import com.wftk.scale.biz.dto.user.sysdepartment.SysDepartmentUpdateDTO;
import com.wftk.scale.biz.entity.SysDepartment;
import com.wftk.scale.biz.vo.SysDepartmentVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SysDepartmentService extends IService<SysDepartment> {

    void createSysDepartment(SysDepartmentCreateDTO dto);

    void updateSysDepartment(SysDepartmentUpdateDTO dto);

    void deleteById(Long id);

    List<SysDepartmentVO> selectSysDepartmentList(SysDepartmentQueryDTO dto);
}
