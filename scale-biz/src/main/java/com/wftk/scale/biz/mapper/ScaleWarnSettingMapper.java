package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.entity.ScaleWarnSetting;
import com.wftk.scale.biz.input.ScaleWarnSettingPageQueryInput;
import com.wftk.scale.biz.output.ScaleScaleFactorOutput;
import com.wftk.scale.biz.output.ScaleWarnSettingPageQueryOutput;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 预警设置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-04 16:56:01
 */
public interface ScaleWarnSettingMapper extends BaseMapper<ScaleWarnSetting> {

    /**
     * 获取量表中的因子是否设置了预警
     * @param scaleWarnConfId
     * @param scaleId
     * @return
     */
    List<ScaleWarnSetting> getByScaleIdAndScaleWarnConfId(@Param("scaleWarnConfId") Long scaleWarnConfId, @Param("scaleId") Long scaleId);

    /**
     * 量表告警设置查询
     * @param input
     * @return
     */
    List<ScaleWarnSettingPageQueryOutput> query(@Param("input") ScaleWarnSettingPageQueryInput input);

    /**
     * 获取量表以及量表因子
     * @return
     */
    List<ScaleScaleFactorOutput> queryScaleScaleFactor();

    ScaleWarnSetting getById(@Param("id") Long id);

    int deleteById(@Param("id") Long id);

}
