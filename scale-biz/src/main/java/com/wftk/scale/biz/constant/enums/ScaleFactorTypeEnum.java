package com.wftk.scale.biz.constant.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @createDate 2025/3/20 16:03
 */
@Getter
public enum ScaleFactorTypeEnum {

    TOTAL(1,"总分"),
    AVG(2,"总均分"),
    POSITIVE_COUNT(3,"阳性数量"),
    OTHER(4,"其他");

    private Integer type;

    private String desc;

    ScaleFactorTypeEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }

}
