package com.wftk.scale.biz.ext.notice;

import com.wftk.scale.biz.entity.NoticeMessage;
import com.wftk.scale.biz.ext.notice.enums.NoticeTypeEnum;
import lombok.Getter;

@Getter
public abstract class NoticeHandler<T extends NoticeRequest>  {

    private final NoticeTypeEnum noticeType;

    public NoticeHandler(NoticeTypeEnum noticeType) {
        this.noticeType = noticeType;
    }

    /**
     * 发送通知
     * @param t
     * @return
     */
    public abstract NoticeResult sendNotice(T t);

    /**
     * 构建通知请求参数
     * @param noticeMessage
     * @return
     */
    public abstract NoticeRequest buildNoticeRequest(NoticeMessage noticeMessage);


}
