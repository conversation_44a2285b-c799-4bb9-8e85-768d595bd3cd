package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.entity.TerminalConf;
import org.springframework.lang.Nullable;

/**
 * @InterfaceName: TerminalConfService
 * @Description: 终端配置信息服务类
 * @Author: mq
 * @Date: 2024-10-24 16:08
 * @Version: 1.0
 **/
public interface TerminalConfService extends IService<TerminalConf> {

    /*
     * @Author: mq
     * @Description: 校验终端信息是否已经存在
     * @Date: 2024/10/24 16:42
     * @Param: id-主键ID
     * @Param: itemCode-配置编码
     * @Param: itemValue-配置的值
     * @return: boolean
     **/
    boolean validTerminalCodeAndValue(Long id, String itemCode, String itemValue);

    /*
     * @Author: mq
     * @Description: 创建终端配置信息
     * @Date: 2024/10/24 16:23
     * @Param: terminalConf-终端配置信息
     * @return: void
     **/
    void create(TerminalConf terminalConf);

    /*
     * @Author: mq
     * @Description: 根据ID删除终端配置信息
     * @Date: 2024/10/24 16:31
     * @Param: id-终端配置主键ID
     * @return: void
     **/
    void delete(Long id);

    /*
     * @Author: mq
     * @Description: 修改终端配置信息
     * @Date: 2024/10/24 16:34
     * @Param: terminalConf-终端配置信息
     * @return: void
     **/
    void modify(TerminalConf terminalConf);

    /*
     * @Author: mq
     * @Description: 根据ID更新终端配置数据的状态
     * @Date: 2024/10/24 16:50
     * @Param: id-主键ID
     * @Param: enable-启用状态(false 未开启 true 已开启)
     * @return: void
     **/
    void updateEnable(Long id, Boolean enable);

    /*
     * @Author: mq
     * @Description: 根据条件检索终端配置分页数据
     * @Date: 2024/10/24 17:40
     * @Param: terminalName
     * @Param: terminalType
     * @Param: userId
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.entity.TerminalConf>
     **/
    Page<TerminalConf> selectPage(String terminalName, Integer terminalType, Long userId);

    /**
     * 根据终端配置编码查询终端配置信息
     * @param terminalId
     * @param itermCode
     * @param enable
     * @return
     */
    String findByItemCode(Long terminalId, String itermCode, @Nullable Boolean enable);


    /**
     * 根据终端配置编码查询终端配置信息
     * @param terminalCode
     * @param itermCode
     * @param enable
     * @return
     */
    String findByItemCode(String terminalCode, String itermCode, @Nullable Boolean enable);
}
