package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 题目答案表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Data
@ToString
@TableName("scale_question")
public class ScaleQuestion implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 题号
     */
    private String questionNumber;

    /**
     * 子题号
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String subNumber;

    /**
     * 量表ID
     */
    private Long scaleId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 问题,富文本
     */
    private String question;

    /**
     * 题干数据类型: TEXT.普通文本;
     */
    private String dataType;

    /**
     * 题型，1单选，2单选其他，3多选，4单行输入
     */
    private Integer type;

    /**
     * 1必答，0不必答
     */
    private Boolean requireAnswer;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 附件地址
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String filePath;

    /**
     * 偏移位置(1.上 2.下 3.左 4.右)
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer offset;

    /**
     * 计分类型：1.正向计分 2.反向计分
     */
    private Integer scoringType;


}
