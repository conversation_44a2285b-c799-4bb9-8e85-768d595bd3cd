package com.wftk.scale.biz.excel.utils;

import cn.hutool.core.collection.CollUtil;
import com.wftk.scale.biz.constant.CompletableFutureConstant;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
public class AsyncTaskUtils {

    public static void execute(List<CompletableTask> tasks) throws Exception {
        if(CollUtil.isEmpty(tasks)){
            return;
        }
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        CompletableFutureUtils futureUtils = new CompletableFutureUtils(new AtomicBoolean(false), new CountDownLatch(tasks.size()));
        for (CompletableTask task : tasks) {
            futures.add(futureUtils.runAsync(task));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(CompletableFutureConstant.TASK_TIMEOUT_VAL, TimeUnit.SECONDS);
    }
}
