package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.scale.biz.entity.ScaleLatest;

/**
 * <p>
 * 量表最新版本 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleLatestService extends IService<ScaleLatest> {

    /* 
     * @Author: mq
     * @Description: 保存量表最新版本的数据信息
     * @Date: 2024/11/1 14:53 
     * @Param: scaleId-最新版本量表主键ID
     * @Param: code-量表编码
     * @return: void 
     **/
    void saveOrUpdateScaleLatest(Long scaleId, String code);
}
