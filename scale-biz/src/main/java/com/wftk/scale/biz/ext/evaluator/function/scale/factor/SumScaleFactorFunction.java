package com.wftk.scale.biz.ext.evaluator.function.scale.factor;

import java.util.List;
import java.util.Map;

import com.googlecode.aviator.runtime.type.AviatorDouble;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.wftk.scale.biz.entity.ScaleFactor;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.ext.evaluator.constant.FunctionNameConstant;

/**
 * 计算量表因子函数(只允许选择单个因子)
 * 示例: f_sum(1), 表示计算当前量表中ID为1的因子的总分
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
public class SumScaleFactorFunction extends BaseScaleFactorFunction {

    @Override
    public String getName() {
        return FunctionNameConstant.Factor.F_SUM;
    }

    @Override
    public AviatorObject call(final Map<String, Object> env, final AviatorObject arg1) {
        Long factorId = (Long) arg1.getValue(env);
        logger.info("f_sum: factorId: {}", factorId);
        if (factorId == null) {
            throw new IllegalArgumentException("factorId must not be null");
        }
        ScaleFactor scaleFactor = getFactorById(env, factorId);
        String questionIds = getQuestionIds(scaleFactor);
        List<ScaleUserResultRecord> userResultOptions = getUserResultOptions(env, questionIds, false);
        double score = sumScore(userResultOptions);
        return new AviatorDouble(score);
    }

}
