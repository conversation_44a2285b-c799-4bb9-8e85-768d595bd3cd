package com.wftk.scale.biz.config;


import com.wftk.lock.spring.boot.autoconfigure.core.factory.DLockFactory;
import com.wftk.lock.spring.boot.autoconfigure.core.factory.DReadWriteLockFactory;
import com.wftk.scale.biz.lock.LockManager;
import com.wftk.scale.biz.lock.RedissonLockManager;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.*;


/**
 * <AUTHOR>
 * @create 2024/10/23 17:19
 */
@Configuration
@Import(ExtConfig.class)
@ComponentScan(value = "com.wftk.scale.biz",
            excludeFilters = @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.wftk.scale.biz.ext.*"))
@MapperScan(basePackages = "com.wftk.scale.biz.mapper")
public class BizConfig {

    @Bean
    LockManager lockManager(DLockFactory<?> dLockFactory, DReadWriteLockFactory<?> dReadWriteLockFactory) {
        return new RedissonLockManager(dLockFactory, dReadWriteLockFactory);
    }

}
