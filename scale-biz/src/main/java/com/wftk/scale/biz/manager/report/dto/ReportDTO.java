package com.wftk.scale.biz.manager.report.dto;

import com.wftk.scale.biz.manager.report.dto.content.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2025/9/8 17:53
 */
@Data
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReportDTO {

    /**
     * 量表名称
     */
    private String scaleName;

    /**
     * 阅读须知
     */
    private ReadingInstructionsContentDTO readingInstructionsContent;

    /**
     * 用户信息
     */
    private UserInfoReportContentDTO userInfoReportContent;

    /**
     * 总分
     */
    private TotalScoreReportContentDTO totalScoreReportContent;

    /**
     * 平均分
     */
    private AvgScoreReportContentDTO avgScoreReportContent;

    /**
     * 阳性数量
     */
    private PositiveCountReportContentDTO positiveCountReportContent;

    /**
     * 因子得分
     */
    private FactorScoreContentDTO factorScoreContentDTO;

    /**
     * 因子分析
     */
    private FactorAnalyseReportContentDTO factorAnalyseReportContent;


}
