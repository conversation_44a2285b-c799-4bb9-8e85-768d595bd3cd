package com.wftk.scale.biz.dto.scale;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ScaleResultIntroDTO
 * @Description: 量表结果解读信息
 * @Author: mq
 * @Date: 2024/12/4
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleResultIntroDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 量表ID
     */
    private Long scaleId;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 结果解读
     */
    private String intro;

    /**
     * 量表因子ID
     */
    private Long factorId;

    /**
     * 量表因子名称
     */
    private String factorName;

    /**
     * 量表因子所得分数（可能是个区间）
     */
    private String score;

    /**
     * 因子分值转换方式: 1.等比转换  2.固定值转换 （如果是等比转换，则转换后的值是计算出来的区间）
     */
    private Integer scoreConvertType;

    /**
     * 转换分值
     */
    private String convertScore;

    /**
     * 结果: 0.阴; 1.阳;
     */
    private Boolean result;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 展示类型
     */
    private String originalShowType;
    /**
     * 展示类型
     */
    private List<String> showType;

    /**
     * 图表类型
     */
    private String chartType;

}
