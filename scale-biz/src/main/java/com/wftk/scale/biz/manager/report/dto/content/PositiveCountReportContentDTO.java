package com.wftk.scale.biz.manager.report.dto.content;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wftk.scale.biz.manager.report.dto.base.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2025/9/10 21:27
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PositiveCountReportContentDTO {

    private String remark;

    private String wordage;

    private ChartDTO chart;

    private FormDTO form;

    private ReultIntroDTO resultIntro;

    private SuggestionDTO suggestion;

    private AudioDTO audio;

    private VideoDTO video;

}
