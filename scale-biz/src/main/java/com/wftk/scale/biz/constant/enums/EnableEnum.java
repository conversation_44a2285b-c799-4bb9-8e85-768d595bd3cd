package com.wftk.scale.biz.constant.enums;


import lombok.Getter;

import java.util.Arrays;

/**
 * @EnumName: EnableEnum
 * @Description:
 * @Author: mq
 * @Date: 2024/11/25
 * @Version: 1.0
 **/
@Getter
public enum EnableEnum {

    ENABLE(true, "启用"),
    DISABLE(false, "禁用"),
    ;
    private Boolean enable;

    private String desc;

    EnableEnum(Boolean enable, String desc) {
        this.enable = enable;
        this.desc = desc;
    }

    public static String getDescByEnable(Boolean enable) {
        return Arrays.stream(EnableEnum.values()).filter(e -> e.enable.equals(enable))
                .findFirst().map(EnableEnum::getDesc)
                .orElse(null);
    }

    public static Boolean getEnableByDesc(String desc) {
        return Arrays.stream(EnableEnum.values()).filter(e -> e.desc.equals(desc))
                .findFirst().map(EnableEnum::getEnable)
                .orElse(null);
    }
}
