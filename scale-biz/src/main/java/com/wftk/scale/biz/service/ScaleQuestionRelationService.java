package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.event.ScaleCreateEvent;
import com.wftk.scale.biz.dto.scale.ScaleQuestionRelationDTO;
import com.wftk.scale.biz.entity.ScaleQuestionRelation;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 量表关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleQuestionRelationService extends IService<ScaleQuestionRelation> {

    /*
     * @Author: mq
     * @Description: 校验量表跳转逻辑的正确性(跳转题号与目标题号不能相同且只能向后续题目进行跳转)
     * @Date: 2024/11/5 17:37
     * @Param: id-跳转逻辑ID
     * @Param: scaleId-量表ID
     * @Param: questionId-题目ID
     * @Param: strategy-跳转策略
     * @return: boolean
     **/
    boolean validRelationStrategyCorrect(Long id, Long scaleId, Long questionId, String strategy, Integer type);

    /*
     * @Author: mq
     * @Description: 校验量表题目跳转逻辑是否有设置
     * @Date: 2024/11/6 16:50
     * @Param: scaleId-量表ID
     * @return: boolean
     **/
    boolean vaildRelationDataIntegrity(Long scaleId);

    /*
     * @Author: mq
     * @Description: 校验题目是否有关联的逻辑跳转信息
     * @Date: 2024/12/11 16:30
     * @Param: scaleId-量表ID
     * @Param: questionId-题目ID
     * @return: String
     **/
    boolean checkRelationQuestion(Long scaleId, Long questionId);

    /*
     * @Author: mq
     * @Description: 创建量表跳转逻辑信息
     * @Date: 2024/11/5 10:38
     * @Param: scaleQuestionRelation-跳转逻辑信息
     * @return: void
     **/
    void create(ScaleQuestionRelation scaleQuestionRelation);

    /*
     * @Author: mq
     * @Description: 修改量表跳转逻辑信息
     * @Date: 2024/11/5 17:25
     * @Param: scaleQuestionRelation-跳转逻辑信息
     * @return: void
     **/
    void modify(ScaleQuestionRelation scaleQuestionRelation);

    /*
     * @Author: mq
     * @Description: 根据ID删除量表跳转逻辑信息
     * @Date: 2024/11/5 17:25
     * @Param: id-跳转逻辑ID
     * @return: void
     **/
    void delete(Long id);

    /*
     * @Author: mq
     * @Description: 根据条件检索量表跳转逻辑信息
     * @Date: 2024/11/5 17:26
     * @Param: scaleId-量表ID
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.dto.scale.ScaleQuestionRelationDTO>
     **/
    Page<ScaleQuestionRelationDTO> selectPage(Long scaleId);

    /*
     * @Author: mq
     * @Description: 保存量表题目关联的逻辑跳转信息
     * @Date: 2024/11/4 17:15
     * @Param: scaleEventDTO-量表信息
     * @Param: newQuestionId-新题目ID
     * @Param: oldQuestionId-就题目ID
     * @return: void
     **/
    void saveScaleQuestionRelation(ScaleCreateEvent scaleEventDTO, Map<Long, Long> oldToNewIdMap);

    /*
     * @Author: mq
     * @Description: 根据量表ID和题目ID，获取题目关联的逻辑跳转信息
     * @Date: 2024/12/14 17:24
     * @Param: scaleId-量表ID
     * @Param: questionId-题目ID
     * @return: List<ScaleQuestionRelation>
     **/
    List<ScaleQuestionRelation> findByScaleIdAndQuestionId(Long scaleId, Long questionId);
}
