package com.wftk.scale.biz.ext.wechat.enums;

import com.wftk.common.core.enums.BaseEnum;

/**
 * <AUTHOR>
 * @create 2023/10/12 17:49
 */
public enum PaymentStatusEnum implements BaseEnum {

    PAY_WAIT(1, "待支付"), PAY_SUCCESS(2, "支付成功"), PAY_FAILED(10, "支付失败"), CLOSED(11, "已关闭");

    private final Integer value;
    private final String label;

    PaymentStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getLabel() {
        return label;
    }

}
