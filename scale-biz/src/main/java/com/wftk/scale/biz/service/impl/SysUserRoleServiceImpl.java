package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.entity.SysUserRole;
import com.wftk.scale.biz.event.SysUserChangeEvent;
import com.wftk.scale.biz.event.SysUserDeleteEvent;
import com.wftk.scale.biz.mapper.SysUserRoleMapper;
import com.wftk.scale.biz.service.SysUserRoleService;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户角色id 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements SysUserRoleService {

    @EventListener(SysUserChangeEvent.class)
    public void lister(SysUserChangeEvent event) {
        Long sysUserId = event.getSysUserId();
        if(CollUtil.isEmpty(event.getRoleId()) || sysUserId == null){
            return;
        }
        baseMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, sysUserId));
        List<Long> roleIdList = event.getRoleId();
        List<SysUserRole> list = new ArrayList<>(10);
        for (Long roleId : roleIdList) {
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(sysUserId);
            sysUserRole.setRoleId(roleId);
            list.add(sysUserRole);
        }
        baseMapper.insert(list);
    }

    @EventListener(SysUserDeleteEvent.class)
    public void lister(SysUserDeleteEvent event) {
        baseMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, event.getSysUserId()));
    }
}
