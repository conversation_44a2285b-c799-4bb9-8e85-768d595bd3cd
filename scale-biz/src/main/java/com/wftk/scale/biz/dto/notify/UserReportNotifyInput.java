package com.wftk.scale.biz.dto.notify;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/12/18 19:25
 */
@Data
public class UserReportNotifyInput {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 终端订单号
     */
    private String terminalSerialNo;

    /**
     * 记录号
     */
    private String evaluationRecordNo;

    /**
     * 量表编码
     */
    private String scaleCode;

    /**
     * 测评报告链接
     */
    private String reportUrl;

    /**
     * 测评报告生成时间
     */
    private LocalDateTime reportTime;

    /**
     * 总分
     */
    private BigDecimal totalScore;

    /**
     * 预警标签
     */
    private String warnTag;

}
