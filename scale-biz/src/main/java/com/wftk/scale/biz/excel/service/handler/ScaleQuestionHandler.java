package com.wftk.scale.biz.excel.service.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO;
import com.wftk.scale.biz.entity.ScaleQuestion;
import com.wftk.scale.biz.entity.ScaleQuestionOption;
import com.wftk.scale.biz.excel.model.ScaleQuestionExcelDataDTO;
import com.wftk.scale.biz.excel.service.convert.ScaleQuestionConvert;
import com.wftk.scale.biz.excel.utils.AsyncTaskUtils;
import com.wftk.scale.biz.excel.utils.CompletableTask;
import com.wftk.scale.biz.excel.utils.ExcelContextUtil;
import com.wftk.scale.biz.excel.utils.ExcelUtil;
import com.wftk.scale.biz.mapper.ScaleQuestionMapper;
import com.wftk.scale.biz.service.ScaleQuestionOptionService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ScaleQuestionHandler extends ServiceImpl<ScaleQuestionMapper, ScaleQuestion> {

    @Resource
    private ScaleQuestionOptionService scaleQuestionOptionService;
    @Resource
    private ScaleQuestionMapper scaleQuestionMapper;

    public void saveBatchQuestion(List<ScaleQuestionQueryDTO> questions) {
        if (CollUtil.isEmpty(questions)) {
            return;
        }
        List<ScaleQuestion> questionList = questions.stream().map(item -> {
            ScaleQuestion question = new ScaleQuestion();
            BeanUtils.copyProperties(item, question);
            return question;
        }).collect(Collectors.toList());
        baseMapper.insert(questionList);
    }

    public void saveBatchQuestionOption(List<ScaleQuestionOption> options) {
        if (CollUtil.isEmpty(options)) {
            return;
        }
        scaleQuestionOptionService.saveBatch(options);
    }

    public void downloadTemplate(HttpServletResponse response) {
        InputStream inputStream = null;
        try {
            String templatePath = "/templates/量表题目模板.xlsx";
            ClassPathResource classPathResource = new ClassPathResource(templatePath);
            inputStream = classPathResource.getInputStream();
            ExcelContextUtil.setExportHeader(response, classPathResource.getFilename());
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            response.flushBuffer();
        } catch (Exception e) {
            log.error("下载量表题目模板错误", e);
            throw new BusinessException("下载量表题目模板错误");
        }finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭流失败", e);
                }
            }
        }
    }

    public void importExcel(Long scaleId, MultipartFile file) throws Exception {
        List<ScaleQuestionExcelDataDTO> importList = ExcelUtil.readContainsHeader(file,
                ScaleQuestionExcelDataDTO.class,
                list -> validImportList(list, scaleId));
        if(CollUtil.isEmpty(importList)){
            return;
        }
        List<ScaleQuestionQueryDTO> realImportList = importList.stream()
                .map(dto -> ScaleQuestionConvert.getInstance().convertData(scaleId, dto)).collect(Collectors.toList());
        List<ScaleQuestionOption> scaleQuestionOptionList = realImportList.stream()
                .map(ScaleQuestionQueryDTO::getOptions).flatMap(Collection::stream).collect(Collectors.toList());

        Authentication authentication = AuthenticationHolder.getAuthentication();
        AsyncTaskUtils.execute(
                List.of(
                    new CompletableTask(authentication, () -> this.saveBatchQuestion(realImportList)),
                    new CompletableTask(authentication, () -> this.saveBatchQuestionOption(scaleQuestionOptionList))
                )
        );
    }


    private void validImportList(List<ScaleQuestionExcelDataDTO> read, Long scaleId){
        Set<String> questionNumSet = read.stream()
                .filter(data -> StrUtil.isBlank(data.getSubNumber()) && StrUtil.isNotBlank(data.getQuestionNumber()))
                .map(ScaleQuestionExcelDataDTO::getQuestionNumber)
                .collect(Collectors.toSet());
        //获取当前量表已有的题号
        Set<String> hasQuestionNum = new HashSet<>(5);
        List<ScaleQuestionQueryDTO> byScaleId = scaleQuestionMapper.findByScaleId(scaleId);
        byScaleId.forEach(dto -> {
            if(StrUtil.isNotBlank(dto.getQuestionNumber()) && StrUtil.isBlank(dto.getSubNumber())){
                questionNumSet.add(dto.getQuestionNumber());
            }
            hasQuestionNum.add(generateKey(dto.getQuestionNumber(), dto.getSubNumber()));
        });
        for (int i = 0 ; i < read.size() ; i++) {
            //增加标题行
            int rowIndex = i + 2;
            ScaleQuestionExcelDataDTO data = read.get(i);
            if(ObjectUtil.isEmpty(data.getQuestion())){
                throw new BusinessException("第" + rowIndex + "行数据有误，问题不能为空");
            }
            if(ObjectUtil.isEmpty(data.getType())){
                throw new BusinessException("第" + rowIndex + "行数据有误，题型不能为空");
            }
            if(ObjectUtil.isEmpty(data.getScoringType())){
                throw new BusinessException("第" + rowIndex + "行数据有误，计分类型不能为空");
            }
            if(checkInputNumberError(data.getQuestionNumber())){
                throw new BusinessException("第" + rowIndex + "行数据有误，题号需为数字");
            }
            if(checkInputNumberError(data.getSubNumber())){
                throw new BusinessException("第" + rowIndex + "行数据有误，子题号需为数字");
            }
            if(StrUtil.isNotBlank(data.getSubNumber()) && !questionNumSet.contains(data.getQuestionNumber())){
                throw new BusinessException("第" + rowIndex + "行数据有误，子题号对应的题号不存在");
            }
            if(hasQuestionNum.contains(generateKey(data.getQuestionNumber(), data.getSubNumber()))){
                throw new BusinessException("第" + rowIndex + "行数据有误，该量表已存在对应题号的数据");
            }
        }
    }

    private String generateKey(String questionNum, String subQuestionNum){
        return questionNum + "." + subQuestionNum;
    }

    private boolean checkInputNumberError(String val){
        try {
            if(StrUtil.isNotBlank(val)){
                Integer.parseInt(val);
            }
            return false;
        }catch (Exception ignore){
            return true;
        }
    }

    public void exportExcel(HttpServletResponse response, String fileName, List<ScaleQuestionQueryDTO> questionList) {
        List<ScaleQuestionExcelDataDTO> dataList = this.convertToExcelData(questionList);
        ExcelUtil.write(response, ScaleQuestionExcelDataDTO.class, fileName, dataList);
    }

    private List<ScaleQuestionExcelDataDTO> convertToExcelData(List<ScaleQuestionQueryDTO> questionList) {
        return questionList.stream().map(question -> {
            ScaleQuestionExcelDataDTO data = ScaleQuestionExcelDataDTO.builder().build();
            BeanUtils.copyProperties(question, data);
            List<ScaleQuestionOption> optionList = question.getOptions();
            if (CollUtil.isNotEmpty(optionList)) {
                for (int index = 1; index <= optionList.size(); index++) {
                    ReflectUtil.setFieldValue(data, "answer" + index, optionList.get(index - 1).getValue());
                    ReflectUtil.setFieldValue(data, "answerScore" + index, optionList.get(index - 1).getScore());
                    ReflectUtil.setFieldValue(data, "property" + index, optionList.get(index - 1).getResult());
                }
            }
            return data;
        }).collect(Collectors.toList());
    }
}