package com.wftk.scale.biz.constant.enums;


import lombok.Getter;

import java.util.Arrays;

/**
 * @EnumName: WhetherEnum
 * @Description: 是否枚举
 * @Author: mq
 * @Date: 2024/11/28
 * @Version: 1.0
 **/
@Getter
public enum WhetherEnum {

    YES(true, "是"),
    NO(false, "否");
    private Boolean status;
    private String value;

    WhetherEnum(Boolean status, String value) {
        this.status = status;
        this.value = value;
    }

    public static Boolean getStatus(String value) {
        return Arrays.stream(WhetherEnum.values())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .map(WhetherEnum::getStatus)
                .orElse(null);
    }

    public static String getValue(Boolean status) {
        return Arrays.stream(WhetherEnum.values())
                .filter(e -> Boolean.compare(e.getStatus(), status) == 0)
                .findFirst()
                .map(WhetherEnum::getValue)
                .orElse(null);
    }
}
