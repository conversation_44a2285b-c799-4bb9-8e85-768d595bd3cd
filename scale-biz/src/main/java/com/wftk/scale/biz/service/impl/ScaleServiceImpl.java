package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.constant.enums.ScaleEnum;
import com.wftk.scale.biz.dto.scale.ScaleDTO;
import com.wftk.scale.biz.dto.scale.ScaleListingDetailDTO;
import com.wftk.scale.biz.dto.scale.ScaleQueryDTO;
import com.wftk.scale.biz.dto.scale.ScaleSerialDTO;
import com.wftk.scale.biz.entity.Scale;
import com.wftk.scale.biz.event.ScaleCreateEvent;
import com.wftk.scale.biz.event.ScaleCreateRelationEvent;
import com.wftk.scale.biz.event.ScaleDelEvent;
import com.wftk.scale.biz.event.publisher.ScaleCreatePublisher;
import com.wftk.scale.biz.event.publisher.ScaleCreateRelationPublisher;
import com.wftk.scale.biz.event.publisher.ScaleDelPublisher;
import com.wftk.scale.biz.mapper.ScaleMapper;
import com.wftk.scale.biz.service.ScaleCombinationDetailService;
import com.wftk.scale.biz.service.ScaleLatestService;
import com.wftk.scale.biz.service.ScaleQuestionService;
import com.wftk.scale.biz.service.ScaleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 量表主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Slf4j
@Service
public class ScaleServiceImpl extends ServiceImpl<ScaleMapper, Scale> implements ScaleService {

    /*
     * 量表复制名称标记符
     */
    private static final String SCALE_COPY_MARK = "_(复制)_";

    @Autowired
    private ScaleMapper scaleMapper;

    @Autowired
    private ScaleLatestService scaleLatestService;

    @Autowired
    private ScaleQuestionService scaleQuestionService;

    @Autowired
    private ScaleDelPublisher scaleDelPublisher;

    @Autowired
    private ScaleCreatePublisher scaleCreatePublisher;

    @Autowired
     private ScaleCreateRelationPublisher scaleCreateRelationPublisher;

    @Resource
    private ScaleCombinationDetailService scaleCombinationDetailService;

    public boolean validScaleName(Long scaleId, String scaleName) {
        boolean checkResult = false;
        if (StrUtil.isEmpty(scaleName)) {
            return checkResult;
        }
        Scale rawData = scaleMapper.selectById(scaleId);
        String scaleCode = ObjectUtil.isNull(rawData) ? null : rawData.getCode();
        checkResult = scaleMapper.validScaleNameExists(scaleId, scaleCode, scaleName);
        return checkResult;
    }

    @Override
    public boolean vaildScaleTimeRange(Integer minTimeLimit, Integer maxTimeLimit) {
        boolean checkResult = false;
        minTimeLimit = ObjectUtil.defaultIfNull(minTimeLimit, 0);
        maxTimeLimit = ObjectUtil.defaultIfNull(maxTimeLimit, 0);
        if ((minTimeLimit < 0 || maxTimeLimit < 0) || (minTimeLimit > maxTimeLimit && maxTimeLimit > 0)) {
            checkResult = true;
        }
        return checkResult;
    }

    @Override
    public boolean vaildCompletedStatus(Long scaleId) {
        Scale rawData = scaleMapper.selectById(scaleId);
        Integer status = ObjectUtil.isNotNull(rawData) ? rawData.getStatus() : ScaleEnum.SCALE_UN_COMPLETED.getCode();
        if (ScaleEnum.SCALE_COMPLETED.getCode().equals(status)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean vaildPriceLessThanOriginalPrice(BigDecimal originalPrice, BigDecimal price) {
        originalPrice = ObjUtil.defaultIfNull(originalPrice, BigDecimal.ZERO);
        price = ObjUtil.defaultIfNull(price, BigDecimal.ZERO);
        if (price.compareTo(originalPrice) <= 0) {
            return true;
        }
        return false;
    }

    private String createScaleCode() {
        //根据规则生成量表编码,当前简单实现
        return IdUtil.simpleUUID();
    }

    private String createVersion() {
        //根据规则生成量表版本号,当前简单实现
        return IdUtil.nanoId();
    }

    private String createScaleName(String name) {
        //根据规则生成量表复制名称,当前简单实现
        StringBuilder builder = new StringBuilder();
        builder.append(name)
                .append(SCALE_COPY_MARK)
                .append(DateUtil.formatTime(DateUtil.date()));
        return builder.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(Scale scale) {
            scale.setCode(this.createScaleCode());
            scale.setVersion(this.createVersion());
            scale.setSort(ObjUtil.defaultIfNull(scale.getSort(), 0));
            scale.setMinTimeLimit(ObjUtil.defaultIfNull(scale.getMinTimeLimit(), 0));
            scale.setMaxTimeLimit(ObjUtil.defaultIfNull(scale.getMaxTimeLimit(), 0));
            scaleMapper.insert(scale);
            scaleLatestService.saveOrUpdateScaleLatest(scale.getId(), scale.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(Scale scale) {
            Long oldScaleId = scale.getId();
            Scale rawData = scaleMapper.selectById(oldScaleId);
            BeanUtils.copyProperties(scale, rawData);
            scaleMapper.updateById(rawData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long scaleId) {
        String opUser = AuthenticationHolder.getAuthentication().getAuthUser().getAccount();
        ScaleDelEvent delEventDTO = ScaleDelEvent.builder()
                .oldScaleId(scaleId)
                .opUser(opUser)
                .build();
        //发布删除事件,删除关联的数据,此处存在缺陷后期优化
        scaleDelPublisher.publishEvent(delEventDTO);
        scaleMapper.deleteById(scaleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateComplateStatus(Long scaleId, Integer complateStatus) {
        AuthUser<?> authUser = AuthenticationHolder.getAuthentication().getAuthUser();
            this.versionControl(scaleId, complateStatus, authUser);
            scaleMapper.updateComplateStatus(scaleId, complateStatus, authUser.getId().toString());
    }

    protected Long versionControl(Long scaleId, Integer status, AuthUser<?> authUser) {
        Long newScaleId = null;
        //未完成状态:全量复制量表以及关联的基础数据,在复制的数据上进行修改。
        Scale rawData = scaleMapper.selectById(scaleId);
        String scaleCode = rawData.getCode();
        String scaleName = rawData.getName();
        //判断当前更新操作前量表是否处于已完成状态
        boolean isCompleted = ScaleEnum.SCALE_COMPLETED.getCode().equals(rawData.getStatus());
        //判断当前更新操作是否将量表设置为未完成状态
        boolean isUnCompleted = ScaleEnum.SCALE_UN_COMPLETED.getCode().equals(status);
        if (isUnCompleted && isCompleted) {
            //基于原始scaleCode,生成新的量表数据版本,将新版量表ID更新至版本控制表scale_latest.
            newScaleId = this.generateNewScale(rawData, scaleCode, scaleName, authUser);
            this.copyRelationData(scaleId, newScaleId, authUser.getAccount());
        }
        return newScaleId;
    }

    @Override
    public List<ScaleQueryDTO> selectListByScaleNameAndCompleteStatus(String name, Integer complateStatus, Long combinationId) {
        List<ScaleQueryDTO> list = scaleMapper.selectListByScaleNameAndCompleteStatus(name, complateStatus);
        //组合量表选择的单个量表与量表无关，即使是切换了版本，旧的数据仍然保留，可以同时有多个版本的量表
        if(combinationId != null){
            List<ScaleQueryDTO> byCombinationId = scaleCombinationDetailService.findByCombinationId(combinationId);
            Set<Long> idSet = list.stream().map(ScaleQueryDTO::getId).collect(Collectors.toSet());
            if(CollUtil.isNotEmpty(byCombinationId)){
                List<ScaleQueryDTO> collect = byCombinationId.stream().filter(it -> !idSet.contains(it.getId())).toList();
                list.addAll(collect);
            }
        }
        return buildScaleData(list);
    }

    @Override
    public Page<ScaleQueryDTO> selectScalePage(String name, Integer complateStatus) {
        return Page.doSelectPage(() -> scaleMapper.selectListByScaleNameAndCompleteStatus(name, complateStatus)).toPage(this::buildScaleData);
    }

    @Override
    public ScaleQueryDTO findByScaleId(Long scaleId) {
        Scale scale = scaleMapper.selectById(scaleId);
        if (ObjectUtil.isNull(scale)) {
            return null;
        }
        ScaleQueryDTO dto = this.buildScaleData(scale);
        return dto;
    }

    @Override
    public ScaleQueryDTO findByScaleCode(String scaleCode) {
        Scale scale = scaleMapper.getLatestScaleByCode(scaleCode);
        ScaleQueryDTO dto = this.buildScaleData(scale);
        return dto;
    }

    public ScaleQueryDTO buildScaleData(Scale scale) {
        if (ObjUtil.isNull(scale)) {
            throw new BusinessException("系统错误");
        }
        ScaleQueryDTO dto = ScaleQueryDTO.builder().build();
        BeanUtils.copyProperties(scale, dto);
        Integer numOfQuestion = scaleQuestionService.getNumOfQuestion(scale.getId());
        dto.setNumOfQuestion(numOfQuestion);
        return dto;
    }

    public List<ScaleQueryDTO> buildScaleData(List<ScaleQueryDTO> list) {
        list = CollUtil.isEmpty(list) ? List.of() : list;
        return list.stream().map(item -> {
            Integer numOfQuestion = scaleQuestionService.getNumOfQuestion(item.getId());
            item.setNumOfQuestion(numOfQuestion);
            return item;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyScale(Long scaleId) {
        Scale rawData = scaleMapper.selectById(scaleId);
        if (ObjectUtil.isNull(rawData)) {
            return null;
        }
        try {
            AuthUser<?> opUser = AuthenticationHolder.getAuthentication().getAuthUser();
            String scaleName = this.createScaleName(rawData.getName());
            Long newScaleId = this.generateNewScale(rawData, null, scaleName, opUser);
            this.copyRelationData(scaleId, newScaleId, opUser.getAccount());
            return newScaleId;
        } catch (Exception e) {
            log.error("复制量表发生异常,量表ID:{}", scaleId, e);
            throw e;
        }
    }

    @Override
    public String getTargetCodeById(Long targetId) {
        Scale scale = scaleMapper.selectById(targetId);
        if (ObjectUtil.isNull(scale)) {
            return null;
        }
        return scale.getCode();
    }

    @Override
    public Scale getLatestScaleByCode(String scaleCode) {
        return scaleMapper.getLatestScaleByCode(scaleCode);
    }

    @Override
    public List<Scale> getLatestScaleListByCodes(List<String> scaleCode) {
        return scaleMapper.getLatestScaleListByCodes(scaleCode);
    }

    @Override
    public List<ScaleDTO> getListedScale(Long scaleId, String scaleCode, String scaleName, Long scaleType, Integer listingShowType, String terminalCode) {
        return scaleMapper.getListedScale(scaleId, scaleCode, scaleName, scaleType, listingShowType, terminalCode);
    }

    @Override
    public ScaleListingDetailDTO getListedScaleDetail(String scaleCode, String terminalCode, Long scaleListingId) {
        return scaleMapper.getListedScaleDetail(scaleCode, terminalCode, scaleListingId);
    }

    @Override
    public List<ScaleListingDetailDTO> getScaleListingDetailDTO(List<ScaleSerialDTO> scaleSerials, String terminalCode) {
        return scaleMapper.getScaleListingDetailDTO(scaleSerials, terminalCode);
    }

    private Long generateNewScale(Scale oldScale, String scaleCode, String scaleName, AuthUser<?> authUser) {
        Scale newScale = new Scale();
        BeanUtils.copyProperties(oldScale, newScale);
        LocalDateTime now = LocalDateTime.now();
        scaleCode = StrUtil.isEmpty(scaleCode) ? this.createScaleCode() : scaleCode;
        newScale.setId(null);
        newScale.setCode(scaleCode);
        newScale.setStatus(ScaleEnum.SCALE_UN_COMPLETED.getCode());
        newScale.setVersion(this.createVersion());
        newScale.setName(scaleName);
        newScale.setCreateTime(now);
        newScale.setUpdateTime(now);
        newScale.setCreateBy(authUser.getId().toString());
        newScale.setUpdateBy(authUser.getId().toString());
        scaleMapper.insert(newScale);
        scaleLatestService.saveOrUpdateScaleLatest(newScale.getId(), scaleCode);
        return newScale.getId();
    }

    private void copyRelationData(Long oldScaleId, Long newScaleId, String opUser) {
        ScaleCreateEvent createEventDTO = ScaleCreateEvent.builder()
                .oldScaleId(oldScaleId)
                .newScaleId(newScaleId)
                .opUser(opUser)
                .build();
        scaleCreatePublisher.publishEvent(createEventDTO);
        //发布设置关联因子维度的事件
        scaleCreateRelationPublisher.publishEvent(ScaleCreateRelationEvent.builder()
                .newScaleId(newScaleId)
                .oldScaleId(oldScaleId)
                .opUser(opUser)
                .build()
        );
    }
}
