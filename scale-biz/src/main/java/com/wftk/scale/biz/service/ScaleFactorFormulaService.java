package com.wftk.scale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.wftk.scale.biz.entity.ScaleFactorFormula;

import java.util.List;

/**
 * <p>
 * 因子公式 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface ScaleFactorFormulaService extends IService<ScaleFactorFormula> {

    /*
     * @Author: mq
     * @Description: 校验因子公式名称是否已经存在
     * @Date: 2024/10/29 14:37
     * @Param: factorFormulaId-主键ID
     * @Param: formulaName-公式名称
     * @return: boolean
     **/
    boolean validFormulaName(Long factorFormulaId, String formulaName);

    /*
    * @Author: mq
    * @Description: 校验因子公式是否开启
    * @Date: 2024/11/26 17:13
    * @Param: factorFormulaId
    * @return: boolean
    **/
    boolean vaildFormulaEnabled(Long factorFormulaId);

    /*
    * @Author: mq
    * @Description: 校验因子公式是否被其它公式嵌套使用
    * @Date: 2024/11/25 13:55
    * @Param: factorFormulaId
    * @return: String
    **/
    String checkFormulaUsed(Long factorFormulaId);

    /*
     * @Author: mq
     * @Description: 创建因子公式信息
     * @Date: 2024/10/29 14:33
     * @Param: scaleFactorFormula-因子公式信息
     * @return: void
     **/
    void create(ScaleFactorFormula scaleFactorFormula);

    /*
     * @Author: mq
     * @Description: 根据ID删除因子公式信息
     * @Date: 2024/10/29 14:34
     * @Param: factorFormulaId-主键ID
     * @return: void
     **/
    void delete(Long factorFormulaId);

    /*
     * @Author: mq
     * @Description: 修改因子公式信息
     * @Date: 2024/10/29 14:34
     * @Param: scaleFactorFormula-因子公式信息
     * @return: void
     **/
    void modify(ScaleFactorFormula scaleFactorFormula);

    /*
     * @Author: mq
     * @Description: 根据ID更新因子公式数据的状态
     * @Date: 2024/10/29 14:35
     * @Param: factorFormulaId-主键ID
     * @Param: enable-是否启用(false-未启用 true-已启用)
     * @return: void
     **/
    void updateEnable(Long factorFormulaId, Boolean enable);

    void updateStatus(Long factorFormulaId, Boolean status);

    /* 
     * @Author: mq
     * @Description: 根据条件检索因子公式分页数据
     * @Date: 2024/10/29 14:36 
     * @Param: formulaName-公式名称
     * @return: com.wftk.pageable.spring.boot.autoconfigure.core.Page<com.wftk.scale.biz.entity.ScaleFactorFormula> 
     **/
    Page<ScaleFactorFormula> selectPage(String formulaName);

    /*
    * @Author: mq
    * @Description: 获取启用的因子公式列表
    * @Date: 2024/11/26 13:37
    * @Param: formulaName-公式名称
    * @Param: status-状态
    * @return: List<ScaleFactorFormula>
    **/
    List<ScaleFactorFormula> getListOfEnabled(String formulaName, Integer status);
}
