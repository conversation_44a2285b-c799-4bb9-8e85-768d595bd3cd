package com.wftk.scale.biz.manager.report.actuator;

import com.wftk.jackson.core.JSONObject;
import com.wftk.scale.biz.constant.ScaleReportConfSettingConstant;
import com.wftk.scale.biz.entity.ScaleReportConfSetting;
import com.wftk.scale.biz.entity.ScaleUserFactorResult;
import com.wftk.scale.biz.manager.report.dto.base.*;
import com.wftk.scale.biz.service.ScaleReportConfSettingService;
import com.wftk.scale.biz.service.ScaleUserFactorResultService;
import com.wftk.scale.biz.util.ScopeUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2025/9/10 20:41
 */
@Slf4j
@Component
public class ChartActuator {

    private final Integer CHART_SETTING = ScaleReportConfSettingConstant.Type.CHART_SETTING;

    @Resource
    ScaleReportConfSettingService scaleReportConfSettingService;

    @Resource
    ScaleUserFactorResultService scaleUserFactorResultService;

    public ChartDTO doChart(Long reportConfId, Integer reportType, Long resultId,List<Long> factorIds) {
        ChartDTO chartDTO = new ChartDTO();
        // 获取图表展示的类型
        List<ScaleReportConfSetting> settingList = scaleReportConfSettingService.selectScaleReportConfSettingByReportConfId(reportConfId, CHART_SETTING, reportType);
        if (settingList.isEmpty()) {
            log.warn("scaleReportConfSetting is null. reportConfId: {} type: {}", reportConfId, CHART_SETTING);
            return null;
        }
        if (settingList.size() > 1) {
            log.warn("scaleReportConfSetting data error. reportConfId: {} type: {} data: {}", reportConfId, CHART_SETTING, settingList);
            return null;
        }

        ScaleReportConfSetting scaleReportConfSetting = settingList.get(0);
        Integer chartType = scaleReportConfSetting.getChartType();
        chartDTO.setType(chartType);
        String style = scaleReportConfSetting.getValue();
        if (Objects.equals(chartType, ScaleReportConfSettingConstant.ChartType.BAR)) {
            List<ScaleUserFactorResult> scaleUserFactorResults = scaleUserFactorResultService.getListByResultId(resultId, factorIds);
            if(scaleUserFactorResults.size() != 1){
                log.warn("scaleUserFactorResult data error. resultId: {}  factorIds: {}", resultId, factorIds);
                return null;
            }
            ScaleUserFactorResult scaleUserFactorResult = scaleUserFactorResults.get(0);
            // TODO 需要加上总分字段并传入
            BarChartDTO barChartDTO = parseBarChart(style, scaleUserFactorResult.getScore(), scaleUserFactorResult.getScore());
            chartDTO.setChartData(barChartDTO);
        } else if (Objects.equals(chartType, ScaleReportConfSettingConstant.ChartType.SINGLE_HORIZONTAL)) {
            // 获取所有因子名称以及数据
            List<ScaleUserFactorResult> scaleUserFactorResults = scaleUserFactorResultService.getListByResultId(resultId, factorIds);
            List<String> factorNames = scaleUserFactorResults.stream().map(ScaleUserFactorResult::getFactorName).toList();
            List<BigDecimal> scoreBigDecimals = scaleUserFactorResults.stream().map(ScaleUserFactorResult::getScore).toList();
            List<String> scores = scoreBigDecimals.stream().map(BigDecimal::toPlainString).toList();
            ColumnarContent content = parseColumnar(factorNames,scores);
            chartDTO.setChartData(content);
        } else {
            log.warn("scaleReportConfSetting chartType illegal reportConfId: {},type: {}", reportConfId, CHART_SETTING);
            return null;
        }

        return chartDTO;
    }

    /**
     * 柱状图
     *
     * @return
     */
    private ColumnarContent parseColumnar(List<String> factorNames, List<String> data) {
        ColumnarContent content = new ColumnarContent();
        content.setSeries(factorNames);

        Columnar columnar = new Columnar();
        // TODO 需要修改为动态赋值
        columnar.setName("因子得分");
        columnar.setType("bar");
        columnar.setData(data);
        content.setCategories(columnar);

        return content;
    }

    private BarChartDTO parseBarChart(String barStyle, BigDecimal totalScore,BigDecimal score) {
        ColumnarStyleDTO columnarStyle = JSONObject.getInstance().parseObject(barStyle, ColumnarStyleDTO.class);
        BarChartDTO barChartDTO = new BarChartDTO();
        int lineNum = Integer.parseInt(columnarStyle.getLineNum());
        List<Bar> bars = new ArrayList<>();
        BigDecimal divide = totalScore.divide(new BigDecimal(lineNum));
        for (int i = 0; i < lineNum; i++) {
            Bar bar = new Bar();
            bar.setId(i + 1);
            bar.setScore(null);
            List<BigDecimal> scopes = new ArrayList<>();
            scopes.add(new BigDecimal(i).multiply(divide));
            scopes.add(new BigDecimal(i + 1).multiply(divide));
            if(ScopeUtil.inScope(scopes,score)){
                bar.setScore(score);
            }
            bars.add(bar);
        }

        barChartDTO.setBars(bars);
        barChartDTO.setLeft(columnarStyle.getLineStartName());
        barChartDTO.setRight(columnarStyle.getLineEndName());

        return barChartDTO;
    }

}
