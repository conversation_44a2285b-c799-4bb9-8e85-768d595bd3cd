package com.wftk.scale.biz.event.publisher;

import com.wftk.scale.biz.event.SysUserChangeEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createDate 2024/12/4 17:27
 */
@Component
public class SysUserChangePublisher implements BaseEventPublisher<SysUserChangeEvent> {

    private final ApplicationEventPublisher applicationEventPublisher;

    public SysUserChangePublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Override
    public void publishEvent(SysUserChangeEvent sysUserChangeEvent) {
        applicationEventPublisher.publishEvent(sysUserChangeEvent);
    }
}
