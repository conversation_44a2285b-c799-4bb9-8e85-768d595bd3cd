package com.wftk.scale.biz.dto.report;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.FileSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.serializer.RFile;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.scale.biz.constant.FileConstant;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/12/6 14:53
 */
@Data
public class UserReportDTO {

    /**
     * 量表名称
     */
    private String scaleName;

    /**
     * 用户信息
     */
    private UserReportInfoDTO userInfo;

    /**
     * 总分
     */
    private BigDecimal totalScore;

    /**
     * 平均分
     */
    private BigDecimal avgScore;

    /**
     * 阳性数量
     */
    private Integer positiveCount;

    /**
     * 测评时间（秒）
     */
    private Long evaluationTime;

    /**
     * 结果解读
     */
    private UserReportReultIntroDTO resultIntro;

    /**
     * 预警标签
     */
    private UserReportWarnTagDTO warnTag;

    /**
     * 音频
     */
    @RFile(role = FileConstant.FILE_SCALE_SIGN_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(contentUsing = FileSerializer.class)
    private List<String> audio;

    /**
     * 视频
     */
    @RFile(role = FileConstant.FILE_SCALE_SIGN_ROLE, resourceManager = OSSConstant.BeanName.SIGNED)
    @JsonSerialize(contentUsing = FileSerializer.class)
    private List<String> video;

    /**
     * 建议
     */
    private List<UserReportSuggestionDTO> suggestion;

    /**
     * 因子分析
     */
    private List<UserReportFactorAnalysisDTO> factorAnalysis;

    /**
     * 测评开始时间
     */
    private String startTime;



}
