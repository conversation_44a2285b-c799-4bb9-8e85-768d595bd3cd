package com.wftk.scale.biz.dto.user;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/11/27 19:14
 */
@Data
public class DepartmentQueryDTO {

    private Long id;

    private String code;

    private String name;

    private String managerName;

    private String managerPhone;

    private String managerEmail;

    private Long parentId;

    private String parentName;

    private Integer sort;

    private Boolean enable;

    private String remark;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private String updateBy;

    /**
     * 终端编码
     */
    private List<String> terminalCode;

    private List<DepartmentQueryDTO> children;

}
