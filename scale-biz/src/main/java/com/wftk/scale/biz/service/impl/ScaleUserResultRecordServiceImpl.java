package com.wftk.scale.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.scale.biz.constant.enums.ScaleQuestionScaleTypeEnum;
import com.wftk.scale.biz.dto.scale.ScaleQuestionQueryDTO;
import com.wftk.scale.biz.entity.ScaleQuestionOption;
import com.wftk.scale.biz.entity.ScaleUserResultRecord;
import com.wftk.scale.biz.mapper.ScaleUserResultRecordMapper;
import com.wftk.scale.biz.service.ScaleQuestionService;
import com.wftk.scale.biz.service.ScaleUserFactorResultService;
import com.wftk.scale.biz.service.ScaleUserResultRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 测评回答答案表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
@Service
@Slf4j
public class ScaleUserResultRecordServiceImpl extends ServiceImpl<ScaleUserResultRecordMapper, ScaleUserResultRecord>
        implements ScaleUserResultRecordService {

    @Autowired
    private ScaleUserResultRecordMapper scaleUserResultRecordMapper;

    @Autowired
    private ScaleUserFactorResultService scaleUserFactorResultService;
    @Autowired
    private ScaleQuestionService scaleQuestionService;

    @Override
    public void create(Long resultId, List<ScaleUserResultRecord> list) {
        if (CollUtil.isEmpty(list)) {
            throw new BusinessException("测评答案不能为空");
        }
        // 防止提交数据重复
        List<ScaleUserResultRecord> unique = list.stream().filter(ObjUtil::isNotNull)
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(e -> e.getResultId() + "-" + e.getQuestionId() + "-" + e.getOptionId()))),
                        ArrayList::new));
        list = unique.stream().map(item -> {
//            item.setId(null);
            item.setResultId(resultId);
            item.setResult(ObjUtil.defaultIfNull(item.getResult(), false));

            ScaleQuestionQueryDTO detail = scaleQuestionService.detail(item.getScaleId(), item.getQuestionId());
            // 进行题目答案正反向计算分数
            if (detail != null && Objects.equals(detail.getScoringType(), ScaleQuestionScaleTypeEnum.REVERSE.getValue())
                    && CollectionUtil.isNotEmpty(detail.getOptions())) {
                // 反向计分转换答案得分，假如答案有ABCDE，A->E，B->D，C->C,D->B,E->A
                ScaleQuestionOption scaleQuestionOption = getReversedOption(detail.getOptions(), item.getId());
                item.setScore(ObjectUtil.isEmpty(scaleQuestionOption.getScore()) ? null : BigDecimal.valueOf(scaleQuestionOption.getScore()));
            }
            // 答案允许为空
//            else {
//                // 正向计分
//                item.setScore(ObjUtil.defaultIfNull(item.getScore(), BigDecimal.ZERO));
//            }

            return item;
        }).collect(Collectors.toList());
        // 保存测评答案
        this.saveBatch(list);
        try {
            scaleUserFactorResultService.saveFactorScore(resultId);
        } catch (Exception e) {
            if (CollUtil.isNotEmpty(list)) {
                baseMapper.deletedByIds(list.stream().map(ScaleUserResultRecord::getId).filter(Objects::nonNull).toList());// 弥补异步线程事务，直接删除数据
            }
            log.error("提交测评结果失败：{}.", resultId, e);
            throw new BusinessException("提交失败：" + e.getMessage());
        }

    }

    @Override
    public List<ScaleUserResultRecord> findByResultId(Long resultId) {
        return scaleUserResultRecordMapper.findByResultId(resultId);
    }

    /**
     * 题目反向转换，假如答案有ABCDE，A->E，B->D，C->C,D->B,E->A
     * 
     * @return ScaleQuestionOption
     */
    public static ScaleQuestionOption getReversedOption(List<ScaleQuestionOption> allOptions,
            Long scaleUserResultRecordId) {
        // 获取选项总数
        int size = allOptions.size();

        // 找到选中答案在列表中的位置（1-based）
        int selectedIndex = -1;
        for (int i = 0; i < size; i++) {
            if (allOptions.get(i).getId().equals(scaleUserResultRecordId)) {
                selectedIndex = i + 1; // 转换为1-based位置
                break;
            }
        }
        // 校验选中的答案是否在列表中
        if (selectedIndex == -1) {
            throw new BusinessException("选中的答案不在选项列表中");
        }

        // 计算反转后的位置（1-based）
        int reversedIndex = size + 1 - selectedIndex;

        // 转换为0-based索引并返回对应的选项
        return allOptions.get(reversedIndex - 1);
    }

    @Override
    public ScaleUserResultRecord findByResultIdAndQuestionId(Long resultId, Long questionId) {
        return baseMapper.findOneByResultIdAndQuestionId(resultId, questionId);
    }
}
