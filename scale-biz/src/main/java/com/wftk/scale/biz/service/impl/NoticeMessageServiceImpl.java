package com.wftk.scale.biz.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.scale.biz.constant.enums.UserTypeEnum;
import com.wftk.scale.biz.entity.NoticeMessage;
import com.wftk.scale.biz.entity.ScaleListingUserRecord;
import com.wftk.scale.biz.entity.ScaleWarn;
import com.wftk.scale.biz.ext.notice.NoticeHandler;
import com.wftk.scale.biz.ext.notice.NoticeHandlerRegister;
import com.wftk.scale.biz.ext.notice.NoticeResult;
import com.wftk.scale.biz.ext.notice.email.ScaleWarnEmailNoticeRequest;
import com.wftk.scale.biz.ext.notice.enums.NoticeEventEnum;
import com.wftk.scale.biz.ext.notice.enums.NoticeMessageStatusEnum;
import com.wftk.scale.biz.ext.notice.enums.NoticeTypeEnum;
import com.wftk.scale.biz.ext.notice.sms.ScaleListingUserRecordSmsNoticeRequest;
import com.wftk.scale.biz.ext.notice.sms.ScaleWarnSmsNoticeRequest;
import com.wftk.scale.biz.mapper.NoticeMessageMapper;
import com.wftk.scale.biz.mapper.UserMapper;
import com.wftk.scale.biz.service.NoticeMessageService;
import com.wftk.scale.biz.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03 14:10:07
 */
@Service
@Slf4j
public class NoticeMessageServiceImpl extends ServiceImpl<NoticeMessageMapper, NoticeMessage> implements NoticeMessageService {

    @Autowired
    private NoticeHandlerRegister noticeHandlerRegister;

    @Autowired
    private UserMapper userMapper;

    @Override
    public void updateNextCountTime(NoticeMessage noticeMessage) {
        Integer count = noticeMessage.getCount();
        if (count == null) {
            count = 0;
        }
        LocalDateTime nextTime = noticeMessage.getNextTime();
        if (nextTime == null) {
            nextTime = LocalDateTime.now();
        }
        DateTime nextDateTime = DateUtil.offsetMinute(new DateTime(nextTime), (int) (15 * (Math.pow(2, count))));
        noticeMessage.setNextTime(nextDateTime.toLocalDateTime());
        noticeMessage.setCount(count + 1);
        updateById(noticeMessage);
    }

    @Override
    public List<NoticeMessage> getNeedSendNoticeMessage(Integer maxNoticeCount) {
        return baseMapper.getNeedSendNoticeMessage(maxNoticeCount);
    }

    @Override
    public NoticeResult sendNoticeByMessage(NoticeMessage noticeMessage) {
        if (noticeMessage == null) {
            throw new BusinessException("消息不存在");
        }
        try {
            Integer status = noticeMessage.getStatus();
            if (!(NoticeMessageStatusEnum.FAIL.getValue().equals(status) || NoticeMessageStatusEnum.WAIT.getValue().equals(status))) {
                throw new BusinessException("仅限失败、待发送状态");
            }
            NoticeHandler handler = noticeHandlerRegister.getHandler(NoticeTypeEnum.getByName(noticeMessage.getChannel()));
            noticeMessage.setStatus(NoticeMessageStatusEnum.ING.getValue());
            updateById(noticeMessage);

            updateNextCountTime(noticeMessage);

            NoticeResult noticeResult = handler.sendNotice(handler.buildNoticeRequest(noticeMessage));

            noticeMessage.setStatus(noticeResult.isOk()?NoticeMessageStatusEnum.OK.getValue():NoticeMessageStatusEnum.FAIL.getValue());
            noticeMessage.setRemark(noticeResult.getMessage());
            updateById(noticeMessage);
            return noticeResult;
        } catch (Exception e) {
            log.error("send notice by message is error. ", e);
            noticeMessage.setStatus(NoticeMessageStatusEnum.FAIL.getValue());
            noticeMessage.setRemark(e.getMessage());
            updateById(noticeMessage);
            return NoticeResult.builder().ok(false).message(e.getMessage()).build();
        }



    }

    @Override
    public NoticeResult sendScaleWarnNoticeByMessage(ScaleWarn scaleWarn) {
        Integer noticeType = scaleWarn.getNoticeType();
        NoticeTypeEnum noticeTypeEnum = NoticeTypeEnum.getNoticeTypeEnumByCode(noticeType);
        if (noticeTypeEnum == null) {// 等于空直接返回成功，例如标注红色，无需要发送通知
            return NoticeResult.builder().ok(true).build();
        }
        NoticeMessage noticeMessage = null;
        UserVO userVO = userMapper.selectByUserId(scaleWarn.getReceivingWarnUserId());
        switch (noticeTypeEnum) {
            case SMS -> {
                ScaleWarnSmsNoticeRequest.ScaleWarnSmsParams scaleWarnSmsParams = ScaleWarnSmsNoticeRequest.ScaleWarnSmsParams
                        .builder()
                        .build();// TODO 模板内容
                String content = JSONObject.getInstance().toJSONString(new ScaleWarnSmsNoticeRequest(userVO.getPhone(), scaleWarnSmsParams));
                noticeMessage = initialize(scaleWarn.getId().toString(),
                        userVO.getPhone(),
                        NoticeTypeEnum.SMS,
                        UserTypeEnum.BIZ_UER,
                        NoticeEventEnum.SCALE_WARNING,
                        content);
            }
            case EMAIL -> {
                ScaleWarnEmailNoticeRequest.ScaleWarnEmailNoticeRequestParams scaleWarnEmailNoticeRequestParams = ScaleWarnEmailNoticeRequest.ScaleWarnEmailNoticeRequestParams
                        .builder()
                        .build();// TODO 邮件内容，后期加
                String content = JSONObject.getInstance().toJSONString(new ScaleWarnEmailNoticeRequest(userVO.getEmail(), scaleWarnEmailNoticeRequestParams));
                noticeMessage = initialize(scaleWarn.getId().toString(),
                        userVO.getEmail(),
                        NoticeTypeEnum.EMAIL,
                        UserTypeEnum.BIZ_UER,
                        NoticeEventEnum.SCALE_WARNING,
                        content);
            }
            default -> {
                log.error("undefined notice type. {}", noticeType);
            }
        }
        return sendNoticeByMessage(noticeMessage);
    }

    @Override
    public NoticeResult sendScaleListingUserRecordSMSMessage(ScaleListingUserRecord scaleListingUserRecord) {
        UserVO userVO = userMapper.selectByUserId(scaleListingUserRecord.getUserId());
        ScaleListingUserRecordSmsNoticeRequest.ScaleListingUserRecordSmsParams scaleListingUserRecordSmsParams = ScaleListingUserRecordSmsNoticeRequest.ScaleListingUserRecordSmsParams
                .builder()
                .build();// TODO 模板内容
        String content = JSONObject.getInstance().toJSONString(new ScaleListingUserRecordSmsNoticeRequest(userVO.getPhone(), scaleListingUserRecordSmsParams));
        NoticeMessage noticeMessage = initialize(scaleListingUserRecord.getId().toString(),
                userVO.getPhone(),
                NoticeTypeEnum.SMS,
                UserTypeEnum.BIZ_UER,
                NoticeEventEnum.SCALE_LISTING_USER_RECORD,
                content);


        return sendNoticeByMessage(noticeMessage);
    }

    private NoticeMessage initialize(String bizNo, String account,NoticeTypeEnum noticeTypeEnum, UserTypeEnum userTypeEnum, NoticeEventEnum noticeEventEnum, String content){
        NoticeMessage noticeMessage = new NoticeMessage();
        noticeMessage.setStatus(NoticeMessageStatusEnum.WAIT.getValue());
        noticeMessage.setEvent(noticeEventEnum.name());
        noticeMessage.setChannel(noticeTypeEnum.name());
        noticeMessage.setBizNo(bizNo);
        noticeMessage.setCount(0);
        noticeMessage.setNextTime(LocalDateTime.now());
        noticeMessage.setIdentifier(userTypeEnum.name());
        noticeMessage.setAccount(account);
        noticeMessage.setContent(content);
        save(noticeMessage);
        return noticeMessage;
    }

}
