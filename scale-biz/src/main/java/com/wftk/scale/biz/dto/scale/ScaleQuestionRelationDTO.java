package com.wftk.scale.biz.dto.scale;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: ScaleQuestionRelationDTO
 * @Description:
 * @Author: mq
 * @Date: 2024/11/26
 * @Version: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScaleQuestionRelationDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 量表ID
     */
    private Long scaleId;

    /**
     * 被建立关系的题干ID
     */
    private Long questionId;

    /**
     * 被建立关系的题号(格式化之后),例如：2.1  3.2
     */
    private String questionNumStr;

    /**
     * 被建立关系的题号
     */
    private String questionNumber;

    /**
     * 被建立关系的子题号
     */
    private String questionSubNumber;


    /**
     * 跳题逻辑: 1.单条件触发跳转; 2.多条件同时满足触发跳转; 3.多条件之一满足触发跳转;
     */
    private Integer type;

    /**
     * 关系策略
     */
    private String strategy;

    /**
     * 题号
     */
    private String questionNumbers;

    /**
     * 选项值
     */
    private String options;

    /**
     * 提示
     */
    private String reminder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    private Boolean deleted;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;
}



