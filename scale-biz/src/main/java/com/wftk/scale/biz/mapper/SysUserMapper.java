package com.wftk.scale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wftk.scale.biz.dto.user.sysuser.SysUserQueryDTO;
import com.wftk.scale.biz.entity.SysUser;
import com.wftk.scale.biz.vo.SysUserDetailVO;
import com.wftk.scale.biz.vo.SysUserVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 系统管理员 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 14:09:03
 */
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 根据账号查询用户
     * @param account
     * @param enable
     * @return
     */
    SysUser selectOneByAccount(@Param("account") String account, @Param("enable") Boolean enable);

    List<SysUserVO> queryListByQueryDto(SysUserQueryDTO dto);

    SysUserDetailVO detailById(@Param("id") Long id);
}
