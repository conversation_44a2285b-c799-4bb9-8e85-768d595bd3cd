package com.wftk.scale.biz.converter;

import com.wftk.scale.biz.entity.ScaleWarn;
import com.wftk.scale.biz.excel.model.ScaleWarnDetailExcelDataDTO;
import com.wftk.scale.biz.vo.ScaleWarnVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface ScaleWarnDetailConvert {

    ScaleWarnVO scaleWarnToVO(ScaleWarn scaleWarn);

    List<ScaleWarnDetailExcelDataDTO> scaleWarnVoListToExcelDataDTOList(List<ScaleWarnVO> scaleWarnVoList);
}
