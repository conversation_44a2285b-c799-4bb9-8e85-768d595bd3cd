package com.wftk.scale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.scale.biz.entity.TenantSetting;
import com.wftk.scale.biz.mapper.TenantSettingMapper;
import com.wftk.scale.biz.service.TenantSettingService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 应用配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 18:51:44
 */
@Service
public class TenantSettingServiceImpl extends ServiceImpl<TenantSettingMapper, TenantSetting> implements TenantSettingService {

    @Override
    public TenantSetting getTenantId(String tenantId) {
        return baseMapper.getTenantId(tenantId);
    }

    @Override
    public List<TenantSetting> getAll() {
        return baseMapper.getAll();
    }
}
