package com.wftk.scale.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 因子公式
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-02 14:11:54
 */
@TableName("scale_factor_formula")
public class ScaleFactorFormula implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 公式名称
     */
    private String name;

    /**
     * 公式编码
     */
    private String code;

    /**
     * 公式算法
     */
    private String formula;

    /**
     * 公式显示的标签
     */
    private String formulaLabel;

    private String description;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 执行操作编码
     */
    private String actionCode;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除状态: 0.未删除; 1.已删除;
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 是否启用: 0.未启用; 1.已启用;
     */
    private Boolean enable;

    /**
     * 是否内置公式: 0.非系统内置; 1.系统内置;
     */
    private Boolean builtin;

    /**
     * 上下架状态: 0.未上架; 1.已上架;
     */
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFormula() {
        return formula;
    }

    public void setFormula(String formula) {
        this.formula = formula;
    }

    public String getFormulaLabel() {
        return formulaLabel;
    }

    public void setFormulaLabel(String formulaLabel) {
        this.formulaLabel = formulaLabel;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getActionCode() {
        return actionCode;
    }

    public void setActionCode(String actionCode) {
        this.actionCode = actionCode;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Boolean getBuiltin() {
        return builtin;
    }

    public void setBuiltin(Boolean builtin) {
        this.builtin = builtin;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "ScaleFactorFormula{" +
            "id = " + id +
            ", name = " + name +
            ", code = " + code +
            ", formula = " + formula +
            ", formulaLabel = " + formulaLabel +
            ", description = " + description +
            ", sceneCode = " + sceneCode +
            ", actionCode = " + actionCode +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
            ", deleted = " + deleted +
            ", createBy = " + createBy +
            ", updateBy = " + updateBy +
            ", tenantId = " + tenantId +
            ", enable = " + enable +
            ", builtin = " + builtin +
            ", status = " + status +
        "}";
    }
}
