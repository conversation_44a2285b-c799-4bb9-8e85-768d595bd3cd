package com.wftk.scale.biz.dto.user;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DepartmentUpdateDTO {

    @NotNull(message = "机构ID不能为空")
    private Long id;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门编码
     */
    private String code;
    /**
     * 负责人名字
     */
    private String managerName;
    /**
     * 负责人联系电话
     */
    private String managerPhone;
    /**
     * 负责人邮箱
     */
    private String managerEmail;
    /**
     * 1开启，0关闭
     */
    private Boolean enable;
    /**
     * 终端编码
     */
    private List<String> terminalCode;
}
