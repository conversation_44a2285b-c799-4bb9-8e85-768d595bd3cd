package com.wftk.scale.biz.dto.user;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/11/27 19:14
 */
@Data
public class UserQueryDTO {

    private Long userId;

    private String userCode;

    private String userAccount;

    private String userName;

    private String departmentName;

    private Boolean enable;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private String updateBy;


}
